{"name": "candlestick-pattern-recognition-frontend", "version": "1.0.0", "description": "蜡烛图形态识别系统前端界面", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "serve -s build"}, "dependencies": {"@ant-design/icons": "^5.2.0", "antd": "^5.12.0", "axios": "^1.6.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-adapter-luxon": "^1.3.1", "chartjs-chart-financial": "^0.2.1", "chartjs-plugin-annotation": "^3.1.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.294.0", "luxon": "^3.6.1", "moment": "^2.29.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "devDependencies": {"@types/lodash": "^4.14.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^4.9.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}