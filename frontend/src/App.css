.App {
  text-align: center;
}

.layout {
  min-height: 100vh;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-area.dragover {
  border-color: #1890ff;
  background: #e6f7ff;
}

.pattern-card {
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
}

.pattern-card.bullish {
  border-left-color: #52c41a;
}

.pattern-card.bearish {
  border-left-color: #ff4d4f;
}

.pattern-card.neutral {
  border-left-color: #faad14;
}

.confidence-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.confidence-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.confidence-fill.high {
  background: #52c41a;
}

.confidence-fill.medium {
  background: #faad14;
}

.confidence-fill.low {
  background: #ff4d4f;
}

.chart-container {
  width: 100%;
  height: 400px;
  margin: 20px 0;
}

.pattern-summary {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  min-width: 100px;
}

.summary-number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.summary-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.data-table {
  margin-top: 20px;
}

.sample-data-button {
  margin-top: 16px;
}

.pattern-description {
  color: #666;
  font-size: 14px;
  margin-top: 8px;
  line-height: 1.5;
}

.pattern-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.pattern-time {
  color: #999;
  font-size: 12px;
}

.pattern-confidence {
  font-weight: bold;
}

.trend-context {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  margin-left: 8px;
}

.trend-context.uptrend {
  background: #52c41a;
}

.trend-context.downtrend {
  background: #ff4d4f;
}

.trend-context.sideways {
  background: #faad14;
}

.trend-context.unknown {
  background: #d9d9d9;
  color: #666;
}
