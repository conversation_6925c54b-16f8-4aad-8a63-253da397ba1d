import axios from 'axios';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

/**
 * 分析蜡烛图形态
 * @param {Array} candles - 蜡烛线数据数组
 * @param {number} startIndex - 开始分析的索引
 * @param {number} endIndex - 结束分析的索引
 * @returns {Promise} 分析结果
 */
export const analyzePatterns = async (candles, startIndex = 0, endIndex = null) => {
  try {
    const response = await api.post('/patterns/analyze', {
      candles: candles.map(candle => ({
        open: parseFloat(candle.open),
        high: parseFloat(candle.high),
        low: parseFloat(candle.low),
        close: parseFloat(candle.close),
        volume: parseFloat(candle.volume || 1000),
        timestamp: candle.timestamp || new Date().toISOString(),
      })),
      start_index: startIndex,
      end_index: endIndex,
    });
    
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || '形态分析失败');
  }
};

/**
 * 获取支持的形态列表
 * @returns {Promise} 支持的形态列表
 */
export const getSupportedPatterns = async () => {
  try {
    const response = await api.get('/patterns/list');
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || '获取形态列表失败');
  }
};

/**
 * 带过滤条件的形态分析
 * @param {Array} candles - 蜡烛线数据数组
 * @param {Object} filters - 过滤条件
 * @returns {Promise} 过滤后的分析结果
 */
export const analyzeWithFilters = async (candles, filters = {}) => {
  try {
    const params = new URLSearchParams();
    
    if (filters.minConfidence !== undefined) {
      params.append('min_confidence', filters.minConfidence);
    }
    
    if (filters.patternTypes && filters.patternTypes.length > 0) {
      filters.patternTypes.forEach(type => {
        params.append('pattern_types', type);
      });
    }
    
    if (filters.signals && filters.signals.length > 0) {
      filters.signals.forEach(signal => {
        params.append('signals', signal);
      });
    }
    
    const response = await api.post(`/patterns/filter?${params.toString()}`, {
      candles: candles.map(candle => ({
        open: parseFloat(candle.open),
        high: parseFloat(candle.high),
        low: parseFloat(candle.low),
        close: parseFloat(candle.close),
        volume: parseFloat(candle.volume || 1000),
        timestamp: candle.timestamp || new Date().toISOString(),
      })),
    });
    
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || '过滤分析失败');
  }
};

/**
 * 获取形态统计信息
 * @returns {Promise} 统计信息
 */
export const getPatternStatistics = async () => {
  try {
    const response = await api.get('/patterns/statistics');
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.detail || '获取统计信息失败');
  }
};

/**
 * 健康检查
 * @returns {Promise} 健康状态
 */
export const healthCheck = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    throw new Error('服务不可用');
  }
};

export default api;
