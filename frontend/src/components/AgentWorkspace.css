/* 智能体工作台样式 */

.agent-workspace {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workspace-header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
}

.workspace-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workspace-tabs .ant-tabs-content-holder {
  padding: 20px;
}

/* 智能体卡片样式 */
.agent-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.agent-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-info {
  margin-bottom: 16px;
}

.agent-info p {
  margin: 8px 0;
  font-size: 14px;
}

.agent-info strong {
  color: #595959;
  margin-right: 8px;
}

/* 消息流样式 */
.message-flow-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-item {
  padding: 8px 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-header strong {
  color: #1890ff;
}

.message-time {
  color: #8c8c8c;
  font-size: 12px;
}

.message-content {
  padding-left: 16px;
}

.message-content p {
  margin: 4px 0 0 0;
  color: #595959;
  font-size: 14px;
}

/* 工作流样式 */
.workflow-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-progress {
  margin-bottom: 20px;
}

.workflow-timeline {
  margin-top: 16px;
}

.stage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stage-duration {
  color: #8c8c8c;
  font-size: 12px;
}

/* 统计卡片样式 */
.stats-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #595959;
  font-weight: 500;
}

.stat-value {
  color: #1890ff;
  font-weight: 600;
}

/* 系统状态样式 */
.system-status {
  padding: 16px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-detail {
  color: #8c8c8c;
  font-size: 12px;
}

/* 加载状态样式 */
.agent-workspace-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.agent-workspace-loading p {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-workspace {
    padding: 10px;
  }
  
  .workspace-header {
    padding: 12px 16px;
  }
  
  .workspace-header h2 {
    font-size: 20px;
  }
  
  .workspace-tabs .ant-tabs-content-holder {
    padding: 16px;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .stage-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-card,
.message-flow-card,
.workflow-card,
.stats-card {
  animation: fadeIn 0.5s ease-out;
}

/* 状态指示器 */
.ant-badge-status-dot {
  width: 8px;
  height: 8px;
}

.ant-badge-status-success {
  background-color: #52c41a;
}

.ant-badge-status-processing {
  background-color: #1890ff;
}

.ant-badge-status-warning {
  background-color: #faad14;
}

.ant-badge-status-error {
  background-color: #ff4d4f;
}

/* 进度条自定义样式 */
.ant-progress-line {
  margin-bottom: 0;
}

.ant-progress-success-bg,
.ant-progress-bg {
  border-radius: 4px;
}

/* 时间线自定义样式 */
.ant-timeline-item-content {
  margin-left: 20px;
}

.ant-timeline-item-tail {
  border-left: 2px solid #f0f0f0;
}

/* 标签页自定义样式 */
.ant-tabs-tab {
  font-weight: 500;
}

.ant-tabs-tab-active {
  color: #1890ff !important;
}

/* 卡片标题样式 */
.ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 徽章样式 */
.ant-badge {
  font-weight: 500;
}

.ant-badge-count {
  background-color: #1890ff;
  border-color: #1890ff;
}
