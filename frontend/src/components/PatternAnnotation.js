import React from 'react';
import { Tag, Tooltip } from 'antd';

// 形态详细信息配置
const PATTERN_DETAILS = {
  'hammer': {
    chinese: '锤子线',
    english: 'Hammer',
    description: '底部反转信号，实体小，下影线长，上影线短或无',
    signal: 'bullish',
    reliability: 'medium',
    context: '下降趋势末期出现'
  },
  'hanging_man': {
    chinese: '上吊线',
    english: 'Hanging Man',
    description: '顶部反转信号，实体小，下影线长，上影线短或无',
    signal: 'bearish',
    reliability: 'medium',
    context: '上升趋势末期出现'
  },
  'doji': {
    chinese: '十字线',
    english: 'Doji',
    description: '市场犹豫不决，开盘价与收盘价几乎相等',
    signal: 'neutral',
    reliability: 'medium',
    context: '趋势可能反转'
  },
  'long_legged_doji': {
    chinese: '长腿十字线',
    english: 'Long-Legged Doji',
    description: '强烈的不确定性，上下影线都很长',
    signal: 'neutral',
    reliability: 'high',
    context: '市场方向不明'
  },
  'gravestone_doji': {
    chinese: '墓碑十字线',
    english: 'Gravestone Doji',
    description: '顶部反转信号，上影线长，无下影线',
    signal: 'bearish',
    reliability: 'high',
    context: '上升趋势顶部'
  },
  'dragonfly_doji': {
    chinese: '蜻蜓十字线',
    english: 'Dragonfly Doji',
    description: '底部反转信号，下影线长，无上影线',
    signal: 'bullish',
    reliability: 'high',
    context: '下降趋势底部'
  },
  'spinning_top': {
    chinese: '纺锤线',
    english: 'Spinning Top',
    description: '市场犹豫，实体小，上下影线较长',
    signal: 'neutral',
    reliability: 'low',
    context: '趋势可能变化'
  },
  'marubozu': {
    chinese: '光头光脚线',
    english: 'Marubozu',
    description: '强烈的趋势信号，无上下影线',
    signal: 'continuation',
    reliability: 'high',
    context: '趋势持续'
  },
  'engulfing_bullish': {
    chinese: '看涨吞没',
    english: 'Bullish Engulfing',
    description: '强烈的底部反转信号，阳线完全吞没前一根阴线',
    signal: 'bullish',
    reliability: 'high',
    context: '下降趋势反转'
  },
  'engulfing_bearish': {
    chinese: '看跌吞没',
    english: 'Bearish Engulfing',
    description: '强烈的顶部反转信号，阴线完全吞没前一根阳线',
    signal: 'bearish',
    reliability: 'high',
    context: '上升趋势反转'
  },
  'dark_cloud_cover': {
    chinese: '乌云盖顶',
    english: 'Dark Cloud Cover',
    description: '顶部反转信号，阴线开盘高于前阳线最高价，收盘在前阳线实体中部以下',
    signal: 'bearish',
    reliability: 'high',
    context: '上升趋势顶部'
  },
  'piercing_pattern': {
    chinese: '刺透形态',
    english: 'Piercing Pattern',
    description: '底部反转信号，阳线开盘低于前阴线最低价，收盘在前阴线实体中部以上',
    signal: 'bullish',
    reliability: 'high',
    context: '下降趋势底部'
  },
  'harami_bullish': {
    chinese: '看涨孕线',
    english: 'Bullish Harami',
    description: '底部反转信号，小阳线完全包含在前一根大阴线内',
    signal: 'bullish',
    reliability: 'medium',
    context: '下降趋势可能反转'
  },
  'harami_bearish': {
    chinese: '看跌孕线',
    english: 'Bearish Harami',
    description: '顶部反转信号，小阴线完全包含在前一根大阳线内',
    signal: 'bearish',
    reliability: 'medium',
    context: '上升趋势可能反转'
  },
  'harami_cross': {
    chinese: '十字孕线',
    english: 'Harami Cross',
    description: '强烈反转信号，十字线包含在前一根大实体内',
    signal: 'reversal',
    reliability: 'high',
    context: '趋势强烈反转'
  },
  'morning_star': {
    chinese: '启明星',
    english: 'Morning Star',
    description: '底部反转信号，三根K线组成：大阴线+小实体+大阳线',
    signal: 'bullish',
    reliability: 'high',
    context: '下降趋势底部'
  },
  'evening_star': {
    chinese: '黄昏星',
    english: 'Evening Star',
    description: '顶部反转信号，三根K线组成：大阳线+小实体+大阴线',
    signal: 'bearish',
    reliability: 'high',
    context: '上升趋势顶部'
  },
  'three_white_soldiers': {
    chinese: '前进白色三兵',
    english: 'Three White Soldiers',
    description: '强烈看涨信号，三根连续上涨的阳线',
    signal: 'bullish',
    reliability: 'high',
    context: '强势上涨'
  },
  'three_black_crows': {
    chinese: '三只乌鸦',
    english: 'Three Black Crows',
    description: '强烈看跌信号，三根连续下跌的阴线',
    signal: 'bearish',
    reliability: 'high',
    context: '强势下跌'
  },
  'shooting_star': {
    chinese: '流星',
    english: 'Shooting Star',
    description: '顶部反转信号，实体小，上影线长，下影线短或无',
    signal: 'bearish',
    reliability: 'medium',
    context: '上升趋势顶部'
  },
  'inverted_hammer': {
    chinese: '倒锤子',
    english: 'Inverted Hammer',
    description: '底部反转信号，实体小，上影线长，下影线短或无',
    signal: 'bullish',
    reliability: 'medium',
    context: '下降趋势底部'
  }
};

// 获取信号颜色
const getSignalColor = (signal) => {
  switch (signal) {
    case 'bullish': return '#52c41a';
    case 'bearish': return '#ff4d4f';
    case 'reversal': return '#722ed1';
    case 'continuation': return '#1890ff';
    default: return '#faad14';
  }
};

// 获取可靠性标签
const getReliabilityTag = (reliability) => {
  const config = {
    high: { color: 'success', text: '高' },
    medium: { color: 'warning', text: '中' },
    low: { color: 'default', text: '低' }
  };
  return config[reliability] || config.medium;
};

const PatternAnnotation = ({ pattern, showEnglish = true, compact = false }) => {
  const details = PATTERN_DETAILS[pattern.pattern_name];
  
  if (!details) {
    return (
      <Tag color="default">
        {pattern.pattern_name}
      </Tag>
    );
  }

  const reliabilityTag = getReliabilityTag(details.reliability);
  const signalColor = getSignalColor(details.signal);

  const tooltipContent = (
    <div style={{ maxWidth: 300 }}>
      <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
        {details.chinese} ({details.english})
      </div>
      <div style={{ marginBottom: 8 }}>
        <strong>描述：</strong>{details.description}
      </div>
      <div style={{ marginBottom: 8 }}>
        <strong>出现环境：</strong>{details.context}
      </div>
      <div style={{ marginBottom: 8 }}>
        <strong>置信度：</strong>{(pattern.confidence * 100).toFixed(1)}%
      </div>
      <div>
        <Tag color={reliabilityTag.color} size="small">
          可靠性: {reliabilityTag.text}
        </Tag>
        <Tag color={details.signal === 'bullish' ? 'green' : details.signal === 'bearish' ? 'red' : 'blue'} size="small">
          {details.signal === 'bullish' ? '看涨' : 
           details.signal === 'bearish' ? '看跌' : 
           details.signal === 'reversal' ? '反转' : '持续'}
        </Tag>
      </div>
    </div>
  );

  if (compact) {
    return (
      <Tooltip title={tooltipContent} placement="top">
        <Tag 
          color={details.signal === 'bullish' ? 'green' : details.signal === 'bearish' ? 'red' : 'blue'}
          style={{ 
            cursor: 'pointer',
            fontSize: '11px',
            padding: '2px 6px'
          }}
        >
          {details.chinese}
        </Tag>
      </Tooltip>
    );
  }

  return (
    <Tooltip title={tooltipContent} placement="top">
      <div style={{ 
        display: 'inline-block',
        padding: '4px 8px',
        backgroundColor: signalColor + '15',
        border: `1px solid ${signalColor}`,
        borderRadius: '4px',
        cursor: 'pointer',
        margin: '2px'
      }}>
        <div style={{ 
          fontWeight: 'bold', 
          color: signalColor,
          fontSize: '12px'
        }}>
          {details.chinese}
          {showEnglish && (
            <div style={{ 
              fontSize: '10px', 
              color: '#666',
              fontWeight: 'normal'
            }}>
              {details.english}
            </div>
          )}
        </div>
        <div style={{ 
          fontSize: '10px', 
          color: '#999',
          marginTop: '2px'
        }}>
          置信度: {(pattern.confidence * 100).toFixed(1)}%
        </div>
      </div>
    </Tooltip>
  );
};

export default PatternAnnotation;
export { PATTERN_DETAILS, getSignalColor };
