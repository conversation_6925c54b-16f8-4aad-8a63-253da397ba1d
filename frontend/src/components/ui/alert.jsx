import React from 'react';

export const Alert = ({ children, className = '', ...props }) => {
  return (
    <div 
      className={`relative w-full rounded-lg border p-4 bg-blue-50 border-blue-200 text-blue-900 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

export const AlertDescription = ({ children, className = '', ...props }) => {
  return (
    <div 
      className={`text-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};
