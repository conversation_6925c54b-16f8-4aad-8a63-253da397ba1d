import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Brain, 
  Users, 
  MessageSquare, 
  TrendingUp, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  BarChart3
} from 'lucide-react';

import PatternValidation from './PatternValidation';
import AgentAnalysis from './AgentAnalysis';
import DebateViewer from './DebateViewer';
import SystemStatus from './SystemStatus';
import AgentDebateViewer from './AgentDebateViewer';

const TradingAgentsPanel = ({
  detectedPatterns = [],
  currentSymbol = 'AAPL',
  candleData = [],
  onValidationComplete,
  stockInfo = null
}) => {
  const [activeTab, setActiveTab] = useState('validation');
  const [systemStatus, setSystemStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [finalRecommendation, setFinalRecommendation] = useState(null);

  // 获取系统状态
  useEffect(() => {
    fetchSystemStatus();
  }, []);

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/trading-agents/status');
      const data = await response.json();
      setSystemStatus(data);
    } catch (error) {
      console.error('获取系统状态失败:', error);
    }
  };

  const getStatusBadge = () => {
    if (!systemStatus) return <Badge variant="secondary">检查中...</Badge>;
    
    if (systemStatus.status === 'success' && systemStatus.data?.initialized) {
      return <Badge variant="success" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        系统就绪
      </Badge>;
    } else {
      return <Badge variant="destructive">
        <AlertTriangle className="w-3 h-3 mr-1" />
        系统异常
      </Badge>;
    }
  };

  const getAgentCount = () => {
    if (!systemStatus?.data?.agents) return 0;
    return systemStatus.data.agents.active_agents || 0;
  };

  return (
    <div className="w-full space-y-4">
      {/* 系统状态头部 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-600" />
              <CardTitle className="text-lg">TradingAgents 多智能体系统</CardTitle>
              {getStatusBadge()}
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>{getAgentCount()} 个智能体</span>
              </div>
              <div className="flex items-center space-x-1">
                <BarChart3 className="w-4 h-4" />
                <span>{detectedPatterns.length} 个形态</span>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* 功能选项卡 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="validation" className="flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>形态验证</span>
          </TabsTrigger>
          <TabsTrigger value="analysis" className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4" />
            <span>智能体分析</span>
          </TabsTrigger>
          <TabsTrigger value="debate" className="flex items-center space-x-2">
            <MessageSquare className="w-4 h-4" />
            <span>智能体辩论</span>
          </TabsTrigger>
          <TabsTrigger value="status" className="flex items-center space-x-2">
            <Brain className="w-4 h-4" />
            <span>系统状态</span>
          </TabsTrigger>
        </TabsList>

        {/* 形态验证 */}
        <TabsContent value="validation" className="space-y-4">
          <PatternValidation
            detectedPatterns={detectedPatterns}
            currentSymbol={currentSymbol}
            candleData={candleData}
            onValidationComplete={onValidationComplete}
          />
        </TabsContent>

        {/* 智能体分析 */}
        <TabsContent value="analysis" className="space-y-4">
          <AgentAnalysis
            currentSymbol={currentSymbol}
            candleData={candleData}
          />
        </TabsContent>

        {/* 智能体辩论 */}
        <TabsContent value="debate" className="space-y-4">
          <AgentDebateViewer
            stockSymbol={currentSymbol}
            patternData={detectedPatterns[0]}
            onRecommendationChange={setFinalRecommendation}
          />
        </TabsContent>

        {/* 系统状态 */}
        <TabsContent value="status" className="space-y-4">
          <SystemStatus
            systemStatus={systemStatus}
            onRefresh={fetchSystemStatus}
          />
        </TabsContent>
      </Tabs>

      {/* 最终投资建议摘要 */}
      {finalRecommendation && (
        <Card className="border-2 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              AI智能体投资建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className={`px-4 py-2 rounded-lg font-bold text-lg ${
                  finalRecommendation.decision === 'buy' ? 'bg-green-100 text-green-800' :
                  finalRecommendation.decision === 'sell' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {finalRecommendation.decision === 'buy' ? '🚀 建议买入' :
                   finalRecommendation.decision === 'sell' ? '📉 建议卖出' :
                   '⏸️ 建议持有'}
                </div>
                <div className="text-sm text-gray-600">
                  置信度: {finalRecommendation.confidence?.toFixed(1)}%
                </div>
              </div>
              <div className="text-sm text-gray-600">
                {finalRecommendation.suggestedPosition}
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-700">
              {finalRecommendation.reasoning}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 系统未就绪提示 */}
      {systemStatus && systemStatus.status !== 'success' && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            TradingAgents系统未完全就绪。某些功能可能不可用。
            请检查系统配置或联系管理员。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default TradingAgentsPanel;
