import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent 
} from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  MessageSquare, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target
} from 'lucide-react';

const AgentDebateViewer = ({ stockSymbol, patternData, onRecommendationChange }) => {
  const [debateState, setDebateState] = useState('idle'); // idle, running, completed
  const [currentRound, setCurrentRound] = useState(0);
  const [debateHistory, setDebateHistory] = useState([]);
  const [finalRecommendation, setFinalRecommendation] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [isAutoPlay, setIsAutoPlay] = useState(false);

  // 智能体参与者配置
  const agentProfiles = {
    'BullResearcher': {
      name: '多头研究员',
      role: '看涨分析',
      color: 'bg-green-100 text-green-800',
      icon: TrendingUp,
      bias: 'bullish'
    },
    'BearResearcher': {
      name: '空头研究员', 
      role: '看跌分析',
      color: 'bg-red-100 text-red-800',
      icon: TrendingDown,
      bias: 'bearish'
    },
    'MarketAnalyst': {
      name: '市场分析师',
      role: '技术分析',
      color: 'bg-blue-100 text-blue-800',
      icon: Target,
      bias: 'neutral'
    },
    'PortfolioManager': {
      name: '投资组合经理',
      role: '风险管理',
      color: 'bg-purple-100 text-purple-800',
      icon: CheckCircle,
      bias: 'conservative'
    },
    'DebateModerator': {
      name: '辩论主持人',
      role: '共识建立',
      color: 'bg-gray-100 text-gray-800',
      icon: Users,
      bias: 'moderator'
    }
  };

  // 模拟辩论数据
  const simulateDebateRound = (round) => {
    const agents = Object.keys(agentProfiles);
    const roundData = {
      round: round + 1,
      timestamp: new Date().toLocaleTimeString(),
      discussions: []
    };

    // 生成每个智能体的发言
    agents.forEach(agentId => {
      if (agentId === 'DebateModerator' && round === 0) {
        roundData.discussions.push({
          agent: agentId,
          message: `欢迎参加${stockSymbol}股票投资决策辩论。请各位智能体基于当前形态分析发表观点。`,
          timestamp: new Date().toLocaleTimeString(),
          type: 'moderation'
        });
      } else if (agentId !== 'DebateModerator') {
        const profile = agentProfiles[agentId];
        let message = '';
        
        switch (profile.bias) {
          case 'bullish':
            message = `基于当前${patternData?.pattern_name || '形态'}分析，我认为这是一个看涨信号。技术指标显示上涨动能强劲，建议买入。`;
            break;
          case 'bearish':
            message = `从风险角度分析，当前形态可能是假突破。市场情绪过于乐观，建议谨慎或考虑卖出。`;
            break;
          case 'neutral':
            message = `技术面显示${stockSymbol}处于关键位置。需要结合成交量和其他指标综合判断，建议观望。`;
            break;
          case 'conservative':
            message = `从投资组合风险管理角度，建议控制仓位。即使看涨也不应超过总资产的5%。`;
            break;
        }

        roundData.discussions.push({
          agent: agentId,
          message: message,
          timestamp: new Date().toLocaleTimeString(),
          type: 'argument',
          stance: profile.bias === 'bullish' ? 'buy' : profile.bias === 'bearish' ? 'sell' : 'hold'
        });
      }
    });

    return roundData;
  };

  // 开始辩论
  const startDebate = async () => {
    setDebateState('running');
    setCurrentRound(0);
    setDebateHistory([]);
    setFinalRecommendation(null);
    
    // 设置参与者
    setParticipants(Object.keys(agentProfiles));

    if (isAutoPlay) {
      // 自动播放模式
      for (let i = 0; i < 3; i++) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        const roundData = simulateDebateRound(i);
        setDebateHistory(prev => [...prev, roundData]);
        setCurrentRound(i + 1);
      }
      
      // 生成最终建议
      await generateFinalRecommendation();
    } else {
      // 手动模式 - 只生成第一轮
      const roundData = simulateDebateRound(0);
      setDebateHistory([roundData]);
      setCurrentRound(1);
    }
  };

  // 下一轮辩论
  const nextRound = async () => {
    if (currentRound < 3) {
      const roundData = simulateDebateRound(currentRound);
      setDebateHistory(prev => [...prev, roundData]);
      setCurrentRound(prev => prev + 1);
      
      if (currentRound === 2) {
        // 最后一轮后生成建议
        setTimeout(() => generateFinalRecommendation(), 1000);
      }
    }
  };

  // 生成最终投资建议
  const generateFinalRecommendation = async () => {
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // 模拟智能体投票
    const votes = {
      buy: Math.floor(Math.random() * 3) + 1,
      sell: Math.floor(Math.random() * 2) + 1,
      hold: Math.floor(Math.random() * 2) + 1
    };

    const totalVotes = votes.buy + votes.sell + votes.hold;
    const confidence = Math.max(votes.buy, votes.sell, votes.hold) / totalVotes;
    
    let decision = 'hold';
    if (votes.buy > votes.sell && votes.buy > votes.hold) {
      decision = 'buy';
    } else if (votes.sell > votes.buy && votes.sell > votes.hold) {
      decision = 'sell';
    }

    const recommendation = {
      decision,
      confidence: confidence * 100,
      votes,
      reasoning: `经过${currentRound}轮辩论，智能体达成共识：${decision === 'buy' ? '买入' : decision === 'sell' ? '卖出' : '持有'}`,
      riskLevel: confidence > 0.7 ? 'low' : confidence > 0.5 ? 'medium' : 'high',
      suggestedPosition: decision === 'buy' ? '建议仓位：3-5%' : decision === 'sell' ? '建议减仓：50-100%' : '维持当前仓位',
      timeHorizon: decision === 'buy' ? '持有期：1-3个月' : decision === 'sell' ? '立即执行' : '继续观察1-2周'
    };

    setFinalRecommendation(recommendation);
    setDebateState('completed');
    
    // 通知父组件
    if (onRecommendationChange) {
      onRecommendationChange(recommendation);
    }
  };

  // 重置辩论
  const resetDebate = () => {
    setDebateState('idle');
    setCurrentRound(0);
    setDebateHistory([]);
    setFinalRecommendation(null);
    setParticipants([]);
  };

  // 获取决策颜色
  const getDecisionColor = (decision) => {
    switch (decision) {
      case 'buy': return 'bg-green-100 text-green-800 border-green-200';
      case 'sell': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }
  };

  // 获取决策图标
  const getDecisionIcon = (decision) => {
    switch (decision) {
      case 'buy': return <TrendingUp className="w-4 h-4" />;
      case 'sell': return <TrendingDown className="w-4 h-4" />;
      default: return <Minus className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            智能体辩论系统
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">股票:</span>
              <Badge variant="outline">{stockSymbol || 'N/A'}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">形态:</span>
              <Badge variant="outline">{patternData?.chinese_name || 'N/A'}</Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">状态:</span>
              <Badge className={
                debateState === 'running' ? 'bg-blue-100 text-blue-800' :
                debateState === 'completed' ? 'bg-green-100 text-green-800' :
                'bg-gray-100 text-gray-800'
              }>
                {debateState === 'running' ? '辩论中' : 
                 debateState === 'completed' ? '已完成' : '待开始'}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {debateState === 'idle' && (
              <>
                <Button onClick={startDebate} className="flex items-center gap-2">
                  <Play className="w-4 h-4" />
                  开始辩论
                </Button>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={isAutoPlay}
                    onChange={(e) => setIsAutoPlay(e.target.checked)}
                  />
                  自动播放
                </label>
              </>
            )}
            
            {debateState === 'running' && !isAutoPlay && currentRound < 3 && (
              <Button onClick={nextRound} className="flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                下一轮 ({currentRound}/3)
              </Button>
            )}
            
            {debateState === 'running' && isAutoPlay && (
              <Button disabled className="flex items-center gap-2">
                <Clock className="w-4 h-4 animate-spin" />
                自动进行中...
              </Button>
            )}
            
            {(debateState === 'completed' || debateState === 'running') && (
              <Button onClick={resetDebate} variant="outline" className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4" />
                重新开始
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 参与者列表 */}
      {participants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              参与智能体 ({participants.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {participants.map(agentId => {
                const profile = agentProfiles[agentId];
                const IconComponent = profile.icon;
                return (
                  <div key={agentId} className="flex items-center gap-2 p-2 border rounded-lg">
                    <IconComponent className="w-4 h-4" />
                    <div>
                      <div className="font-medium text-sm">{profile.name}</div>
                      <div className="text-xs text-gray-500">{profile.role}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 辩论历史 */}
      {debateHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>辩论过程</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {debateHistory.map((round, roundIndex) => (
                <div key={roundIndex} className="border-l-2 border-blue-200 pl-4">
                  <div className="font-medium text-sm text-blue-600 mb-2">
                    第 {round.round} 轮 - {round.timestamp}
                  </div>
                  <div className="space-y-3">
                    {round.discussions.map((discussion, discussionIndex) => {
                      const profile = agentProfiles[discussion.agent];
                      const IconComponent = profile.icon;
                      return (
                        <div key={discussionIndex} className="flex gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className="flex-shrink-0">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${profile.color}`}>
                              <IconComponent className="w-4 h-4" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-sm">{profile.name}</span>
                              {discussion.stance && (
                                <Badge className={getDecisionColor(discussion.stance)}>
                                  {getDecisionIcon(discussion.stance)}
                                  <span className="ml-1">
                                    {discussion.stance === 'buy' ? '买入' : 
                                     discussion.stance === 'sell' ? '卖出' : '持有'}
                                  </span>
                                </Badge>
                              )}
                              <span className="text-xs text-gray-500">{discussion.timestamp}</span>
                            </div>
                            <p className="text-sm text-gray-700">{discussion.message}</p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 最终建议 */}
      {finalRecommendation && (
        <Card className="border-2 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              最终投资建议
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* 主要决策 */}
              <div className={`p-4 rounded-lg border-2 ${getDecisionColor(finalRecommendation.decision)}`}>
                <div className="flex items-center gap-3 mb-2">
                  {getDecisionIcon(finalRecommendation.decision)}
                  <span className="text-lg font-bold">
                    {finalRecommendation.decision === 'buy' ? '建议买入' : 
                     finalRecommendation.decision === 'sell' ? '建议卖出' : '建议持有'}
                  </span>
                  <Badge variant="outline">
                    置信度: {finalRecommendation.confidence.toFixed(1)}%
                  </Badge>
                </div>
                <p className="text-sm">{finalRecommendation.reasoning}</p>
              </div>

              {/* 投票详情 */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{finalRecommendation.votes.buy}</div>
                  <div className="text-sm text-green-600">买入票</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{finalRecommendation.votes.sell}</div>
                  <div className="text-sm text-red-600">卖出票</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{finalRecommendation.votes.hold}</div>
                  <div className="text-sm text-yellow-600">持有票</div>
                </div>
              </div>

              {/* 详细建议 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">风险等级:</span>
                  <Badge className={
                    finalRecommendation.riskLevel === 'low' ? 'bg-green-100 text-green-800 ml-2' :
                    finalRecommendation.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 ml-2' :
                    'bg-red-100 text-red-800 ml-2'
                  }>
                    {finalRecommendation.riskLevel === 'low' ? '低风险' :
                     finalRecommendation.riskLevel === 'medium' ? '中风险' : '高风险'}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">建议仓位:</span>
                  <span className="ml-2">{finalRecommendation.suggestedPosition}</span>
                </div>
                <div>
                  <span className="font-medium">时间框架:</span>
                  <span className="ml-2">{finalRecommendation.timeHorizon}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AgentDebateViewer;
