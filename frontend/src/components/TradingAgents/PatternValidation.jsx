import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  Minus,
  Loader2,
  RefreshC<PERSON>,
  Eye,
  Brain
} from 'lucide-react';

const PatternValidation = ({ 
  detectedPatterns = [], 
  currentSymbol,
  candleData = [],
  onValidationComplete 
}) => {
  const [validationResults, setValidationResults] = useState({});
  const [isValidating, setIsValidating] = useState(false);
  const [selectedPattern, setSelectedPattern] = useState(null);

  // 自动验证检测到的形态
  useEffect(() => {
    if (detectedPatterns.length > 0 && candleData.length > 0) {
      handleBatchValidation();
    }
  }, [detectedPatterns, candleData]);

  const validateSinglePattern = async (pattern) => {
    setIsValidating(true);
    
    try {
      const response = await fetch('/api/trading-agents/validate-pattern', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pattern_name: pattern.name,
          symbol: currentSymbol,
          candle_data: candleData,
          confidence: pattern.confidence || 0.8
        })
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setValidationResults(prev => ({
          ...prev,
          [pattern.name]: result.data
        }));
        
        if (onValidationComplete) {
          onValidationComplete(pattern.name, result.data);
        }
      } else {
        console.error('验证失败:', result.message);
      }
    } catch (error) {
      console.error('验证请求失败:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const handleBatchValidation = async () => {
    if (detectedPatterns.length === 0) return;
    
    setIsValidating(true);
    
    try {
      const patterns = detectedPatterns.map(pattern => ({
        pattern_name: pattern.name,
        symbol: currentSymbol,
        candle_data: candleData,
        confidence: pattern.confidence || 0.8
      }));

      const response = await fetch('/api/trading-agents/batch-validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ patterns })
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        const newResults = {};
        result.data.results.forEach(validationResult => {
          if (validationResult.status === 'completed') {
            newResults[validationResult.pattern_name] = validationResult;
          }
        });
        setValidationResults(newResults);
      }
    } catch (error) {
      console.error('批量验证失败:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const getValidationBadge = (validation) => {
    if (!validation) return null;
    
    const conclusion = validation.validation_conclusion;
    const reliability = validation.reliability_level;
    
    if (conclusion === 'confirmed') {
      return <Badge variant="success" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        已确认 ({reliability})
      </Badge>;
    } else if (conclusion === 'probable') {
      return <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
        <AlertTriangle className="w-3 h-3 mr-1" />
        可能 ({reliability})
      </Badge>;
    } else {
      return <Badge variant="destructive">
        <AlertTriangle className="w-3 h-3 mr-1" />
        存疑 ({reliability})
      </Badge>;
    }
  };

  const getSignalIcon = (signal) => {
    switch (signal) {
      case 'buy':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'sell':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <Minus className="w-4 h-4 text-gray-600" />;
    }
  };

  const getConfidenceColor = (score) => {
    if (score >= 0.8) return 'bg-green-500';
    if (score >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Shield className="w-5 h-5 text-blue-600" />
          <span>AI智能体形态验证</span>
        </h3>
        <div className="flex space-x-2">
          <Button 
            onClick={handleBatchValidation}
            disabled={isValidating || detectedPatterns.length === 0}
            size="sm"
          >
            {isValidating ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            {isValidating ? '验证中...' : '重新验证'}
          </Button>
        </div>
      </div>

      {/* 形态列表 */}
      {detectedPatterns.length === 0 ? (
        <Alert>
          <Eye className="h-4 w-4" />
          <AlertDescription>
            暂未检测到蜡烛图形态。请先进行形态识别。
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid gap-4">
          {detectedPatterns.map((pattern, index) => {
            const validation = validationResults[pattern.name];
            
            return (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div>
                        <CardTitle className="text-base">{pattern.chinese_name || pattern.name}</CardTitle>
                        <p className="text-sm text-gray-600">{pattern.name}</p>
                      </div>
                      {validation && getValidationBadge(validation)}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedPattern(selectedPattern === pattern.name ? null : pattern.name)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      {selectedPattern === pattern.name ? '收起' : '详情'}
                    </Button>
                  </div>
                </CardHeader>

                <CardContent className="space-y-3">
                  {/* 基本信息 */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">原始置信度:</span>
                      <div className="flex items-center space-x-2 mt-1">
                        <Progress 
                          value={(pattern.confidence || 0.8) * 100} 
                          className="flex-1 h-2"
                        />
                        <span className="font-medium">{((pattern.confidence || 0.8) * 100).toFixed(0)}%</span>
                      </div>
                    </div>
                    {validation && (
                      <div>
                        <span className="text-gray-600">AI验证分数:</span>
                        <div className="flex items-center space-x-2 mt-1">
                          <Progress 
                            value={validation.final_score * 100} 
                            className="flex-1 h-2"
                          />
                          <span className="font-medium">{(validation.final_score * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 验证详情 */}
                  {selectedPattern === pattern.name && validation && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3">
                      <h4 className="font-medium flex items-center space-x-2">
                        <Brain className="w-4 h-4" />
                        <span>AI验证详情</span>
                      </h4>
                      
                      {/* 置信度变化 */}
                      <div className="flex items-center justify-between text-sm">
                        <span>置信度变化:</span>
                        <span className={`font-medium ${
                          validation.confidence_improvement > 0 ? 'text-green-600' : 
                          validation.confidence_improvement < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          {validation.confidence_improvement > 0 ? '+' : ''}
                          {(validation.confidence_improvement * 100).toFixed(1)}%
                        </span>
                      </div>

                      {/* AI建议 */}
                      {validation.recommendations && validation.recommendations.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-gray-700 mb-2">💡 AI建议:</h5>
                          <ul className="text-sm space-y-1">
                            {validation.recommendations.map((rec, idx) => (
                              <li key={idx} className="flex items-start space-x-2">
                                <span className="text-blue-600">•</span>
                                <span>{rec}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* 风险提示 */}
                      {validation.risk_warnings && validation.risk_warnings.length > 0 && (
                        <div>
                          <h5 className="text-sm font-medium text-red-700 mb-2">⚠️ 风险提示:</h5>
                          <ul className="text-sm space-y-1">
                            {validation.risk_warnings.map((warning, idx) => (
                              <li key={idx} className="flex items-start space-x-2">
                                <span className="text-red-600">•</span>
                                <span>{warning}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 加载状态 */}
                  {isValidating && !validation && (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                      <span className="ml-2 text-sm text-gray-600">AI智能体验证中...</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* 验证摘要 */}
      {Object.keys(validationResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">验证摘要</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(validationResults).filter(v => v.validation_conclusion === 'confirmed').length}
                </div>
                <div className="text-sm text-gray-600">已确认形态</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">
                  {Object.values(validationResults).filter(v => v.validation_conclusion === 'probable').length}
                </div>
                <div className="text-sm text-gray-600">可能形态</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {Object.values(validationResults).filter(v => v.validation_conclusion === 'questionable').length}
                </div>
                <div className="text-sm text-gray-600">存疑形态</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PatternValidation;
