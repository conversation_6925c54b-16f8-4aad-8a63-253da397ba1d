import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Brain,
  BarChart3,
  Shield,
  MessageSquare,
  Zap,
  Loader2,
  RefreshCw
} from 'lucide-react';

const AgentAnalysis = ({ currentSymbol, candleData = [] }) => {
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);

  // 自动分析
  useEffect(() => {
    if (currentSymbol && candleData.length > 0) {
      handleAnalysis();
    }
  }, [currentSymbol, candleData]);

  const handleAnalysis = async () => {
    setIsAnalyzing(true);
    
    try {
      const response = await fetch('/api/trading-agents/agent-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: currentSymbol,
          candle_data: candleData,
          fundamentals: {} // 可以添加基本面数据
        })
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setAnalysisResult(result.data);
      } else {
        console.error('分析失败:', result.message);
      }
    } catch (error) {
      console.error('分析请求失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getAgentIcon = (agentName) => {
    const icons = {
      'MarketAnalyst': <BarChart3 className="w-5 h-5" />,
      'BullResearcher': <TrendingUp className="w-5 h-5" />,
      'BearResearcher': <TrendingDown className="w-5 h-5" />,
      'PortfolioManager': <Shield className="w-5 h-5" />,
      'DebateModerator': <MessageSquare className="w-5 h-5" />,
      'ExecutionAgent': <Zap className="w-5 h-5" />,
      'CandlestickAnalyst': <Brain className="w-5 h-5" />
    };
    return icons[agentName] || <Brain className="w-5 h-5" />;
  };

  const getAgentName = (agentName) => {
    const names = {
      'MarketAnalyst': '市场分析师',
      'BullResearcher': '多头研究员',
      'BearResearcher': '空头研究员',
      'PortfolioManager': '投资组合经理',
      'DebateModerator': '辩论主持人',
      'ExecutionAgent': '执行智能体',
      'CandlestickAnalyst': '蜡烛图分析师'
    };
    return names[agentName] || agentName;
  };

  const getSignalBadge = (signal, confidence) => {
    const signalConfig = {
      'buy': { color: 'bg-green-100 text-green-800', icon: <TrendingUp className="w-3 h-3" />, text: '买入' },
      'sell': { color: 'bg-red-100 text-red-800', icon: <TrendingDown className="w-3 h-3" />, text: '卖出' },
      'hold': { color: 'bg-gray-100 text-gray-800', icon: <Minus className="w-3 h-3" />, text: '持有' }
    };
    
    const config = signalConfig[signal] || signalConfig['hold'];
    
    return (
      <Badge className={config.color}>
        {config.icon}
        <span className="ml-1">{config.text}</span>
        <span className="ml-1">({(confidence * 100).toFixed(0)}%)</span>
      </Badge>
    );
  };

  const getSentimentColor = (sentiment) => {
    switch (sentiment) {
      case 'bullish': return 'text-green-600';
      case 'bearish': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Users className="w-5 h-5 text-blue-600" />
          <span>多智能体市场分析</span>
        </h3>
        <Button 
          onClick={handleAnalysis}
          disabled={isAnalyzing || !currentSymbol}
          size="sm"
        >
          {isAnalyzing ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          {isAnalyzing ? '分析中...' : '重新分析'}
        </Button>
      </div>

      {/* 分析结果 */}
      {isAnalyzing ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">AI智能体正在分析 {currentSymbol}...</p>
            </div>
          </CardContent>
        </Card>
      ) : analysisResult ? (
        <>
          {/* 分析摘要 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">分析摘要 - {analysisResult.symbol}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {analysisResult.summary?.total_agents || 0}
                  </div>
                  <div className="text-sm text-gray-600">参与智能体</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {analysisResult.summary?.consensus_signals?.buy || 0}
                  </div>
                  <div className="text-sm text-gray-600">买入信号</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {analysisResult.summary?.consensus_signals?.sell || 0}
                  </div>
                  <div className="text-sm text-gray-600">卖出信号</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-600">
                    {analysisResult.summary?.consensus_signals?.hold || 0}
                  </div>
                  <div className="text-sm text-gray-600">持有信号</div>
                </div>
              </div>
              
              {/* 整体结论 */}
              {analysisResult.summary && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">整体建议:</span>
                    {getSignalBadge(
                      analysisResult.summary.overall_signal, 
                      analysisResult.summary.consensus_signals?.total ? 
                        Math.max(
                          analysisResult.summary.consensus_signals.buy,
                          analysisResult.summary.consensus_signals.sell,
                          analysisResult.summary.consensus_signals.hold
                        ) / analysisResult.summary.consensus_signals.total : 0.5
                    )}
                  </div>
                  <div className="mt-2">
                    <span className="text-sm text-gray-600">市场情绪: </span>
                    <span className={`text-sm font-medium ${getSentimentColor(analysisResult.summary.overall_sentiment)}`}>
                      {analysisResult.summary.overall_sentiment === 'bullish' ? '看涨' :
                       analysisResult.summary.overall_sentiment === 'bearish' ? '看跌' : '中性'}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 各智能体分析 */}
          <div className="grid gap-4">
            {analysisResult.agent_analyses && Object.entries(analysisResult.agent_analyses).map(([agentName, analysis]) => {
              if (analysis.status === 'failed') {
                return (
                  <Card key={agentName} className="border-red-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-2">
                        {getAgentIcon(agentName)}
                        <CardTitle className="text-base">{getAgentName(agentName)}</CardTitle>
                        <Badge variant="destructive">分析失败</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-red-600">{analysis.error}</p>
                    </CardContent>
                  </Card>
                );
              }

              const agentAnalysis = analysis.analysis || {};
              
              return (
                <Card key={agentName} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getAgentIcon(agentName)}
                        <div>
                          <CardTitle className="text-base">{getAgentName(agentName)}</CardTitle>
                          <p className="text-sm text-gray-600">{agentAnalysis.perspective || '综合分析'}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getSignalBadge(agentAnalysis.signal, agentAnalysis.confidence)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedAgent(selectedAgent === agentName ? null : agentName)}
                        >
                          {selectedAgent === agentName ? '收起' : '详情'}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent>
                    {/* 基本信息 */}
                    <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                      <div>
                        <span className="text-gray-600">情绪倾向:</span>
                        <span className={`ml-2 font-medium ${getSentimentColor(agentAnalysis.sentiment)}`}>
                          {agentAnalysis.sentiment === 'bullish' ? '看涨' :
                           agentAnalysis.sentiment === 'bearish' ? '看跌' : '中性'}
                        </span>
                      </div>
                      <div>
                        <span className="text-gray-600">置信度:</span>
                        <div className="flex items-center space-x-2 mt-1">
                          <Progress 
                            value={(agentAnalysis.confidence || 0.5) * 100} 
                            className="flex-1 h-2"
                          />
                          <span className="font-medium">{((agentAnalysis.confidence || 0.5) * 100).toFixed(0)}%</span>
                        </div>
                      </div>
                    </div>

                    {/* 详细分析 */}
                    {selectedAgent === agentName && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-medium mb-2">详细分析:</h4>
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {agentAnalysis.summary || agentAnalysis.analysis || '暂无详细分析'}
                        </p>
                        
                        {/* 特定智能体的额外信息 */}
                        {agentName === 'BullResearcher' && agentAnalysis.opportunities && (
                          <div className="mt-3">
                            <h5 className="text-sm font-medium text-green-700 mb-1">🐂 看涨机会:</h5>
                            <ul className="text-sm space-y-1">
                              {agentAnalysis.opportunities.map((opp, idx) => (
                                <li key={idx} className="flex items-start space-x-2">
                                  <span className="text-green-600">•</span>
                                  <span>{opp}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {agentName === 'BearResearcher' && agentAnalysis.risks && (
                          <div className="mt-3">
                            <h5 className="text-sm font-medium text-red-700 mb-1">🐻 风险因素:</h5>
                            <ul className="text-sm space-y-1">
                              {agentAnalysis.risks.map((risk, idx) => (
                                <li key={idx} className="flex items-start space-x-2">
                                  <span className="text-red-600">•</span>
                                  <span>{risk}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </>
      ) : (
        <Alert>
          <Brain className="h-4 w-4" />
          <AlertDescription>
            请选择股票并确保有蜡烛图数据后开始智能体分析。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default AgentAnalysis;
