import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  MessageSquare, 
  Users, 
  Play,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  Loader2,
  MessageCircle
} from 'lucide-react';

const DebateViewer = ({ currentSymbol, detectedPatterns = [] }) => {
  const [debateResult, setDebateResult] = useState(null);
  const [isDebating, setIsDebating] = useState(false);
  const [customTopic, setCustomTopic] = useState('');
  const [selectedRound, setSelectedRound] = useState(null);

  const startDebate = async (topic) => {
    setIsDebating(true);
    setDebateResult(null);
    
    try {
      const response = await fetch('/api/trading-agents/debate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: topic,
          context: {
            symbol: currentSymbol,
            detected_patterns: detectedPatterns
          }
        })
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setDebateResult(result.data);
      } else {
        console.error('辩论失败:', result.message);
      }
    } catch (error) {
      console.error('辩论请求失败:', error);
    } finally {
      setIsDebating(false);
    }
  };

  const handleCustomDebate = () => {
    if (customTopic.trim()) {
      startDebate(customTopic.trim());
    }
  };

  const getPresetTopics = () => {
    const topics = [
      `${currentSymbol}是否值得投资？`,
      `${currentSymbol}的技术面分析`,
      `${currentSymbol}的风险评估`,
      `当前市场环境下的投资策略`
    ];
    
    if (detectedPatterns.length > 0) {
      topics.push(`${currentSymbol}检测到的${detectedPatterns[0].name}形态是否可靠？`);
    }
    
    return topics;
  };

  const getPositionBadge = (position) => {
    const positionConfig = {
      'bullish': { color: 'bg-green-100 text-green-800', icon: <TrendingUp className="w-3 h-3" />, text: '看涨' },
      'bearish': { color: 'bg-red-100 text-red-800', icon: <TrendingDown className="w-3 h-3" />, text: '看跌' },
      'neutral': { color: 'bg-gray-100 text-gray-800', icon: <Minus className="w-3 h-3" />, text: '中性' },
      'FOR': { color: 'bg-green-100 text-green-800', icon: <CheckCircle className="w-3 h-3" />, text: '支持' },
      'AGAINST': { color: 'bg-red-100 text-red-800', icon: <TrendingDown className="w-3 h-3" />, text: '反对' },
      'NEUTRAL': { color: 'bg-gray-100 text-gray-800', icon: <Minus className="w-3 h-3" />, text: '中立' }
    };
    
    const config = positionConfig[position] || positionConfig['neutral'];
    
    return (
      <Badge className={config.color}>
        {config.icon}
        <span className="ml-1">{config.text}</span>
      </Badge>
    );
  };

  const getAgentName = (agentName) => {
    const names = {
      'MarketAnalyst': '市场分析师',
      'BullResearcher': '多头研究员',
      'BearResearcher': '空头研究员',
      'PortfolioManager': '投资组合经理',
      'DebateModerator': '辩论主持人',
      'ExecutionAgent': '执行智能体',
      'CandlestickAnalyst': '蜡烛图分析师'
    };
    return names[agentName] || agentName;
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-blue-600" />
          <span>AI智能体辩论</span>
        </h3>
      </div>

      {/* 辩论主题选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">选择辩论主题</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 预设主题 */}
          <div>
            <h4 className="text-sm font-medium mb-2">预设主题:</h4>
            <div className="grid gap-2">
              {getPresetTopics().map((topic, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="justify-start text-left h-auto py-2 px-3"
                  onClick={() => startDebate(topic)}
                  disabled={isDebating}
                >
                  <Play className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{topic}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* 自定义主题 */}
          <div>
            <h4 className="text-sm font-medium mb-2">自定义主题:</h4>
            <div className="flex space-x-2">
              <Input
                placeholder="输入自定义辩论主题..."
                value={customTopic}
                onChange={(e) => setCustomTopic(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleCustomDebate()}
                disabled={isDebating}
              />
              <Button 
                onClick={handleCustomDebate}
                disabled={isDebating || !customTopic.trim()}
              >
                {isDebating ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 辩论进行中 */}
      {isDebating && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-2" />
              <p className="text-sm text-gray-600">AI智能体正在进行结构化辩论...</p>
              <p className="text-xs text-gray-500 mt-1">这可能需要几分钟时间</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 辩论结果 */}
      {debateResult && (
        <div className="space-y-4">
          {/* 辩论摘要 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">辩论结果</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600">辩论主题:</span>
                  <p className="font-medium">{debateResult.topic}</p>
                </div>
                
                {debateResult.consensus && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">最终决策:</span>
                      <div className="mt-1">
                        {getPositionBadge(debateResult.consensus.decision)}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">共识置信度:</span>
                      <p className="font-medium">{(debateResult.consensus.confidence * 100).toFixed(0)}%</p>
                    </div>
                  </div>
                )}
                
                {debateResult.consensus?.reasoning && (
                  <div>
                    <span className="text-sm text-gray-600">决策理由:</span>
                    <p className="text-sm mt-1 p-3 bg-blue-50 rounded-lg">
                      {debateResult.consensus.reasoning}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 辩论轮次 */}
          {debateResult.rounds && debateResult.rounds.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>辩论过程 ({debateResult.rounds.length} 轮)</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {debateResult.rounds.map((round, roundIndex) => (
                    <div key={roundIndex} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">第 {round.round} 轮</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedRound(selectedRound === roundIndex ? null : roundIndex)}
                        >
                          {selectedRound === roundIndex ? '收起' : '查看详情'}
                        </Button>
                      </div>
                      
                      {/* 轮次摘要 */}
                      {round.round_summary && (
                        <div className="text-sm text-gray-600 mb-2">
                          <span>本轮观点: </span>
                          <span className="font-medium">
                            {round.round_summary.dominant_position === 'bullish' ? '偏向看涨' :
                             round.round_summary.dominant_position === 'bearish' ? '偏向看跌' : '观点平衡'}
                          </span>
                          <span className="ml-2">
                            ({round.round_summary.bullish_arguments || 0} 看涨, {round.round_summary.bearish_arguments || 0} 看跌)
                          </span>
                        </div>
                      )}

                      {/* 详细论点 */}
                      {selectedRound === roundIndex && round.arguments && (
                        <div className="mt-3 space-y-2">
                          {round.arguments.map((argument, argIndex) => (
                            <div key={argIndex} className="p-3 bg-gray-50 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center space-x-2">
                                  <MessageCircle className="w-4 h-4 text-blue-600" />
                                  <span className="font-medium">{getAgentName(argument.agent)}</span>
                                  {getPositionBadge(argument.position)}
                                </div>
                                <span className="text-xs text-gray-500">
                                  置信度: {(argument.confidence * 100).toFixed(0)}%
                                </span>
                              </div>
                              <p className="text-sm text-gray-700">{argument.argument}</p>
                              
                              {argument.evidence && argument.evidence.length > 0 && (
                                <div className="mt-2">
                                  <span className="text-xs text-gray-600">支持证据:</span>
                                  <ul className="text-xs mt-1 space-y-1">
                                    {argument.evidence.map((evidence, evidenceIndex) => (
                                      <li key={evidenceIndex} className="flex items-start space-x-1">
                                        <span className="text-blue-600">•</span>
                                        <span>{evidence}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* 空状态 */}
      {!debateResult && !isDebating && (
        <Alert>
          <Users className="h-4 w-4" />
          <AlertDescription>
            选择一个主题开始AI智能体辩论。智能体将从不同角度分析并达成共识。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default DebateViewer;
