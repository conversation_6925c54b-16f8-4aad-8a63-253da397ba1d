import React from 'react';
import { Card, List, Tag, Progress, Descriptions, Empty, Statistic, Row, Col } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  ClockCircleOutlined,
  PercentageOutlined
} from '@ant-design/icons';

const PatternResults = ({ patterns, detailed = false }) => {
  if (!patterns || patterns.length === 0) {
    return (
      <Card>
        <Empty 
          description="暂无识别结果"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // 统计信息 - 兼容不同的数据结构
  const stats = {
    total: patterns.length,
    bullish: patterns.filter(p => p.signal === 'bullish' || p.signal === 'buy').length,
    bearish: patterns.filter(p => p.signal === 'bearish' || p.signal === 'sell').length,
    neutral: patterns.filter(p => p.signal === 'neutral' || p.signal === 'hold' || (!p.signal)).length,
    highConfidence: patterns.filter(p => p.confidence >= 0.8).length,
    mediumConfidence: patterns.filter(p => p.confidence >= 0.6 && p.confidence < 0.8).length,
    lowConfidence: patterns.filter(p => p.confidence < 0.6).length,
  };

  const getSignalIcon = (signal) => {
    switch (signal) {
      case 'bullish':
      case 'buy':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />;
      case 'bearish':
      case 'sell':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />;
      case 'neutral':
      case 'hold':
      default:
        return <MinusOutlined style={{ color: '#faad14' }} />;
    }
  };

  const getSignalColor = (signal) => {
    switch (signal) {
      case 'bullish':
        return 'success';
      case 'bearish':
        return 'error';
      default:
        return 'warning';
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const getPatternTypeColor = (type) => {
    switch (type) {
      case 'reversal':
        return 'purple';
      case 'continuation':
        return 'blue';
      case 'indecision':
        return 'orange';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'single':
        return 'green';
      case 'double':
        return 'blue';
      case 'triple':
        return 'purple';
      case 'multiple':
        return 'red';
      default:
        return 'default';
    }
  };

  const formatPatternName = (name) => {
    const nameMap = {
      'hammer': '锤子线',
      'hanging_man': '上吊线',
      'doji': '十字线',
      'long_legged_doji': '长腿十字线',
      'gravestone_doji': '墓碑十字线',
      'dragonfly_doji': '蜻蜓十字线',
      'spinning_top': '纺锤线',
      'marubozu': '光头光脚线',
      'engulfing_bullish': '看涨吞没',
      'engulfing_bearish': '看跌吞没',
      'dark_cloud_cover': '乌云盖顶',
      'piercing_pattern': '刺透形态',
      'harami_bullish': '看涨孕线',
      'harami_bearish': '看跌孕线',
      'harami_cross': '十字孕线',
      'tweezers_top': '平头顶部',
      'tweezers_bottom': '平头底部',
      'morning_star': '启明星',
      'evening_star': '黄昏星',
      'morning_doji_star': '十字启明星',
      'evening_doji_star': '十字黄昏星',
      'three_white_soldiers': '前进白色三兵',
      'three_black_crows': '三只乌鸦',
      'rising_three_methods': '上升三法',
      'falling_three_methods': '下降三法',
      'shooting_star': '流星',
      'inverted_hammer': '倒锤子',
    };
    return nameMap[name] || name;
  };

  const renderSummary = () => (
    <Card title="识别统计" style={{ marginBottom: '16px' }}>
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="总形态数"
            value={stats.total}
            prefix={<PercentageOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="看涨形态"
            value={stats.bullish}
            valueStyle={{ color: '#52c41a' }}
            prefix={<ArrowUpOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="看跌形态"
            value={stats.bearish}
            valueStyle={{ color: '#ff4d4f' }}
            prefix={<ArrowDownOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="高置信度"
            value={stats.highConfidence}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
      </Row>
    </Card>
  );

  const renderPatternItem = (pattern) => (
    <List.Item key={`${pattern.pattern_name}-${pattern.start_index}`}>
      <Card 
        size="small" 
        className={`pattern-card ${pattern.signal}`}
        style={{ width: '100%' }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
              {getSignalIcon(pattern.signal)}
              <span style={{ marginLeft: '8px', fontWeight: 'bold', fontSize: '16px' }}>
                {formatPatternName(pattern.pattern_name)}
              </span>
              <Tag color={getSignalColor(pattern.signal)} style={{ marginLeft: '8px' }}>
                {pattern.signal === 'bullish' ? '看涨' : pattern.signal === 'bearish' ? '看跌' : '中性'}
              </Tag>
            </div>
            
            <div style={{ marginBottom: '8px' }}>
              <Tag color={getPatternTypeColor(pattern.pattern_type)}>
                {pattern.pattern_type === 'reversal' ? '反转' : 
                 pattern.pattern_type === 'continuation' ? '持续' : '犹豫'}
              </Tag>
              <Tag color={getCategoryColor(pattern.pattern_category)}>
                {pattern.pattern_category === 'single' ? '单根' :
                 pattern.pattern_category === 'double' ? '双根' :
                 pattern.pattern_category === 'triple' ? '三根' : '多根'}
              </Tag>
              {pattern.trend_context && (
                <Tag className={`trend-context ${pattern.trend_context}`}>
                  {pattern.trend_context === 'uptrend' ? '上升趋势' :
                   pattern.trend_context === 'downtrend' ? '下降趋势' :
                   pattern.trend_context === 'sideways' ? '横盘' : '未知趋势'}
                </Tag>
              )}
            </div>
            
            <p className="pattern-description">{pattern.description}</p>
            
            <div className="pattern-meta">
              <span className="pattern-time">
                <ClockCircleOutlined /> 位置: {pattern.start_index}
                {pattern.end_index !== pattern.start_index && ` - ${pattern.end_index}`}
              </span>
              <span className="pattern-confidence" style={{ color: getConfidenceColor(pattern.confidence) }}>
                置信度: {(pattern.confidence * 100).toFixed(1)}%
              </span>
            </div>
          </div>
          
          <div style={{ marginLeft: '16px', minWidth: '80px' }}>
            <Progress
              type="circle"
              size={60}
              percent={Math.round(pattern.confidence * 100)}
              strokeColor={getConfidenceColor(pattern.confidence)}
              format={percent => `${percent}%`}
            />
          </div>
        </div>
        
        {detailed && pattern.key_levels && (
          <Descriptions size="small" column={2} style={{ marginTop: '12px' }}>
            {pattern.key_levels.support && (
              <Descriptions.Item label="支撑位">
                {pattern.key_levels.support.toFixed(2)}
              </Descriptions.Item>
            )}
            {pattern.key_levels.resistance && (
              <Descriptions.Item label="阻力位">
                {pattern.key_levels.resistance.toFixed(2)}
              </Descriptions.Item>
            )}
            {pattern.volume_confirmation !== null && (
              <Descriptions.Item label="成交量确认">
                {pattern.volume_confirmation ? '是' : '否'}
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Card>
    </List.Item>
  );

  return (
    <div>
      {detailed && renderSummary()}
      
      <List
        dataSource={patterns}
        renderItem={renderPatternItem}
        pagination={detailed ? { pageSize: 5 } : false}
      />
    </div>
  );
};

export default PatternResults;
