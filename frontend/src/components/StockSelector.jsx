import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Select, 
  Button, 
  List, 
  Tag, 
  Space, 
  Typography, 
  Spin, 
  Alert,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd';
import {
  SearchOutlined,
  StockOutlined,
  RiseOutlined as TrendingUpOutlined,
  FallOutlined as TrendingDownOutlined,
  ReloadOutlined,
  DollarOutlined
} from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;
const { Title, Text } = Typography;

const StockSelector = ({ onStockSelect, loading }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedStock, setSelectedStock] = useState(null);
  const [stockData, setStockData] = useState(null);
  const [period, setPeriod] = useState('1y');
  const [searching, setSearching] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [error, setError] = useState(null);
  const [marketStatus, setMarketStatus] = useState(null);

  // 热门股票列表
  const popularStocks = [
    { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },
    { symbol: 'MSFT', name: 'Microsoft Corporation', exchange: 'NASDAQ' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' },
    { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },
    { symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ' },
    { symbol: 'NVDA', name: 'NVIDIA Corporation', exchange: 'NASDAQ' },
    { symbol: 'JPM', name: 'JPMorgan Chase & Co.', exchange: 'NYSE' }
  ];

  // 获取市场状态
  useEffect(() => {
    fetchMarketStatus();
  }, []);

  const fetchMarketStatus = async () => {
    try {
      const response = await axios.get('http://localhost:3000/api/v1/market/status');
      if (response.data.status === 'success') {
        setMarketStatus(response.data.data);
      }
    } catch (error) {
      console.error('获取市场状态失败:', error);
    }
  };

  // 搜索股票
  const searchStocks = async (query) => {
    if (!query.trim()) {
      setSearchResults(popularStocks);
      return;
    }

    setSearching(true);
    try {
      const response = await axios.post('http://localhost:3000/api/v1/stocks/search', {
        query: query
      });

      if (response.data.status === 'success') {
        setSearchResults(response.data.data.results);
      } else {
        setError('搜索失败');
      }
    } catch (error) {
      console.error('搜索股票失败:', error);
      setError('搜索服务暂时不可用');
      // 使用本地过滤作为备选
      const filtered = popularStocks.filter(stock => 
        stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
        stock.name.toLowerCase().includes(query.toLowerCase())
      );
      setSearchResults(filtered);
    } finally {
      setSearching(false);
    }
  };

  // 获取股票数据
  const fetchStockData = async (symbol, selectedPeriod = period) => {
    setLoadingData(true);
    setError(null);
    
    try {
      const response = await axios.post('http://localhost:3000/api/v1/stocks/data', {
        symbol: symbol,
        period: selectedPeriod,
        source: 'yahoo'
      });

      if (response.data.status === 'success') {
        const data = response.data.data;
        setStockData(data);
        
        // 通知父组件
        if (onStockSelect) {
          onStockSelect({
            symbol: symbol,
            stockInfo: data.stock_info,
            candles: data.candles,
            period: selectedPeriod
          });
        }
      } else {
        setError('获取股票数据失败');
      }
    } catch (error) {
      console.error('获取股票数据失败:', error);
      setError('股票数据服务暂时不可用');
    } finally {
      setLoadingData(false);
    }
  };

  // 选择股票
  const handleStockSelect = (stock) => {
    setSelectedStock(stock);
    fetchStockData(stock.symbol);
  };

  // 处理搜索输入
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);
    searchStocks(value);
  };

  // 处理周期变化
  const handlePeriodChange = (newPeriod) => {
    setPeriod(newPeriod);
    if (selectedStock) {
      fetchStockData(selectedStock.symbol, newPeriod);
    }
  };

  // 格式化价格变化
  const formatPriceChange = (change, changePercent) => {
    const isPositive = change >= 0;
    const color = isPositive ? '#52c41a' : '#ff4d4f';
    const icon = isPositive ? <TrendingUpOutlined /> : <TrendingDownOutlined />;
    
    return (
      <Space style={{ color }}>
        {icon}
        <Text style={{ color }}>
          {isPositive ? '+' : ''}{change.toFixed(2)} ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)
        </Text>
      </Space>
    );
  };

  // 初始化搜索结果
  useEffect(() => {
    setSearchResults(popularStocks);
  }, []);

  return (
    <div>
      {/* 市场状态 */}
      {marketStatus && (
        <Alert
          message={
            <Space>
              <Text>市场状态:</Text>
              <Tag color={marketStatus.is_open ? 'green' : 'red'}>
                {marketStatus.is_open ? '开市' : '休市'}
              </Tag>
              <Text type="secondary">{marketStatus.timezone}</Text>
            </Space>
          }
          type={marketStatus.is_open ? 'success' : 'info'}
          style={{ marginBottom: 16 }}
          showIcon
        />
      )}

      {/* 股票搜索 */}
      <Card title="选择股票" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            placeholder="搜索股票代码或公司名称 (如: AAPL, Apple)"
            prefix={<SearchOutlined />}
            value={searchQuery}
            onChange={handleSearchChange}
            loading={searching}
            allowClear
          />

          <Row gutter={16}>
            <Col span={12}>
              <Select
                placeholder="选择时间周期"
                value={period}
                onChange={handlePeriodChange}
                style={{ width: '100%' }}
              >
                <Option value="1d">1天</Option>
                <Option value="5d">5天</Option>
                <Option value="1mo">1个月</Option>
                <Option value="3mo">3个月</Option>
                <Option value="6mo">6个月</Option>
                <Option value="1y">1年</Option>
                <Option value="2y">2年</Option>
                <Option value="5y">5年</Option>
              </Select>
            </Col>
            <Col span={12}>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={() => selectedStock && fetchStockData(selectedStock.symbol)}
                loading={loadingData}
                disabled={!selectedStock}
                style={{ width: '100%' }}
              >
                刷新数据
              </Button>
            </Col>
          </Row>
        </Space>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 当前选中的股票信息 */}
      {stockData && (
        <Card title="股票信息" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title="当前价格"
                value={stockData.stock_info.current_price}
                precision={2}
                prefix={<DollarOutlined />}
                suffix={stockData.stock_info.currency}
              />
            </Col>
            <Col span={12}>
              <div>
                <Text type="secondary">价格变化</Text>
                <div>
                  {formatPriceChange(
                    stockData.stock_info.price_change,
                    stockData.stock_info.price_change_percent
                  )}
                </div>
              </div>
            </Col>
          </Row>
          
          <Divider />
          
          <Row gutter={16}>
            <Col span={8}>
              <Text type="secondary">公司:</Text>
              <div>{stockData.stock_info.company_name}</div>
            </Col>
            <Col span={8}>
              <Text type="secondary">交易所:</Text>
              <div>{stockData.stock_info.exchange}</div>
            </Col>
            <Col span={8}>
              <Text type="secondary">数据点:</Text>
              <div>{stockData.total_candles} 根蜡烛线</div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 股票搜索结果 */}
      <Card title={searchQuery ? `搜索结果 "${searchQuery}"` : "热门股票"}>
        <Spin spinning={searching}>
          <List
            dataSource={searchResults}
            renderItem={(stock) => (
              <List.Item
                actions={[
                  <Button
                    type={selectedStock?.symbol === stock.symbol ? "primary" : "default"}
                    icon={<StockOutlined />}
                    onClick={() => handleStockSelect(stock)}
                    loading={loadingData && selectedStock?.symbol === stock.symbol}
                  >
                    {selectedStock?.symbol === stock.symbol ? '已选择' : '选择'}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  title={
                    <Space>
                      <Text strong>{stock.symbol}</Text>
                      <Tag color="blue">{stock.exchange}</Tag>
                    </Space>
                  }
                  description={stock.name}
                />
              </List.Item>
            )}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default StockSelector;
