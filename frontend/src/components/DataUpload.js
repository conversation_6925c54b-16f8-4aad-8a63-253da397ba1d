import React, { useState } from 'react';
import { Card, Upload, Button, Table, message, Divider, Space } from 'antd';
import { UploadOutlined, FileTextOutlined, DownloadOutlined } from '@ant-design/icons';

const DataUpload = ({ onDataUpload, loading }) => {
  const [uploadedData, setUploadedData] = useState([]);
  const [fileList, setFileList] = useState([]);

  // 示例数据
  const generateSampleData = () => {
    const sampleData = [];
    const baseTime = new Date('2024-01-01T09:00:00Z');
    let price = 100;
    
    for (let i = 0; i < 50; i++) {
      const change = (Math.random() - 0.5) * 4; // -2 到 +2 的随机变化
      const open = price;
      const close = price + change;
      const high = Math.max(open, close) + Math.random() * 2;
      const low = Math.min(open, close) - Math.random() * 2;
      const volume = Math.floor(Math.random() * 10000) + 1000;
      
      const timestamp = new Date(baseTime.getTime() + i * 60 * 60 * 1000); // 每小时一根K线
      
      sampleData.push({
        key: i,
        timestamp: timestamp.toISOString(),
        open: parseFloat(open.toFixed(2)),
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        close: parseFloat(close.toFixed(2)),
        volume: volume,
      });
      
      price = close;
    }
    
    return sampleData;
  };

  const handleSampleData = () => {
    const sampleData = generateSampleData();
    setUploadedData(sampleData);
    message.success('已加载示例数据');
  };

  const handleFileUpload = (file) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const text = e.target.result;
        
        if (file.name.endsWith('.csv')) {
          // 简单的CSV解析
          const lines = text.split('\n').filter(line => line.trim());
          if (lines.length < 2) {
            message.error('CSV文件格式错误');
            return;
          }

          const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
          const data = [];

          for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',');
            if (values.length !== headers.length) continue;

            const row = {};
            headers.forEach((header, index) => {
              row[header] = values[index]?.trim();
            });

            data.push({
              key: i - 1,
              timestamp: row.timestamp || row.time || row.date || new Date().toISOString(),
              open: parseFloat(row.open || 0),
              high: parseFloat(row.high || 0),
              low: parseFloat(row.low || 0),
              close: parseFloat(row.close || 0),
              volume: parseFloat(row.volume || 1000),
            });
          }

          setUploadedData(data);
          message.success(`成功上传 ${data.length} 条数据`);
        } else if (file.name.endsWith('.json')) {
          // 解析JSON文件
          const jsonData = JSON.parse(text);
          const data = Array.isArray(jsonData) ? jsonData : [jsonData];
          
          const formattedData = data.map((item, index) => ({
            key: index,
            timestamp: item.timestamp || item.time || item.date || new Date().toISOString(),
            open: parseFloat(item.open || item.Open || 0),
            high: parseFloat(item.high || item.High || 0),
            low: parseFloat(item.low || item.Low || 0),
            close: parseFloat(item.close || item.Close || 0),
            volume: parseFloat(item.volume || item.Volume || 1000),
          }));
          
          setUploadedData(formattedData);
          message.success(`成功上传 ${formattedData.length} 条数据`);
        } else {
          message.error('不支持的文件格式，请上传CSV或JSON文件');
        }
      } catch (error) {
        message.error('文件解析失败: ' + error.message);
      }
    };
    
    reader.readAsText(file);
    return false; // 阻止默认上传行为
  };

  const handleAnalyze = () => {
    if (uploadedData.length === 0) {
      message.warning('请先上传数据');
      return;
    }
    
    if (uploadedData.length < 3) {
      message.warning('数据量太少，至少需要3条数据进行形态识别');
      return;
    }
    
    onDataUpload(uploadedData);
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text) => new Date(text).toLocaleString(),
      width: 150,
    },
    {
      title: '开盘价',
      dataIndex: 'open',
      key: 'open',
      render: (value) => value.toFixed(2),
    },
    {
      title: '最高价',
      dataIndex: 'high',
      key: 'high',
      render: (value) => value.toFixed(2),
    },
    {
      title: '最低价',
      dataIndex: 'low',
      key: 'low',
      render: (value) => value.toFixed(2),
    },
    {
      title: '收盘价',
      dataIndex: 'close',
      key: 'close',
      render: (value) => value.toFixed(2),
    },
    {
      title: '成交量',
      dataIndex: 'volume',
      key: 'volume',
      render: (value) => value.toLocaleString(),
    },
  ];

  return (
    <div>
      <Card title="数据上传" bordered={false}>
        <div className="upload-area">
          <Upload
            fileList={fileList}
            beforeUpload={handleFileUpload}
            onChange={({ fileList }) => setFileList(fileList)}
            accept=".csv,.json"
            showUploadList={false}
          >
            <Button icon={<UploadOutlined />} size="large">
              点击上传CSV或JSON文件
            </Button>
          </Upload>
          
          <Divider>或</Divider>
          
          <Button 
            icon={<FileTextOutlined />} 
            onClick={handleSampleData}
            className="sample-data-button"
          >
            使用示例数据
          </Button>
        </div>
        
        <div style={{ marginTop: '20px', textAlign: 'left' }}>
          <h4>数据格式要求：</h4>
          <ul>
            <li>CSV文件：包含 timestamp, open, high, low, close, volume 列</li>
            <li>JSON文件：包含相同字段的对象数组</li>
            <li>时间格式：ISO 8601 格式 (如: 2024-01-01T09:00:00Z)</li>
            <li>价格数据：数值类型</li>
          </ul>
        </div>
      </Card>
      
      {uploadedData.length > 0 && (
        <Card 
          title={`数据预览 (${uploadedData.length} 条记录)`}
          style={{ marginTop: '20px' }}
          extra={
            <Space>
              <Button 
                type="primary" 
                onClick={handleAnalyze}
                loading={loading}
                disabled={uploadedData.length < 3}
              >
                开始分析
              </Button>
            </Space>
          }
        >
          <Table
            columns={columns}
            dataSource={uploadedData}
            pagination={{ pageSize: 10 }}
            scroll={{ x: 800 }}
            size="small"
          />
        </Card>
      )}
    </div>
  );
};

export default DataUpload;
