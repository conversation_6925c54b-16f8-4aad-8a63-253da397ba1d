import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Badge, Timeline, Button, Spin, Alert, Tabs, Progress } from 'antd';
import { 
  RobotOutlined, 
  MessageOutlined, 
  BarChartOutlined, 
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import './AgentWorkspace.css';

const { TabPane } = Tabs;

const AgentWorkspace = ({ currentSymbol = 'AAPL', stockInfo = null, patterns = [] }) => {
  const [agents, setAgents] = useState([]);
  const [messages, setMessages] = useState([]);
  const [workflows, setWorkflows] = useState([]);
  const [llmStats, setLlmStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 模拟数据（实际应该从API获取）
  useEffect(() => {
    // 只在组件首次挂载时加载数据
    if (agents.length === 0) {
      loadMockData();
    }

    // 完全移除自动刷新功能
    // 用户可以通过手动刷新按钮更新数据
  }, []); // 空依赖数组确保只执行一次

  const loadMockData = () => {
    setLoading(true);
    
    // 模拟API调用延迟
    setTimeout(() => {
      setAgents([
        {
          id: 'technical_analyst_001',
          name: '技术分析师',
          role: 'technical_analyst',
          status: 'active',
          analysisCount: 15,
          accuracy: 0.85,
          lastActivity: '2分钟前'
        },
        {
          id: 'bullish_researcher_001',
          name: '看涨研究员',
          role: 'bullish_researcher',
          status: 'active',
          analysisCount: 12,
          accuracy: 0.78,
          lastActivity: '1分钟前'
        },
        {
          id: 'bearish_researcher_001',
          name: '看跌研究员',
          role: 'bearish_researcher',
          status: 'active',
          analysisCount: 10,
          accuracy: 0.82,
          lastActivity: '3分钟前'
        },
        {
          id: 'trader_001',
          name: '交易员',
          role: 'trader',
          status: 'busy',
          analysisCount: 8,
          accuracy: 0.90,
          lastActivity: '刚刚'
        },
        {
          id: 'risk_manager_001',
          name: '风险管理',
          role: 'risk_manager',
          status: 'idle',
          analysisCount: 5,
          accuracy: 0.95,
          lastActivity: '5分钟前'
        }
      ]);

      setMessages([
        {
          id: 1,
          from: 'technical_analyst_001',
          to: ['bullish_researcher_001', 'bearish_researcher_001'],
          type: 'analysis_report',
          content: '发现启明星形态，建议关注反转信号',
          timestamp: '14:32:15',
          status: 'delivered'
        },
        {
          id: 2,
          from: 'bullish_researcher_001',
          to: ['trader_001'],
          type: 'research_opinion',
          content: '看涨观点：技术面支持突破，建议买入',
          timestamp: '14:33:22',
          status: 'delivered'
        },
        {
          id: 3,
          from: 'bearish_researcher_001',
          to: ['trader_001'],
          type: 'research_opinion',
          content: '看跌观点：仍需关注风险，建议谨慎',
          timestamp: '14:33:45',
          status: 'delivered'
        },
        {
          id: 4,
          from: 'trader_001',
          to: ['risk_manager_001'],
          type: 'trading_signal',
          content: '交易决策：BUY，置信度80%',
          timestamp: '14:35:10',
          status: 'processing'
        }
      ]);

      setWorkflows([
        {
          id: 'workflow_001',
          name: '市场分析工作流',
          symbol: currentSymbol,
          status: patterns.length > 0 ? 'completed' : 'running',
          progress: patterns.length > 0 ? 1.0 : 0.6,
          stages: [
            { name: '数据获取', status: stockInfo ? 'completed' : 'pending', duration: stockInfo ? '1分钟' : '等待中' },
            { name: '形态识别', status: patterns.length > 0 ? 'completed' : 'running', duration: patterns.length > 0 ? '2分钟' : '进行中' },
            { name: 'AI验证', status: patterns.length > 0 ? 'completed' : 'pending', duration: patterns.length > 0 ? '3分钟' : '等待中' },
            { name: '智能体辩论', status: patterns.length > 0 ? 'running' : 'pending', duration: patterns.length > 0 ? '进行中' : '等待中' },
            { name: '投资建议', status: 'pending', duration: '等待中' }
          ]
        }
      ]);

      setLlmStats({
        provider: 'Mock Provider',
        totalRequests: 45,
        totalTokens: 12500,
        totalCost: 0.0,
        avgResponseTime: 1.2
      });

      setLoading(false);
      setError(null);
    }, 1000);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'busy': return 'orange';
      case 'idle': return 'blue';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined style={{ color: 'green' }} />;
      case 'running': return <SyncOutlined spin style={{ color: 'blue' }} />;
      case 'pending': return <ClockCircleOutlined style={{ color: 'gray' }} />;
      case 'error': return <ExclamationCircleOutlined style={{ color: 'red' }} />;
      default: return <ClockCircleOutlined />;
    }
  };

  if (loading) {
    return (
      <div className="agent-workspace-loading">
        <Spin size="large" />
        <p>加载智能体工作台...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadMockData}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <div className="agent-workspace">
      <div className="workspace-header">
        <h2>
          <RobotOutlined /> 智能体工作台
        </h2>
        <Button 
          icon={<SyncOutlined />} 
          onClick={loadMockData}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      <Tabs defaultActiveKey="1" className="workspace-tabs">
        <TabPane tab={<span><RobotOutlined />智能体状态</span>} key="1">
          <Row gutter={[16, 16]}>
            {agents.map(agent => (
              <Col xs={24} sm={12} lg={8} key={agent.id}>
                <Card 
                  className="agent-card"
                  title={
                    <div className="agent-title">
                      <Badge 
                        status={getStatusColor(agent.status)} 
                        text={agent.name}
                      />
                    </div>
                  }
                  extra={<Badge count={agent.analysisCount} />}
                >
                  <div className="agent-info">
                    <p><strong>角色:</strong> {agent.role}</p>
                    <p><strong>状态:</strong> 
                      <Badge 
                        status={getStatusColor(agent.status)} 
                        text={agent.status}
                      />
                    </p>
                    <p><strong>准确率:</strong> {(agent.accuracy * 100).toFixed(1)}%</p>
                    <p><strong>最后活动:</strong> {agent.lastActivity}</p>
                  </div>
                  <Progress 
                    percent={agent.accuracy * 100} 
                    size="small"
                    status={agent.accuracy > 0.8 ? 'success' : 'normal'}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab={<span><MessageOutlined />消息流</span>} key="2">
          <Card title="智能体通信" className="message-flow-card">
            <Timeline>
              {messages.map(message => (
                <Timeline.Item
                  key={message.id}
                  dot={getStatusIcon(message.status)}
                >
                  <div className="message-item">
                    <div className="message-header">
                      <strong>{message.from}</strong> → {message.to.join(', ')}
                      <span className="message-time">{message.timestamp}</span>
                    </div>
                    <div className="message-content">
                      <Badge color="blue" text={message.type} />
                      <p>{message.content}</p>
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </TabPane>

        <TabPane tab={<span><BarChartOutlined />工作流</span>} key="3">
          {workflows.map(workflow => (
            <Card 
              key={workflow.id}
              title={`${workflow.name} - ${workflow.symbol}`}
              className="workflow-card"
              extra={
                <Badge 
                  status={getStatusColor(workflow.status)} 
                  text={workflow.status}
                />
              }
            >
              <div className="workflow-progress">
                <Progress 
                  percent={workflow.progress * 100} 
                  status={workflow.status === 'running' ? 'active' : 'success'}
                />
              </div>
              
              <Timeline className="workflow-timeline">
                {workflow.stages.map((stage, index) => (
                  <Timeline.Item
                    key={index}
                    dot={getStatusIcon(stage.status)}
                  >
                    <div className="stage-item">
                      <strong>{stage.name}</strong>
                      <span className="stage-duration">{stage.duration}</span>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </Card>
          ))}
        </TabPane>

        <TabPane tab={<span><SyncOutlined />LLM统计</span>} key="4">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="LLM使用统计" className="stats-card">
                <div className="stat-item">
                  <span className="stat-label">提供商:</span>
                  <span className="stat-value">{llmStats.provider}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">总请求数:</span>
                  <span className="stat-value">{llmStats.totalRequests}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">总Token数:</span>
                  <span className="stat-value">{llmStats.totalTokens?.toLocaleString()}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">总成本:</span>
                  <span className="stat-value">${llmStats.totalCost?.toFixed(4)}</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">平均响应时间:</span>
                  <span className="stat-value">{llmStats.avgResponseTime}秒</span>
                </div>
              </Card>
            </Col>
            
            <Col xs={24} lg={12}>
              <Card title="系统状态" className="stats-card">
                <div className="system-status">
                  <div className="status-item">
                    <Badge status="success" text="智能体系统" />
                    <span className="status-detail">正常运行</span>
                  </div>
                  <div className="status-item">
                    <Badge status="success" text="LLM服务" />
                    <span className="status-detail">连接正常</span>
                  </div>
                  <div className="status-item">
                    <Badge status="success" text="通信中心" />
                    <span className="status-detail">运行中</span>
                  </div>
                  <div className="status-item">
                    <Badge status="warning" text="实时数据" />
                    <span className="status-detail">模拟模式</span>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default AgentWorkspace;
