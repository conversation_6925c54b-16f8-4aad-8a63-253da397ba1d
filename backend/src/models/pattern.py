"""
蜡烛图形态识别结果数据模型
基于《日本蜡烛图技术》一书的形态分类
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any
from enum import Enum


class PatternType(Enum):
    """形态类型"""
    REVERSAL = "reversal"        # 反转形态
    CONTINUATION = "continuation" # 持续形态
    INDECISION = "indecision"    # 犹豫形态


class PatternSignal(Enum):
    """形态信号"""
    BULLISH = "bullish"      # 看涨信号
    BEARISH = "bearish"      # 看跌信号
    NEUTRAL = "neutral"      # 中性信号


class PatternCategory(Enum):
    """形态分类（按蜡烛线数量）"""
    SINGLE = "single"        # 单根蜡烛线形态
    DOUBLE = "double"        # 双根蜡烛线形态
    TRIPLE = "triple"        # 三根蜡烛线形态
    MULTIPLE = "multiple"    # 多根蜡烛线形态


class PatternName(Enum):
    """具体形态名称（基于书中的形态）"""
    
    # 单根蜡烛线形态
    HAMMER = "hammer"                           # 锤子线
    HANGING_MAN = "hanging_man"                 # 上吊线
    DOJI = "doji"                              # 十字线
    LONG_LEGGED_DOJI = "long_legged_doji"      # 长腿十字线
    GRAVESTONE_DOJI = "gravestone_doji"        # 墓碑十字线
    DRAGONFLY_DOJI = "dragonfly_doji"          # 蜻蜓十字线
    SPINNING_TOP = "spinning_top"              # 纺锤线
    MARUBOZU = "marubozu"                      # 光头光脚线
    
    # 双根蜡烛线形态
    ENGULFING_BULLISH = "engulfing_bullish"    # 看涨吞没形态
    ENGULFING_BEARISH = "engulfing_bearish"    # 看跌吞没形态
    DARK_CLOUD_COVER = "dark_cloud_cover"      # 乌云盖顶
    PIERCING_PATTERN = "piercing_pattern"      # 刺透形态
    HARAMI_BULLISH = "harami_bullish"          # 看涨孕线
    HARAMI_BEARISH = "harami_bearish"          # 看跌孕线
    HARAMI_CROSS = "harami_cross"              # 十字孕线
    TWEEZERS_TOP = "tweezers_top"              # 平头顶部
    TWEEZERS_BOTTOM = "tweezers_bottom"        # 平头底部
    KICKING_PATTERN = "kicking_pattern"        # 踢腿形态
    MEETING_LINES = "meeting_lines"            # 会合线
    
    # 三根蜡烛线形态
    MORNING_STAR = "morning_star"              # 启明星
    EVENING_STAR = "evening_star"              # 黄昏星
    MORNING_DOJI_STAR = "morning_doji_star"    # 十字启明星
    EVENING_DOJI_STAR = "evening_doji_star"    # 十字黄昏星
    THREE_WHITE_SOLDIERS = "three_white_soldiers"  # 前进白色三兵
    THREE_BLACK_CROWS = "three_black_crows"    # 三只乌鸦
    RISING_THREE_METHODS = "rising_three_methods"   # 上升三法
    FALLING_THREE_METHODS = "falling_three_methods" # 下降三法
    
    # 其他形态
    SHOOTING_STAR = "shooting_star"            # 流星
    INVERTED_HAMMER = "inverted_hammer"        # 倒锤子
    BELT_HOLD_BULLISH = "belt_hold_bullish"    # 看涨捉腰带线
    BELT_HOLD_BEARISH = "belt_hold_bearish"    # 看跌捉腰带线
    GAP_UP = "gap_up"                          # 向上跳空
    GAP_DOWN = "gap_down"                      # 向下跳空


@dataclass
class PatternResult:
    """形态识别结果"""
    pattern_name: PatternName          # 形态名称
    pattern_type: PatternType          # 形态类型
    pattern_category: PatternCategory  # 形态分类
    signal: PatternSignal             # 信号方向
    confidence: float                 # 置信度 (0.0 - 1.0)
    start_index: int                  # 起始位置索引
    end_index: int                    # 结束位置索引
    start_time: datetime              # 起始时间
    end_time: datetime                # 结束时间
    description: str                  # 形态描述
    
    # 可选的详细信息
    key_levels: Optional[Dict[str, float]] = None      # 关键价位
    volume_confirmation: Optional[bool] = None         # 成交量确认
    trend_context: Optional[str] = None                # 趋势背景
    reliability_factors: Optional[List[str]] = None    # 可靠性因素
    
    def __post_init__(self):
        """数据验证"""
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError("置信度必须在0.0到1.0之间")
        if self.start_index > self.end_index:
            raise ValueError("起始索引不能大于结束索引")
        if self.start_time > self.end_time:
            raise ValueError("起始时间不能晚于结束时间")

    @property
    def candle_count(self) -> int:
        """形态包含的蜡烛线数量"""
        return self.end_index - self.start_index + 1

    @property
    def is_bullish(self) -> bool:
        """是否为看涨形态"""
        return self.signal == PatternSignal.BULLISH

    @property
    def is_bearish(self) -> bool:
        """是否为看跌形态"""
        return self.signal == PatternSignal.BEARISH

    @property
    def is_reversal(self) -> bool:
        """是否为反转形态"""
        return self.pattern_type == PatternType.REVERSAL

    @property
    def is_continuation(self) -> bool:
        """是否为持续形态"""
        return self.pattern_type == PatternType.CONTINUATION

    def get_chinese_name(self) -> str:
        """获取中文形态名称"""
        chinese_names = {
            PatternName.HAMMER: "锤子线",
            PatternName.HANGING_MAN: "上吊线",
            PatternName.DOJI: "十字线",
            PatternName.LONG_LEGGED_DOJI: "长腿十字线",
            PatternName.GRAVESTONE_DOJI: "墓碑十字线",
            PatternName.DRAGONFLY_DOJI: "蜻蜓十字线",
            PatternName.SPINNING_TOP: "纺锤线",
            PatternName.MARUBOZU: "光头光脚线",
            PatternName.ENGULFING_BULLISH: "看涨吞没形态",
            PatternName.ENGULFING_BEARISH: "看跌吞没形态",
            PatternName.DARK_CLOUD_COVER: "乌云盖顶",
            PatternName.PIERCING_PATTERN: "刺透形态",
            PatternName.HARAMI_BULLISH: "看涨孕线",
            PatternName.HARAMI_BEARISH: "看跌孕线",
            PatternName.HARAMI_CROSS: "十字孕线",
            PatternName.TWEEZERS_TOP: "平头顶部",
            PatternName.TWEEZERS_BOTTOM: "平头底部",
            PatternName.KICKING_PATTERN: "踢腿形态",
            PatternName.MEETING_LINES: "会合线",
            PatternName.MORNING_STAR: "启明星",
            PatternName.EVENING_STAR: "黄昏星",
            PatternName.MORNING_DOJI_STAR: "十字启明星",
            PatternName.EVENING_DOJI_STAR: "十字黄昏星",
            PatternName.THREE_WHITE_SOLDIERS: "前进白色三兵",
            PatternName.THREE_BLACK_CROWS: "三只乌鸦",
            PatternName.RISING_THREE_METHODS: "上升三法",
            PatternName.FALLING_THREE_METHODS: "下降三法",
            PatternName.SHOOTING_STAR: "流星",
            PatternName.INVERTED_HAMMER: "倒锤子",
            PatternName.BELT_HOLD_BULLISH: "看涨捉腰带线",
            PatternName.BELT_HOLD_BEARISH: "看跌捉腰带线",
            PatternName.GAP_UP: "向上跳空",
            PatternName.GAP_DOWN: "向下跳空",
        }
        return chinese_names.get(self.pattern_name, self.pattern_name.value)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "pattern_name": self.pattern_name.value,
            "chinese_name": self.get_chinese_name(),
            "pattern_type": self.pattern_type.value,
            "pattern_category": self.pattern_category.value,
            "signal": self.signal.value,
            "confidence": self.confidence,
            "start_index": self.start_index,
            "end_index": self.end_index,
            "candle_count": self.candle_count,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "description": self.description,
            "key_levels": self.key_levels,
            "volume_confirmation": self.volume_confirmation,
            "trend_context": self.trend_context,
            "reliability_factors": self.reliability_factors,
        }

    def __str__(self) -> str:
        return (f"{self.get_chinese_name()}({self.pattern_name.value}) - "
                f"{self.signal.value} - 置信度: {self.confidence:.2f}")


@dataclass
class PatternAnalysisResult:
    """形态分析结果集合"""
    patterns: List[PatternResult]
    analysis_time: datetime
    total_candles: int
    time_range: tuple[datetime, datetime]
    
    def __post_init__(self):
        """按置信度排序"""
        self.patterns.sort(key=lambda p: p.confidence, reverse=True)
    
    def get_bullish_patterns(self) -> List[PatternResult]:
        """获取看涨形态"""
        return [p for p in self.patterns if p.is_bullish]
    
    def get_bearish_patterns(self) -> List[PatternResult]:
        """获取看跌形态"""
        return [p for p in self.patterns if p.is_bearish]
    
    def get_reversal_patterns(self) -> List[PatternResult]:
        """获取反转形态"""
        return [p for p in self.patterns if p.is_reversal]
    
    def get_continuation_patterns(self) -> List[PatternResult]:
        """获取持续形态"""
        return [p for p in self.patterns if p.is_continuation]

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "patterns": [p.to_dict() for p in self.patterns],
            "analysis_time": self.analysis_time.isoformat(),
            "total_candles": self.total_candles,
            "time_range": [self.time_range[0].isoformat(), self.time_range[1].isoformat()],
            "summary": {
                "total_patterns": len(self.patterns),
                "bullish_patterns": len(self.get_bullish_patterns()),
                "bearish_patterns": len(self.get_bearish_patterns()),
                "reversal_patterns": len(self.get_reversal_patterns()),
                "continuation_patterns": len(self.get_continuation_patterns()),
                "avg_confidence": sum(p.confidence for p in self.patterns) / len(self.patterns) if self.patterns else 0.0,
                "strongest_signal": {
                    "pattern_name": max(self.patterns, key=lambda p: p.confidence).pattern_name.value,
                    "confidence": max(self.patterns, key=lambda p: p.confidence).confidence
                } if self.patterns else None
            }
        }
    
    def get_high_confidence_patterns(self, threshold: float = 0.7) -> List[PatternResult]:
        """获取高置信度形态"""
        return [p for p in self.patterns if p.confidence >= threshold]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "patterns": [p.to_dict() for p in self.patterns],
            "analysis_time": self.analysis_time.isoformat(),
            "total_candles": self.total_candles,
            "time_range": [self.time_range[0].isoformat(), self.time_range[1].isoformat()],
            "summary": {
                "total_patterns": len(self.patterns),
                "bullish_patterns": len(self.get_bullish_patterns()),
                "bearish_patterns": len(self.get_bearish_patterns()),
                "reversal_patterns": len(self.get_reversal_patterns()),
                "continuation_patterns": len(self.get_continuation_patterns()),
                "high_confidence_patterns": len(self.get_high_confidence_patterns()),
            }
        }
