"""
蜡烛图形态识别系统 - 数据模型包
基于《日本蜡烛图技术》一书实现
"""

from .candle import (
    Candle,
    CandleSequence,
    CandleColor,
    CandleType
)

from .pattern import (
    PatternResult,
    PatternAnalysisResult,
    PatternType,
    PatternSignal,
    PatternCategory,
    PatternName
)

__all__ = [
    # 蜡烛图相关
    'Candle',
    'CandleSequence', 
    'CandleColor',
    'CandleType',
    
    # 形态识别相关
    'PatternResult',
    'PatternAnalysisResult',
    'PatternType',
    'PatternSignal',
    'PatternCategory',
    'PatternName',
]
