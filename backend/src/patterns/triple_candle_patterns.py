"""
三根蜡烛线形态识别器
基于《日本蜡烛图技术》一书第五章、第七章的内容实现
"""

from typing import List, Optional
from datetime import datetime
from ..models import (
    Candle, PatternResult, PatternName, PatternType, 
    PatternSignal, PatternCategory
)
from ..utils import PatternUtils


class TripleCandlePatternRecognizer:
    """三根蜡烛线形态识别器"""
    
    def __init__(self):
        self.pattern_utils = PatternUtils()
    
    def identify_patterns(self, candles: List[Candle], index: int) -> List[PatternResult]:
        """
        识别指定位置结束的三根蜡烛线形态
        
        Args:
            candles: 蜡烛线列表
            index: 第三根蜡烛线的索引
            
        Returns:
            List[PatternResult]: 识别到的形态列表
        """
        if index < 2 or index >= len(candles):
            return []
        
        candle1 = candles[index - 2]  # 第一根蜡烛线
        candle2 = candles[index - 1]  # 第二根蜡烛线（星线）
        candle3 = candles[index]      # 第三根蜡烛线
        patterns = []
        
        # 获取趋势背景
        trend_context = "unknown"
        if index >= 5:
            trend_context = self.pattern_utils.get_trend_direction(candles[:index-2])
        
        # 识别各种三根蜡烛线形态
        patterns.extend(self._identify_star_patterns(candle1, candle2, candle3, index, trend_context))
        patterns.extend(self._identify_three_soldiers_crows(candle1, candle2, candle3, index, trend_context))
        patterns.extend(self._identify_three_methods(candles, index, trend_context))
        
        return patterns

    def _identify_star_patterns(self, candle1: Candle, candle2: Candle, candle3: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别星线形态（启明星、黄昏星等）"""
        patterns = []

        # 星线形态的基本条件：
        # 1. 中间的蜡烛线（星线）与前后蜡烛线有跳空
        # 2. 星线实体较小
        # 3. 第一根和第三根蜡烛线颜色相反

        # 检查星线是否有跳空
        if not self._has_gap_with_star(candle1, candle2) or not self._has_gap_with_star(candle2, candle3):
            return patterns

        # 星线应该有小实体
        if not candle2.is_small_body(0.3):
            return patterns

        # 第一根和第三根蜡烛线应该颜色相反
        if candle1.color == candle3.color:
            return patterns

        if candle1.is_bearish and candle3.is_bullish:
            # 可能是启明星
            if trend_context == "downtrend":
                if candle2.is_doji:
                    pattern_name = PatternName.MORNING_DOJI_STAR
                    description = "十字启明星，下降趋势中的强烈看涨反转信号"
                    confidence = 0.85
                else:
                    pattern_name = PatternName.MORNING_STAR
                    description = "启明星，下降趋势中的看涨反转信号"
                    confidence = 0.8

                signal = PatternSignal.BULLISH
                pattern_type = PatternType.REVERSAL

                # 如果第三根蜡烛线深入第一根蜡烛线实体，更可靠
                if candle3.close > (candle1.open + candle1.close) / 2:
                    confidence += 0.05
                    description += "，第三根蜡烛线深入第一根实体"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.TRIPLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 2,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle3.timestamp,
                    description=description,
                    trend_context=trend_context,
                    key_levels={
                        "support": min(candle1.low, candle2.low, candle3.low),
                        "resistance": max(candle1.high, candle2.high, candle3.high)
                    }
                )
                patterns.append(pattern)

        elif candle1.is_bullish and candle3.is_bearish:
            # 可能是黄昏星
            if trend_context == "uptrend":
                if candle2.is_doji:
                    pattern_name = PatternName.EVENING_DOJI_STAR
                    description = "十字黄昏星，上升趋势中的强烈看跌反转信号"
                    confidence = 0.85
                else:
                    pattern_name = PatternName.EVENING_STAR
                    description = "黄昏星，上升趋势中的看跌反转信号"
                    confidence = 0.8

                signal = PatternSignal.BEARISH
                pattern_type = PatternType.REVERSAL

                # 如果第三根蜡烛线深入第一根蜡烛线实体，更可靠
                if candle3.close < (candle1.open + candle1.close) / 2:
                    confidence += 0.05
                    description += "，第三根蜡烛线深入第一根实体"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.TRIPLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 2,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle3.timestamp,
                    description=description,
                    trend_context=trend_context,
                    key_levels={
                        "support": min(candle1.low, candle2.low, candle3.low),
                        "resistance": max(candle1.high, candle2.high, candle3.high)
                    }
                )
                patterns.append(pattern)

        return patterns

    def _has_gap_with_star(self, candle1: Candle, candle2: Candle) -> bool:
        """检查两根蜡烛线之间是否有跳空（用于星线形态）"""
        # 向上跳空：第二根蜡烛线的最低价高于第一根蜡烛线的最高价
        if candle2.low > candle1.high:
            return True
        # 向下跳空：第二根蜡烛线的最高价低于第一根蜡烛线的最低价
        if candle2.high < candle1.low:
            return True
        return False

    def _identify_three_soldiers_crows(self, candle1: Candle, candle2: Candle, candle3: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别三白兵和三乌鸦形态"""
        patterns = []

        # 检查三白兵
        if (candle1.is_bullish and candle2.is_bullish and candle3.is_bullish):
            # 三根阳线，每根都比前一根高
            if (candle2.close > candle1.close and candle3.close > candle2.close and
                candle2.open > candle1.open and candle3.open > candle2.open):

                # 检查实体大小是否合适（不能太小）
                avg_body = (candle1.body_size + candle2.body_size + candle3.body_size) / 3
                if avg_body > 0:  # 确保有实体
                    pattern_name = PatternName.THREE_WHITE_SOLDIERS
                    signal = PatternSignal.BULLISH
                    pattern_type = PatternType.CONTINUATION if trend_context == "uptrend" else PatternType.REVERSAL
                    confidence = 0.75
                    description = "前进白色三兵，三根连续上涨的阳线"

                    # 在下降趋势中作为反转信号更可靠
                    if trend_context == "downtrend":
                        confidence += 0.1
                        description += "，在下降趋势中出现，强烈看涨反转信号"
                    elif trend_context == "uptrend":
                        description += "，在上升趋势中出现，持续看涨信号"

                    # 如果每根蜡烛线都没有长上影线，更可靠
                    if (not candle1.is_long_upper_shadow(1.0) and
                        not candle2.is_long_upper_shadow(1.0) and
                        not candle3.is_long_upper_shadow(1.0)):
                        confidence += 0.05
                        description += "，上影线较短"

                    pattern = PatternResult(
                        pattern_name=pattern_name,
                        pattern_type=pattern_type,
                        pattern_category=PatternCategory.TRIPLE,
                        signal=signal,
                        confidence=min(confidence, 1.0),
                        start_index=index - 2,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle3.timestamp,
                        description=description,
                        trend_context=trend_context,
                        key_levels={
                            "support": min(candle1.low, candle2.low, candle3.low),
                            "resistance": max(candle1.high, candle2.high, candle3.high)
                        }
                    )
                    patterns.append(pattern)

        # 检查三乌鸦
        elif (candle1.is_bearish and candle2.is_bearish and candle3.is_bearish):
            # 三根阴线，每根都比前一根低
            if (candle2.close < candle1.close and candle3.close < candle2.close and
                candle2.open < candle1.open and candle3.open < candle2.open):

                # 检查实体大小是否合适（不能太小）
                avg_body = (candle1.body_size + candle2.body_size + candle3.body_size) / 3
                if avg_body > 0:  # 确保有实体
                    pattern_name = PatternName.THREE_BLACK_CROWS
                    signal = PatternSignal.BEARISH
                    pattern_type = PatternType.CONTINUATION if trend_context == "downtrend" else PatternType.REVERSAL
                    confidence = 0.75
                    description = "三只乌鸦，三根连续下跌的阴线"

                    # 在上升趋势中作为反转信号更可靠
                    if trend_context == "uptrend":
                        confidence += 0.1
                        description += "，在上升趋势中出现，强烈看跌反转信号"
                    elif trend_context == "downtrend":
                        description += "，在下降趋势中出现，持续看跌信号"

                    # 如果每根蜡烛线都没有长下影线，更可靠
                    if (not candle1.is_long_lower_shadow(1.0) and
                        not candle2.is_long_lower_shadow(1.0) and
                        not candle3.is_long_lower_shadow(1.0)):
                        confidence += 0.05
                        description += "，下影线较短"

                    pattern = PatternResult(
                        pattern_name=pattern_name,
                        pattern_type=pattern_type,
                        pattern_category=PatternCategory.TRIPLE,
                        signal=signal,
                        confidence=min(confidence, 1.0),
                        start_index=index - 2,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle3.timestamp,
                        description=description,
                        trend_context=trend_context,
                        key_levels={
                            "support": min(candle1.low, candle2.low, candle3.low),
                            "resistance": max(candle1.high, candle2.high, candle3.high)
                        }
                    )
                    patterns.append(pattern)

        return patterns

    def _identify_three_methods(self, candles: List[Candle], index: int, trend_context: str) -> List[PatternResult]:
        """识别三法形态（需要5根蜡烛线）"""
        patterns = []

        # 三法形态需要5根蜡烛线
        if index < 4:
            return patterns

        candle1 = candles[index - 4]  # 第一根长蜡烛线
        candle2 = candles[index - 3]  # 第一根小蜡烛线
        candle3 = candles[index - 2]  # 第二根小蜡烛线
        candle4 = candles[index - 1]  # 第三根小蜡烛线
        candle5 = candles[index]      # 最后一根长蜡烛线

        # 上升三法
        if (candle1.is_bullish and candle5.is_bullish and
            candle1.body_size > 0 and candle5.body_size > 0):

            # 中间三根蜡烛线应该在第一根蜡烛线的价格区间内
            if (all(c.high <= candle1.high and c.low >= candle1.low for c in [candle2, candle3, candle4]) and
                candle5.close > candle1.close):

                pattern_name = PatternName.RISING_THREE_METHODS
                signal = PatternSignal.BULLISH
                pattern_type = PatternType.CONTINUATION
                confidence = 0.7
                description = "上升三法，上升趋势中的持续形态"

                # 在上升趋势中更可靠
                if trend_context == "uptrend":
                    confidence += 0.1
                    description += "，确认上升趋势继续"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.MULTIPLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 4,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle5.timestamp,
                    description=description,
                    trend_context=trend_context
                )
                patterns.append(pattern)

        # 下降三法
        elif (candle1.is_bearish and candle5.is_bearish and
              candle1.body_size > 0 and candle5.body_size > 0):

            # 中间三根蜡烛线应该在第一根蜡烛线的价格区间内
            if (all(c.high <= candle1.high and c.low >= candle1.low for c in [candle2, candle3, candle4]) and
                candle5.close < candle1.close):

                pattern_name = PatternName.FALLING_THREE_METHODS
                signal = PatternSignal.BEARISH
                pattern_type = PatternType.CONTINUATION
                confidence = 0.7
                description = "下降三法，下降趋势中的持续形态"

                # 在下降趋势中更可靠
                if trend_context == "downtrend":
                    confidence += 0.1
                    description += "，确认下降趋势继续"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.MULTIPLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 4,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle5.timestamp,
                    description=description,
                    trend_context=trend_context
                )
                patterns.append(pattern)

        return patterns
    
    def _identify_star_patterns(self, candle1: Candle, candle2: Candle, candle3: Candle, 
                              index: int, trend_context: str) -> List[PatternResult]:
        """识别星线形态"""
        patterns = []
        
        # 检查第二根蜡烛线是否为星线
        if not self.pattern_utils.is_star_pattern(candle1, candle2):
            return patterns
        
        # 启明星形态：下降趋势 + 阴线 + 星线 + 阳线
        if (trend_context == "downtrend" and 
            candle1.is_bearish and candle3.is_bullish and
            candle3.close > candle1.get_body_midpoint()):
            
            # 判断是否为十字启明星
            pattern_name = PatternName.MORNING_DOJI_STAR if candle2.is_doji else PatternName.MORNING_STAR
            confidence = 0.85 if candle2.is_doji else 0.8
            
            # 如果第三根蜡烛线收盘价超过第一根开盘价，置信度更高
            if candle3.close > candle1.open:
                confidence += 0.1
            
            patterns.append(PatternResult(
                pattern_name=pattern_name,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.TRIPLE,
                signal=PatternSignal.BULLISH,
                confidence=confidence,
                start_index=index - 2,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle3.timestamp,
                description=f"{'十字' if candle2.is_doji else ''}启明星：下降趋势反转信号，表示卖压减弱买盘增强",
                trend_context=trend_context,
                key_levels={
                    "star_low": candle2.low,
                    "confirmation_level": candle1.get_body_midpoint()
                }
            ))
        
        # 黄昏星形态：上升趋势 + 阳线 + 星线 + 阴线
        elif (trend_context == "uptrend" and 
              candle1.is_bullish and candle3.is_bearish and
              candle3.close < candle1.get_body_midpoint()):
            
            # 判断是否为十字黄昏星
            pattern_name = PatternName.EVENING_DOJI_STAR if candle2.is_doji else PatternName.EVENING_STAR
            confidence = 0.85 if candle2.is_doji else 0.8
            
            # 如果第三根蜡烛线收盘价低于第一根开盘价，置信度更高
            if candle3.close < candle1.open:
                confidence += 0.1
            
            patterns.append(PatternResult(
                pattern_name=pattern_name,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.TRIPLE,
                signal=PatternSignal.BEARISH,
                confidence=confidence,
                start_index=index - 2,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle3.timestamp,
                description=f"{'十字' if candle2.is_doji else ''}黄昏星：上升趋势反转信号，表示买盘减弱卖压增强",
                trend_context=trend_context,
                key_levels={
                    "star_high": candle2.high,
                    "confirmation_level": candle1.get_body_midpoint()
                }
            ))
        
        return patterns
    
    def _identify_three_soldiers_crows(self, candle1: Candle, candle2: Candle, candle3: Candle, 
                                     index: int, trend_context: str) -> List[PatternResult]:
        """识别前进白色三兵和三只乌鸦形态"""
        patterns = []
        
        # 前进白色三兵：三根连续上涨的阳线
        if (candle1.is_bullish and candle2.is_bullish and candle3.is_bullish and
            candle2.close > candle1.close and candle3.close > candle2.close and
            candle2.open > candle1.open and candle3.open > candle2.open):
            
            # 检查实体大小是否合理（不应该过小）
            avg_body_size = (candle1.body_size + candle2.body_size + candle3.body_size) / 3
            if avg_body_size > 0:  # 确保有实体
                
                confidence = 0.8
                if trend_context == "downtrend":
                    confidence = 0.9  # 在下降趋势中作为反转信号更可靠
                
                patterns.append(PatternResult(
                    pattern_name=PatternName.THREE_WHITE_SOLDIERS,
                    pattern_type=PatternType.REVERSAL if trend_context == "downtrend" else PatternType.CONTINUATION,
                    pattern_category=PatternCategory.TRIPLE,
                    signal=PatternSignal.BULLISH,
                    confidence=confidence,
                    start_index=index - 2,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle3.timestamp,
                    description="前进白色三兵：三根连续上涨的阳线，显示强劲的买盘力量",
                    trend_context=trend_context
                ))
        
        # 三只乌鸦：三根连续下跌的阴线
        elif (candle1.is_bearish and candle2.is_bearish and candle3.is_bearish and
              candle2.close < candle1.close and candle3.close < candle2.close and
              candle2.open < candle1.open and candle3.open < candle2.open):
            
            # 检查实体大小是否合理
            avg_body_size = (candle1.body_size + candle2.body_size + candle3.body_size) / 3
            if avg_body_size > 0:
                
                confidence = 0.8
                if trend_context == "uptrend":
                    confidence = 0.9  # 在上升趋势中作为反转信号更可靠
                
                patterns.append(PatternResult(
                    pattern_name=PatternName.THREE_BLACK_CROWS,
                    pattern_type=PatternType.REVERSAL if trend_context == "uptrend" else PatternType.CONTINUATION,
                    pattern_category=PatternCategory.TRIPLE,
                    signal=PatternSignal.BEARISH,
                    confidence=confidence,
                    start_index=index - 2,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle3.timestamp,
                    description="三只乌鸦：三根连续下跌的阴线，显示强劲的卖压",
                    trend_context=trend_context
                ))
        
        return patterns
    
    def _identify_three_methods(self, candles: List[Candle], index: int, trend_context: str) -> List[PatternResult]:
        """识别三法形态（需要5根蜡烛线）"""
        patterns = []
        
        # 三法形态需要5根蜡烛线
        if index < 4:
            return patterns
        
        candle1 = candles[index - 4]  # 第一根长蜡烛线
        candle2 = candles[index - 3]  # 第一根小蜡烛线
        candle3 = candles[index - 2]  # 第二根小蜡烛线
        candle4 = candles[index - 1]  # 第三根小蜡烛线
        candle5 = candles[index]      # 最后一根长蜡烛线
        
        # 上升三法
        if (candle1.is_bullish and candle5.is_bullish and
            candle5.close > candle1.close and candle5.open > candle1.open):
            
            # 中间三根蜡烛线应该是小实体，且在第一根蜡烛线的价格范围内
            middle_candles = [candle2, candle3, candle4]
            if all(c.is_small_body() for c in middle_candles):
                
                # 检查中间蜡烛线是否在第一根蜡烛线范围内
                all_in_range = all(
                    c.high <= candle1.high and c.low >= candle1.low 
                    for c in middle_candles
                )
                
                if all_in_range and trend_context == "uptrend":
                    patterns.append(PatternResult(
                        pattern_name=PatternName.RISING_THREE_METHODS,
                        pattern_type=PatternType.CONTINUATION,
                        pattern_category=PatternCategory.MULTIPLE,
                        signal=PatternSignal.BULLISH,
                        confidence=0.8,
                        start_index=index - 4,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle5.timestamp,
                        description="上升三法：上升趋势中的持续形态，短暂整理后继续上涨",
                        trend_context=trend_context
                    ))
        
        # 下降三法
        elif (candle1.is_bearish and candle5.is_bearish and
              candle5.close < candle1.close and candle5.open < candle1.open):
            
            # 中间三根蜡烛线应该是小实体，且在第一根蜡烛线的价格范围内
            middle_candles = [candle2, candle3, candle4]
            if all(c.is_small_body() for c in middle_candles):
                
                # 检查中间蜡烛线是否在第一根蜡烛线范围内
                all_in_range = all(
                    c.high <= candle1.high and c.low >= candle1.low 
                    for c in middle_candles
                )
                
                if all_in_range and trend_context == "downtrend":
                    patterns.append(PatternResult(
                        pattern_name=PatternName.FALLING_THREE_METHODS,
                        pattern_type=PatternType.CONTINUATION,
                        pattern_category=PatternCategory.MULTIPLE,
                        signal=PatternSignal.BEARISH,
                        confidence=0.8,
                        start_index=index - 4,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle5.timestamp,
                        description="下降三法：下降趋势中的持续形态，短暂整理后继续下跌",
                        trend_context=trend_context
                    ))
        
        return patterns
