"""
蜡烛图形态识别器主类
整合所有形态识别功能
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from ..models import (
    Candle, CandleSequence, PatternResult, PatternAnalysisResult
)
from ..utils import PatternUtils
from .single_candle_patterns import SingleCandlePatternRecognizer
from .double_candle_patterns import DoubleCandlePatternRecognizer
from .triple_candle_patterns import TripleCandlePatternRecognizer


class PatternRecognizer:
    """蜡烛图形态识别器主类"""
    
    def __init__(self):
        self.single_recognizer = SingleCandlePatternRecognizer()
        self.double_recognizer = DoubleCandlePatternRecognizer()
        self.triple_recognizer = TripleCandlePatternRecognizer()
        self.pattern_utils = PatternUtils()
    
    def identify_patterns(self, candles: List[Candle], 
                         start_index: int = 0, 
                         end_index: Optional[int] = None) -> PatternAnalysisResult:
        """
        识别蜡烛图形态
        
        Args:
            candles: 蜡烛线列表
            start_index: 开始分析的索引
            end_index: 结束分析的索引（不包含），None表示到最后
            
        Returns:
            PatternAnalysisResult: 形态分析结果
        """
        if not candles:
            raise ValueError("蜡烛线列表不能为空")
        
        if end_index is None:
            end_index = len(candles)
        
        if start_index < 0 or end_index > len(candles) or start_index >= end_index:
            raise ValueError("索引范围无效")
        
        all_patterns = []
        
        # 逐个位置分析形态
        for i in range(start_index, end_index):
            # 单根蜡烛线形态
            single_patterns = self.single_recognizer.identify_patterns(candles, i)
            all_patterns.extend(single_patterns)
            
            # 双根蜡烛线形态
            if i >= 1:
                double_patterns = self.double_recognizer.identify_patterns(candles, i)
                all_patterns.extend(double_patterns)
            
            # 三根蜡烛线形态
            if i >= 2:
                triple_patterns = self.triple_recognizer.identify_patterns(candles, i)
                all_patterns.extend(triple_patterns)
        
        # 后处理：去重和优化
        optimized_patterns = self._optimize_patterns(all_patterns)
        
        # 添加成交量确认
        for pattern in optimized_patterns:
            pattern.volume_confirmation = self.pattern_utils.validate_volume_confirmation(
                candles, pattern.start_index, pattern.end_index
            )
        
        # 创建分析结果
        analysis_result = PatternAnalysisResult(
            patterns=optimized_patterns,
            analysis_time=datetime.now(),
            total_candles=len(candles),
            time_range=(candles[0].timestamp, candles[-1].timestamp)
        )
        
        return analysis_result
    
    def identify_single_pattern(self, candles: List[Candle], index: int) -> List[PatternResult]:
        """
        识别指定位置的所有形态
        
        Args:
            candles: 蜡烛线列表
            index: 要分析的位置索引
            
        Returns:
            List[PatternResult]: 该位置识别到的所有形态
        """
        patterns = []
        
        # 单根蜡烛线形态
        single_patterns = self.single_recognizer.identify_patterns(candles, index)
        patterns.extend(single_patterns)
        
        # 双根蜡烛线形态
        if index >= 1:
            double_patterns = self.double_recognizer.identify_patterns(candles, index)
            patterns.extend(double_patterns)
        
        # 三根蜡烛线形态
        if index >= 2:
            triple_patterns = self.triple_recognizer.identify_patterns(candles, index)
            patterns.extend(triple_patterns)
        
        return patterns
    
    def _optimize_patterns(self, patterns: List[PatternResult]) -> List[PatternResult]:
        """
        优化形态列表：去重、合并、筛选
        
        Args:
            patterns: 原始形态列表
            
        Returns:
            List[PatternResult]: 优化后的形态列表
        """
        if not patterns:
            return patterns
        
        # 按位置和置信度排序
        patterns.sort(key=lambda p: (p.start_index, -p.confidence))
        
        # 去除重叠的低置信度形态
        optimized = []
        for pattern in patterns:
            # 检查是否与已有形态重叠
            overlapping = False
            for existing in optimized:
                if self._patterns_overlap(pattern, existing):
                    # 如果新形态置信度更高，替换现有形态
                    if pattern.confidence > existing.confidence:
                        optimized.remove(existing)
                        break
                    else:
                        overlapping = True
                        break
            
            if not overlapping:
                optimized.append(pattern)
        
        # 按置信度排序
        optimized.sort(key=lambda p: p.confidence, reverse=True)
        
        return optimized
    
    def _patterns_overlap(self, pattern1: PatternResult, pattern2: PatternResult) -> bool:
        """
        判断两个形态是否重叠
        
        Args:
            pattern1: 第一个形态
            pattern2: 第二个形态
            
        Returns:
            bool: 是否重叠
        """
        # 检查时间范围是否重叠
        return not (pattern1.end_index < pattern2.start_index or 
                   pattern2.end_index < pattern1.start_index)
    
    def get_pattern_statistics(self, patterns: List[PatternResult]) -> Dict[str, Any]:
        """
        获取形态统计信息
        
        Args:
            patterns: 形态列表
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not patterns:
            return {}
        
        stats = {
            "total_patterns": len(patterns),
            "pattern_types": {},
            "pattern_signals": {},
            "pattern_categories": {},
            "confidence_distribution": {
                "high": 0,    # >= 0.8
                "medium": 0,  # 0.6 - 0.8
                "low": 0      # < 0.6
            },
            "average_confidence": 0.0,
            "most_common_pattern": None,
            "strongest_signal": None
        }
        
        # 统计各种分类
        type_counts = {}
        signal_counts = {}
        category_counts = {}
        pattern_name_counts = {}
        
        total_confidence = 0.0
        
        for pattern in patterns:
            # 形态类型统计
            type_key = pattern.pattern_type.value
            type_counts[type_key] = type_counts.get(type_key, 0) + 1
            
            # 信号统计
            signal_key = pattern.signal.value
            signal_counts[signal_key] = signal_counts.get(signal_key, 0) + 1
            
            # 分类统计
            category_key = pattern.pattern_category.value
            category_counts[category_key] = category_counts.get(category_key, 0) + 1
            
            # 形态名称统计
            name_key = pattern.pattern_name.value
            pattern_name_counts[name_key] = pattern_name_counts.get(name_key, 0) + 1
            
            # 置信度统计
            if pattern.confidence >= 0.8:
                stats["confidence_distribution"]["high"] += 1
            elif pattern.confidence >= 0.6:
                stats["confidence_distribution"]["medium"] += 1
            else:
                stats["confidence_distribution"]["low"] += 1
            
            total_confidence += pattern.confidence
        
        # 填充统计结果
        stats["pattern_types"] = type_counts
        stats["pattern_signals"] = signal_counts
        stats["pattern_categories"] = category_counts
        stats["average_confidence"] = total_confidence / len(patterns)
        
        # 最常见的形态
        if pattern_name_counts:
            most_common = max(pattern_name_counts.items(), key=lambda x: x[1])
            stats["most_common_pattern"] = {
                "name": most_common[0],
                "count": most_common[1]
            }
        
        # 最强信号（最高置信度的形态）
        strongest_pattern = max(patterns, key=lambda p: p.confidence)
        stats["strongest_signal"] = {
            "pattern_name": strongest_pattern.pattern_name.value,
            "signal": strongest_pattern.signal.value,
            "confidence": strongest_pattern.confidence
        }
        
        return stats
    
    def filter_patterns(self, patterns: List[PatternResult], 
                       min_confidence: float = 0.0,
                       pattern_types: Optional[List[str]] = None,
                       signals: Optional[List[str]] = None) -> List[PatternResult]:
        """
        筛选形态
        
        Args:
            patterns: 形态列表
            min_confidence: 最小置信度
            pattern_types: 允许的形态类型列表
            signals: 允许的信号类型列表
            
        Returns:
            List[PatternResult]: 筛选后的形态列表
        """
        filtered = []
        
        for pattern in patterns:
            # 置信度筛选
            if pattern.confidence < min_confidence:
                continue
            
            # 形态类型筛选
            if pattern_types and pattern.pattern_type.value not in pattern_types:
                continue
            
            # 信号类型筛选
            if signals and pattern.signal.value not in signals:
                continue
            
            filtered.append(pattern)
        
        return filtered
