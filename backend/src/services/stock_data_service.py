#!/usr/bin/env python3
"""
真实股票数据服务
支持多个数据源：Alpha Vantage, Yahoo Finance, Polygon
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import yfinance as yf
import pandas as pd

class StockDataService:
    """股票数据服务类"""
    
    def __init__(self, alpha_vantage_key: str = None):
        self.alpha_vantage_key = alpha_vantage_key or "demo"  # 使用demo key进行测试
        self.alpha_vantage_base = "https://www.alphavantage.co/query"
        self.cache = {}  # 简单缓存
        
    def get_stock_data(self, symbol: str, period: str = "1y", source: str = "yahoo") -> Dict:
        """
        获取股票数据
        
        Args:
            symbol: 股票代码 (如 AAPL, TSLA)
            period: 时间周期 (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            source: 数据源 (yahoo, alpha_vantage)
        
        Returns:
            包含股票数据的字典
        """
        cache_key = f"{symbol}_{period}_{source}"
        
        # 检查缓存 (5分钟有效期)
        if cache_key in self.cache:
            cached_time, cached_data = self.cache[cache_key]
            if time.time() - cached_time < 300:  # 5分钟缓存
                return cached_data
        
        try:
            if source == "yahoo":
                data = self._get_yahoo_data(symbol, period)
            elif source == "alpha_vantage":
                data = self._get_alpha_vantage_data(symbol)
            else:
                raise ValueError(f"不支持的数据源: {source}")
            
            # 缓存结果
            self.cache[cache_key] = (time.time(), data)
            return data
            
        except Exception as e:
            raise Exception(f"获取股票数据失败: {str(e)}")
    
    def _get_yahoo_data(self, symbol: str, period: str) -> Dict:
        """使用Yahoo Finance获取数据"""
        try:
            # 创建股票对象
            stock = yf.Ticker(symbol)
            
            # 获取股票信息
            info = stock.info
            
            # 获取历史数据
            hist = stock.history(period=period)
            
            if hist.empty:
                raise Exception(f"未找到股票代码 {symbol} 的数据")
            
            # 转换为我们需要的格式
            candles = []
            for index, row in hist.iterrows():
                candles.append({
                    "timestamp": index.strftime("%Y-%m-%d %H:%M:%S"),
                    "open": float(row['Open']),
                    "high": float(row['High']),
                    "low": float(row['Low']),
                    "close": float(row['Close']),
                    "volume": int(row['Volume'])
                })
            
            return {
                "status": "success",
                "symbol": symbol.upper(),
                "company_name": info.get('longName', symbol.upper()),
                "currency": info.get('currency', 'USD'),
                "exchange": info.get('exchange', 'Unknown'),
                "market_cap": info.get('marketCap'),
                "current_price": float(hist['Close'].iloc[-1]),
                "price_change": float(hist['Close'].iloc[-1] - hist['Close'].iloc[-2]) if len(hist) > 1 else 0,
                "price_change_percent": float((hist['Close'].iloc[-1] - hist['Close'].iloc[-2]) / hist['Close'].iloc[-2] * 100) if len(hist) > 1 else 0,
                "candles": candles,
                "total_candles": len(candles),
                "period": period,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Yahoo Finance数据获取失败: {str(e)}")
    
    def _get_alpha_vantage_data(self, symbol: str) -> Dict:
        """使用Alpha Vantage获取数据"""
        try:
            params = {
                'function': 'TIME_SERIES_DAILY',
                'symbol': symbol,
                'apikey': self.alpha_vantage_key,
                'outputsize': 'compact'  # 最近100天
            }
            
            response = requests.get(self.alpha_vantage_base, params=params, timeout=10)
            data = response.json()
            
            if 'Error Message' in data:
                raise Exception(f"Alpha Vantage错误: {data['Error Message']}")
            
            if 'Note' in data:
                raise Exception("API调用频率限制，请稍后再试")
            
            time_series = data.get('Time Series (Daily)', {})
            if not time_series:
                raise Exception(f"未找到股票代码 {symbol} 的数据")
            
            # 转换数据格式
            candles = []
            for date_str, values in sorted(time_series.items()):
                candles.append({
                    "timestamp": f"{date_str} 16:00:00",  # 假设收盘时间
                    "open": float(values['1. open']),
                    "high": float(values['2. high']),
                    "low": float(values['3. low']),
                    "close": float(values['4. close']),
                    "volume": int(values['5. volume'])
                })
            
            # 获取股票元信息
            meta_data = data.get('Meta Data', {})
            
            return {
                "status": "success",
                "symbol": symbol.upper(),
                "company_name": meta_data.get('2. Symbol', symbol.upper()),
                "currency": "USD",
                "exchange": "Unknown",
                "current_price": float(candles[-1]['close']) if candles else 0,
                "price_change": float(candles[-1]['close'] - candles[-2]['close']) if len(candles) > 1 else 0,
                "price_change_percent": float((candles[-1]['close'] - candles[-2]['close']) / candles[-2]['close'] * 100) if len(candles) > 1 else 0,
                "candles": candles,
                "total_candles": len(candles),
                "period": "100d",
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Alpha Vantage数据获取失败: {str(e)}")
    
    def search_stocks(self, query: str) -> List[Dict]:
        """搜索股票"""
        try:
            # 使用Yahoo Finance搜索
            import yfinance as yf
            
            # 常见股票列表（作为备选）
            popular_stocks = [
                {"symbol": "AAPL", "name": "Apple Inc.", "exchange": "NASDAQ"},
                {"symbol": "MSFT", "name": "Microsoft Corporation", "exchange": "NASDAQ"},
                {"symbol": "GOOGL", "name": "Alphabet Inc.", "exchange": "NASDAQ"},
                {"symbol": "AMZN", "name": "Amazon.com Inc.", "exchange": "NASDAQ"},
                {"symbol": "TSLA", "name": "Tesla Inc.", "exchange": "NASDAQ"},
                {"symbol": "META", "name": "Meta Platforms Inc.", "exchange": "NASDAQ"},
                {"symbol": "NVDA", "name": "NVIDIA Corporation", "exchange": "NASDAQ"},
                {"symbol": "JPM", "name": "JPMorgan Chase & Co.", "exchange": "NYSE"},
                {"symbol": "JNJ", "name": "Johnson & Johnson", "exchange": "NYSE"},
                {"symbol": "V", "name": "Visa Inc.", "exchange": "NYSE"}
            ]
            
            # 简单的模糊匹配
            query_upper = query.upper()
            results = []
            
            for stock in popular_stocks:
                if (query_upper in stock["symbol"] or 
                    query_upper in stock["name"].upper()):
                    results.append(stock)
            
            return results[:10]  # 返回前10个结果
            
        except Exception as e:
            return []
    
    def get_market_status(self) -> Dict:
        """获取市场状态"""
        try:
            # 简单的市场状态检查
            now = datetime.now()
            
            # 美股交易时间 (简化版)
            is_weekend = now.weekday() >= 5
            is_trading_hours = 9 <= now.hour <= 16
            
            return {
                "is_open": not is_weekend and is_trading_hours,
                "next_open": "下个交易日 09:30",
                "next_close": "今日 16:00" if not is_weekend and is_trading_hours else "下个交易日 16:00",
                "timezone": "US/Eastern"
            }
            
        except Exception:
            return {
                "is_open": False,
                "next_open": "未知",
                "next_close": "未知",
                "timezone": "Unknown"
            }

# 全局实例
stock_service = StockDataService()

def get_stock_service() -> StockDataService:
    """获取股票数据服务实例"""
    return stock_service
