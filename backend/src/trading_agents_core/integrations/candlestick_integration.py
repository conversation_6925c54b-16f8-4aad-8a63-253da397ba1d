"""
蜡烛图形态识别系统集成模块
将TradingAgents多智能体系统集成到蜡烛图形态识别中
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.agent_manager import AgentManager
from core.debate_system import DebateSystem
from agents.candlestick_analyst import CandlestickAnalyst
from utils.config import Config
from utils.llm_client import LLMClient


class CandlestickPatternValidator:
    """
    蜡烛图形态验证器 - 集成多智能体系统验证形态
    """
    
    def __init__(self, config_path: str = None):
        """初始化形态验证器"""
        self.logger = logging.getLogger("CandlestickValidator")
        
        # 加载配置
        if config_path:
            self.config = Config(config_path)
        else:
            self.config = self._get_default_config()
        
        # 初始化组件
        self.agent_manager = None
        self.debate_system = None
        self.candlestick_analyst = None
        self.is_initialized = False
        
        self.logger.info("蜡烛图形态验证器创建完成")
    
    def _get_default_config(self) -> Config:
        """获取默认配置"""
        config = Config()
        # 添加蜡烛图分析师到智能体配置
        config.set('agents.candlestick_analyst', {
            'enabled': True,
            'name': 'CandlestickAnalyst',
            'model': 'deepseek-ai/DeepSeek-V3',
            'temperature': 0.2,
            'specialization': 'candlestick_analysis'
        })
        return config
    
    async def initialize(self):
        """初始化验证器"""
        if self.is_initialized:
            return
        
        self.logger.info("🔧 初始化蜡烛图形态验证器...")
        
        try:
            # 初始化智能体管理器
            self.agent_manager = AgentManager(self.config)
            await self.agent_manager.initialize_agents()
            
            # 初始化辩论系统
            self.debate_system = DebateSystem(self.config)
            await self.debate_system.initialize()
            
            # 初始化蜡烛图分析师
            candlestick_config = self.config.get('agents.candlestick_analyst', {})
            self.candlestick_analyst = CandlestickAnalyst(
                name=candlestick_config.get('name', 'CandlestickAnalyst'),
                config=candlestick_config,
                global_config=self.config
            )
            await self.candlestick_analyst.initialize()
            
            self.is_initialized = True
            self.logger.info("✅ 蜡烛图形态验证器初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 验证器初始化失败: {e}")
            raise
    
    async def validate_pattern(self, 
                             pattern_name: str, 
                             symbol: str, 
                             candle_data: List[Dict[str, Any]], 
                             pattern_confidence: float = 0.8) -> Dict[str, Any]:
        """
        验证单个蜡烛图形态
        
        Args:
            pattern_name: 形态名称
            symbol: 股票代码
            candle_data: 蜡烛图数据
            pattern_confidence: 形态识别置信度
            
        Returns:
            验证结果
        """
        if not self.is_initialized:
            await self.initialize()
        
        self.logger.info(f"🕯️ 验证蜡烛图形态: {pattern_name} ({symbol})")
        
        try:
            validation_result = {
                'pattern_name': pattern_name,
                'symbol': symbol,
                'original_confidence': pattern_confidence,
                'timestamp': datetime.now().isoformat(),
                'validation_status': 'pending'
            }
            
            # 准备市场数据
            market_data = {
                'symbol': symbol,
                'candles': candle_data,
                'pattern_detected': {
                    'name': pattern_name,
                    'confidence': pattern_confidence
                }
            }
            
            # 蜡烛图专家分析
            candlestick_analysis = await self.candlestick_analyst.analyze(symbol, market_data)
            validation_result['candlestick_analysis'] = candlestick_analysis
            
            # 多智能体协作验证
            agent_validation = await self._multi_agent_validation(symbol, pattern_name, market_data)
            validation_result['agent_validation'] = agent_validation
            
            # 智能体辩论
            debate_result = await self._conduct_pattern_debate(pattern_name, symbol, validation_result)
            validation_result['debate_result'] = debate_result
            
            # 生成最终验证结论
            final_assessment = await self._generate_final_assessment(validation_result)
            validation_result['final_assessment'] = final_assessment
            
            validation_result['validation_status'] = 'completed'
            
            self.logger.info(f"✅ 形态验证完成: {pattern_name} ({symbol})")
            return validation_result
            
        except Exception as e:
            self.logger.error(f"❌ 形态验证失败: {pattern_name} ({symbol}): {e}")
            return {
                'pattern_name': pattern_name,
                'symbol': symbol,
                'validation_status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _multi_agent_validation(self, symbol: str, pattern_name: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """多智能体协作验证"""
        self.logger.info(f"🤖 启动多智能体验证: {pattern_name}")
        
        # 获取可用智能体进行分析
        available_agents = self.agent_manager.get_active_agents()
        
        validation_results = {
            'participating_agents': available_agents,
            'agent_analyses': {},
            'consensus_score': 0.0
        }
        
        # 让每个智能体分析形态
        for agent_name in available_agents:
            try:
                agent = self.agent_manager.get_agent(agent_name)
                if agent:
                    analysis = await agent.analyze(symbol, market_data)
                    validation_results['agent_analyses'][agent_name] = analysis
                    self.logger.info(f"✅ {agent_name} 分析完成")
            except Exception as e:
                self.logger.warning(f"⚠️ {agent_name} 分析失败: {e}")
                validation_results['agent_analyses'][agent_name] = {'error': str(e)}
        
        # 计算共识分数
        validation_results['consensus_score'] = self._calculate_consensus_score(validation_results['agent_analyses'])
        
        return validation_results
    
    def _calculate_consensus_score(self, agent_analyses: Dict[str, Any]) -> float:
        """计算智能体共识分数"""
        if not agent_analyses:
            return 0.0
        
        signals = []
        confidences = []
        
        for agent_name, analysis in agent_analyses.items():
            if 'error' not in analysis:
                # 提取信号和置信度
                signal = analysis.get('signal', 'hold')
                confidence = analysis.get('confidence', 0.5)
                
                signals.append(signal)
                confidences.append(confidence)
        
        if not signals:
            return 0.0
        
        # 计算信号一致性
        buy_count = signals.count('buy')
        sell_count = signals.count('sell')
        hold_count = signals.count('hold')
        
        total_signals = len(signals)
        max_agreement = max(buy_count, sell_count, hold_count)
        signal_consensus = max_agreement / total_signals
        
        # 计算平均置信度
        avg_confidence = sum(confidences) / len(confidences)
        
        # 综合共识分数
        consensus_score = (signal_consensus * 0.6 + avg_confidence * 0.4)
        
        return consensus_score
    
    async def _conduct_pattern_debate(self, pattern_name: str, symbol: str, validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """进行形态验证辩论"""
        self.logger.info(f"🎭 启动形态验证辩论: {pattern_name}")
        
        debate_topic = f"验证{symbol}的{pattern_name}形态是否可靠"
        
        try:
            # 准备辩论上下文
            debate_context = {
                'pattern_name': pattern_name,
                'symbol': symbol,
                'candlestick_analysis': validation_data.get('candlestick_analysis', {}),
                'agent_validation': validation_data.get('agent_validation', {})
            }
            
            # 进行辩论
            debate_result = await self.debate_system.conduct_debate(
                topic=debate_topic,
                analysis_results=validation_data
            )
            
            return debate_result
            
        except Exception as e:
            self.logger.error(f"❌ 辩论失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'topic': debate_topic
            }
    
    async def _generate_final_assessment(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成最终验证评估"""
        pattern_name = validation_result['pattern_name']
        original_confidence = validation_result['original_confidence']
        
        # 获取各项分析结果
        candlestick_analysis = validation_result.get('candlestick_analysis', {})
        agent_validation = validation_result.get('agent_validation', {})
        debate_result = validation_result.get('debate_result', {})
        
        # 计算验证分数
        validation_scores = []
        
        # 蜡烛图专家分数
        if candlestick_analysis.get('overall_assessment'):
            cs_confidence = candlestick_analysis['overall_assessment'].get('confidence_level', 0.5)
            validation_scores.append(cs_confidence)
        
        # 多智能体共识分数
        consensus_score = agent_validation.get('consensus_score', 0.5)
        validation_scores.append(consensus_score)
        
        # 辩论结果分数
        if debate_result.get('summary'):
            debate_confidence = debate_result['summary'].get('confidence', 0.5)
            validation_scores.append(debate_confidence)
        
        # 计算最终验证分数
        if validation_scores:
            final_validation_score = sum(validation_scores) / len(validation_scores)
        else:
            final_validation_score = 0.5
        
        # 确定验证结论
        if final_validation_score >= 0.7:
            validation_conclusion = 'confirmed'
            reliability = 'high'
        elif final_validation_score >= 0.5:
            validation_conclusion = 'probable'
            reliability = 'medium'
        else:
            validation_conclusion = 'questionable'
            reliability = 'low'
        
        # 生成建议
        recommendations = []
        
        if validation_conclusion == 'confirmed':
            recommendations.append(f"{pattern_name}形态得到多智能体确认，可作为交易参考")
        elif validation_conclusion == 'probable':
            recommendations.append(f"{pattern_name}形态较为可靠，建议结合其他指标确认")
        else:
            recommendations.append(f"{pattern_name}形态可靠性存疑，建议谨慎对待")
        
        # 风险提示
        risk_warnings = []
        if original_confidence < 0.6:
            risk_warnings.append("原始形态识别置信度较低")
        if consensus_score < 0.5:
            risk_warnings.append("智能体意见分歧较大")
        
        return {
            'validation_conclusion': validation_conclusion,
            'reliability_level': reliability,
            'final_validation_score': final_validation_score,
            'original_confidence': original_confidence,
            'validation_improvement': final_validation_score - original_confidence,
            'recommendations': recommendations,
            'risk_warnings': risk_warnings,
            'validation_components': {
                'candlestick_expert': candlestick_analysis.get('overall_assessment', {}).get('confidence_level', 0.5),
                'multi_agent_consensus': consensus_score,
                'debate_outcome': debate_result.get('summary', {}).get('confidence', 0.5)
            }
        }
    
    async def batch_validate_patterns(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量验证多个形态"""
        self.logger.info(f"📊 开始批量验证{len(patterns)}个形态")
        
        validation_results = []
        
        for i, pattern_info in enumerate(patterns, 1):
            self.logger.info(f"🔄 验证进度: {i}/{len(patterns)}")
            
            try:
                result = await self.validate_pattern(
                    pattern_name=pattern_info['pattern_name'],
                    symbol=pattern_info['symbol'],
                    candle_data=pattern_info['candle_data'],
                    pattern_confidence=pattern_info.get('confidence', 0.8)
                )
                validation_results.append(result)
                
            except Exception as e:
                self.logger.error(f"❌ 批量验证失败 {i}: {e}")
                validation_results.append({
                    'pattern_name': pattern_info.get('pattern_name', 'unknown'),
                    'symbol': pattern_info.get('symbol', 'unknown'),
                    'validation_status': 'failed',
                    'error': str(e)
                })
            
            # 避免API限制，添加延迟
            if i < len(patterns):
                await asyncio.sleep(1)
        
        self.logger.info(f"✅ 批量验证完成: {len(validation_results)}个结果")
        return validation_results
    
    async def get_validation_summary(self, validation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取验证结果摘要"""
        if not validation_results:
            return {'error': '没有验证结果'}
        
        summary = {
            'total_patterns': len(validation_results),
            'confirmed_patterns': 0,
            'probable_patterns': 0,
            'questionable_patterns': 0,
            'failed_validations': 0,
            'average_validation_score': 0.0,
            'top_patterns': [],
            'risk_patterns': []
        }
        
        validation_scores = []
        
        for result in validation_results:
            if result.get('validation_status') == 'completed':
                final_assessment = result.get('final_assessment', {})
                conclusion = final_assessment.get('validation_conclusion', 'unknown')
                score = final_assessment.get('final_validation_score', 0.0)
                
                validation_scores.append(score)
                
                if conclusion == 'confirmed':
                    summary['confirmed_patterns'] += 1
                elif conclusion == 'probable':
                    summary['probable_patterns'] += 1
                elif conclusion == 'questionable':
                    summary['questionable_patterns'] += 1
                
                # 收集高质量形态
                if score >= 0.8:
                    summary['top_patterns'].append({
                        'pattern': result['pattern_name'],
                        'symbol': result['symbol'],
                        'score': score
                    })
                
                # 收集风险形态
                if score < 0.4:
                    summary['risk_patterns'].append({
                        'pattern': result['pattern_name'],
                        'symbol': result['symbol'],
                        'score': score
                    })
            else:
                summary['failed_validations'] += 1
        
        # 计算平均分数
        if validation_scores:
            summary['average_validation_score'] = sum(validation_scores) / len(validation_scores)
        
        # 排序
        summary['top_patterns'].sort(key=lambda x: x['score'], reverse=True)
        summary['risk_patterns'].sort(key=lambda x: x['score'])
        
        return summary
    
    async def shutdown(self):
        """关闭验证器"""
        self.logger.info("🔄 关闭蜡烛图形态验证器...")
        
        try:
            if self.debate_system:
                await self.debate_system.shutdown()
            if self.agent_manager:
                await self.agent_manager.shutdown()
            if self.candlestick_analyst:
                await self.candlestick_analyst.shutdown()
            
            self.is_initialized = False
            self.logger.info("✅ 验证器关闭完成")
            
        except Exception as e:
            self.logger.error(f"❌ 验证器关闭失败: {e}")


# 便捷函数
async def validate_candlestick_pattern(pattern_name: str, 
                                     symbol: str, 
                                     candle_data: List[Dict[str, Any]], 
                                     confidence: float = 0.8) -> Dict[str, Any]:
    """
    便捷函数：验证单个蜡烛图形态
    
    Args:
        pattern_name: 形态名称
        symbol: 股票代码
        candle_data: 蜡烛图数据
        confidence: 原始识别置信度
        
    Returns:
        验证结果
    """
    validator = CandlestickPatternValidator()
    try:
        result = await validator.validate_pattern(pattern_name, symbol, candle_data, confidence)
        return result
    finally:
        await validator.shutdown()


async def batch_validate_patterns(patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    便捷函数：批量验证蜡烛图形态
    
    Args:
        patterns: 形态列表，每个包含pattern_name, symbol, candle_data, confidence
        
    Returns:
        验证结果列表
    """
    validator = CandlestickPatternValidator()
    try:
        results = await validator.batch_validate_patterns(patterns)
        return results
    finally:
        await validator.shutdown()
