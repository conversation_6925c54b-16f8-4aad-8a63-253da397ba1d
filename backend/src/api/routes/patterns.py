"""
形态识别路由
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

# from ...models import Candle, PatternAnalysisResult
# from ...patterns import PatternRecognizer

# 临时使用简化的模型
class Candle(BaseModel):
    timestamp: str
    open: float
    high: float
    low: float
    close: float
    volume: int

class PatternAnalysisResult(BaseModel):
    patterns: List[dict]
    confidence: float

router = APIRouter()

# 请求/响应模型
class CandleData(BaseModel):
    """蜡烛线数据模型"""
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价") 
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: float = Field(..., description="成交量")
    timestamp: datetime = Field(..., description="时间戳")

class PatternRequest(BaseModel):
    """形态识别请求模型"""
    candles: List[CandleData] = Field(..., description="蜡烛线数据列表")
    start_index: Optional[int] = Field(0, description="开始分析的索引")
    end_index: Optional[int] = Field(None, description="结束分析的索引")

class PatternResponse(BaseModel):
    """形态识别响应模型"""
    pattern_name: str
    pattern_type: str
    pattern_category: str
    signal: str
    confidence: float
    start_index: int
    end_index: int
    start_time: datetime
    end_time: datetime
    description: str
    trend_context: Optional[str] = None
    volume_confirmation: Optional[bool] = None
    key_levels: Optional[dict] = None

class AnalysisResponse(BaseModel):
    """分析结果响应模型"""
    patterns: List[PatternResponse]
    analysis_time: datetime
    total_candles: int
    time_range: List[datetime]
    summary: dict

# 创建形态识别器实例 - 简化版
class SimplePatternRecognizer:
    def analyze(self, candles):
        # 简化的形态识别
        return {
            'patterns': [
                {'name': 'doji', 'confidence': 0.8, 'position': len(candles)-1},
                {'name': 'hammer', 'confidence': 0.7, 'position': len(candles)-2}
            ],
            'confidence': 0.75
        }

recognizer = SimplePatternRecognizer()

@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_patterns(request: PatternRequest):
    """
    分析蜡烛图形态
    
    接收蜡烛线数据，返回识别到的形态
    """
    try:
        # 转换数据格式
        candles = []
        for candle_data in request.candles:
            candle = Candle(
                open=candle_data.open,
                high=candle_data.high,
                low=candle_data.low,
                close=candle_data.close,
                volume=candle_data.volume,
                timestamp=candle_data.timestamp
            )
            candles.append(candle)
        
        # 执行形态识别
        result = recognizer.identify_patterns(
            candles=candles,
            start_index=request.start_index,
            end_index=request.end_index
        )
        
        # 转换响应格式
        pattern_responses = []
        for pattern in result.patterns:
            pattern_response = PatternResponse(
                pattern_name=pattern.pattern_name.value,
                pattern_type=pattern.pattern_type.value,
                pattern_category=pattern.pattern_category.value,
                signal=pattern.signal.value,
                confidence=pattern.confidence,
                start_index=pattern.start_index,
                end_index=pattern.end_index,
                start_time=pattern.start_time,
                end_time=pattern.end_time,
                description=pattern.description,
                trend_context=pattern.trend_context,
                volume_confirmation=pattern.volume_confirmation,
                key_levels=pattern.key_levels
            )
            pattern_responses.append(pattern_response)
        
        return AnalysisResponse(
            patterns=pattern_responses,
            analysis_time=result.analysis_time,
            total_candles=result.total_candles,
            time_range=[result.time_range[0], result.time_range[1]],
            summary=result.to_dict()["summary"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"分析失败: {str(e)}")

@router.get("/patterns/list")
async def list_supported_patterns():
    """
    获取支持的形态列表
    """
    from ...models import PatternName, PatternType, PatternCategory
    
    patterns = []
    for pattern_name in PatternName:
        patterns.append({
            "name": pattern_name.value,
            "display_name": pattern_name.name,
            "description": f"形态: {pattern_name.value}"
        })
    
    return {
        "supported_patterns": patterns,
        "pattern_types": [pt.value for pt in PatternType],
        "pattern_categories": [pc.value for pc in PatternCategory],
        "total_patterns": len(patterns)
    }

@router.post("/patterns/filter")
async def filter_patterns(
    request: PatternRequest,
    min_confidence: float = Query(0.0, description="最小置信度"),
    pattern_types: Optional[List[str]] = Query(None, description="形态类型过滤"),
    signals: Optional[List[str]] = Query(None, description="信号类型过滤")
):
    """
    带过滤条件的形态识别
    """
    try:
        # 转换数据格式
        candles = []
        for candle_data in request.candles:
            candle = Candle(
                open=candle_data.open,
                high=candle_data.high,
                low=candle_data.low,
                close=candle_data.close,
                volume=candle_data.volume,
                timestamp=candle_data.timestamp
            )
            candles.append(candle)
        
        # 执行形态识别
        result = recognizer.identify_patterns(
            candles=candles,
            start_index=request.start_index,
            end_index=request.end_index
        )
        
        # 应用过滤条件
        filtered_patterns = recognizer.filter_patterns(
            patterns=result.patterns,
            min_confidence=min_confidence,
            pattern_types=pattern_types,
            signals=signals
        )
        
        # 转换响应格式
        pattern_responses = []
        for pattern in filtered_patterns:
            pattern_response = PatternResponse(
                pattern_name=pattern.pattern_name.value,
                pattern_type=pattern.pattern_type.value,
                pattern_category=pattern.pattern_category.value,
                signal=pattern.signal.value,
                confidence=pattern.confidence,
                start_index=pattern.start_index,
                end_index=pattern.end_index,
                start_time=pattern.start_time,
                end_time=pattern.end_time,
                description=pattern.description,
                trend_context=pattern.trend_context,
                volume_confirmation=pattern.volume_confirmation,
                key_levels=pattern.key_levels
            )
            pattern_responses.append(pattern_response)
        
        return {
            "patterns": pattern_responses,
            "total_patterns": len(pattern_responses),
            "filters_applied": {
                "min_confidence": min_confidence,
                "pattern_types": pattern_types,
                "signals": signals
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"过滤分析失败: {str(e)}")

@router.get("/patterns/statistics")
async def get_pattern_statistics():
    """
    获取形态统计信息
    """
    from ...models import PatternName, PatternType, PatternCategory, PatternSignal
    
    return {
        "total_supported_patterns": len(PatternName),
        "pattern_categories": {
            "single": len([p for p in PatternName if "doji" in p.value or "hammer" in p.value or "spinning" in p.value or "marubozu" in p.value]),
            "double": len([p for p in PatternName if "engulfing" in p.value or "harami" in p.value or "piercing" in p.value or "dark_cloud" in p.value or "tweezers" in p.value]),
            "triple": len([p for p in PatternName if "star" in p.value or "soldiers" in p.value or "crows" in p.value or "methods" in p.value])
        },
        "pattern_types": [pt.value for pt in PatternType],
        "signal_types": [ps.value for ps in PatternSignal],
        "categories": [pc.value for pc in PatternCategory]
    }


# 新增高级市场分析端点
class MarketAnalysisRequest(BaseModel):
    """市场分析请求模型"""
    symbol: str = Field(..., description="股票代码")
    interval: str = Field("1h", description="时间间隔")
    include_fundamentals: bool = Field(True, description="是否包含基本面分析")


@router.post("/market/comprehensive-analysis")
async def comprehensive_market_analysis(request: MarketAnalysisRequest):
    """
    综合市场分析
    结合技术分析、基本面分析和市场情绪分析
    """
    try:
        # 导入市场分析器（延迟导入避免循环依赖）
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.market_analyzer import market_analyzer

        # 执行综合分析
        analysis_result = await market_analyzer.comprehensive_analysis(
            symbol=request.symbol,
            interval=request.interval,
            include_fundamentals=request.include_fundamentals
        )

        return analysis_result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"综合市场分析失败: {str(e)}")


@router.get("/market/stock-info/{symbol}")
async def get_stock_info(symbol: str):
    """
    获取股票基本信息
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        stock_info = await market_data_manager.get_stock_info(symbol)
        return stock_info

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取股票信息失败: {str(e)}")


@router.get("/market/realtime-data/{symbol}")
async def get_realtime_data(
    symbol: str,
    interval: str = Query("1h", description="时间间隔"),
    period: str = Query("1d", description="时间周期")
):
    """
    获取实时市场数据
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        market_data = await market_data_manager.get_stock_data(symbol, interval)
        return market_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取实时数据失败: {str(e)}")


@router.post("/market/watchlist")
async def manage_watchlist(action: str, symbol: str):
    """
    管理监控列表
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        if action == "add":
            market_data_manager.add_to_watchlist(symbol)
            message = f"已添加 {symbol} 到监控列表"
        elif action == "remove":
            market_data_manager.remove_from_watchlist(symbol)
            message = f"已从监控列表移除 {symbol}"
        else:
            raise HTTPException(status_code=400, detail="无效的操作，请使用 'add' 或 'remove'")

        return {
            "message": message,
            "watchlist": market_data_manager.watchlist,
            "watchlist_size": len(market_data_manager.watchlist)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"管理监控列表失败: {str(e)}")


@router.get("/market/watchlist")
async def get_watchlist_data():
    """
    获取监控列表数据
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        watchlist_data = await market_data_manager.get_watchlist_data()

        return {
            "watchlist": market_data_manager.watchlist,
            "data": watchlist_data,
            "last_updated": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取监控列表数据失败: {str(e)}")


@router.get("/market/cache-stats")
async def get_cache_stats():
    """
    获取缓存统计信息
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        stats = market_data_manager.get_cache_stats()
        return {
            "cache_statistics": stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@router.delete("/market/cache")
async def clear_cache():
    """
    清空市场数据缓存
    """
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))

        from market_data.yahoo_finance_provider import market_data_manager

        market_data_manager.clear_cache()

        return {
            "message": "缓存已清空",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")
