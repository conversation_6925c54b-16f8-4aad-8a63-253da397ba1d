"""
健康检查路由
"""

from fastapi import APIRouter
from datetime import datetime
import sys
import os

router = APIRouter()

@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "蜡烛图形态识别系统",
        "version": "1.0.0"
    }

@router.get("/status")
async def system_status():
    """系统状态检查"""
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "python_version": sys.version,
        "platform": sys.platform,
        "working_directory": os.getcwd(),
        "memory_usage": "N/A",  # 可以添加内存使用情况
        "uptime": "N/A"  # 可以添加运行时间
    }
