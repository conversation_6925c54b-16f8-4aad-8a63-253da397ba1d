"""
蜡烛图形态识别工具函数
基于《日本蜡烛图技术》一书的技术分析方法
"""

from typing import List, Tuple, Optional
from ..models import Candle, CandleSequence


class PatternUtils:
    """形态识别工具类"""
    
    @staticmethod
    def is_gap_up(candle1: Candle, candle2: Candle, min_gap_percent: float = 0.1) -> bool:
        """
        判断是否存在向上跳空
        
        Args:
            candle1: 前一根蜡烛线
            candle2: 后一根蜡烛线  
            min_gap_percent: 最小跳空百分比
            
        Returns:
            bool: 是否存在向上跳空
        """
        gap = candle2.low - candle1.high
        if gap <= 0:
            return False
        
        gap_percent = gap / candle1.high
        return gap_percent >= min_gap_percent
    
    @staticmethod
    def is_gap_down(candle1: Candle, candle2: Candle, min_gap_percent: float = 0.1) -> bool:
        """
        判断是否存在向下跳空
        
        Args:
            candle1: 前一根蜡烛线
            candle2: 后一根蜡烛线
            min_gap_percent: 最小跳空百分比
            
        Returns:
            bool: 是否存在向下跳空
        """
        gap = candle1.low - candle2.high
        if gap <= 0:
            return False
            
        gap_percent = gap / candle1.low
        return gap_percent >= min_gap_percent
    
    @staticmethod
    def is_engulfing(candle1: Candle, candle2: Candle) -> bool:
        """
        判断是否为吞没形态
        第二根蜡烛线的实体完全包含第一根蜡烛线的实体
        
        Args:
            candle1: 第一根蜡烛线
            candle2: 第二根蜡烛线
            
        Returns:
            bool: 是否为吞没形态
        """
        # 颜色必须相反
        if candle1.color == candle2.color:
            return False
        
        # 第二根蜡烛线的实体必须完全包含第一根
        body1_top = max(candle1.open, candle1.close)
        body1_bottom = min(candle1.open, candle1.close)
        body2_top = max(candle2.open, candle2.close)
        body2_bottom = min(candle2.open, candle2.close)
        
        return body2_top > body1_top and body2_bottom < body1_bottom
    
    @staticmethod
    def is_harami(candle1: Candle, candle2: Candle) -> bool:
        """
        判断是否为孕线形态
        第二根蜡烛线的实体被第一根蜡烛线的实体完全包含
        
        Args:
            candle1: 第一根蜡烛线（母线）
            candle2: 第二根蜡烛线（子线）
            
        Returns:
            bool: 是否为孕线形态
        """
        # 第一根蜡烛线应该有较大的实体
        if candle1.is_small_body():
            return False
        
        # 第二根蜡烛线的实体必须被第一根完全包含
        body1_top = max(candle1.open, candle1.close)
        body1_bottom = min(candle1.open, candle1.close)
        body2_top = max(candle2.open, candle2.close)
        body2_bottom = min(candle2.open, candle2.close)
        
        return body1_top > body2_top and body1_bottom < body2_bottom
    
    @staticmethod
    def is_star_pattern(candle1: Candle, candle2: Candle, gap_threshold: float = 0.1) -> bool:
        """
        判断第二根蜡烛线是否为星线
        星线的特征：小实体，且与前一根蜡烛线之间有跳空
        
        Args:
            candle1: 第一根蜡烛线
            candle2: 第二根蜡烛线（星线）
            gap_threshold: 跳空阈值
            
        Returns:
            bool: 是否为星线形态
        """
        # 第二根蜡烛线必须是小实体
        if not candle2.is_small_body():
            return False
        
        # 检查是否有跳空
        if candle1.is_bullish:
            # 前一根为阳线，检查向上跳空
            return PatternUtils.is_gap_up(candle1, candle2, gap_threshold)
        else:
            # 前一根为阴线，检查向下跳空
            return PatternUtils.is_gap_down(candle1, candle2, gap_threshold)
    
    @staticmethod
    def calculate_penetration_ratio(candle1: Candle, candle2: Candle) -> float:
        """
        计算第二根蜡烛线对第一根蜡烛线实体的渗透比例
        用于判断刺透形态和乌云盖顶形态
        
        Args:
            candle1: 第一根蜡烛线
            candle2: 第二根蜡烛线
            
        Returns:
            float: 渗透比例 (0.0 - 1.0)
        """
        body1_size = candle1.body_size
        if body1_size == 0:
            return 0.0
        
        # 计算第二根蜡烛线渗透到第一根实体内的部分
        body1_top = max(candle1.open, candle1.close)
        body1_bottom = min(candle1.open, candle1.close)
        
        if candle2.is_bullish:
            # 看涨蜡烛线，计算向上渗透
            penetration = min(candle2.close, body1_top) - max(candle2.open, body1_bottom)
        else:
            # 看跌蜡烛线，计算向下渗透
            penetration = min(candle2.open, body1_top) - max(candle2.close, body1_bottom)
        
        return max(0.0, penetration / body1_size)
    
    @staticmethod
    def is_similar_highs(candle1: Candle, candle2: Candle, tolerance_percent: float = 0.5) -> bool:
        """
        判断两根蜡烛线是否有相似的最高价（平头形态）
        
        Args:
            candle1: 第一根蜡烛线
            candle2: 第二根蜡烛线
            tolerance_percent: 容差百分比
            
        Returns:
            bool: 是否有相似的最高价
        """
        diff = abs(candle1.high - candle2.high)
        avg_high = (candle1.high + candle2.high) / 2
        return (diff / avg_high) <= (tolerance_percent / 100)
    
    @staticmethod
    def is_similar_lows(candle1: Candle, candle2: Candle, tolerance_percent: float = 0.5) -> bool:
        """
        判断两根蜡烛线是否有相似的最低价（平头形态）
        
        Args:
            candle1: 第一根蜡烛线
            candle2: 第二根蜡烛线
            tolerance_percent: 容差百分比
            
        Returns:
            bool: 是否有相似的最低价
        """
        diff = abs(candle1.low - candle2.low)
        avg_low = (candle1.low + candle2.low) / 2
        return (diff / avg_low) <= (tolerance_percent / 100)
    
    @staticmethod
    def get_trend_direction(candles: List[Candle], lookback: int = 5) -> str:
        """
        简单的趋势判断
        
        Args:
            candles: 蜡烛线列表
            lookback: 回看周期
            
        Returns:
            str: 趋势方向 ('uptrend', 'downtrend', 'sideways')
        """
        if len(candles) < lookback + 1:
            return 'sideways'
        
        recent_candles = candles[-lookback-1:]
        
        # 计算价格变化
        start_price = (recent_candles[0].high + recent_candles[0].low) / 2
        end_price = (recent_candles[-1].high + recent_candles[-1].low) / 2
        
        price_change_percent = (end_price - start_price) / start_price * 100
        
        if price_change_percent > 2.0:
            return 'uptrend'
        elif price_change_percent < -2.0:
            return 'downtrend'
        else:
            return 'sideways'
    
    @staticmethod
    def calculate_average_body_size(candles: List[Candle], periods: int = 10) -> float:
        """
        计算平均实体大小
        
        Args:
            candles: 蜡烛线列表
            periods: 计算周期
            
        Returns:
            float: 平均实体大小
        """
        if not candles:
            return 0.0
        
        recent_candles = candles[-periods:] if len(candles) >= periods else candles
        total_body_size = sum(candle.body_size for candle in recent_candles)
        
        return total_body_size / len(recent_candles)
    
    @staticmethod
    def calculate_average_range(candles: List[Candle], periods: int = 10) -> float:
        """
        计算平均价格区间
        
        Args:
            candles: 蜡烛线列表
            periods: 计算周期
            
        Returns:
            float: 平均价格区间
        """
        if not candles:
            return 0.0
        
        recent_candles = candles[-periods:] if len(candles) >= periods else candles
        total_range = sum(candle.total_range for candle in recent_candles)
        
        return total_range / len(recent_candles)
    
    @staticmethod
    def is_long_candle(candle: Candle, avg_body_size: float, threshold: float = 1.5) -> bool:
        """
        判断是否为长蜡烛线
        
        Args:
            candle: 蜡烛线
            avg_body_size: 平均实体大小
            threshold: 阈值倍数
            
        Returns:
            bool: 是否为长蜡烛线
        """
        return candle.body_size > avg_body_size * threshold
    
    @staticmethod
    def validate_volume_confirmation(candles: List[Candle], pattern_start: int, pattern_end: int) -> bool:
        """
        验证成交量确认
        形态期间的成交量应该高于平均水平
        
        Args:
            candles: 蜡烛线列表
            pattern_start: 形态开始索引
            pattern_end: 形态结束索引
            
        Returns:
            bool: 是否有成交量确认
        """
        if pattern_start < 10:  # 需要足够的历史数据
            return False
        
        # 计算形态前的平均成交量
        pre_pattern_candles = candles[pattern_start-10:pattern_start]
        avg_volume = sum(c.volume for c in pre_pattern_candles) / len(pre_pattern_candles)
        
        # 计算形态期间的平均成交量
        pattern_candles = candles[pattern_start:pattern_end+1]
        pattern_avg_volume = sum(c.volume for c in pattern_candles) / len(pattern_candles)
        
        # 形态期间成交量应该高于历史平均
        return pattern_avg_volume > avg_volume * 1.2
