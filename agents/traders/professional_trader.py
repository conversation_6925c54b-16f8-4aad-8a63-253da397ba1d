"""
专业交易员智能体
综合技术分析和研究观点做出交易决策
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from agents.base.agent import AnalystAgent, AgentRole, AnalysisResult, Message, MessageType
from llm_integration.llm_manager import llm_manager
from llm_integration.prompts.candlestick_prompts import TRADER_DECISION_PROMPT


class ProfessionalTrader(AnalystAgent):
    """专业交易员智能体"""
    
    def __init__(self, agent_id: str, llm_config: Optional[Dict[str, Any]] = None):
        super().__init__(agent_id, AgentRole.TRADER, llm_config)
        self.trading_style = "balanced"  # conservative, balanced, aggressive
        self.max_risk_per_trade = 0.02  # 单笔交易最大风险2%
        self.pending_decisions = {}  # 待决策的交易
        
    def _get_specialization(self) -> str:
        """获取专业化领域"""
        return "professional_trading"

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
        你是一位经验丰富的专业交易员，拥有15年以上的市场交易经验。
        
        你的交易哲学：
        - 严格的风险控制是第一要务
        - 基于数据和分析做决策，不凭感觉
        - 保持纪律性，严格执行交易计划
        - 持续学习和适应市场变化
        
        你的交易技能：
        - 精通技术分析和基本面分析
        - 擅长风险管理和仓位控制
        - 能够快速适应不同市场环境
        - 具备丰富的实战经验和市场直觉
        
        你的决策原则：
        1. 风险收益比至少1:2
        2. 单笔交易风险不超过总资金的2%
        3. 必须有明确的入场和出场策略
        4. 综合考虑技术面和基本面因素
        5. 保持客观，避免情绪化决策
        
        请始终保持专业、理性的交易态度。
        """
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易决策分析"""
        try:
            # 提取输入信息
            technical_analysis = market_data.get('technical_analysis', {})
            bullish_opinion = market_data.get('bullish_opinion', {})
            bearish_opinion = market_data.get('bearish_opinion', {})
            symbol = market_data.get('symbol', 'Unknown')
            
            # 构建决策提示词
            prompt = TRADER_DECISION_PROMPT.format(
                technical_analysis=json.dumps(technical_analysis, indent=2, ensure_ascii=False),
                bullish_opinion=json.dumps(bullish_opinion, indent=2, ensure_ascii=False),
                bearish_opinion=json.dumps(bearish_opinion, indent=2, ensure_ascii=False)
            )
            
            # 调用LLM进行决策
            response = await llm_manager.generate_response(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                temperature=0.1,  # 低温度确保决策一致性
                max_tokens=2000
            )
            
            # 解析响应
            decision_result = self._parse_llm_response(response.content)
            
            # 验证和调整决策
            decision_result = self._validate_trading_decision(decision_result)
            
            # 添加元数据
            decision_result.update({
                'agent_id': self.agent_id,
                'agent_role': self.role.value,
                'trading_style': self.trading_style,
                'decision_timestamp': datetime.now().isoformat(),
                'llm_tokens_used': response.tokens_used,
                'llm_cost': response.cost
            })
            
            self.logger.info(f"Trading decision completed for {symbol}: {decision_result.get('decision')}")
            return decision_result
            
        except Exception as e:
            self.logger.error(f"Trading decision analysis failed: {e}")
            return self._get_fallback_decision(market_data)
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，创建默认决策
                return self._get_default_decision()
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse LLM response as JSON: {e}")
            return self._get_default_decision()
    
    def _validate_trading_decision(self, decision: Dict[str, Any]) -> Dict[str, Any]:
        """验证和调整交易决策"""
        # 确保决策字段完整
        required_fields = ['decision', 'confidence', 'reasoning']
        for field in required_fields:
            if field not in decision:
                decision[field] = self._get_default_value(field)
        
        # 验证决策类型
        if decision['decision'] not in ['BUY', 'SELL', 'HOLD']:
            decision['decision'] = 'HOLD'
            decision['reasoning'] = '决策不明确，选择观望'
        
        # 验证置信度
        if not isinstance(decision.get('confidence'), (int, float)) or not (0 <= decision['confidence'] <= 1):
            decision['confidence'] = 0.5
        
        # 确保风险管理字段
        if 'risk_management' not in decision:
            decision['risk_management'] = {
                'max_drawdown': 0.05,
                'risk_reward_ratio': 2.0,
                'contingency_plan': '严格执行止损'
            }
        
        # 确保仓位管理字段
        if 'position_sizing' not in decision:
            decision['position_sizing'] = {
                'recommended_size': '小仓位',
                'max_risk': self.max_risk_per_trade,
                'rationale': '保守的仓位管理'
            }
        
        return decision
    
    def _get_default_value(self, field: str) -> Any:
        """获取字段默认值"""
        defaults = {
            'decision': 'HOLD',
            'confidence': 0.5,
            'reasoning': '基于当前信息的综合判断',
            'entry_strategy': {'price': '市价', 'timing': '适当时机', 'conditions': ['确认信号']},
            'exit_strategy': {'stop_loss': '2%', 'take_profit': '4%', 'trailing_stop': False}
        }
        return defaults.get(field, '待确定')
    
    def _get_default_decision(self) -> Dict[str, Any]:
        """获取默认交易决策"""
        return {
            'decision': 'HOLD',
            'confidence': 0.5,
            'reasoning': '当前信息不足以做出明确的交易决策，建议继续观望',
            'entry_strategy': {
                'price': '等待合适价位',
                'timing': '确认信号后',
                'conditions': ['技术确认', '风险可控']
            },
            'exit_strategy': {
                'stop_loss': '2%',
                'take_profit': '4%',
                'trailing_stop': False
            },
            'position_sizing': {
                'recommended_size': '小仓位',
                'max_risk': self.max_risk_per_trade,
                'rationale': '保守的风险控制'
            },
            'risk_management': {
                'max_drawdown': 0.05,
                'risk_reward_ratio': 2.0,
                'contingency_plan': '严格执行止损计划'
            }
        }
    
    def _get_fallback_decision(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取备用交易决策"""
        symbol = market_data.get('symbol', 'Unknown')
        
        # 基于简单规则的决策
        bullish_opinion = market_data.get('bullish_opinion', {})
        bearish_opinion = market_data.get('bearish_opinion', {})
        
        bullish_confidence = bullish_opinion.get('confidence', 0.5)
        bearish_confidence = bearish_opinion.get('confidence', 0.5)
        
        if bullish_confidence > bearish_confidence + 0.2:
            decision = 'BUY'
            confidence = bullish_confidence * 0.8  # 降低置信度
        elif bearish_confidence > bullish_confidence + 0.2:
            decision = 'SELL'
            confidence = bearish_confidence * 0.8
        else:
            decision = 'HOLD'
            confidence = 0.5
        
        fallback_decision = self._get_default_decision()
        fallback_decision.update({
            'decision': decision,
            'confidence': confidence,
            'reasoning': f'基于研究员观点的简化决策：看涨置信度{bullish_confidence:.2f}，看跌置信度{bearish_confidence:.2f}',
            'agent_id': self.agent_id,
            'agent_role': self.role.value,
            'trading_style': self.trading_style,
            'decision_timestamp': datetime.now().isoformat(),
            'fallback_used': True
        })
        
        return fallback_decision
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理消息"""
        if message.message_type == MessageType.RESEARCH_OPINION:
            # 收集研究观点，等待所有观点到齐后做决策
            analysis_id = message.content.get('analysis_id', 'unknown')
            agent_role = message.content.get('agent_role', 'unknown')
            
            if analysis_id not in self.pending_decisions:
                self.pending_decisions[analysis_id] = {
                    'technical_analysis': {},
                    'bullish_opinion': {},
                    'bearish_opinion': {},
                    'received_count': 0,
                    'expected_count': 2  # 期望收到看涨和看跌两个观点
                }
            
            # 存储观点
            if agent_role == 'bullish_researcher':
                self.pending_decisions[analysis_id]['bullish_opinion'] = message.content
            elif agent_role == 'bearish_researcher':
                self.pending_decisions[analysis_id]['bearish_opinion'] = message.content
            
            self.pending_decisions[analysis_id]['received_count'] += 1
            
            # 如果收到所有期望的观点，进行决策
            if self.pending_decisions[analysis_id]['received_count'] >= self.pending_decisions[analysis_id]['expected_count']:
                decision_result = await self._perform_analysis(self.pending_decisions[analysis_id])
                
                # 清理待决策数据
                del self.pending_decisions[analysis_id]
                
                # 创建交易信号消息
                response_message = Message(
                    message_type=MessageType.TRADING_SIGNAL,
                    from_agent=self.agent_id,
                    to_agents=['risk_manager_001'],  # 发送给风险管理
                    content=decision_result,
                    timestamp=datetime.now(),
                    message_id=f"trading_signal_{datetime.now().timestamp()}",
                    priority=1  # 高优先级
                )
                
                return response_message
        
        elif message.message_type == MessageType.DECISION_REQUEST:
            # 处理工作流决策请求
            workflow_id = message.content.get('workflow_id')
            stage = message.content.get('stage')
            
            if stage == 'trading_decision':
                decision_result = await self._perform_analysis(message.content)
                
                # 通知工作流完成
                if self.communication_hub:
                    await self.communication_hub.complete_workflow_stage(
                        workflow_id=workflow_id,
                        stage_name=stage,
                        result=decision_result
                    )
        
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        base_status = super().get_status()
        base_status.update({
            'trading_style': self.trading_style,
            'max_risk_per_trade': self.max_risk_per_trade,
            'pending_decisions': len(self.pending_decisions),
            'specialization': '交易决策制定'
        })
        return base_status
