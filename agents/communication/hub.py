"""
智能体通信中心
负责智能体之间的消息路由、协调和工作流管理
"""

import asyncio
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import json
import logging
from collections import defaultdict, deque
from dataclasses import asdict

from ..base.agent import BaseAgent, Message, MessageType, AgentRole


class CommunicationHub:
    """智能体通信中心"""
    
    def __init__(self, max_message_history: int = 1000):
        self.agents: Dict[str, BaseAgent] = {}
        self.message_queue = asyncio.Queue()
        self.message_history = deque(maxlen=max_message_history)
        self.active_workflows: Dict[str, Dict] = {}
        self.logger = logging.getLogger("CommunicationHub")
        
        # 消息路由规则
        self.routing_rules = self._setup_routing_rules()
        
        # 工作流模板
        self.workflow_templates = self._setup_workflow_templates()
        
        # 统计信息
        self.stats = {
            'messages_sent': 0,
            'messages_processed': 0,
            'workflows_completed': 0,
            'active_agents': 0
        }
        
        self.is_running = False
        self.message_processor_task = None
    
    def _setup_routing_rules(self) -> Dict[MessageType, List[AgentRole]]:
        """设置消息路由规则"""
        return {
            MessageType.ANALYSIS_REPORT: [
                AgentRole.BULLISH_RESEARCHER,
                AgentRole.BEARISH_RESEARCHER,
                AgentRole.TRADER
            ],
            MessageType.RESEARCH_OPINION: [
                AgentRole.TRADER,
                AgentRole.RISK_MANAGER
            ],
            MessageType.TRADING_SIGNAL: [
                AgentRole.RISK_MANAGER,
                AgentRole.PORTFOLIO_MANAGER
            ],
            MessageType.RISK_ASSESSMENT: [
                AgentRole.PORTFOLIO_MANAGER,
                AgentRole.TRADER
            ]
        }
    
    def _setup_workflow_templates(self) -> Dict[str, Dict]:
        """设置工作流模板"""
        return {
            'market_analysis': {
                'name': '市场分析工作流',
                'steps': [
                    {
                        'stage': 'analysis',
                        'agents': [AgentRole.TECHNICAL_ANALYST, AgentRole.FUNDAMENTAL_ANALYST],
                        'timeout': 300,  # 5分钟
                        'required': True
                    },
                    {
                        'stage': 'research',
                        'agents': [AgentRole.BULLISH_RESEARCHER, AgentRole.BEARISH_RESEARCHER],
                        'timeout': 600,  # 10分钟
                        'required': True,
                        'depends_on': ['analysis']
                    },
                    {
                        'stage': 'debate',
                        'agents': [AgentRole.BULLISH_RESEARCHER, AgentRole.BEARISH_RESEARCHER],
                        'timeout': 900,  # 15分钟
                        'required': False,
                        'depends_on': ['research']
                    },
                    {
                        'stage': 'trading_decision',
                        'agents': [AgentRole.TRADER],
                        'timeout': 300,  # 5分钟
                        'required': True,
                        'depends_on': ['research']
                    },
                    {
                        'stage': 'risk_management',
                        'agents': [AgentRole.RISK_MANAGER],
                        'timeout': 300,  # 5分钟
                        'required': True,
                        'depends_on': ['trading_decision']
                    }
                ]
            }
        }
    
    async def start(self):
        """启动通信中心"""
        if self.is_running:
            return
        
        self.is_running = True
        self.message_processor_task = asyncio.create_task(self._process_messages())
        self.logger.info("Communication hub started")
    
    async def stop(self):
        """停止通信中心"""
        self.is_running = False
        if self.message_processor_task:
            self.message_processor_task.cancel()
            try:
                await self.message_processor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("Communication hub stopped")
    
    def register_agent(self, agent: BaseAgent):
        """注册智能体"""
        self.agents[agent.agent_id] = agent
        agent.communication_hub = self
        self.stats['active_agents'] = len(self.agents)
        self.logger.info(f"Agent {agent.agent_id} registered")
    
    def unregister_agent(self, agent_id: str):
        """注销智能体"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            self.stats['active_agents'] = len(self.agents)
            self.logger.info(f"Agent {agent_id} unregistered")
    
    async def send_message(self, message: Message) -> bool:
        """发送消息"""
        try:
            # 验证消息
            if not self._validate_message(message):
                return False
            
            # 添加到队列
            await self.message_queue.put(message)
            self.stats['messages_sent'] += 1
            
            self.logger.debug(f"Message {message.message_id} queued for delivery")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            return False
    
    async def _process_messages(self):
        """处理消息队列"""
        while self.is_running:
            try:
                # 等待消息，设置超时避免阻塞
                message = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )
                
                await self._deliver_message(message)
                self.stats['messages_processed'] += 1
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
    
    async def _deliver_message(self, message: Message):
        """投递消息给目标智能体"""
        # 记录消息历史
        self.message_history.append({
            'timestamp': message.timestamp,
            'from': message.from_agent,
            'to': message.to_agents,
            'type': message.message_type.value,
            'message_id': message.message_id
        })
        
        # 投递给指定的智能体
        delivery_tasks = []
        for agent_id in message.to_agents:
            if agent_id in self.agents:
                task = asyncio.create_task(
                    self._deliver_to_agent(agent_id, message)
                )
                delivery_tasks.append(task)
            else:
                self.logger.warning(f"Agent {agent_id} not found for message delivery")
        
        # 等待所有投递完成
        if delivery_tasks:
            await asyncio.gather(*delivery_tasks, return_exceptions=True)
    
    async def _deliver_to_agent(self, agent_id: str, message: Message):
        """投递消息给特定智能体"""
        try:
            agent = self.agents[agent_id]
            response = await agent.process_message(message)
            
            # 如果有响应，继续路由
            if response:
                await self.send_message(response)
                
        except Exception as e:
            self.logger.error(f"Failed to deliver message to {agent_id}: {e}")
    
    def _validate_message(self, message: Message) -> bool:
        """验证消息格式"""
        if not message.from_agent:
            self.logger.error("Message missing from_agent")
            return False
        
        if not message.to_agents:
            self.logger.error("Message missing to_agents")
            return False
        
        if not message.content:
            self.logger.error("Message missing content")
            return False
        
        return True
    
    async def start_workflow(
        self,
        workflow_type: str,
        input_data: Dict[str, Any],
        workflow_id: Optional[str] = None
    ) -> str:
        """启动工作流"""
        if workflow_type not in self.workflow_templates:
            raise ValueError(f"Unknown workflow type: {workflow_type}")
        
        if not workflow_id:
            workflow_id = f"{workflow_type}_{datetime.now().timestamp()}"
        
        template = self.workflow_templates[workflow_type]
        
        workflow = {
            'id': workflow_id,
            'type': workflow_type,
            'status': 'RUNNING',
            'start_time': datetime.now(),
            'input_data': input_data,
            'template': template,
            'current_stage': 0,
            'stage_results': {},
            'completed_stages': set(),
            'failed_stages': set()
        }
        
        self.active_workflows[workflow_id] = workflow
        
        # 启动第一个阶段
        await self._execute_workflow_stage(workflow_id, 0)
        
        self.logger.info(f"Workflow {workflow_id} started")
        return workflow_id
    
    async def _execute_workflow_stage(self, workflow_id: str, stage_index: int):
        """执行工作流阶段"""
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        template = workflow['template']
        
        if stage_index >= len(template['steps']):
            # 工作流完成
            await self._complete_workflow(workflow_id)
            return
        
        stage = template['steps'][stage_index]
        
        # 检查依赖
        if 'depends_on' in stage:
            for dep_stage in stage['depends_on']:
                if dep_stage not in workflow['completed_stages']:
                    self.logger.warning(f"Stage {stage['stage']} depends on incomplete stage {dep_stage}")
                    return
        
        # 执行阶段
        self.logger.info(f"Executing workflow {workflow_id} stage: {stage['stage']}")
        
        # 向相关智能体发送任务
        for agent_role in stage['agents']:
            agent_id = self._find_agent_by_role(agent_role)
            if agent_id:
                message = Message(
                    message_type=MessageType.DECISION_REQUEST,
                    from_agent="workflow_manager",
                    to_agents=[agent_id],
                    content={
                        'workflow_id': workflow_id,
                        'stage': stage['stage'],
                        'input_data': workflow['input_data'],
                        'stage_results': workflow['stage_results']
                    },
                    timestamp=datetime.now(),
                    message_id=f"workflow_{workflow_id}_{stage['stage']}_{agent_id}",
                    priority=3
                )
                await self.send_message(message)
        
        # 设置超时
        asyncio.create_task(
            self._handle_stage_timeout(workflow_id, stage_index, stage['timeout'])
        )
    
    async def _handle_stage_timeout(self, workflow_id: str, stage_index: int, timeout: int):
        """处理阶段超时"""
        await asyncio.sleep(timeout)
        
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        stage = workflow['template']['steps'][stage_index]
        
        if stage['stage'] not in workflow['completed_stages']:
            self.logger.warning(f"Workflow {workflow_id} stage {stage['stage']} timed out")
            
            if stage['required']:
                workflow['status'] = 'FAILED'
                workflow['failed_stages'].add(stage['stage'])
            else:
                # 非必需阶段，继续下一阶段
                await self._execute_workflow_stage(workflow_id, stage_index + 1)
    
    async def complete_workflow_stage(
        self,
        workflow_id: str,
        stage_name: str,
        result: Dict[str, Any]
    ):
        """完成工作流阶段"""
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        workflow['stage_results'][stage_name] = result
        workflow['completed_stages'].add(stage_name)
        
        self.logger.info(f"Workflow {workflow_id} stage {stage_name} completed")
        
        # 查找下一个可执行的阶段
        template = workflow['template']
        for i, stage in enumerate(template['steps']):
            if stage['stage'] not in workflow['completed_stages']:
                # 检查依赖是否满足
                if 'depends_on' in stage:
                    deps_satisfied = all(
                        dep in workflow['completed_stages']
                        for dep in stage['depends_on']
                    )
                    if deps_satisfied:
                        await self._execute_workflow_stage(workflow_id, i)
                        break
                else:
                    await self._execute_workflow_stage(workflow_id, i)
                    break
    
    async def _complete_workflow(self, workflow_id: str):
        """完成工作流"""
        if workflow_id not in self.active_workflows:
            return
        
        workflow = self.active_workflows[workflow_id]
        workflow['status'] = 'COMPLETED'
        workflow['end_time'] = datetime.now()
        
        self.stats['workflows_completed'] += 1
        self.logger.info(f"Workflow {workflow_id} completed")
        
        # 可以在这里添加工作流完成的回调
    
    def _find_agent_by_role(self, role: AgentRole) -> Optional[str]:
        """根据角色查找智能体"""
        for agent_id, agent in self.agents.items():
            if agent.role == role:
                return agent_id
        return None
    
    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流状态"""
        if workflow_id not in self.active_workflows:
            return None
        
        workflow = self.active_workflows[workflow_id]
        return {
            'id': workflow['id'],
            'type': workflow['type'],
            'status': workflow['status'],
            'start_time': workflow['start_time'].isoformat(),
            'completed_stages': list(workflow['completed_stages']),
            'failed_stages': list(workflow['failed_stages']),
            'progress': len(workflow['completed_stages']) / len(workflow['template']['steps'])
        }
    
    def get_agent_status(self) -> Dict[str, Any]:
        """获取所有智能体状态"""
        agent_statuses = {}
        for agent_id, agent in self.agents.items():
            agent_statuses[agent_id] = agent.get_status()
        return agent_statuses
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """获取通信统计"""
        return {
            **self.stats,
            'message_queue_size': self.message_queue.qsize(),
            'active_workflows': len(self.active_workflows),
            'message_history_size': len(self.message_history)
        }
    
    def get_recent_messages(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的消息历史"""
        return list(self.message_history)[-limit:]
