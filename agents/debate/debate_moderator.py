"""
辩论主持人智能体
负责引导和管理智能体间的辩论
"""

import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from agents.base.agent import AnalystAgent, AgentRole, Message, MessageType
from llm_integration.llm_manager import llm_manager
from llm_integration.prompts.candlestick_prompts import DEBATE_MODERATOR_PROMPT


@dataclass
class DebateRound:
    """辩论轮次"""
    round_number: int
    topic: str
    bullish_argument: str
    bearish_argument: str
    moderator_questions: List[str]
    timestamp: datetime
    duration: float = 0.0


@dataclass
class DebateSession:
    """辩论会话"""
    session_id: str
    topic: str
    participants: List[str]
    rounds: List[DebateRound]
    start_time: datetime
    end_time: Optional[datetime] = None
    status: str = "active"  # active, completed, cancelled
    consensus_reached: bool = False
    final_decision: Optional[Dict[str, Any]] = None


class DebateModerator(AnalystAgent):
    """辩论主持人智能体"""
    
    def __init__(self, agent_id: str, llm_config: Optional[Dict[str, Any]] = None):
        super().__init__(agent_id, AgentRole.MODERATOR, llm_config)
        self.active_debates: Dict[str, DebateSession] = {}
        self.max_rounds = 3  # 最大辩论轮数
        self.round_timeout = 300  # 每轮超时时间（秒）
        
    def _get_specialization(self) -> str:
        """获取专业化领域"""
        return "debate_moderation"

    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行分析（主持人不直接分析市场，而是分析辩论）"""
        return {
            'role': 'moderator',
            'analysis_type': 'debate_moderation',
            'timestamp': datetime.now().isoformat()
        }
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
        你是一位专业的投资决策辩论主持人，拥有丰富的金融市场经验和辩论管理技能。
        
        你的职责：
        - 确保辩论聚焦于关键投资问题
        - 引导双方进行建设性讨论
        - 提出深入的追问和质疑
        - 总结辩论要点和分歧
        - 促进共识达成或明确分歧点
        
        你的辩论管理原则：
        1. 保持中立，不偏向任何一方
        2. 确保辩论基于事实和数据
        3. 引导讨论深入核心问题
        4. 控制辩论节奏和时间
        5. 总结关键观点和结论
        
        你的提问技巧：
        - 针对薄弱环节进行质疑
        - 要求提供具体证据支持
        - 探讨假设条件和风险因素
        - 比较不同情景下的结果
        - 寻找观点的共同点和分歧点
        
        请始终保持专业、客观、建设性的态度。
        """
    
    async def start_debate(
        self,
        topic: str,
        participants: List[str],
        initial_data: Dict[str, Any]
    ) -> str:
        """启动辩论会话"""
        session_id = f"debate_{datetime.now().timestamp()}"
        
        debate_session = DebateSession(
            session_id=session_id,
            topic=topic,
            participants=participants,
            rounds=[],
            start_time=datetime.now()
        )
        
        self.active_debates[session_id] = debate_session
        
        self.logger.info(f"Started debate session {session_id}: {topic}")
        
        # 发送辩论开始通知
        if self.communication_hub:
            start_message = Message(
                message_type=MessageType.DEBATE_START,
                from_agent=self.agent_id,
                to_agents=participants,
                content={
                    'session_id': session_id,
                    'topic': topic,
                    'initial_data': initial_data,
                    'max_rounds': self.max_rounds
                },
                timestamp=datetime.now(),
                message_id=f"debate_start_{session_id}",
                priority=1
            )
            
            await self.communication_hub.send_message(start_message)
        
        return session_id
    
    async def moderate_round(
        self,
        session_id: str,
        round_number: int,
        bullish_argument: str,
        bearish_argument: str
    ) -> Dict[str, Any]:
        """主持辩论轮次"""
        if session_id not in self.active_debates:
            raise ValueError(f"Debate session {session_id} not found")
        
        session = self.active_debates[session_id]
        start_time = datetime.now()
        
        try:
            # 构建辩论分析提示词
            prompt = DEBATE_MODERATOR_PROMPT.format(
                debate_topic=session.topic,
                bullish_view=bullish_argument,
                bearish_view=bearish_argument
            )
            
            # 调用LLM进行辩论分析
            response = await llm_manager.generate_response(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                temperature=0.3,  # 适中的创造性
                max_tokens=2000
            )
            
            # 解析辩论总结
            moderation_result = self._parse_moderation_response(response.content)
            
            # 创建辩论轮次记录
            debate_round = DebateRound(
                round_number=round_number,
                topic=session.topic,
                bullish_argument=bullish_argument,
                bearish_argument=bearish_argument,
                moderator_questions=moderation_result.get('follow_up_questions', []),
                timestamp=start_time,
                duration=(datetime.now() - start_time).total_seconds()
            )
            
            session.rounds.append(debate_round)
            
            # 添加元数据
            moderation_result.update({
                'session_id': session_id,
                'round_number': round_number,
                'moderator_id': self.agent_id,
                'timestamp': datetime.now().isoformat(),
                'llm_tokens_used': response.tokens_used,
                'llm_cost': response.cost
            })
            
            self.logger.info(f"Moderated round {round_number} for session {session_id}")
            
            # 检查是否应该结束辩论
            if self._should_end_debate(session, moderation_result):
                await self._end_debate(session_id, moderation_result)
            
            return moderation_result
            
        except Exception as e:
            self.logger.error(f"Error moderating debate round: {e}")
            return self._get_fallback_moderation(session_id, round_number)
    
    def _parse_moderation_response(self, response_content: str) -> Dict[str, Any]:
        """解析主持人响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，创建结构化响应
                return {
                    'debate_summary': response_content[:200] + '...' if len(response_content) > 200 else response_content,
                    'key_disagreements': ['观点存在分歧'],
                    'consensus_points': ['需要进一步讨论'],
                    'unresolved_issues': ['待解决的问题'],
                    'follow_up_questions': ['请提供更多证据支持'],
                    'recommendation': {
                        'suggested_approach': '继续深入讨论',
                        'confidence_level': 0.5,
                        'next_steps': ['收集更多数据']
                    }
                }
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse moderation response as JSON: {e}")
            return self._get_default_moderation()
    
    def _get_default_moderation(self) -> Dict[str, Any]:
        """获取默认主持结果"""
        return {
            'debate_summary': '辩论双方提出了不同观点，需要进一步分析',
            'key_disagreements': [
                '对市场趋势的判断存在分歧',
                '对风险评估的重视程度不同'
            ],
            'consensus_points': [
                '都认为需要基于数据做决策',
                '都强调风险控制的重要性'
            ],
            'unresolved_issues': [
                '市场时机的把握',
                '仓位大小的确定'
            ],
            'follow_up_questions': [
                '请提供更具体的数据支持',
                '如何应对不确定性因素？'
            ],
            'recommendation': {
                'suggested_approach': '综合考虑双方观点',
                'confidence_level': 0.6,
                'next_steps': ['收集更多市场数据', '进行风险评估']
            }
        }
    
    def _should_end_debate(self, session: DebateSession, moderation_result: Dict[str, Any]) -> bool:
        """判断是否应该结束辩论"""
        # 达到最大轮数
        if len(session.rounds) >= self.max_rounds:
            return True
        
        # 达成共识
        confidence = moderation_result.get('recommendation', {}).get('confidence_level', 0)
        if confidence > 0.8:
            session.consensus_reached = True
            return True
        
        # 无法解决的分歧
        unresolved_issues = moderation_result.get('unresolved_issues', [])
        if len(unresolved_issues) == 0:
            return True
        
        return False
    
    async def _end_debate(self, session_id: str, final_moderation: Dict[str, Any]):
        """结束辩论会话"""
        if session_id not in self.active_debates:
            return
        
        session = self.active_debates[session_id]
        session.end_time = datetime.now()
        session.status = "completed"
        session.final_decision = final_moderation.get('recommendation', {})
        
        self.logger.info(f"Ended debate session {session_id}")
        
        # 发送辩论结束通知
        if self.communication_hub:
            end_message = Message(
                message_type=MessageType.DEBATE_END,
                from_agent=self.agent_id,
                to_agents=session.participants,
                content={
                    'session_id': session_id,
                    'final_decision': session.final_decision,
                    'consensus_reached': session.consensus_reached,
                    'total_rounds': len(session.rounds),
                    'duration': (session.end_time - session.start_time).total_seconds()
                },
                timestamp=datetime.now(),
                message_id=f"debate_end_{session_id}",
                priority=1
            )
            
            await self.communication_hub.send_message(end_message)
    
    def _get_fallback_moderation(self, session_id: str, round_number: int) -> Dict[str, Any]:
        """获取备用主持结果"""
        return {
            'debate_summary': f'第{round_number}轮辩论完成，双方观点已记录',
            'key_disagreements': ['技术分析vs基本面分析', '短期vs长期观点'],
            'consensus_points': ['都认为需要数据支持', '都重视风险控制'],
            'unresolved_issues': ['市场时机选择', '仓位配置策略'],
            'follow_up_questions': ['如何量化风险？', '何时调整策略？'],
            'recommendation': {
                'suggested_approach': '综合双方观点，谨慎决策',
                'confidence_level': 0.6,
                'next_steps': ['继续监控市场', '准备应急方案']
            },
            'session_id': session_id,
            'round_number': round_number,
            'moderator_id': self.agent_id,
            'timestamp': datetime.now().isoformat(),
            'fallback_used': True
        }
    
    def get_debate_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取辩论状态"""
        if session_id not in self.active_debates:
            return None
        
        session = self.active_debates[session_id]
        
        return {
            'session_id': session_id,
            'topic': session.topic,
            'participants': session.participants,
            'status': session.status,
            'rounds_completed': len(session.rounds),
            'max_rounds': self.max_rounds,
            'consensus_reached': session.consensus_reached,
            'start_time': session.start_time.isoformat(),
            'end_time': session.end_time.isoformat() if session.end_time else None,
            'duration': (
                (session.end_time or datetime.now()) - session.start_time
            ).total_seconds()
        }
    
    def get_all_debates(self) -> List[Dict[str, Any]]:
        """获取所有辩论会话"""
        return [self.get_debate_status(session_id) for session_id in self.active_debates.keys()]
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理消息"""
        if message.message_type == MessageType.DEBATE_ARGUMENT:
            # 处理辩论论点
            session_id = message.content.get('session_id')
            round_number = message.content.get('round_number', 1)
            argument_type = message.content.get('argument_type')  # 'bullish' or 'bearish'
            argument_content = message.content.get('argument')
            
            # 这里需要收集双方论点后再进行主持
            # 实际实现中需要更复杂的状态管理
            
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        base_status = super().get_status()
        base_status.update({
            'active_debates': len(self.active_debates),
            'max_rounds': self.max_rounds,
            'round_timeout': self.round_timeout,
            'specialization': '辩论主持与共识达成'
        })
        return base_status
