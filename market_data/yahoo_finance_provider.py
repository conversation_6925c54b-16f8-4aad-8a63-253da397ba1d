"""
Yahoo Finance数据提供商
实时获取股票市场数据
"""

import asyncio
import aiohttp
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import json


class YahooFinanceProvider:
    """Yahoo Finance数据提供商"""
    
    def __init__(self):
        self.base_url = "https://query1.finance.yahoo.com/v8/finance/chart"
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger("YahooFinanceProvider")
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_realtime_data(
        self,
        symbol: str,
        interval: str = "1h",
        period: str = "1d"
    ) -> Dict[str, Any]:
        """获取实时数据"""
        try:
            url = f"{self.base_url}/{symbol}"
            params = {
                'interval': interval,
                'period': period,
                'includePrePost': 'true'
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_yahoo_data(data, symbol)
                else:
                    self.logger.error(f"Failed to fetch data for {symbol}: {response.status}")
                    return self._get_mock_data(symbol)
                    
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return self._get_mock_data(symbol)
    
    def _parse_yahoo_data(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """解析Yahoo Finance数据"""
        try:
            chart = data['chart']['result'][0]
            meta = chart['meta']
            indicators = chart['indicators']['quote'][0]
            timestamps = chart['timestamp']
            
            # 构建蜡烛数据
            candles = []
            for i, timestamp in enumerate(timestamps):
                if (indicators['open'][i] is not None and 
                    indicators['high'][i] is not None and
                    indicators['low'][i] is not None and
                    indicators['close'][i] is not None):
                    
                    candle = {
                        'timestamp': datetime.fromtimestamp(timestamp).isoformat(),
                        'open': float(indicators['open'][i]),
                        'high': float(indicators['high'][i]),
                        'low': float(indicators['low'][i]),
                        'close': float(indicators['close'][i]),
                        'volume': int(indicators['volume'][i]) if indicators['volume'][i] else 0
                    }
                    candles.append(candle)
            
            return {
                'symbol': symbol,
                'currency': meta.get('currency', 'USD'),
                'exchange': meta.get('exchangeName', 'Unknown'),
                'timezone': meta.get('timezone', 'UTC'),
                'current_price': meta.get('regularMarketPrice', 0),
                'previous_close': meta.get('previousClose', 0),
                'candles': candles,
                'last_updated': datetime.now().isoformat(),
                'data_source': 'yahoo_finance'
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing Yahoo data: {e}")
            return self._get_mock_data(symbol)
    
    def _get_mock_data(self, symbol: str) -> Dict[str, Any]:
        """获取模拟数据（当API不可用时）"""
        base_price = 150.0
        candles = []
        
        # 生成24小时的模拟数据
        for i in range(24):
            timestamp = datetime.now() - timedelta(hours=23-i)
            
            # 简单的随机游走
            price_change = (hash(f"{symbol}_{i}") % 200 - 100) / 100.0  # -1 to 1
            open_price = base_price + price_change
            close_price = open_price + (hash(f"{symbol}_{i}_close") % 100 - 50) / 100.0
            high_price = max(open_price, close_price) + abs(hash(f"{symbol}_{i}_high") % 50) / 100.0
            low_price = min(open_price, close_price) - abs(hash(f"{symbol}_{i}_low") % 50) / 100.0
            volume = 1000000 + (hash(f"{symbol}_{i}_vol") % 500000)
            
            candle = {
                'timestamp': timestamp.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            }
            candles.append(candle)
            base_price = close_price
        
        return {
            'symbol': symbol,
            'currency': 'USD',
            'exchange': 'NASDAQ',
            'timezone': 'America/New_York',
            'current_price': candles[-1]['close'],
            'previous_close': candles[-2]['close'],
            'candles': candles,
            'last_updated': datetime.now().isoformat(),
            'data_source': 'mock_data'
        }
    
    async def get_multiple_symbols(
        self,
        symbols: List[str],
        interval: str = "1h",
        period: str = "1d"
    ) -> Dict[str, Dict[str, Any]]:
        """获取多个股票的数据"""
        tasks = []
        for symbol in symbols:
            task = self.get_realtime_data(symbol, interval, period)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        data_dict = {}
        for i, result in enumerate(results):
            symbol = symbols[i]
            if isinstance(result, Exception):
                self.logger.error(f"Error fetching data for {symbol}: {result}")
                data_dict[symbol] = self._get_mock_data(symbol)
            else:
                data_dict[symbol] = result

        return data_dict

    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            url = f"https://query1.finance.yahoo.com/v10/finance/quoteSummary/{symbol}"
            params = {
                'modules': 'summaryDetail,financialData,defaultKeyStatistics'
            }

            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_stock_info(data, symbol)
                else:
                    return self._get_mock_stock_info(symbol)

        except Exception as e:
            self.logger.error(f"Error fetching stock info for {symbol}: {e}")
            return self._get_mock_stock_info(symbol)

    def _parse_stock_info(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """解析股票基本信息"""
        try:
            result = data['quoteSummary']['result'][0]
            summary = result.get('summaryDetail', {})
            financial = result.get('financialData', {})
            key_stats = result.get('defaultKeyStatistics', {})

            return {
                'symbol': symbol,
                'company_name': symbol,  # Yahoo API限制，需要其他API获取公司名
                'sector': 'Technology',  # 简化处理
                'market_cap': key_stats.get('marketCap', {}).get('raw', 0),
                'pe_ratio': summary.get('trailingPE', {}).get('raw', 0),
                'dividend_yield': summary.get('dividendYield', {}).get('raw', 0),
                'beta': key_stats.get('beta', {}).get('raw', 1.0),
                'fifty_two_week_high': summary.get('fiftyTwoWeekHigh', {}).get('raw', 0),
                'fifty_two_week_low': summary.get('fiftyTwoWeekLow', {}).get('raw', 0),
                'average_volume': summary.get('averageVolume', {}).get('raw', 0),
                'revenue_growth': financial.get('revenueGrowth', {}).get('raw', 0),
                'profit_margin': financial.get('profitMargins', {}).get('raw', 0),
                'last_updated': datetime.now().isoformat(),
                'data_source': 'yahoo_finance'
            }

        except Exception as e:
            self.logger.error(f"Error parsing stock info: {e}")
            return self._get_mock_stock_info(symbol)

    def _get_mock_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取模拟股票信息"""
        return {
            'symbol': symbol,
            'company_name': f"{symbol} Inc.",
            'sector': 'Technology',
            'market_cap': 1000000000,  # 10亿
            'pe_ratio': 25.5,
            'dividend_yield': 0.02,
            'beta': 1.2,
            'fifty_two_week_high': 200.0,
            'fifty_two_week_low': 100.0,
            'average_volume': 5000000,
            'revenue_growth': 0.15,
            'profit_margin': 0.20,
            'last_updated': datetime.now().isoformat(),
            'data_source': 'mock_data'
        }
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """获取市场概况"""
        # 主要指数
        major_indices = ['%5EGSPC', '%5EDJI', '%5EIXIC']  # S&P 500, Dow Jones, NASDAQ
        
        try:
            indices_data = await self.get_multiple_symbols(major_indices, '1d', '5d')
            
            summary = {
                'timestamp': datetime.now().isoformat(),
                'indices': {},
                'market_status': 'open',  # 简化处理
                'data_source': 'yahoo_finance'
            }
            
            index_names = {
                '%5EGSPC': 'S&P 500',
                '%5EDJI': 'Dow Jones',
                '%5EIXIC': 'NASDAQ'
            }
            
            for symbol, data in indices_data.items():
                if data['candles']:
                    current = data['candles'][-1]['close']
                    previous = data['previous_close']
                    change = current - previous
                    change_percent = (change / previous) * 100 if previous != 0 else 0
                    
                    summary['indices'][index_names.get(symbol, symbol)] = {
                        'current': current,
                        'change': round(change, 2),
                        'change_percent': round(change_percent, 2)
                    }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error fetching market summary: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'indices': {
                    'S&P 500': {'current': 4500.0, 'change': 25.5, 'change_percent': 0.57},
                    'Dow Jones': {'current': 35000.0, 'change': 150.0, 'change_percent': 0.43},
                    'NASDAQ': {'current': 14000.0, 'change': 75.0, 'change_percent': 0.54}
                },
                'market_status': 'open',
                'data_source': 'mock_data'
            }


class MarketDataManager:
    """市场数据管理器"""

    def __init__(self):
        self.provider = YahooFinanceProvider()
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.logger = logging.getLogger("MarketDataManager")
        self.watchlist: List[str] = []  # 监控列表
        self.alerts: List[Dict[str, Any]] = []  # 价格提醒

    async def get_stock_data(
        self,
        symbol: str,
        interval: str = "1h",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """获取股票数据"""
        cache_key = f"{symbol}_{interval}"

        # 检查缓存
        if use_cache and cache_key in self.cache:
            cached_data = self.cache[cache_key]
            cache_time = datetime.fromisoformat(cached_data['last_updated'])
            if (datetime.now() - cache_time).total_seconds() < self.cache_ttl:
                self.logger.debug(f"Using cached data for {symbol}")
                return cached_data

        # 获取新数据
        async with self.provider as provider:
            data = await provider.get_realtime_data(symbol, interval)

            # 更新缓存
            if use_cache:
                self.cache[cache_key] = data

            return data

    async def get_stock_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        cache_key = f"{symbol}_info"

        # 检查缓存（股票信息缓存时间更长）
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            cache_time = datetime.fromisoformat(cached_data['last_updated'])
            if (datetime.now() - cache_time).total_seconds() < 3600:  # 1小时缓存
                return cached_data

        # 获取新数据
        async with self.provider as provider:
            data = await provider.get_stock_info(symbol)
            self.cache[cache_key] = data
            return data

    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概况"""
        async with self.provider as provider:
            return await provider.get_market_summary()

    async def get_multiple_stocks(
        self,
        symbols: List[str],
        interval: str = "1h"
    ) -> Dict[str, Dict[str, Any]]:
        """获取多个股票数据"""
        async with self.provider as provider:
            return await provider.get_multiple_symbols(symbols, interval)

    def add_to_watchlist(self, symbol: str):
        """添加到监控列表"""
        if symbol not in self.watchlist:
            self.watchlist.append(symbol)
            self.logger.info(f"Added {symbol} to watchlist")

    def remove_from_watchlist(self, symbol: str):
        """从监控列表移除"""
        if symbol in self.watchlist:
            self.watchlist.remove(symbol)
            self.logger.info(f"Removed {symbol} from watchlist")

    async def get_watchlist_data(self) -> Dict[str, Dict[str, Any]]:
        """获取监控列表数据"""
        if not self.watchlist:
            return {}

        return await self.get_multiple_stocks(self.watchlist)

    def add_price_alert(
        self,
        symbol: str,
        target_price: float,
        condition: str = "above",  # "above" or "below"
        alert_type: str = "price"
    ):
        """添加价格提醒"""
        alert = {
            'id': f"{symbol}_{target_price}_{condition}",
            'symbol': symbol,
            'target_price': target_price,
            'condition': condition,
            'alert_type': alert_type,
            'created_at': datetime.now().isoformat(),
            'triggered': False
        }
        self.alerts.append(alert)
        self.logger.info(f"Added price alert for {symbol}: {condition} {target_price}")

    async def check_alerts(self) -> List[Dict[str, Any]]:
        """检查价格提醒"""
        triggered_alerts = []

        for alert in self.alerts:
            if alert['triggered']:
                continue

            symbol = alert['symbol']
            try:
                data = await self.get_stock_data(symbol, use_cache=True)
                current_price = data['current_price']
                target_price = alert['target_price']
                condition = alert['condition']

                triggered = False
                if condition == "above" and current_price >= target_price:
                    triggered = True
                elif condition == "below" and current_price <= target_price:
                    triggered = True

                if triggered:
                    alert['triggered'] = True
                    alert['triggered_at'] = datetime.now().isoformat()
                    alert['triggered_price'] = current_price
                    triggered_alerts.append(alert)
                    self.logger.info(f"Alert triggered for {symbol}: {current_price}")

            except Exception as e:
                self.logger.error(f"Error checking alert for {symbol}: {e}")

        return triggered_alerts

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'cache_size': len(self.cache),
            'cache_ttl': self.cache_ttl,
            'watchlist_size': len(self.watchlist),
            'alerts_count': len(self.alerts),
            'active_alerts': len([a for a in self.alerts if not a['triggered']])
        }

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("Market data cache cleared")

    def clear_alerts(self):
        """清空提醒"""
        self.alerts.clear()
        self.logger.info("Price alerts cleared")


# 全局市场数据管理器实例
market_data_manager = MarketDataManager()
