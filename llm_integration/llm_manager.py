"""
LLM管理器
统一管理多个LLM提供商，提供负载均衡和故障转移
"""

import os
import asyncio
from typing import Dict, Any, Optional, List, Union
from enum import Enum
import logging

from .providers.base import BaseLLMProvider, LLMConfig, LLMResponse
from .providers.openai_provider import OpenAIProvider
from .providers.mock_provider import MockLLMProvider


class LLMProviderType(Enum):
    """LLM提供商类型"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    MOCK = "mock"


class LLMManager:
    """LLM管理器"""
    
    def __init__(self):
        self.providers: Dict[str, BaseLLMProvider] = {}
        self.default_provider: Optional[str] = None
        self.logger = logging.getLogger("LLMManager")
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        # 检查是否使用模拟响应
        mock_responses = os.getenv('MOCK_LLM_RESPONSES', 'false').lower() == 'true'
        
        if mock_responses:
            self.logger.info("Using mock LLM responses for development")
            self._setup_mock_provider()
        else:
            self._setup_real_providers()
    
    def _setup_mock_provider(self):
        """设置模拟提供商"""
        config = LLMConfig(
            model="gpt-4-mock",
            temperature=0.1,
            max_tokens=2000
        )
        
        provider = MockLLMProvider(config)
        self.add_provider("mock", provider)
        self.set_default_provider("mock")
    
    def _setup_real_providers(self):
        """设置真实提供商"""
        # OpenAI
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            config = LLMConfig(
                model=os.getenv('OPENAI_MODEL', 'gpt-4'),
                temperature=float(os.getenv('OPENAI_TEMPERATURE', '0.1')),
                max_tokens=int(os.getenv('OPENAI_MAX_TOKENS', '2000'))
            )
            
            provider = OpenAIProvider(config, openai_key)
            self.add_provider("openai", provider)
            
            # 设置为默认提供商
            if not self.default_provider:
                self.set_default_provider("openai")
        
        # Anthropic (如果需要的话)
        # anthropic_key = os.getenv('ANTHROPIC_API_KEY')
        # if anthropic_key:
        #     # 实现Anthropic提供商
        #     pass
        
        # 如果没有配置任何真实提供商，回退到模拟提供商
        if not self.providers:
            self.logger.warning("No real LLM providers configured, falling back to mock provider")
            self._setup_mock_provider()
    
    def add_provider(self, name: str, provider: BaseLLMProvider):
        """添加LLM提供商"""
        self.providers[name] = provider
        self.logger.info(f"Added LLM provider: {name} ({provider.get_provider_name()})")
    
    def set_default_provider(self, name: str):
        """设置默认提供商"""
        if name not in self.providers:
            raise ValueError(f"Provider {name} not found")
        
        self.default_provider = name
        self.logger.info(f"Set default LLM provider: {name}")
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        provider: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成响应"""
        provider_name = provider or self.default_provider
        
        if not provider_name or provider_name not in self.providers:
            raise ValueError(f"No valid provider specified or found: {provider_name}")
        
        llm_provider = self.providers[provider_name]
        
        try:
            response = await llm_provider.generate_response(
                prompt=prompt,
                system_prompt=system_prompt,
                **kwargs
            )
            
            self.logger.debug(f"Generated response using {provider_name}: {response.tokens_used} tokens")
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating response with {provider_name}: {e}")
            
            # 尝试故障转移到其他提供商
            if len(self.providers) > 1:
                return await self._fallback_generate(prompt, system_prompt, provider_name, **kwargs)
            else:
                raise
    
    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成对话响应"""
        provider_name = provider or self.default_provider
        
        if not provider_name or provider_name not in self.providers:
            raise ValueError(f"No valid provider specified or found: {provider_name}")
        
        llm_provider = self.providers[provider_name]
        
        try:
            response = await llm_provider.generate_chat_response(
                messages=messages,
                **kwargs
            )
            
            self.logger.debug(f"Generated chat response using {provider_name}: {response.tokens_used} tokens")
            return response
            
        except Exception as e:
            self.logger.error(f"Error generating chat response with {provider_name}: {e}")
            
            # 尝试故障转移
            if len(self.providers) > 1:
                return await self._fallback_generate_chat(messages, provider_name, **kwargs)
            else:
                raise
    
    async def _fallback_generate(
        self,
        prompt: str,
        system_prompt: Optional[str],
        failed_provider: str,
        **kwargs
    ) -> LLMResponse:
        """故障转移生成响应"""
        for name, provider in self.providers.items():
            if name != failed_provider:
                try:
                    self.logger.info(f"Falling back to provider: {name}")
                    return await provider.generate_response(
                        prompt=prompt,
                        system_prompt=system_prompt,
                        **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"Fallback provider {name} also failed: {e}")
                    continue
        
        raise Exception("All LLM providers failed")
    
    async def _fallback_generate_chat(
        self,
        messages: List[Dict[str, str]],
        failed_provider: str,
        **kwargs
    ) -> LLMResponse:
        """故障转移生成对话响应"""
        for name, provider in self.providers.items():
            if name != failed_provider:
                try:
                    self.logger.info(f"Falling back to provider: {name}")
                    return await provider.generate_chat_response(
                        messages=messages,
                        **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"Fallback provider {name} also failed: {e}")
                    continue
        
        raise Exception("All LLM providers failed")
    
    def get_provider_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有提供商的统计信息"""
        stats = {}
        for name, provider in self.providers.items():
            stats[name] = provider.get_stats()
        return stats
    
    def get_available_providers(self) -> List[str]:
        """获取可用的提供商列表"""
        return list(self.providers.keys())
    
    def estimate_cost(self, tokens: int, provider: Optional[str] = None) -> float:
        """估算成本"""
        provider_name = provider or self.default_provider
        
        if provider_name and provider_name in self.providers:
            return self.providers[provider_name].estimate_cost(tokens)
        
        return 0.0


# 全局LLM管理器实例
llm_manager = LLMManager()
