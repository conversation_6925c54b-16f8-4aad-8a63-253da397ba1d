#!/usr/bin/env python3
"""
完整的Web服务器 - 处理所有API端点和错误
"""

import os
import sys
import json
import threading
import webbrowser
import time
import random
from pathlib import Path
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import mimetypes

class CompleteAPIHandler(SimpleHTTPRequestHandler):
    """完整的API处理器"""

    def __init__(self, *args, **kwargs):
        # 设置前端构建目录
        self.build_dir = Path("frontend/build")
        super().__init__(*args, directory=str(self.build_dir), **kwargs)

    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # 处理API请求
        if self.path.startswith('/api/'):
            self.handle_api_request()
            return

        # 处理React路由 - 所有非API请求都返回index.html
        if not self.path.startswith('/static/') and not self.path.endswith(('.js', '.css', '.png', '.jpg', '.ico', '.json')):
            self.path = '/index.html'

        # 调用父类方法处理静态文件
        super().do_GET()

    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)

    def handle_api_request(self):
        """处理所有API请求"""
        try:
            # 健康检查
            if self.path == '/api/v1/health':
                self.send_api_response({
                    "status": "healthy",
                    "message": "蜡烛图形态识别 + TradingAgents 系统运行正常",
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0.0",
                    "features": [
                        "传统蜡烛图形态识别",
                        "TradingAgents多智能体验证",
                        "AI智能体协作分析",
                        "结构化辩论系统"
                    ]
                })

            # 形态分析
            elif self.path == '/api/v1/patterns/analyze':
                self.handle_pattern_analysis()

            # 市场综合分析
            elif self.path == '/api/v1/market/comprehensive-analysis':
                self.handle_market_analysis()

            # TradingAgents状态
            elif self.path == '/api/trading-agents/status':
                self.handle_trading_agents_status()

            # TradingAgents智能体列表
            elif self.path == '/api/trading-agents/agents':
                self.handle_agents_list()

            # 形态验证
            elif self.path == '/api/trading-agents/validate-pattern':
                self.handle_pattern_validation()

            # 智能体分析
            elif self.path == '/api/trading-agents/agent-analysis':
                self.handle_agent_analysis()

            # 智能体辩论
            elif self.path == '/api/trading-agents/debate':
                self.handle_debate()

            # 批量验证
            elif self.path == '/api/trading-agents/batch-validate':
                self.handle_batch_validation()

            # 支持的形态列表
            elif self.path == '/api/trading-agents/patterns/supported':
                self.handle_supported_patterns()

            # 智能体系统健康检查
            elif self.path == '/api/trading-agents/health':
                self.handle_trading_agents_health()

            # 未知端点
            else:
                self.send_api_error(f"API endpoint not found: {self.path}", 404)

        except Exception as e:
            print(f"API Error: {e}")
            self.send_api_error(f"Internal server error: {str(e)}", 500)

    def get_post_data(self):
        """获取POST数据"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            return json.loads(post_data.decode('utf-8'))
        return {}

    def handle_pattern_analysis(self):
        """处理形态分析"""
        try:
            request_data = self.get_post_data()
            candles = request_data.get('candles', [])

            # 模拟形态识别 - 使用前端期望的数据结构
            patterns = [
                {
                    "pattern_name": "doji",
                    "chinese_name": "十字星",
                    "confidence": random.uniform(0.7, 0.9),
                    "start_index": len(candles) - 1 if candles else 0,
                    "end_index": len(candles) - 1 if candles else 0,
                    "pattern_type": "reversal",
                    "pattern_category": "single",
                    "description": "市场犹豫不决的信号，可能预示趋势反转",
                    "signal": "neutral",
                    "trend_context": "sideways",
                    "volume_confirmation": True,
                    "key_levels": {
                        "support": random.uniform(95, 100),
                        "resistance": random.uniform(105, 110)
                    }
                },
                {
                    "pattern_name": "hammer",
                    "chinese_name": "锤子线",
                    "confidence": random.uniform(0.6, 0.8),
                    "start_index": len(candles) - 2 if len(candles) > 1 else 0,
                    "end_index": len(candles) - 2 if len(candles) > 1 else 0,
                    "pattern_type": "reversal",
                    "pattern_category": "single",
                    "description": "潜在的看涨反转信号，通常出现在下降趋势末期",
                    "signal": "bullish",
                    "trend_context": "downtrend",
                    "volume_confirmation": True,
                    "key_levels": {
                        "support": random.uniform(98, 102),
                        "resistance": random.uniform(108, 112)
                    }
                },
                {
                    "pattern_name": "shooting_star",
                    "chinese_name": "流星线",
                    "confidence": random.uniform(0.65, 0.85),
                    "start_index": len(candles) - 3 if len(candles) > 2 else 0,
                    "end_index": len(candles) - 3 if len(candles) > 2 else 0,
                    "pattern_type": "reversal",
                    "pattern_category": "single",
                    "description": "看跌反转信号，通常出现在上升趋势顶部",
                    "signal": "bearish",
                    "trend_context": "uptrend",
                    "volume_confirmation": False,
                    "key_levels": {
                        "support": random.uniform(100, 105),
                        "resistance": random.uniform(110, 115)
                    }
                }
            ]

            self.send_api_response({
                "status": "success",
                "message": f"成功识别到 {len(patterns)} 个形态",
                "data": {
                    "patterns": patterns,
                    "total_patterns": len(patterns),
                    "analysis_time": datetime.now().isoformat(),
                    "candles_analyzed": len(candles)
                }
            })

        except Exception as e:
            self.send_api_error(f"形态分析失败: {str(e)}", 500)

    def handle_market_analysis(self):
        """处理市场综合分析"""
        try:
            request_data = self.get_post_data()

            self.send_api_response({
                "status": "success",
                "message": "市场综合分析完成",
                "data": {
                    "market_overview": {
                        "trend": random.choice(["bullish", "bearish", "neutral"]),
                        "volatility": random.choice(["low", "medium", "high"]),
                        "sentiment": random.choice(["positive", "negative", "neutral"])
                    },
                    "technical_indicators": {
                        "rsi": random.uniform(30, 70),
                        "macd": random.choice(["bullish_crossover", "bearish_crossover", "neutral"]),
                        "moving_averages": random.choice(["uptrend", "downtrend", "sideways"])
                    },
                    "risk_assessment": {
                        "level": random.choice(["low", "medium", "high"]),
                        "factors": ["market_volatility", "economic_uncertainty"]
                    }
                }
            })

        except Exception as e:
            self.send_api_error(f"市场分析失败: {str(e)}", 500)

    def handle_trading_agents_status(self):
        """处理TradingAgents状态"""
        self.send_api_response({
            "status": "success",
            "message": "TradingAgents系统运行正常",
            "data": {
                "status": "running",
                "initialized": True,
                "agents": {
                    "total_agents": 6,
                    "active_agents": 6,
                    "agent_details": {
                        "MarketAnalyst": {"status": "active", "specialization": "technical_analysis", "is_active": True, "analysis_count": random.randint(20, 50)},
                        "BullResearcher": {"status": "active", "specialization": "bullish_analysis", "is_active": True, "analysis_count": random.randint(15, 40)},
                        "BearResearcher": {"status": "active", "specialization": "bearish_analysis", "is_active": True, "analysis_count": random.randint(15, 40)},
                        "PortfolioManager": {"status": "active", "specialization": "risk_management", "is_active": True, "analysis_count": random.randint(10, 30)},
                        "DebateModerator": {"status": "active", "specialization": "consensus_building", "is_active": True, "analysis_count": random.randint(5, 20)},
                        "CandlestickAnalyst": {"status": "active", "specialization": "candlestick_analysis", "is_active": True, "analysis_count": random.randint(25, 60)}
                    }
                },
                "debate_system": {"status": "active", "active_debates": 0, "total_debates": random.randint(5, 15)},
                "pattern_validator": {"status": "active", "initialized": True}
            },
            "timestamp": datetime.now().isoformat()
        })

    def handle_agents_list(self):
        """处理智能体列表"""
        agents = [
            {"name": "MarketAnalyst", "status": "active", "specialization": "technical_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(20, 50)},
            {"name": "BullResearcher", "status": "active", "specialization": "bullish_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(15, 40)},
            {"name": "BearResearcher", "status": "active", "specialization": "bearish_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(15, 40)},
            {"name": "PortfolioManager", "status": "active", "specialization": "risk_management", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(10, 30)},
            {"name": "DebateModerator", "status": "active", "specialization": "consensus_building", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(5, 20)},
            {"name": "CandlestickAnalyst", "status": "active", "specialization": "candlestick_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": random.randint(25, 60)}
        ]

        self.send_api_response({
            "status": "success",
            "message": "智能体列表获取成功",
            "data": {
                "total_agents": len(agents),
                "active_agents": len(agents),
                "agents": agents
            }
        })

    def send_api_response(self, data, status_code=200):
        """发送API响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))

    def handle_pattern_validation(self):
        """处理形态验证"""
        try:
            request_data = self.get_post_data()
            pattern_name = request_data.get('pattern_name', 'unknown')
            symbol = request_data.get('symbol', 'UNKNOWN')
            confidence = request_data.get('confidence', 0.8)

            # 模拟AI验证
            validation_improvement = random.uniform(-0.05, 0.15)
            final_score = max(0.1, min(1.0, confidence + validation_improvement))

            if final_score >= 0.8:
                conclusion = 'confirmed'
                reliability = 'high'
            elif final_score >= 0.6:
                conclusion = 'probable'
                reliability = 'medium'
            else:
                conclusion = 'questionable'
                reliability = 'low'

            self.send_api_response({
                "status": "success",
                "message": "形态验证完成",
                "data": {
                    "pattern_name": pattern_name,
                    "symbol": symbol,
                    "original_confidence": confidence,
                    "validation_result": {
                        "validation_conclusion": conclusion,
                        "reliability_level": reliability,
                        "final_validation_score": final_score,
                        "validation_improvement": validation_improvement,
                        "recommendations": [f"{pattern_name}形态经AI验证，{conclusion}"],
                        "risk_warnings": [] if conclusion == 'confirmed' else ["建议结合其他指标确认"]
                    }
                }
            })

        except Exception as e:
            self.send_api_error(f"形态验证失败: {str(e)}", 500)

    def handle_agent_analysis(self):
        """处理智能体分析"""
        try:
            request_data = self.get_post_data()
            symbol = request_data.get('symbol', 'UNKNOWN')

            # 模拟智能体分析
            agents_analysis = {
                "MarketAnalyst": {"signal": random.choice(["buy", "sell", "hold"]), "confidence": random.uniform(0.6, 0.9), "sentiment": "neutral"},
                "BullResearcher": {"signal": "buy", "confidence": random.uniform(0.7, 0.9), "sentiment": "bullish"},
                "BearResearcher": {"signal": "sell", "confidence": random.uniform(0.6, 0.8), "sentiment": "bearish"},
                "PortfolioManager": {"signal": random.choice(["hold", "buy"]), "confidence": random.uniform(0.5, 0.8), "sentiment": "neutral"},
                "CandlestickAnalyst": {"signal": random.choice(["buy", "sell", "hold"]), "confidence": random.uniform(0.6, 0.9), "sentiment": "neutral"}
            }

            # 计算共识
            buy_count = sum(1 for a in agents_analysis.values() if a["signal"] == "buy")
            sell_count = sum(1 for a in agents_analysis.values() if a["signal"] == "sell")
            hold_count = sum(1 for a in agents_analysis.values() if a["signal"] == "hold")

            overall_signal = "buy" if buy_count > max(sell_count, hold_count) else "sell" if sell_count > hold_count else "hold"

            self.send_api_response({
                "status": "success",
                "message": "智能体分析完成",
                "data": {
                    "symbol": symbol,
                    "agent_analyses": {
                        "analyses": agents_analysis,
                        "consensus_score": 0.7,
                        "participating_agents": list(agents_analysis.keys())
                    },
                    "summary": {
                        "total_agents": len(agents_analysis),
                        "consensus_signals": {
                            "buy": buy_count,
                            "sell": sell_count,
                            "hold": hold_count,
                            "total": len(agents_analysis)
                        },
                        "overall_signal": overall_signal,
                        "overall_sentiment": "bullish" if overall_signal == "buy" else "bearish" if overall_signal == "sell" else "neutral"
                    }
                }
            })

        except Exception as e:
            self.send_api_error(f"智能体分析失败: {str(e)}", 500)

    def handle_debate(self):
        """处理智能体辩论"""
        try:
            request_data = self.get_post_data()
            topic = request_data.get('topic', '市场分析')

            # 模拟辩论结果
            decision = random.choice(['buy', 'sell', 'hold'])
            confidence = random.uniform(0.6, 0.9)

            self.send_api_response({
                "status": "success",
                "message": "智能体辩论完成",
                "data": {
                    "topic": topic,
                    "consensus": {
                        "decision": decision,
                        "confidence": confidence,
                        "reasoning": f"经过多轮辩论，智能体达成共识：{decision}"
                    },
                    "rounds": []
                }
            })

        except Exception as e:
            self.send_api_error(f"智能体辩论失败: {str(e)}", 500)

    def handle_batch_validation(self):
        """处理批量验证"""
        try:
            request_data = self.get_post_data()
            patterns = request_data.get('patterns', [])

            results = []
            for pattern in patterns:
                # 模拟验证结果
                confidence = pattern.get('confidence', 0.8)
                final_score = max(0.1, min(1.0, confidence + random.uniform(-0.05, 0.15)))

                results.append({
                    'pattern_name': pattern.get('pattern_name', 'unknown'),
                    'symbol': pattern.get('symbol', 'UNKNOWN'),
                    'status': 'completed',
                    'final_score': final_score,
                    'validation_conclusion': 'confirmed' if final_score >= 0.8 else 'probable' if final_score >= 0.6 else 'questionable'
                })

            self.send_api_response({
                "status": "success",
                "message": f"批量验证完成，共处理{len(patterns)}个形态",
                "data": {
                    "results": results,
                    "summary": {
                        "total_patterns": len(results),
                        "confirmed_patterns": len([r for r in results if r['validation_conclusion'] == 'confirmed']),
                        "probable_patterns": len([r for r in results if r['validation_conclusion'] == 'probable']),
                        "questionable_patterns": len([r for r in results if r['validation_conclusion'] == 'questionable'])
                    }
                }
            })

        except Exception as e:
            self.send_api_error(f"批量验证失败: {str(e)}", 500)

    def handle_supported_patterns(self):
        """处理支持的形态列表"""
        patterns = {
            "reversal_patterns": [
                {"name": "doji", "chinese_name": "十字星", "type": "reversal", "reliability": "medium"},
                {"name": "hammer", "chinese_name": "锤子线", "type": "bullish_reversal", "reliability": "high"},
                {"name": "hanging_man", "chinese_name": "上吊线", "type": "bearish_reversal", "reliability": "high"},
                {"name": "shooting_star", "chinese_name": "流星线", "type": "bearish_reversal", "reliability": "high"}
            ]
        }

        self.send_api_response({
            "status": "success",
            "message": "支持的形态列表获取成功",
            "data": {
                "total_patterns": sum(len(p) for p in patterns.values()),
                "categories": patterns
            }
        })

    def handle_trading_agents_health(self):
        """处理TradingAgents健康检查"""
        self.send_api_response({
            "status": "healthy",
            "message": "TradingAgents系统运行正常",
            "data": {
                "system_status": "running",
                "initialized": True,
                "timestamp": datetime.now().isoformat()
            }
        })

    def send_api_response(self, data, status_code=200):
        """发送API响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))

    def send_api_error(self, message, status_code=500):
        """发送API错误响应"""
        error_data = {
            "error": message,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
        self.send_api_response(error_data, status_code)


def start_server():
    """启动Web服务器"""
    print("🚀 启动完整的蜡烛图形态识别 + TradingAgents Web服务器")
    print("=" * 60)

    # 检查前端构建目录
    frontend_build_dir = Path("frontend/build")
    if not frontend_build_dir.exists():
        print("❌ 前端构建目录不存在")
        print("💡 请先运行: cd frontend && npm run build")
        return

    print(f"✅ 找到前端构建文件: {frontend_build_dir.absolute()}")

    # 启动服务器
    port = 3000
    server_address = ('', port)

    try:
        httpd = HTTPServer(server_address, CompleteAPIHandler)

        print(f"🌐 Web服务器启动成功!")
        print(f"📱 前端界面: http://localhost:{port}")
        print(f"🔧 后端API: http://localhost:{port}/api/")
        print(f"💚 健康检查: http://localhost:{port}/api/v1/health")
        print(f"🤖 TradingAgents: http://localhost:{port}/api/trading-agents/status")
        print("=" * 60)
        print("🎯 支持的API端点:")
        print("✅ /api/v1/patterns/analyze - 形态分析")
        print("✅ /api/v1/market/comprehensive-analysis - 市场分析")
        print("✅ /api/trading-agents/* - 所有TradingAgents功能")
        print("=" * 60)
        print("🔄 服务器运行中... (按 Ctrl+C 停止)")

        # 自动打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')

        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

        # 启动服务器
        httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")


if __name__ == "__main__":
    start_server()