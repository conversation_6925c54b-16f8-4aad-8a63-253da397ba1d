# 🕯️ AI智能体投资分析平台

> 基于《日本蜡烛图技术》+ TradingAgents + GPT-4的革命性投资决策系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![React](https://img.shields.io/badge/React-18+-61dafb.svg)](https://reactjs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-009688.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🎯 项目概述

这是**全球首个AI智能体驱动的投资决策辩论系统**，结合传统技术分析理论与现代AI技术，通过多智能体协作和实时辩论机制，提供机构级的投资分析和决策支持。

### 🌟 核心创新

- 🤖 **多智能体协作** - 5个专业AI智能体团队协作
- 🎭 **智能辩论机制** - 看涨vs看跌研究员实时对抗
- 🧠 **LLM驱动分析** - GPT-4级别的专业市场解读
- 📊 **经典形态识别** - 基于《日本蜡烛图技术》的20+种形态
- 🔄 **结构化决策流程** - 模拟真实投资委员会流程

## ✨ 主要功能

### 🔍 **智能分析引擎**
- 🕯️ **蜡烛图形态识别** - 20+种经典形态自动识别
- 📈 **技术指标分析** - 多维度技术分析
- 🧠 **LLM智能解读** - AI驱动的专业市场分析
- 📊 **实时数据集成** - Yahoo Finance等数据源

### 🤖 **AI智能体团队**
- 🔬 **蜡烛图专家** - 专业形态识别和技术分析
- 🐂 **看涨研究员** - 寻找买入机会和上涨催化剂
- 🐻 **看跌研究员** - 识别风险和下跌信号
- 💼 **专业交易员** - 综合决策和仓位管理
- 🛡️ **风险管理专家** - 多层次风险控制

### 🎭 **辩论决策系统**
- 🔄 **多轮结构化辩论** - 逐步深入核心问题
- 🤝 **智能共识达成** - 自动化决策收敛
- 📊 **观点分歧量化** - 决策质量评估
- 🔍 **全程可追溯** - 透明化决策路径

## 🚀 快速开始

### 📋 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### ⚡ 一键启动

1. **克隆项目**
```bash
git clone <repository-url>
cd AI-Investment-Analysis-Platform
```

2. **启动后端服务**
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload
```

3. **启动前端应用**
```bash
cd frontend
npm install
npm start
```

4. **访问系统**
- 🎨 **前端应用**: http://localhost:3000
- 🔧 **API文档**: http://localhost:8000/docs
- 🎭 **演示页面**: 打开 `demo.html`

### 🎯 快速体验

#### 方法1：使用演示页面
1. 打开 `demo.html` 文件
2. 点击各种功能按钮体验系统
3. 查看实时API调用结果

#### 方法2：运行智能体辩论
```bash
python3 standalone_debate_demo.py
```

#### 方法3：测试高级智能体
```bash
python3 test_advanced_agents.py
```

## 📊 支持的蜡烛图形态

### 🔄 **反转形态**
| 形态 | 中文名 | 信号 | 置信度 |
|------|--------|------|--------|
| Morning Star | 启明星 | 看涨 | 高 |
| Evening Star | 黄昏星 | 看跌 | 高 |
| Hammer | 锤子线 | 看涨 | 中 |
| Hanging Man | 上吊线 | 看跌 | 中 |
| Engulfing | 吞没形态 | 双向 | 高 |

### ⬆️ **持续形态**
| 形态 | 中文名 | 信号 | 置信度 |
|------|--------|------|--------|
| Three White Soldiers | 前进白色三兵 | 看涨 | 高 |
| Three Black Crows | 三只乌鸦 | 看跌 | 高 |
| Rising Three Methods | 上升三法 | 看涨 | 中 |
| Falling Three Methods | 下降三法 | 看跌 | 中 |

### ✝️ **十字星系列**
| 形态 | 中文名 | 信号 | 置信度 |
|------|--------|------|--------|
| Doji | 十字星 | 中性 | 中 |
| Gravestone Doji | 墓碑十字线 | 看跌 | 中 |
| Dragonfly Doji | 蜻蜓十字线 | 看涨 | 中 |

## 🔧 API接口文档

### 🎯 **核心端点**

#### 蜡烛图分析
```http
POST /api/v1/analyze
Content-Type: application/json

{
  "candles": [
    {
      "open": 100.0,
      "high": 102.0,
      "low": 99.0,
      "close": 101.0,
      "volume": 1500,
      "timestamp": "2024-01-01T09:00:00Z"
    }
  ]
}
```

#### 智能体状态
```http
GET /api/v1/agents/status
```

#### 多智能体分析
```http
POST /api/v1/agents/analyze
Content-Type: application/json

{
  "symbol": "AAPL",
  "timeframe": "1H",
  "analysis_type": "comprehensive",
  "candles": [...]
}
```

## 🎭 智能体辩论演示

### 🔄 **辩论流程**
1. **技术分析阶段** - 蜡烛图专家识别形态
2. **研究员分析** - 看涨/看跌研究员独立分析
3. **正式辩论** - 多轮结构化辩论
4. **主持人总结** - 观点分析和共识达成
5. **最终决策** - 综合建议和风险评估

### 📊 **辩论结果示例**
```json
{
  "symbol": "TSLA",
  "debate_result": {
    "bullish_confidence": 0.75,
    "bearish_confidence": 0.60,
    "consensus_reached": false,
    "final_recommendation": "综合考虑双方观点",
    "decision_confidence": 0.65
  }
}
```

## 🎯 使用场景

### 👨‍💼 **个人投资者**
- 📊 技术分析学习和实践
- 🎯 投资决策辅助
- 📈 市场趋势判断

### 🏢 **机构投资者**
- 🤖 自动化分析流程
- 👥 团队决策支持
- 📋 风险管理工具

### 🎓 **教育培训**
- 📚 技术分析教学
- 🧠 AI应用演示
- 💡 创新思维启发

## 📖 详细文档

- 📋 [使用指南](USAGE_GUIDE.md) - 详细的功能使用说明
- 🔧 [API文档](docs/api/) - 完整的API接口文档
- 📊 [形态说明](docs/patterns/) - 蜡烛图形态详解

## 🎉 项目亮点

### 🏆 **技术创新**
- 🌍 **全球首创** - AI智能体投资辩论系统
- 🧠 **LLM集成** - GPT-4级别的专业分析
- 🤖 **多智能体协作** - 模拟真实投资团队
- 📊 **量化辩论** - 观点分歧和共识智能评估

### 🎯 **业务价值**
- 📈 **决策质量提升** - 多角度分析避免盲区
- 🛡️ **风险控制增强** - 对抗性分析发现风险
- ⚡ **效率大幅提升** - 自动化专业分析流程
- 🔍 **透明化决策** - 完整的决策路径记录

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 🔧 **开发贡献**
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 📝 **其他贡献**
- 🐛 报告Bug
- 💡 提出新功能建议
- 📖 改进文档
- 🌍 翻译项目

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 📚 《日本蜡烛图技术》- Steve Nison
- 🤖 TradingAgents 理论框架
- 🧠 OpenAI GPT-4 技术支持
- 🎨 Ant Design 设计系统

---

<div align="center">

**🌟 如果这个项目对你有帮助，请给我们一个Star！**

</div>
