# 蜡烛图形态识别系统

基于《日本蜡烛图技术》一书开发的智能形态识别系统，能够自动识别各种经典的蜡烛图形态。

## 功能特性

### 支持的形态类别

#### 1. 单根蜡烛线形态
- 锤子线 (Hammer)
- 上吊线 (Hanging Man)
- 十字线 (<PERSON><PERSON>)
- 长腿十字线 (Long-Le<PERSON> Doji)
- 墓碑十字线 (Gravestone Doji)
- 纺锤线 (Spinning Top)

#### 2. 反转形态
- 吞没形态 (Engulfing Pattern)
- 乌云盖顶 (Dark Cloud Cover)
- 刺透形态 (Piercing Pattern)
- 孕线形态 (Harami Pattern)
- 十字孕线 (Harami Cross)
- 平头顶部/底部 (Tweezers)

#### 3. 星线形态
- 启明星 (Morning Star)
- 黄昏星 (Evening Star)
- 十字启明星 (Morning Doji Star)
- 十字黄昏星 (Evening Doji Star)
- 流星 (Shooting Star)
- 倒锤子 (Inverted Hammer)

#### 4. 持续形态
- 窗口/跳空 (Gap/Window)
- 上升三法 (Rising Three Methods)
- 下降三法 (Falling Three Methods)
- 前进白色三兵 (Three White Soldiers)
- 三只乌鸦 (Three Black Crows)

## 技术架构

```
candlestick-pattern-recognition/
├── backend/                    # 后端服务
│   ├── src/
│   │   ├── models/            # 数据模型
│   │   ├── patterns/          # 形态识别算法
│   │   ├── utils/             # 工具函数
│   │   ├── api/               # API接口
│   │   └── tests/             # 测试用例
│   ├── requirements.txt       # Python依赖
│   └── main.py               # 主程序入口
├── frontend/                  # 前端界面
│   ├── src/
│   │   ├── components/        # React组件
│   │   ├── services/          # API服务
│   │   └── utils/             # 工具函数
│   ├── package.json          # Node.js依赖
│   └── public/               # 静态资源
├── data/                     # 数据文件
│   ├── samples/              # 示例数据
│   └── test_cases/           # 测试数据
└── docs/                     # 文档
    ├── patterns/             # 形态说明文档
    └── api/                  # API文档
```

## 核心算法

### 数据结构
```python
@dataclass
class Candle:
    open: float      # 开盘价
    high: float      # 最高价
    low: float       # 最低价
    close: float     # 收盘价
    volume: float    # 成交量
    timestamp: datetime  # 时间戳

@dataclass
class PatternResult:
    pattern_name: str     # 形态名称
    pattern_type: str     # 形态类型 (reversal/continuation)
    signal: str          # 信号 (bullish/bearish/neutral)
    confidence: float    # 置信度 (0-1)
    start_index: int     # 起始位置
    end_index: int       # 结束位置
    description: str     # 形态描述
```

### 识别流程
1. 数据预处理和验证
2. 单根蜡烛线特征计算
3. 多根蜡烛线组合分析
4. 形态匹配和置信度计算
5. 结果输出和可视化

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- 现代浏览器

### 安装步骤
```bash
# 克隆项目
git clone <repository-url>
cd candlestick-pattern-recognition

# 安装后端依赖
cd backend
pip install -r requirements.txt

# 安装前端依赖
cd ../frontend
npm install

# 启动服务
# 后端
cd ../backend
python main.py

# 前端
cd ../frontend
npm start
```

## 使用说明

### 🌐 Web界面使用
1. 启动前后端服务后，访问 http://localhost:3000
2. 在"数据上传"区域上传CSV文件或使用示例数据
3. 点击"开始分析"按钮进行形态识别
4. 在"分析结果"区域查看识别到的形态
5. 在"蜡烛图"区域查看可视化图表

### 📁 数据格式要求
CSV文件需包含以下列（顺序不限）：
```csv
timestamp,open,high,low,close,volume
2024-01-01T09:00:00Z,100.0,102.0,99.0,99.5,1000
2024-01-01T10:00:00Z,99.5,100.5,98.0,98.2,1200
```

### 🔧 API使用示例

#### Python示例
```python
import requests
import json

# 准备数据
data = {
    "candles": [
        {
            "open": 100.0,
            "high": 102.0,
            "low": 99.0,
            "close": 99.5,
            "volume": 1000,
            "timestamp": "2024-01-01T09:00:00Z"
        },
        # ... 更多数据
    ]
}

# 发送请求
response = requests.post(
    "http://localhost:8000/api/v1/analyze",
    json=data,
    headers={"Content-Type": "application/json"}
)

# 处理结果
if response.status_code == 200:
    result = response.json()
    for pattern in result["patterns"]:
        print(f"发现形态: {pattern['pattern_name']}")
        print(f"信号: {pattern['signal']}")
        print(f"置信度: {pattern['confidence']:.2f}")
```

#### JavaScript示例
```javascript
const analyzePatterns = async (candleData) => {
  try {
    const response = await fetch('http://localhost:8000/api/v1/analyze', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ candles: candleData })
    });

    const result = await response.json();
    console.log('识别结果:', result);
    return result;
  } catch (error) {
    console.error('分析失败:', error);
  }
};
```

### 📊 支持的形态类型

#### 反转形态 (Reversal Patterns)
- **锤子线/上吊线** - 在趋势底部/顶部出现的反转信号
- **十字星系列** - 市场犹豫不决，可能反转
- **吞没形态** - 强烈的反转信号
- **启明星/黄昏星** - 三根K线组成的反转形态

#### 持续形态 (Continuation Patterns)
- **三白兵/三只乌鸦** - 趋势持续的强烈信号
- **上升/下降三法** - 趋势中的短暂调整后继续

#### 不确定形态 (Indecision Patterns)
- **纺锤线** - 市场方向不明确
- **孕线形态** - 趋势可能发生变化

## 项目状态

### ✅ 已完成功能
- [x] 项目架构设计
- [x] 核心数据结构实现
- [x] 基础形态识别算法
- [x] 31种蜡烛图形态识别
- [x] 反转形态识别
- [x] 星线形态识别
- [x] 持续形态识别
- [x] 可视化功能
- [x] Web界面开发
- [x] 前后端集成
- [x] API接口完善

### 🔄 当前功能
- **形态识别**: 支持31种经典蜡烛图形态
- **智能分析**: 置信度评分、趋势上下文分析
- **可视化**: 交互式蜡烛图展示
- **Web界面**: 完整的前端操作界面
- **API服务**: RESTful API接口

### 🚀 未来计划
- [ ] 更多复杂形态识别
- [ ] 机器学习优化
- [ ] 实时数据接入
- [ ] 移动端适配
- [ ] 性能优化

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
