#!/usr/bin/env python3
"""
高级智能体测试
验证新创建的专业智能体功能
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🔧 测试高级智能体导入...")
    
    try:
        # 测试LLM集成
        from llm_integration.llm_manager import llm_manager
        print("✅ LLM管理器导入成功")
        
        # 测试提示词模板
        from llm_integration.prompts.candlestick_prompts import BULLISH_RESEARCHER_PROMPT
        print("✅ 提示词模板导入成功")
        
        # 测试研究员智能体
        from agents.researchers.bullish_researcher import BullishResearcher
        from agents.researchers.bearish_researcher import BearishResearcher
        print("✅ 研究员智能体导入成功")
        
        # 测试交易员智能体
        from agents.traders.professional_trader import ProfessionalTrader
        print("✅ 专业交易员导入成功")
        
        # 测试风险管理智能体
        from agents.risk_managers.risk_manager import RiskManager
        print("✅ 风险管理智能体导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """测试智能体创建"""
    print("\n🤖 测试智能体创建...")
    
    try:
        from agents.researchers.bullish_researcher import BullishResearcher
        from agents.researchers.bearish_researcher import BearishResearcher
        from agents.traders.professional_trader import ProfessionalTrader
        from agents.risk_managers.risk_manager import RiskManager
        
        # 创建智能体实例
        bullish_researcher = BullishResearcher("test_bullish_001")
        bearish_researcher = BearishResearcher("test_bearish_001")
        trader = ProfessionalTrader("test_trader_001")
        risk_manager = RiskManager("test_risk_001")
        
        print(f"✅ 看涨研究员: {bullish_researcher.agent_id}")
        print(f"✅ 看跌研究员: {bearish_researcher.agent_id}")
        print(f"✅ 专业交易员: {trader.agent_id}")
        print(f"✅ 风险管理: {risk_manager.agent_id}")
        
        # 测试状态获取
        status = bullish_researcher.get_status()
        print(f"✅ 智能体状态: {status['role']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_integration():
    """测试LLM集成"""
    print("\n🧠 测试LLM集成...")
    
    try:
        from llm_integration.llm_manager import llm_manager
        
        # 检查提供商
        providers = llm_manager.get_available_providers()
        default = llm_manager.default_provider
        
        print(f"✅ 可用提供商: {providers}")
        print(f"✅ 默认提供商: {default}")
        
        # 测试简单响应
        response = await llm_manager.generate_response(
            prompt="请简单分析一下启明星形态的市场含义。",
            system_prompt="你是一位技术分析师。"
        )
        
        print(f"✅ LLM响应生成成功")
        print(f"   模型: {response.model}")
        print(f"   Token: {response.tokens_used}")
        print(f"   成本: ${response.cost:.4f}")
        print(f"   内容: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM集成测试失败: {e}")
        return False

async def test_agent_analysis():
    """测试智能体分析功能"""
    print("\n📊 测试智能体分析功能...")
    
    try:
        from agents.researchers.bullish_researcher import BullishResearcher
        
        # 创建看涨研究员
        researcher = BullishResearcher("test_researcher")
        
        # 模拟市场数据
        test_data = {
            'symbol': 'AAPL',
            'patterns': [
                {
                    'name': 'MORNING_STAR',
                    'chinese_name': '启明星',
                    'signal': 'BULLISH',
                    'confidence': 0.85
                }
            ],
            'technical_analysis': {
                'trend': 'UPTREND',
                'volume': 'INCREASING'
            }
        }
        
        # 执行分析
        result = await researcher._perform_analysis(test_data)
        
        print(f"✅ 分析完成")
        print(f"   观点: {result.get('opinion', 'N/A')}")
        print(f"   置信度: {result.get('confidence', 0):.2f}")
        print(f"   理由: {result.get('reasoning', 'N/A')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_templates():
    """测试提示词模板"""
    print("\n📝 测试提示词模板...")
    
    try:
        from llm_integration.prompts.candlestick_prompts import (
            BULLISH_RESEARCHER_PROMPT,
            BEARISH_RESEARCHER_PROMPT,
            TRADER_DECISION_PROMPT,
            RISK_MANAGER_PROMPT
        )
        
        print("✅ 看涨研究员提示词模板")
        print("✅ 看跌研究员提示词模板")
        print("✅ 交易员决策提示词模板")
        print("✅ 风险管理提示词模板")
        
        # 测试模板格式化
        test_data = {"test": "data"}
        formatted = BULLISH_RESEARCHER_PROMPT.format(analysis_data="test")
        print(f"✅ 模板格式化成功: {len(formatted)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词模板测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎯 高级智能体系统测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("智能体创建", test_agent_creation),
        ("提示词模板", test_prompt_templates),
        ("LLM集成", test_llm_integration),
        ("智能体分析", test_agent_analysis),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有高级智能体测试通过！")
        print("\n✨ 系统已具备以下能力:")
        print("   🧠 专业LLM集成 - 智能分析和解读")
        print("   🤖 高级智能体 - 专业分工协作")
        print("   📝 专业提示词 - 基于金融理论设计")
        print("   🔄 异步处理 - 高效并发执行")
        print("   🛡️ 风险控制 - 多层次风险管理")
        
        print("\n🚀 可以开始:")
        print("1. 配置真实LLM API密钥")
        print("2. 运行完整的专业分析流程")
        print("3. 集成实时市场数据")
        print("4. 部署到生产环境")
    else:
        print("⚠️  部分测试失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())
