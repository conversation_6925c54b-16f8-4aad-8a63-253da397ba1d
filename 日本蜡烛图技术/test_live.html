<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AI投资分析平台 - 实时测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .status-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #52c41a;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .test-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        
        .loading {
            color: #faad14;
        }
        
        .success {
            color: #52c41a;
        }
        
        .error {
            color: #ff4d4f;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .input-group input, .input-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .input-group label {
            font-weight: 500;
            min-width: 80px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI投资分析平台 - 实时测试</h1>
            <p>测试所有核心功能，验证系统运行状态</p>
        </div>
        
        <!-- 系统状态 -->
        <div class="status-grid">
            <div class="status-card">
                <h3>
                    <span class="status-indicator"></span>
                    后端API服务
                </h3>
                <p>FastAPI服务器运行在 http://localhost:8000</p>
                <p id="backend-status">检查中...</p>
            </div>
            
            <div class="status-card">
                <h3>
                    <span class="status-indicator"></span>
                    智能体系统
                </h3>
                <p>多智能体协作和辩论系统</p>
                <p id="agents-status">检查中...</p>
            </div>
            
            <div class="status-card">
                <h3>
                    <span class="status-indicator"></span>
                    市场数据
                </h3>
                <p>实时股票数据和分析引擎</p>
                <p id="market-status">检查中...</p>
            </div>
        </div>
        
        <!-- 基础API测试 -->
        <div class="test-section">
            <h2>🔧 基础API测试</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testHealth()">健康检查</button>
                <button class="test-btn" onclick="testAgentStatus()">智能体状态</button>
                <button class="test-btn" onclick="testPatternList()">形态列表</button>
                <button class="test-btn" onclick="testPatternAnalysis()">形态分析</button>
            </div>
            <div class="result-area" id="basic-results">点击按钮开始测试...</div>
        </div>
        
        <!-- 市场分析测试 -->
        <div class="test-section">
            <h2>📊 市场分析测试</h2>
            <div class="input-group">
                <label>股票代码:</label>
                <input type="text" id="symbol-input" value="AAPL" placeholder="输入股票代码">
                <label>时间间隔:</label>
                <select id="interval-select">
                    <option value="1m">1分钟</option>
                    <option value="5m">5分钟</option>
                    <option value="15m">15分钟</option>
                    <option value="1h" selected>1小时</option>
                    <option value="1d">1天</option>
                </select>
            </div>
            <div class="test-buttons">
                <button class="test-btn" onclick="testStockInfo()">股票信息</button>
                <button class="test-btn" onclick="testRealtimeData()">实时数据</button>
                <button class="test-btn" onclick="testComprehensiveAnalysis()">综合分析</button>
                <button class="test-btn" onclick="testWatchlist()">监控列表</button>
            </div>
            <div class="result-area" id="market-results">选择股票代码并点击按钮测试...</div>
        </div>
        
        <!-- 智能体测试 -->
        <div class="test-section">
            <h2>🤖 智能体系统测试</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="testAgentAnalysis()">智能体分析</button>
                <button class="test-btn" onclick="testDebateSystem()">辩论系统</button>
                <button class="test-btn" onclick="testWorkflow()">工作流</button>
                <button class="test-btn" onclick="runStandaloneDemo()">独立演示</button>
            </div>
            <div class="result-area" id="agent-results">点击按钮测试智能体功能...</div>
        </div>
        
        <!-- 快速链接 -->
        <div class="test-section">
            <h2>🔗 快速链接</h2>
            <div class="test-buttons">
                <button class="test-btn" onclick="openApiDocs()">API文档</button>
                <button class="test-btn" onclick="openMainDemo()">主演示页面</button>
                <button class="test-btn" onclick="clearAllResults()">清空结果</button>
                <button class="test-btn" onclick="runFullTest()">完整测试</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 页面加载时检查系统状态
        window.onload = function() {
            checkSystemStatus();
        };
        
        // 检查系统状态
        async function checkSystemStatus() {
            // 检查后端
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                document.getElementById('backend-status').innerHTML = 
                    `<span class="success">✅ 运行正常 - ${data.service}</span>`;
            } catch (error) {
                document.getElementById('backend-status').innerHTML = 
                    `<span class="error">❌ 连接失败</span>`;
            }
            
            // 检查智能体
            try {
                const response = await fetch(`${API_BASE}/agents/status`);
                const data = await response.json();
                document.getElementById('agents-status').innerHTML = 
                    `<span class="success">✅ ${data.total_agents || 5} 个智能体就绪</span>`;
            } catch (error) {
                document.getElementById('agents-status').innerHTML = 
                    `<span class="error">❌ 智能体系统离线</span>`;
            }
            
            // 检查市场数据
            try {
                const response = await fetch(`${API_BASE}/market/cache-stats`);
                const data = await response.json();
                document.getElementById('market-status').innerHTML = 
                    `<span class="success">✅ 缓存: ${data.cache_statistics?.cache_size || 0} 项</span>`;
            } catch (error) {
                document.getElementById('market-status').innerHTML = 
                    `<span class="error">❌ 市场数据服务离线</span>`;
            }
        }
        
        // 输出结果到指定区域
        function outputResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'loading' ? 'loading' : '';
            element.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            element.scrollTop = element.scrollHeight;
        }
        
        // 基础API测试
        async function testHealth() {
            outputResult('basic-results', '🔍 测试健康检查...', 'loading');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                outputResult('basic-results', `✅ 健康检查成功: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                outputResult('basic-results', `❌ 健康检查失败: ${error.message}`, 'error');
            }
        }
        
        async function testAgentStatus() {
            outputResult('basic-results', '🤖 测试智能体状态...', 'loading');
            try {
                const response = await fetch(`${API_BASE}/agents/status`);
                const data = await response.json();
                outputResult('basic-results', `✅ 智能体状态: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                outputResult('basic-results', `❌ 智能体状态查询失败: ${error.message}`, 'error');
            }
        }
        
        async function testPatternList() {
            outputResult('basic-results', '📊 获取形态列表...', 'loading');
            try {
                const response = await fetch(`${API_BASE}/patterns/statistics`);
                const data = await response.json();
                outputResult('basic-results', `✅ 形态统计: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                outputResult('basic-results', `❌ 形态列表获取失败: ${error.message}`, 'error');
            }
        }
        
        async function testPatternAnalysis() {
            outputResult('basic-results', '🔍 测试形态分析...', 'loading');
            const sampleData = {
                candles: [
                    {"open": 100.0, "high": 102.0, "low": 99.0, "close": 99.5, "volume": 1500, "timestamp": "2024-01-01T09:00:00Z"},
                    {"open": 99.5, "high": 100.5, "low": 98.0, "close": 98.2, "volume": 1600, "timestamp": "2024-01-01T10:00:00Z"},
                    {"open": 98.2, "high": 99.0, "low": 97.0, "close": 97.5, "volume": 1700, "timestamp": "2024-01-01T11:00:00Z"}
                ]
            };
            
            try {
                const response = await fetch(`${API_BASE}/analyze`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(sampleData)
                });
                const data = await response.json();
                outputResult('basic-results', `✅ 形态分析结果: 识别到 ${data.patterns?.length || 0} 个形态`, 'success');
            } catch (error) {
                outputResult('basic-results', `❌ 形态分析失败: ${error.message}`, 'error');
            }
        }
        
        // 市场分析测试
        async function testStockInfo() {
            const symbol = document.getElementById('symbol-input').value || 'AAPL';
            outputResult('market-results', `📈 获取 ${symbol} 股票信息...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/market/stock-info/${symbol}`);
                const data = await response.json();
                outputResult('market-results', `✅ ${symbol} 信息: 市值 $${(data.market_cap/1e9).toFixed(1)}B, PE ${data.pe_ratio}`, 'success');
            } catch (error) {
                outputResult('market-results', `❌ 股票信息获取失败: ${error.message}`, 'error');
            }
        }
        
        async function testRealtimeData() {
            const symbol = document.getElementById('symbol-input').value || 'AAPL';
            const interval = document.getElementById('interval-select').value;
            outputResult('market-results', `📊 获取 ${symbol} 实时数据 (${interval})...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/market/realtime-data/${symbol}?interval=${interval}`);
                const data = await response.json();
                outputResult('market-results', `✅ ${symbol} 实时数据: $${data.current_price} (${data.candles?.length || 0} 个数据点)`, 'success');
            } catch (error) {
                outputResult('market-results', `❌ 实时数据获取失败: ${error.message}`, 'error');
            }
        }
        
        async function testComprehensiveAnalysis() {
            const symbol = document.getElementById('symbol-input').value || 'AAPL';
            const interval = document.getElementById('interval-select').value;
            outputResult('market-results', `🧠 执行 ${symbol} 综合分析...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/market/comprehensive-analysis`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, interval, include_fundamentals: true })
                });
                const data = await response.json();
                const rec = data.recommendation;
                outputResult('market-results', `✅ ${symbol} 分析完成: ${rec?.action} (${rec?.confidence}) - ${rec?.reasoning}`, 'success');
            } catch (error) {
                outputResult('market-results', `❌ 综合分析失败: ${error.message}`, 'error');
            }
        }
        
        async function testWatchlist() {
            const symbol = document.getElementById('symbol-input').value || 'AAPL';
            outputResult('market-results', `📋 管理监控列表...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/market/watchlist?action=add&symbol=${symbol}`, {
                    method: 'POST'
                });
                const data = await response.json();
                outputResult('market-results', `✅ 监控列表: ${data.message} (共 ${data.watchlist_size} 只股票)`, 'success');
            } catch (error) {
                outputResult('market-results', `❌ 监控列表操作失败: ${error.message}`, 'error');
            }
        }
        
        // 智能体测试
        async function testAgentAnalysis() {
            const symbol = document.getElementById('symbol-input').value || 'AAPL';
            outputResult('agent-results', `🤖 启动智能体分析 ${symbol}...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/agents/analyze`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ symbol, analysis_type: 'comprehensive' })
                });
                const data = await response.json();
                outputResult('agent-results', `✅ 智能体分析完成: ${data.status || '分析成功'}`, 'success');
            } catch (error) {
                outputResult('agent-results', `❌ 智能体分析失败: ${error.message}`, 'error');
            }
        }
        
        async function testDebateSystem() {
            outputResult('agent-results', `🎭 启动辩论系统...`, 'loading');
            outputResult('agent-results', `💡 提示: 辩论系统需要在终端中运行 python3 standalone_debate_demo.py`, 'info');
        }
        
        async function testWorkflow() {
            outputResult('agent-results', `🔄 测试工作流...`, 'loading');
            try {
                const response = await fetch(`${API_BASE}/agents/workflow/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ workflow_type: 'investment_decision' })
                });
                const data = await response.json();
                outputResult('agent-results', `✅ 工作流启动: ${data.workflow_id || '成功'}`, 'success');
            } catch (error) {
                outputResult('agent-results', `❌ 工作流启动失败: ${error.message}`, 'error');
            }
        }
        
        function runStandaloneDemo() {
            outputResult('agent-results', `🎪 独立演示说明:`, 'info');
            outputResult('agent-results', `在终端中运行以下命令:`, 'info');
            outputResult('agent-results', `cd /Users/<USER>/Desktop/日本蜡烛图技术`, 'info');
            outputResult('agent-results', `python3 standalone_debate_demo.py`, 'info');
        }
        
        // 工具函数
        function openApiDocs() {
            window.open('http://localhost:8000/docs', '_blank');
        }
        
        function openMainDemo() {
            window.open('demo.html', '_blank');
        }
        
        function clearAllResults() {
            document.getElementById('basic-results').innerHTML = '结果已清空...';
            document.getElementById('market-results').innerHTML = '结果已清空...';
            document.getElementById('agent-results').innerHTML = '结果已清空...';
        }
        
        async function runFullTest() {
            clearAllResults();
            outputResult('basic-results', '🚀 开始完整测试...', 'loading');
            
            // 依次运行所有测试
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testAgentStatus();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testPatternList();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testStockInfo();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testRealtimeData();
            
            outputResult('basic-results', '🎉 完整测试完成！', 'success');
        }
    </script>
</body>
</html>
