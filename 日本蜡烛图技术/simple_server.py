#!/usr/bin/env python3
"""
简单的集成服务器 - 同时提供前端和后端服务
"""

import os
import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import threading
import webbrowser
import time

class CORSHTTPRequestHandler(SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # API路由处理
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            # 静态文件处理
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)
    
    def handle_api_request(self):
        """处理API请求"""
        try:
            if self.path == '/api/v1/health':
                self.send_api_response({
                    "status": "healthy",
                    "message": "蜡烛图形态识别 + TradingAgents 系统运行正常",
                    "timestamp": datetime.now().isoformat(),
                    "features": [
                        "传统蜡烛图形态识别",
                        "TradingAgents多智能体验证",
                        "AI智能体协作分析",
                        "结构化辩论系统"
                    ]
                })
            
            elif self.path == '/api/trading-agents/status':
                self.send_api_response({
                    "status": "success",
                    "message": "TradingAgents系统运行正常",
                    "data": {
                        "status": "running",
                        "initialized": True,
                        "agents": {
                            "total_agents": 6,
                            "active_agents": 6,
                            "agent_details": {
                                "MarketAnalyst": {"status": "active", "specialization": "technical_analysis"},
                                "BullResearcher": {"status": "active", "specialization": "bullish_analysis"},
                                "BearResearcher": {"status": "active", "specialization": "bearish_analysis"},
                                "PortfolioManager": {"status": "active", "specialization": "risk_management"},
                                "DebateModerator": {"status": "active", "specialization": "consensus_building"},
                                "CandlestickAnalyst": {"status": "active", "specialization": "candlestick_analysis"}
                            }
                        },
                        "debate_system": {"status": "active", "active_debates": 0, "total_debates": 5},
                        "pattern_validator": {"status": "active", "initialized": True}
                    },
                    "timestamp": datetime.now().isoformat()
                })
            
            elif self.path == '/api/trading-agents/validate-pattern':
                # 处理POST请求
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    request_data = json.loads(post_data.decode('utf-8'))
                    pattern_name = request_data.get('pattern_name', 'unknown')
                    symbol = request_data.get('symbol', 'UNKNOWN')
                    confidence = request_data.get('confidence', 0.8)
                    
                    # 模拟验证结果
                    validation_result = {
                        "status": "success",
                        "message": "形态验证完成",
                        "data": {
                            "pattern_name": pattern_name,
                            "symbol": symbol,
                            "original_confidence": confidence,
                            "validation_result": {
                                "validation_conclusion": "confirmed",
                                "reliability_level": "high",
                                "final_validation_score": min(1.0, confidence + 0.1),
                                "validation_improvement": 0.1,
                                "recommendations": [f"{pattern_name}形态得到AI确认，可作为交易参考"],
                                "risk_warnings": [],
                                "validation_components": {
                                    "candlestick_expert": confidence * 0.9,
                                    "multi_agent_consensus": confidence * 1.1,
                                    "debate_outcome": confidence
                                }
                            }
                        },
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    self.send_api_response(validation_result)
                    
                except json.JSONDecodeError:
                    self.send_api_error("Invalid JSON data", 400)
            
            elif self.path == '/api/trading-agents/agents':
                self.send_api_response({
                    "status": "success",
                    "message": "智能体列表获取成功",
                    "data": {
                        "total_agents": 6,
                        "active_agents": 6,
                        "agents": [
                            {"name": "MarketAnalyst", "status": "active", "specialization": "technical_analysis", "is_active": True},
                            {"name": "BullResearcher", "status": "active", "specialization": "bullish_analysis", "is_active": True},
                            {"name": "BearResearcher", "status": "active", "specialization": "bearish_analysis", "is_active": True},
                            {"name": "PortfolioManager", "status": "active", "specialization": "risk_management", "is_active": True},
                            {"name": "DebateModerator", "status": "active", "specialization": "consensus_building", "is_active": True},
                            {"name": "CandlestickAnalyst", "status": "active", "specialization": "candlestick_analysis", "is_active": True}
                        ]
                    }
                })
            
            else:
                self.send_api_error("API endpoint not found", 404)
                
        except Exception as e:
            self.send_api_error(f"Internal server error: {str(e)}", 500)
    
    def send_api_response(self, data, status_code=200):
        """发送API响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_api_error(self, message, status_code=500):
        """发送API错误响应"""
        error_data = {
            "error": message,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
        self.send_api_response(error_data, status_code)

def start_server():
    """启动服务器"""
    print("🚀 启动蜡烛图形态识别 + TradingAgents 集成服务器")
    print("=" * 60)
    
    # 切换到前端构建目录
    frontend_build_dir = Path("frontend/build")
    if frontend_build_dir.exists():
        os.chdir(frontend_build_dir)
        print(f"✅ 找到前端构建文件: {frontend_build_dir.absolute()}")
    else:
        print("⚠️ 未找到前端构建文件，将提供基本API服务")
    
    # 启动服务器
    port = 3000
    server_address = ('', port)
    
    try:
        httpd = HTTPServer(server_address, CORSHTTPRequestHandler)
        
        print(f"🌐 服务器启动成功!")
        print(f"📱 前端界面: http://localhost:{port}")
        print(f"🔧 后端API: http://localhost:{port}/api/")
        print(f"💚 健康检查: http://localhost:{port}/api/v1/health")
        print(f"🤖 TradingAgents: http://localhost:{port}/api/trading-agents/status")
        print("=" * 60)
        print("🎯 功能特色:")
        print("✅ 传统蜡烛图形态识别")
        print("✅ TradingAgents多智能体验证")
        print("✅ AI智能体协作分析")
        print("✅ 结构化辩论系统")
        print("✅ 投资建议生成")
        print("=" * 60)
        print("🔄 服务器运行中... (按 Ctrl+C 停止)")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动服务器
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    start_server()
