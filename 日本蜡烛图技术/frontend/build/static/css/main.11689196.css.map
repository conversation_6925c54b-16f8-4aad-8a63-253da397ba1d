{"version": 3, "file": "static/css/main.11689196.css", "mappings": "AAEA,iBAEE,kBAAmB,CACnB,gBAAiB,CAFjB,YAGF,CAEA,kBAGE,kBAAmB,CAGnB,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CAPxC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,iBAIF,CAEA,qBAEE,aAAc,CACd,cAAe,CAFf,QAGF,CAEA,gBACE,eAAiB,CACjB,iBAAkB,CAClB,8BACF,CAEA,yCACE,YACF,CAGA,YAGE,iBAAkB,CAClB,8BAAwC,CAHxC,WAAY,CACZ,uBAGF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,YACE,kBACF,CAEA,cAEE,cAAe,CADf,YAEF,CAEA,mBACE,aAAc,CACd,gBACF,CAGA,mBACE,iBAAkB,CAClB,8BACF,CAEA,cACE,aACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,iBACF,CAEA,uBACE,aACF,CAEA,cACE,aAAc,CACd,cACF,CAEA,iBACE,iBACF,CAEA,mBAEE,aAAc,CACd,cAAe,CAFf,cAGF,CAGA,eAEE,iBAAkB,CAClB,8BAAwC,CAFxC,kBAGF,CAEA,mBACE,kBACF,CAEA,mBACE,eACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,6BAEF,CAEA,gBACE,aAAc,CACd,cACF,CAGA,YAEE,iBAAkB,CAClB,8BAAwC,CAFxC,WAGF,CAEA,WAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAEF,CAEA,sBACE,kBACF,CAEA,YACE,aAAc,CACd,eACF,CAEA,YACE,aAAc,CACd,eACF,CAGA,eACE,cACF,CAEA,aAGE,kBAAmB,CAEnB,+BAAgC,CAJhC,YAAa,CACb,6BAA8B,CAE9B,cAEF,CAEA,wBACE,kBACF,CAEA,eACE,aAAc,CACd,cACF,CAGA,yBAIE,kBAAmB,CAEnB,eAAiB,CACjB,iBAAkB,CAClB,8BAAwC,CAPxC,YAAa,CACb,qBAAsB,CAGtB,YAAa,CAFb,sBAMF,CAEA,2BAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAGA,yBACE,iBACE,YACF,CAEA,kBACE,iBACF,CAEA,qBACE,cACF,CAEA,yCACE,YACF,CAQA,4BAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,0DAIE,6BACF,CAGA,sBAEE,UAAW,CADX,SAEF,CAEA,0BACE,wBACF,CAEA,6BACE,wBACF,CAEA,0BACE,wBACF,CAEA,wBACE,wBACF,CAGA,mBACE,eACF,CAEA,0CAEE,iBACF,CAGA,2BACE,gBACF,CAEA,wBACE,6BACF,CAGA,cACE,eACF,CAEA,qBACE,uBACF,CAGA,qBAEE,aAAc,CADd,eAEF,CAGA,WACE,eACF,CAEA,iBACE,wBAAyB,CACzB,oBACF,CCzTA,KACE,iBACF,CAEA,QACE,gBACF,CAEA,aAGE,kBAAmB,CAFnB,yBAA0B,CAC1B,iBAAkB,CAIlB,cAAe,CAFf,YAAa,CACb,iBAAkB,CAElB,2BACF,CAEA,mBACE,oBACF,CAEA,sBAEE,kBAAmB,CADnB,oBAEF,CAEA,cAEE,6BAA8B,CAD9B,kBAEF,CAEA,sBACE,yBACF,CAEA,sBACE,yBACF,CAEA,sBACE,yBACF,CAEA,gBAEE,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAIX,cAAe,CADf,eAEF,CAEA,iBACE,WAAY,CACZ,yBACF,CAEA,sBACE,kBACF,CAEA,wBACE,kBACF,CAEA,qBACE,kBACF,CAEA,iBAEE,YAAa,CACb,aAAc,CAFd,UAGF,CAEA,iBACE,YAAa,CACb,4BAA6B,CAC7B,kBACF,CAEA,cAGE,kBAAmB,CACnB,iBAAkB,CAClB,eAAgB,CAHhB,YAAa,CADb,iBAKF,CAEA,gBAGE,aAAc,CAFd,cAAe,CACf,eAEF,CAEA,eAEE,UAAW,CADX,cAAe,CAEf,cACF,CAEA,YACE,eACF,CAEA,oBACE,eACF,CAEA,qBACE,UAAW,CACX,cAAe,CAEf,eAAgB,CADhB,cAEF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,eACF,CAEA,cACE,UAAW,CACX,cACF,CAEA,oBACE,eACF,CAEA,eAGE,kBAAmB,CAEnB,UAAY,CAJZ,oBAAqB,CAGrB,cAAe,CAEf,eAAgB,CAJhB,eAKF,CAEA,uBACE,kBACF,CAEA,yBACE,kBACF,CAEA,wBACE,kBACF,CAEA,uBACE,kBAAmB,CACnB,UACF,CC3JA,UAGE,WAAY,CADZ,UAEF,CACA,mCAEE,YACF,CACA,iBAGE,qBACF,CACA,KAGE,6BAA8B,CAC9B,yBAA0B,CAC1B,4BAA6B,CAC7B,yCAA6C,CAL7C,sBAAuB,CACvB,gBAKF,CAIA,KACE,QACF,CACA,sBACE,YACF,CACA,GACE,kBAAuB,CACvB,QAAS,CACT,gBACF,CACA,kBAQE,eAAgB,CADhB,kBAAoB,CADpB,YAGF,CACA,EAEE,iBAAkB,CADlB,YAEF,CACA,sCAIE,eAAgB,CAChB,WAAY,CAHZ,wCAAyC,CACzC,gCAGF,CACA,QAEE,iBAAkB,CAClB,mBAAoB,CAFpB,iBAGF,CACA,kEAIE,uBACF,CACA,SAIE,iBAAkB,CADlB,YAEF,CACA,wBAIE,eACF,CACA,GACE,eACF,CACA,GACE,kBAAoB,CACpB,aACF,CACA,WACE,cACF,CACA,IACE,iBACF,CACA,SAEE,kBACF,CACA,MACE,aACF,CACA,QAGE,aAAc,CACd,aAAc,CAFd,iBAAkB,CAGlB,sBACF,CACA,IACE,aACF,CACA,IACE,SACF,CACA,kBAKE,2EAAqF,CADrF,aAEF,CACA,IAEE,iBAAkB,CADlB,YAAa,CAEb,aACF,CACA,OACE,cACF,CACA,IAEE,iBAAkB,CADlB,qBAEF,CACA,kFASE,yBACF,CACA,MACE,wBACF,CACA,QAIE,mBAAoB,CAFpB,mBAAqB,CADrB,iBAAmB,CAEnB,eAEF,CACA,sCAME,aAAc,CAEd,mBAAoB,CADpB,iBAAkB,CAElB,mBAAoB,CAJpB,QAKF,CACA,aAEE,gBACF,CACA,cAEE,mBACF,CACA,qDAIE,yBACF,CACA,wHAKE,iBAAkB,CADlB,SAEF,CACA,uCAEE,qBAAsB,CACtB,SACF,CACA,+EAIE,0BACF,CACA,SACE,aAAc,CACd,eACF,CACA,SAIE,QAAS,CAFT,QAAS,CADT,WAAY,CAEZ,SAEF,CACA,OAME,aAAc,CALd,aAAc,CAMd,eAAgB,CAChB,mBAAoB,CAJpB,kBAAoB,CADpB,cAAe,CAEf,SAAU,CAIV,kBAAmB,CAPnB,UAQF,CACA,SACE,sBACF,CACA,kFAEE,WACF,CACA,cAEE,uBAAwB,CADxB,mBAEF,CACA,qFAEE,uBACF,CACA,6BAEE,yBAA0B,CAD1B,YAEF,CACA,OACE,oBACF,CACA,QACE,iBACF,CACA,SACE,YACF,CACA,SACE,sBACF,CACA,KAEE,wBAAyB,CADzB,YAEF", "sources": ["components/AgentWorkspace.css", "App.css", "../node_modules/antd/dist/reset.css"], "sourcesContent": ["/* 智能体工作台样式 */\n\n.agent-workspace {\n  padding: 20px;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.workspace-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 16px 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.workspace-header h2 {\n  margin: 0;\n  color: #1890ff;\n  font-size: 24px;\n}\n\n.workspace-tabs {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.workspace-tabs .ant-tabs-content-holder {\n  padding: 20px;\n}\n\n/* 智能体卡片样式 */\n.agent-card {\n  height: 100%;\n  transition: all 0.3s ease;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.agent-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n.agent-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.agent-info {\n  margin-bottom: 16px;\n}\n\n.agent-info p {\n  margin: 8px 0;\n  font-size: 14px;\n}\n\n.agent-info strong {\n  color: #595959;\n  margin-right: 8px;\n}\n\n/* 消息流样式 */\n.message-flow-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.message-item {\n  padding: 8px 0;\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.message-header strong {\n  color: #1890ff;\n}\n\n.message-time {\n  color: #8c8c8c;\n  font-size: 12px;\n}\n\n.message-content {\n  padding-left: 16px;\n}\n\n.message-content p {\n  margin: 4px 0 0 0;\n  color: #595959;\n  font-size: 14px;\n}\n\n/* 工作流样式 */\n.workflow-card {\n  margin-bottom: 16px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.workflow-progress {\n  margin-bottom: 20px;\n}\n\n.workflow-timeline {\n  margin-top: 16px;\n}\n\n.stage-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.stage-duration {\n  color: #8c8c8c;\n  font-size: 12px;\n}\n\n/* 统计卡片样式 */\n.stats-card {\n  height: 100%;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.stat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.stat-item:last-child {\n  border-bottom: none;\n}\n\n.stat-label {\n  color: #595959;\n  font-weight: 500;\n}\n\n.stat-value {\n  color: #1890ff;\n  font-weight: 600;\n}\n\n/* 系统状态样式 */\n.system-status {\n  padding: 16px 0;\n}\n\n.status-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.status-item:last-child {\n  border-bottom: none;\n}\n\n.status-detail {\n  color: #8c8c8c;\n  font-size: 12px;\n}\n\n/* 加载状态样式 */\n.agent-workspace-loading {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.agent-workspace-loading p {\n  margin-top: 16px;\n  color: #8c8c8c;\n  font-size: 16px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .agent-workspace {\n    padding: 10px;\n  }\n  \n  .workspace-header {\n    padding: 12px 16px;\n  }\n  \n  .workspace-header h2 {\n    font-size: 20px;\n  }\n  \n  .workspace-tabs .ant-tabs-content-holder {\n    padding: 16px;\n  }\n  \n  .message-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n  \n  .stage-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.agent-card,\n.message-flow-card,\n.workflow-card,\n.stats-card {\n  animation: fadeIn 0.5s ease-out;\n}\n\n/* 状态指示器 */\n.ant-badge-status-dot {\n  width: 8px;\n  height: 8px;\n}\n\n.ant-badge-status-success {\n  background-color: #52c41a;\n}\n\n.ant-badge-status-processing {\n  background-color: #1890ff;\n}\n\n.ant-badge-status-warning {\n  background-color: #faad14;\n}\n\n.ant-badge-status-error {\n  background-color: #ff4d4f;\n}\n\n/* 进度条自定义样式 */\n.ant-progress-line {\n  margin-bottom: 0;\n}\n\n.ant-progress-success-bg,\n.ant-progress-bg {\n  border-radius: 4px;\n}\n\n/* 时间线自定义样式 */\n.ant-timeline-item-content {\n  margin-left: 20px;\n}\n\n.ant-timeline-item-tail {\n  border-left: 2px solid #f0f0f0;\n}\n\n/* 标签页自定义样式 */\n.ant-tabs-tab {\n  font-weight: 500;\n}\n\n.ant-tabs-tab-active {\n  color: #1890ff !important;\n}\n\n/* 卡片标题样式 */\n.ant-card-head-title {\n  font-weight: 600;\n  color: #262626;\n}\n\n/* 徽章样式 */\n.ant-badge {\n  font-weight: 500;\n}\n\n.ant-badge-count {\n  background-color: #1890ff;\n  border-color: #1890ff;\n}\n", ".App {\n  text-align: center;\n}\n\n.layout {\n  min-height: 100vh;\n}\n\n.upload-area {\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  background: #fafafa;\n  padding: 40px;\n  text-align: center;\n  cursor: pointer;\n  transition: border-color 0.3s;\n}\n\n.upload-area:hover {\n  border-color: #1890ff;\n}\n\n.upload-area.dragover {\n  border-color: #1890ff;\n  background: #e6f7ff;\n}\n\n.pattern-card {\n  margin-bottom: 16px;\n  border-left: 4px solid #1890ff;\n}\n\n.pattern-card.bullish {\n  border-left-color: #52c41a;\n}\n\n.pattern-card.bearish {\n  border-left-color: #ff4d4f;\n}\n\n.pattern-card.neutral {\n  border-left-color: #faad14;\n}\n\n.confidence-bar {\n  height: 8px;\n  background: #f0f0f0;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-top: 8px;\n}\n\n.confidence-fill {\n  height: 100%;\n  transition: width 0.3s ease;\n}\n\n.confidence-fill.high {\n  background: #52c41a;\n}\n\n.confidence-fill.medium {\n  background: #faad14;\n}\n\n.confidence-fill.low {\n  background: #ff4d4f;\n}\n\n.chart-container {\n  width: 100%;\n  height: 400px;\n  margin: 20px 0;\n}\n\n.pattern-summary {\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: 20px;\n}\n\n.summary-item {\n  text-align: center;\n  padding: 16px;\n  background: #f5f5f5;\n  border-radius: 8px;\n  min-width: 100px;\n}\n\n.summary-number {\n  font-size: 24px;\n  font-weight: bold;\n  color: #1890ff;\n}\n\n.summary-label {\n  font-size: 12px;\n  color: #666;\n  margin-top: 4px;\n}\n\n.data-table {\n  margin-top: 20px;\n}\n\n.sample-data-button {\n  margin-top: 16px;\n}\n\n.pattern-description {\n  color: #666;\n  font-size: 14px;\n  margin-top: 8px;\n  line-height: 1.5;\n}\n\n.pattern-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 12px;\n}\n\n.pattern-time {\n  color: #999;\n  font-size: 12px;\n}\n\n.pattern-confidence {\n  font-weight: bold;\n}\n\n.trend-context {\n  display: inline-block;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  color: white;\n  margin-left: 8px;\n}\n\n.trend-context.uptrend {\n  background: #52c41a;\n}\n\n.trend-context.downtrend {\n  background: #ff4d4f;\n}\n\n.trend-context.sideways {\n  background: #faad14;\n}\n\n.trend-context.unknown {\n  background: #d9d9d9;\n  color: #666;\n}\n", "/* stylelint-disable */\nhtml,\nbody {\n  width: 100%;\n  height: 100%;\n}\ninput::-ms-clear,\ninput::-ms-reveal {\n  display: none;\n}\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -ms-text-size-adjust: 100%;\n  -ms-overflow-style: scrollbar;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n@-ms-viewport {\n  width: device-width;\n}\nbody {\n  margin: 0;\n}\n[tabindex='-1']:focus {\n  outline: none;\n}\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin-top: 0;\n  margin-bottom: 0.5em;\n  font-weight: 500;\n}\np {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nabbr[title],\nabbr[data-original-title] {\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n  border-bottom: 0;\n  cursor: help;\n}\naddress {\n  margin-bottom: 1em;\n  font-style: normal;\n  line-height: inherit;\n}\ninput[type='text'],\ninput[type='password'],\ninput[type='number'],\ntextarea {\n  -webkit-appearance: none;\n}\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\ndt {\n  font-weight: 500;\n}\ndd {\n  margin-bottom: 0.5em;\n  margin-left: 0;\n}\nblockquote {\n  margin: 0 0 1em;\n}\ndfn {\n  font-style: italic;\n}\nb,\nstrong {\n  font-weight: bolder;\n}\nsmall {\n  font-size: 80%;\n}\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\npre,\ncode,\nkbd,\nsamp {\n  font-size: 1em;\n  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n}\npre {\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow: auto;\n}\nfigure {\n  margin: 0 0 1em;\n}\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\na,\narea,\nbutton,\n[role='button'],\ninput:not([type='range']),\nlabel,\nselect,\nsummary,\ntextarea {\n  touch-action: manipulation;\n}\ntable {\n  border-collapse: collapse;\n}\ncaption {\n  padding-top: 0.75em;\n  padding-bottom: 0.3em;\n  text-align: left;\n  caption-side: bottom;\n}\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n}\nbutton,\ninput {\n  overflow: visible;\n}\nbutton,\nselect {\n  text-transform: none;\n}\nbutton,\nhtml [type='button'],\n[type='reset'],\n[type='submit'] {\n  -webkit-appearance: button;\n}\nbutton::-moz-focus-inner,\n[type='button']::-moz-focus-inner,\n[type='reset']::-moz-focus-inner,\n[type='submit']::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\ninput[type='radio'],\ninput[type='checkbox'] {\n  box-sizing: border-box;\n  padding: 0;\n}\ninput[type='date'],\ninput[type='time'],\ninput[type='datetime-local'],\ninput[type='month'] {\n  -webkit-appearance: listbox;\n}\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\nfieldset {\n  min-width: 0;\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 0.5em;\n  padding: 0;\n  color: inherit;\n  font-size: 1.5em;\n  line-height: inherit;\n  white-space: normal;\n}\nprogress {\n  vertical-align: baseline;\n}\n[type='number']::-webkit-inner-spin-button,\n[type='number']::-webkit-outer-spin-button {\n  height: auto;\n}\n[type='search'] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n[type='search']::-webkit-search-cancel-button,\n[type='search']::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\noutput {\n  display: inline-block;\n}\nsummary {\n  display: list-item;\n}\ntemplate {\n  display: none;\n}\n[hidden] {\n  display: none !important;\n}\nmark {\n  padding: 0.2em;\n  background-color: #feffe6;\n}\n"], "names": [], "sourceRoot": ""}