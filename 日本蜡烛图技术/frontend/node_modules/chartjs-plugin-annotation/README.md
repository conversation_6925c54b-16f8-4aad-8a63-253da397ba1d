# chartjs-plugin-annotation.js

[![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/chartjs/chartjs-plugin-annotation/ci.yml?branch=master)](https://github.com/chartjs/chartjs-plugin-annotation/actions/workflows/ci.yml)
[![Coverage Status](https://coveralls.io/repos/github/chartjs/chartjs-plugin-annotation/badge.svg?branch=master)](https://coveralls.io/github/chartjs/chartjs-plugin-annotation?branch=master)
[![release](https://img.shields.io/github/v/release/chartjs/chartjs-plugin-annotation?include_prereleases)](https://github.com/chartjs/chartjs-plugin-annotation/releases)
[![npm (latest)](https://img.shields.io/npm/v/chartjs-plugin-annotation/latest)](https://www.npmjs.com/package/chartjs-plugin-annotation/v/latest)
[![documentation](https://img.shields.io/static/v1?message=Documentation&color=informational)](https://www.chartjs.org/chartjs-plugin-annotation/index)
<a href="https://github.com/chartjs/awesome"><img src="https://awesome.re/badge-flat2.svg" alt="Awesome"></a>

An annotation plugin for Chart.js >= 4.0.0

---
> This plugin needs to be registered. It does not function as inline plugin.
---

For Chart.js 3.7.0 to 3.9.1 support, use [version 2.2.1 of this plugin](https://github.com/chartjs/chartjs-plugin-annotation/releases/tag/v2.2.1)

For Chart.js 3.0.0 to 3.6.2 support, use [version 1.4.0 of this plugin](https://github.com/chartjs/chartjs-plugin-annotation/releases/tag/v1.4.0)

For Chart.js 2.4.0 to 2.9.x support, use [version 0.5.7 of this plugin](https://github.com/chartjs/chartjs-plugin-annotation/releases/tag/v0.5.7)

This plugin draws lines, boxes, points, labels, polygons and ellipses on the chart area.

Annotations work with line, bar, scatter and bubble charts that use linear, logarithmic, time, or category scales. 
Furthermore you can use a doughnut label annotation which can be used to add contents (text, image, canvas) in the middle area of the doughnut charts.

![Example Screenshot](docs/guide/banner.png)

[View this example](https://www.chartjs.org/chartjs-plugin-annotation/latest/samples/intro.html)

## Documentation

You can find documentation for chartjs-plugin-annotation at [www.chartjs.org/chartjs-plugin-annotation](https://www.chartjs.org/chartjs-plugin-annotation/index).

## Contributing

Before submitting an issue or a pull request to the project, please take a moment to look over the [contributing guidelines](CONTRIBUTING.md) first.

## License

Chart.Annotation.js is available under the [MIT license](LICENSE.md).
