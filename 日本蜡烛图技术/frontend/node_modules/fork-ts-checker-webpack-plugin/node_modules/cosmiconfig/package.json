{"name": "cosmiconfig", "version": "6.0.0", "description": "Find and load configuration from a package.json property, rc file, or CommonJS module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "del-cli --dot=true \"./dist/**/*\"", "build": "npm run clean && npm run build:compile && npm run build:types", "build:compile": "cross-env NODE_ENV=production babel src -d dist --verbose --extensions .js,.ts --ignore \"**/**/*.test.js\",\"**/**/*.test.ts\" --source-maps", "build:types": "cross-env NODE_ENV=production tsc --project tsconfig.types.json", "dev": "npm run clean && npm run build:compile -- --watch", "lint": "eslint --ext .js,.ts . && npm run lint:md", "lint:fix": "eslint --ext .js,.ts . --fix", "lint:md": "remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "format": "prettier \"**/*.{js,ts,json,yml,yaml}\" --write", "format:md": "remark-preset-david<PERSON><PERSON><PERSON>k --format", "format:check": "prettier \"**/*.{js,ts,json,yml,yaml}\" --check", "typescript": "tsc", "test": "jest --coverage", "test:watch": "jest --watch", "check:all": "npm run test && npm run typescript && npm run lint && npm run format:check", "prepublishOnly": "npm run check:all && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged && npm run typescript && npm run test", "pre-push": "npm run check:all"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write", "git add"], "*.{json,yml,yaml}": ["prettier --write", "git add"], "*.md": ["remark-preset-da<PERSON><PERSON><PERSON><PERSON><PERSON>", "remark-preset-david<PERSON><PERSON><PERSON>k --format", "git add"]}, "repository": {"type": "git", "url": "git+https://github.com/davidtheclark/cosmiconfig.git"}, "keywords": ["load", "configuration", "config"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/davidtheclark/cosmiconfig/issues"}, "homepage": "https://github.com/davidtheclark/cosmiconfig#readme", "prettier": {"trailingComma": "all", "arrowParens": "always", "singleQuote": true, "printWidth": 80, "tabWidth": 2}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{js,ts}"], "coverageReporters": ["text", "html", "lcov"], "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "resetModules": true, "resetMocks": true, "restoreMocks": true}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "8.9"}}], "@babel/preset-typescript"]}, "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.1.0", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.7.2"}, "devDependencies": {"@babel/cli": "^7.6.4", "@babel/core": "^7.6.4", "@babel/preset-env": "^7.6.3", "@babel/preset-typescript": "^7.6.0", "@types/jest": "^24.0.19", "@types/node": "^12.11.5", "@typescript-eslint/eslint-plugin": "^2.5.0", "@typescript-eslint/parser": "^2.5.0", "cross-env": "^6.0.3", "del": "^5.1.0", "del-cli": "^3.0.0", "eslint": "^6.5.1", "eslint-config-davidtheclark-node": "^0.2.2", "eslint-config-prettier": "^6.4.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.20.0", "eslint-plugin-node": "^10.0.0", "husky": "^3.0.9", "jest": "^24.9.0", "lint-staged": "^9.4.2", "make-dir": "^3.0.0", "parent-module": "^2.0.0", "prettier": "^1.18.2", "remark-preset-davidtheclark": "^0.10.0", "typescript": "^3.6.4"}, "engines": {"node": ">=8"}}