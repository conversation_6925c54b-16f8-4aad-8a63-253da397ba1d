"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.loaders = void 0;

/* eslint-disable @typescript-eslint/no-require-imports */
let importFresh;

const loadJs = function loadJs(filepath) {
  if (importFresh === undefined) {
    importFresh = require('import-fresh');
  }

  const result = importFresh(filepath);
  return result;
};

let parseJson;

const loadJson = function loadJson(filepath, content) {
  if (parseJson === undefined) {
    parseJson = require('parse-json');
  }

  try {
    const result = parseJson(content);
    return result;
  } catch (error) {
    error.message = `JSON Error in ${filepath}:\n${error.message}`;
    throw error;
  }
};

let yaml;

const loadYaml = function loadYaml(filepath, content) {
  if (yaml === undefined) {
    yaml = require('yaml');
  }

  try {
    const result = yaml.parse(content, {
      prettyErrors: true
    });
    return result;
  } catch (error) {
    error.message = `YAML Error in ${filepath}:\n${error.message}`;
    throw error;
  }
};

const loaders = {
  loadJs,
  loadJson,
  loadYaml
};
exports.loaders = loaders;
//# sourceMappingURL=loaders.js.map