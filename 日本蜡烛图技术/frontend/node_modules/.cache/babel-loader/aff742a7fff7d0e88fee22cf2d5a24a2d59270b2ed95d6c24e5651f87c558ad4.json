{"ast": null, "code": "import { useRef, useEffect } from 'react';\nexport var defaultProps = {\n  percent: 0,\n  prefixCls: 'rc-progress',\n  strokeColor: '#2db7f5',\n  strokeLinecap: 'round',\n  strokeWidth: 1,\n  trailColor: '#D9D9D9',\n  trailWidth: 1,\n  gapPosition: 'bottom'\n};\nexport var useTransitionDuration = function useTransitionDuration() {\n  var pathsRef = useRef([]);\n  var prevTimeStamp = useRef(null);\n  useEffect(function () {\n    var now = Date.now();\n    var updated = false;\n    pathsRef.current.forEach(function (path) {\n      if (!path) {\n        return;\n      }\n      updated = true;\n      var pathStyle = path.style;\n      pathStyle.transitionDuration = '.3s, .3s, .3s, .06s';\n      if (prevTimeStamp.current && now - prevTimeStamp.current < 100) {\n        pathStyle.transitionDuration = '0s, 0s';\n      }\n    });\n    if (updated) {\n      prevTimeStamp.current = Date.now();\n    }\n  });\n  return pathsRef.current;\n};", "map": {"version": 3, "names": ["useRef", "useEffect", "defaultProps", "percent", "prefixCls", "strokeColor", "strokeLinecap", "strokeWidth", "trailColor", "trailWidth", "gapPosition", "useTransitionDuration", "pathsRef", "prevTimeStamp", "now", "Date", "updated", "current", "for<PERSON>ach", "path", "pathStyle", "style", "transitionDuration"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/rc-progress/es/common.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nexport var defaultProps = {\n  percent: 0,\n  prefixCls: 'rc-progress',\n  strokeColor: '#2db7f5',\n  strokeLinecap: 'round',\n  strokeWidth: 1,\n  trailColor: '#D9D9D9',\n  trailWidth: 1,\n  gapPosition: 'bottom'\n};\nexport var useTransitionDuration = function useTransitionDuration() {\n  var pathsRef = useRef([]);\n  var prevTimeStamp = useRef(null);\n  useEffect(function () {\n    var now = Date.now();\n    var updated = false;\n    pathsRef.current.forEach(function (path) {\n      if (!path) {\n        return;\n      }\n      updated = true;\n      var pathStyle = path.style;\n      pathStyle.transitionDuration = '.3s, .3s, .3s, .06s';\n      if (prevTimeStamp.current && now - prevTimeStamp.current < 100) {\n        pathStyle.transitionDuration = '0s, 0s';\n      }\n    });\n    if (updated) {\n      prevTimeStamp.current = Date.now();\n    }\n  });\n  return pathsRef.current;\n};"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAO,IAAIC,YAAY,GAAG;EACxBC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,aAAa;EACxBC,WAAW,EAAE,SAAS;EACtBC,aAAa,EAAE,OAAO;EACtBC,WAAW,EAAE,CAAC;EACdC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;EAClE,IAAIC,QAAQ,GAAGZ,MAAM,CAAC,EAAE,CAAC;EACzB,IAAIa,aAAa,GAAGb,MAAM,CAAC,IAAI,CAAC;EAChCC,SAAS,CAAC,YAAY;IACpB,IAAIa,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACpB,IAAIE,OAAO,GAAG,KAAK;IACnBJ,QAAQ,CAACK,OAAO,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACvC,IAAI,CAACA,IAAI,EAAE;QACT;MACF;MACAH,OAAO,GAAG,IAAI;MACd,IAAII,SAAS,GAAGD,IAAI,CAACE,KAAK;MAC1BD,SAAS,CAACE,kBAAkB,GAAG,qBAAqB;MACpD,IAAIT,aAAa,CAACI,OAAO,IAAIH,GAAG,GAAGD,aAAa,CAACI,OAAO,GAAG,GAAG,EAAE;QAC9DG,SAAS,CAACE,kBAAkB,GAAG,QAAQ;MACzC;IACF,CAAC,CAAC;IACF,IAAIN,OAAO,EAAE;MACXH,aAAa,CAACI,OAAO,GAAGF,IAAI,CAACD,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAOF,QAAQ,CAACK,OAAO;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}