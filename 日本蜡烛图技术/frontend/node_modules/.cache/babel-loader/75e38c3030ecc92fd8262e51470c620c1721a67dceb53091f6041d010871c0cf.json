{"ast": null, "code": "import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}", "map": {"version": 3, "names": ["formatOffset", "signedOffset", "Zone", "singleton", "FixedOffsetZone", "utcInstance", "instance", "offset", "parseSpecifier", "s", "r", "match", "constructor", "fixed", "type", "name", "<PERSON><PERSON><PERSON><PERSON>", "offsetName", "ts", "format", "isUniversal", "equals", "otherZone", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/zones/fixedOffsetZone.js"], "sourcesContent": ["import { formatOffset, signedOffset } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * A zone with a fixed offset (meaning no DST)\n * @implements {Zone}\n */\nexport default class FixedOffsetZone extends Zone {\n  /**\n   * Get a singleton instance of UTC\n   * @return {FixedOffsetZone}\n   */\n  static get utcInstance() {\n    if (singleton === null) {\n      singleton = new FixedOffsetZone(0);\n    }\n    return singleton;\n  }\n\n  /**\n   * Get an instance with a specified offset\n   * @param {number} offset - The offset in minutes\n   * @return {FixedOffsetZone}\n   */\n  static instance(offset) {\n    return offset === 0 ? FixedOffsetZone.utcInstance : new FixedOffsetZone(offset);\n  }\n\n  /**\n   * Get an instance of FixedOffsetZone from a UTC offset string, like \"UTC+6\"\n   * @param {string} s - The offset string to parse\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+6\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC+06\")\n   * @example FixedOffsetZone.parseSpecifier(\"UTC-6:00\")\n   * @return {FixedOffsetZone}\n   */\n  static parseSpecifier(s) {\n    if (s) {\n      const r = s.match(/^utc(?:([+-]\\d{1,2})(?::(\\d{2}))?)?$/i);\n      if (r) {\n        return new FixedOffsetZone(signedOffset(r[1], r[2]));\n      }\n    }\n    return null;\n  }\n\n  constructor(offset) {\n    super();\n    /** @private **/\n    this.fixed = offset;\n  }\n\n  /**\n   * The type of zone. `fixed` for all instances of `FixedOffsetZone`.\n   * @override\n   * @type {string}\n   */\n  get type() {\n    return \"fixed\";\n  }\n\n  /**\n   * The name of this zone.\n   * All fixed zones' names always start with \"UTC\" (plus optional offset)\n   * @override\n   * @type {string}\n   */\n  get name() {\n    return this.fixed === 0 ? \"UTC\" : `UTC${formatOffset(this.fixed, \"narrow\")}`;\n  }\n\n  /**\n   * The IANA name of this zone, i.e. `Etc/UTC` or `Etc/GMT+/-nn`\n   *\n   * @override\n   * @type {string}\n   */\n  get ianaName() {\n    if (this.fixed === 0) {\n      return \"Etc/UTC\";\n    } else {\n      return `Etc/GMT${formatOffset(-this.fixed, \"narrow\")}`;\n    }\n  }\n\n  /**\n   * Returns the offset's common name at the specified timestamp.\n   *\n   * For fixed offset zones this equals to the zone name.\n   * @override\n   */\n  offsetName() {\n    return this.name;\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @override\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    return formatOffset(this.fixed, format);\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year:\n   * Always returns true for all fixed offset zones.\n   * @override\n   * @type {boolean}\n   */\n  get isUniversal() {\n    return true;\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   *\n   * For fixed offset zones, this is constant and does not depend on a timestamp.\n   * @override\n   * @return {number}\n   */\n  offset() {\n    return this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone (i.e. also fixed and same offset)\n   * @override\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    return otherZone.type === \"fixed\" && otherZone.fixed === this.fixed;\n  }\n\n  /**\n   * Return whether this Zone is valid:\n   * All fixed offset zones are valid.\n   * @override\n   * @type {boolean}\n   */\n  get isValid() {\n    return true;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,QAAQ,iBAAiB;AAC5D,OAAOC,IAAI,MAAM,YAAY;AAE7B,IAAIC,SAAS,GAAG,IAAI;;AAEpB;AACA;AACA;AACA;AACA,eAAe,MAAMC,eAAe,SAASF,IAAI,CAAC;EAChD;AACF;AACA;AACA;EACE,WAAWG,WAAWA,CAAA,EAAG;IACvB,IAAIF,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAG,IAAIC,eAAe,CAAC,CAAC,CAAC;IACpC;IACA,OAAOD,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOG,QAAQA,CAACC,MAAM,EAAE;IACtB,OAAOA,MAAM,KAAK,CAAC,GAAGH,eAAe,CAACC,WAAW,GAAG,IAAID,eAAe,CAACG,MAAM,CAAC;EACjF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,cAAcA,CAACC,CAAC,EAAE;IACvB,IAAIA,CAAC,EAAE;MACL,MAAMC,CAAC,GAAGD,CAAC,CAACE,KAAK,CAAC,uCAAuC,CAAC;MAC1D,IAAID,CAAC,EAAE;QACL,OAAO,IAAIN,eAAe,CAACH,YAAY,CAACS,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD;IACF;IACA,OAAO,IAAI;EACb;EAEAE,WAAWA,CAACL,MAAM,EAAE;IAClB,KAAK,CAAC,CAAC;IACP;IACA,IAAI,CAACM,KAAK,GAAGN,MAAM;EACrB;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIO,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,MAAMb,YAAY,CAAC,IAAI,CAACa,KAAK,EAAE,QAAQ,CAAC,EAAE;EAC9E;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,QAAQA,CAAA,EAAG;IACb,IAAI,IAAI,CAACH,KAAK,KAAK,CAAC,EAAE;MACpB,OAAO,SAAS;IAClB,CAAC,MAAM;MACL,OAAO,UAAUb,YAAY,CAAC,CAAC,IAAI,CAACa,KAAK,EAAE,QAAQ,CAAC,EAAE;IACxD;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEI,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACF,IAAI;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEf,YAAYA,CAACkB,EAAE,EAAEC,MAAM,EAAE;IACvB,OAAOnB,YAAY,CAAC,IAAI,CAACa,KAAK,EAAEM,MAAM,CAAC;EACzC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEb,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACM,KAAK;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEQ,MAAMA,CAACC,SAAS,EAAE;IAChB,OAAOA,SAAS,CAACR,IAAI,KAAK,OAAO,IAAIQ,SAAS,CAACT,KAAK,KAAK,IAAI,CAACA,KAAK;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIU,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}