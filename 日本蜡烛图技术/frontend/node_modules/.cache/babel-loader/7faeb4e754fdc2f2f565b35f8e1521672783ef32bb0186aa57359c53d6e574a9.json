{"ast": null, "code": "import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}", "map": {"version": 3, "names": ["ZoneIsAbstractError", "Zone", "type", "name", "<PERSON><PERSON><PERSON><PERSON>", "isUniversal", "offsetName", "ts", "opts", "formatOffset", "format", "offset", "equals", "otherZone", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/zone.js"], "sourcesContent": ["import { ZoneIsAbstractError } from \"./errors.js\";\n\n/**\n * @interface\n */\nexport default class Zone {\n  /**\n   * The type of zone\n   * @abstract\n   * @type {string}\n   */\n  get type() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The name of this zone.\n   * @abstract\n   * @type {string}\n   */\n  get name() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * The IANA name of this zone.\n   * Defaults to `name` if not overwritten by a subclass.\n   * @abstract\n   * @type {string}\n   */\n  get ianaName() {\n    return this.name;\n  }\n\n  /**\n   * Returns whether the offset is known to be fixed for the whole year.\n   * @abstract\n   * @type {boolean}\n   */\n  get isUniversal() {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's common name (such as EST) at the specified timestamp\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the name\n   * @param {Object} opts - Options to affect the format\n   * @param {string} opts.format - What style of offset to return. Accepts 'long' or 'short'.\n   * @param {string} opts.locale - What locale to return the offset name in.\n   * @return {string}\n   */\n  offsetName(ts, opts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Returns the offset's value as a string\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to get the offset\n   * @param {string} format - What style of offset to return.\n   *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n   * @return {string}\n   */\n  formatOffset(ts, format) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return the offset in minutes for this zone at the specified timestamp.\n   * @abstract\n   * @param {number} ts - Epoch milliseconds for which to compute the offset\n   * @return {number}\n   */\n  offset(ts) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is equal to another zone\n   * @abstract\n   * @param {Zone} otherZone - the zone to compare\n   * @return {boolean}\n   */\n  equals(otherZone) {\n    throw new ZoneIsAbstractError();\n  }\n\n  /**\n   * Return whether this Zone is valid.\n   * @abstract\n   * @type {boolean}\n   */\n  get isValid() {\n    throw new ZoneIsAbstractError();\n  }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAa;;AAEjD;AACA;AACA;AACA,eAAe,MAAMC,IAAI,CAAC;EACxB;AACF;AACA;AACA;AACA;EACE,IAAIC,IAAIA,CAAA,EAAG;IACT,MAAM,IAAIF,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIG,IAAIA,CAAA,EAAG;IACT,MAAM,IAAIH,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAII,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,IAAI;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIE,WAAWA,CAAA,EAAG;IAChB,MAAM,IAAIL,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,UAAUA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACnB,MAAM,IAAIR,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACES,YAAYA,CAACF,EAAE,EAAEG,MAAM,EAAE;IACvB,MAAM,IAAIV,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEW,MAAMA,CAACJ,EAAE,EAAE;IACT,MAAM,IAAIP,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEY,MAAMA,CAACC,SAAS,EAAE;IAChB,MAAM,IAAIb,mBAAmB,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIc,OAAOA,CAAA,EAAG;IACZ,MAAM,IAAId,mBAAmB,CAAC,CAAC;EACjC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}