{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Input,Select,Button,List,Tag,Space,Typography,Spin,Alert,Row,Col,Statistic,Divider}from'antd';import{SearchOutlined,StockOutlined,RiseOutlined as TrendingUpOutlined,FallOutlined as TrendingDownOutlined,ReloadOutlined,DollarOutlined}from'@ant-design/icons';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Option}=Select;const{Title,Text}=Typography;const StockSelector=_ref=>{let{onStockSelect,loading}=_ref;const[searchQuery,setSearchQuery]=useState('');const[searchResults,setSearchResults]=useState([]);const[selectedStock,setSelectedStock]=useState(null);const[stockData,setStockData]=useState(null);const[period,setPeriod]=useState('1y');const[searching,setSearching]=useState(false);const[loadingData,setLoadingData]=useState(false);const[error,setError]=useState(null);const[marketStatus,setMarketStatus]=useState(null);// 热门股票列表\nconst popularStocks=[{symbol:'AAPL',name:'Apple Inc.',exchange:'NASDAQ'},{symbol:'MSFT',name:'Microsoft Corporation',exchange:'NASDAQ'},{symbol:'GOOGL',name:'Alphabet Inc.',exchange:'NASDAQ'},{symbol:'AMZN',name:'Amazon.com Inc.',exchange:'NASDAQ'},{symbol:'TSLA',name:'Tesla Inc.',exchange:'NASDAQ'},{symbol:'META',name:'Meta Platforms Inc.',exchange:'NASDAQ'},{symbol:'NVDA',name:'NVIDIA Corporation',exchange:'NASDAQ'},{symbol:'JPM',name:'JPMorgan Chase & Co.',exchange:'NYSE'}];// 获取市场状态\nuseEffect(()=>{fetchMarketStatus();},[]);const fetchMarketStatus=async()=>{try{const response=await axios.get('http://localhost:3000/api/v1/market/status');if(response.data.status==='success'){setMarketStatus(response.data.data);}}catch(error){console.error('获取市场状态失败:',error);}};// 搜索股票\nconst searchStocks=async query=>{if(!query.trim()){setSearchResults(popularStocks);return;}setSearching(true);try{const response=await axios.post('http://localhost:3000/api/v1/stocks/search',{query:query});if(response.data.status==='success'){setSearchResults(response.data.data.results);}else{setError('搜索失败');}}catch(error){console.error('搜索股票失败:',error);setError('搜索服务暂时不可用');// 使用本地过滤作为备选\nconst filtered=popularStocks.filter(stock=>stock.symbol.toLowerCase().includes(query.toLowerCase())||stock.name.toLowerCase().includes(query.toLowerCase()));setSearchResults(filtered);}finally{setSearching(false);}};// 获取股票数据\nconst fetchStockData=async function(symbol){let selectedPeriod=arguments.length>1&&arguments[1]!==undefined?arguments[1]:period;setLoadingData(true);setError(null);try{const response=await axios.post('http://localhost:3000/api/v1/stocks/data',{symbol:symbol,period:selectedPeriod,source:'yahoo'});if(response.data.status==='success'){const data=response.data.data;setStockData(data);// 通知父组件\nif(onStockSelect){onStockSelect({symbol:symbol,stockInfo:data.stock_info,candles:data.candles,period:selectedPeriod});}}else{setError('获取股票数据失败');}}catch(error){console.error('获取股票数据失败:',error);setError('股票数据服务暂时不可用');}finally{setLoadingData(false);}};// 选择股票\nconst handleStockSelect=stock=>{setSelectedStock(stock);fetchStockData(stock.symbol);};// 处理搜索输入\nconst handleSearchChange=e=>{const value=e.target.value;setSearchQuery(value);searchStocks(value);};// 处理周期变化\nconst handlePeriodChange=newPeriod=>{setPeriod(newPeriod);if(selectedStock){fetchStockData(selectedStock.symbol,newPeriod);}};// 格式化价格变化\nconst formatPriceChange=(change,changePercent)=>{const isPositive=change>=0;const color=isPositive?'#52c41a':'#ff4d4f';const icon=isPositive?/*#__PURE__*/_jsx(TrendingUpOutlined,{}):/*#__PURE__*/_jsx(TrendingDownOutlined,{});return/*#__PURE__*/_jsxs(Space,{style:{color},children:[icon,/*#__PURE__*/_jsxs(Text,{style:{color},children:[isPositive?'+':'',change.toFixed(2),\" (\",isPositive?'+':'',changePercent.toFixed(2),\"%)\"]})]});};// 初始化搜索结果\nuseEffect(()=>{setSearchResults(popularStocks);},[]);return/*#__PURE__*/_jsxs(\"div\",{children:[marketStatus&&/*#__PURE__*/_jsx(Alert,{message:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Text,{children:\"\\u5E02\\u573A\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(Tag,{color:marketStatus.is_open?'green':'red',children:marketStatus.is_open?'开市':'休市'}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:marketStatus.timezone})]}),type:marketStatus.is_open?'success':'info',style:{marginBottom:16},showIcon:true}),/*#__PURE__*/_jsx(Card,{title:\"\\u9009\\u62E9\\u80A1\\u7968\",style:{marginBottom:16},children:/*#__PURE__*/_jsxs(Space,{direction:\"vertical\",style:{width:'100%'},children:[/*#__PURE__*/_jsx(Input,{placeholder:\"\\u641C\\u7D22\\u80A1\\u7968\\u4EE3\\u7801\\u6216\\u516C\\u53F8\\u540D\\u79F0 (\\u5982: AAPL, Apple)\",prefix:/*#__PURE__*/_jsx(SearchOutlined,{}),value:searchQuery,onChange:handleSearchChange,loading:searching,allowClear:true}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u9009\\u62E9\\u65F6\\u95F4\\u5468\\u671F\",value:period,onChange:handlePeriodChange,style:{width:'100%'},children:[/*#__PURE__*/_jsx(Option,{value:\"1d\",children:\"1\\u5929\"}),/*#__PURE__*/_jsx(Option,{value:\"5d\",children:\"5\\u5929\"}),/*#__PURE__*/_jsx(Option,{value:\"1mo\",children:\"1\\u4E2A\\u6708\"}),/*#__PURE__*/_jsx(Option,{value:\"3mo\",children:\"3\\u4E2A\\u6708\"}),/*#__PURE__*/_jsx(Option,{value:\"6mo\",children:\"6\\u4E2A\\u6708\"}),/*#__PURE__*/_jsx(Option,{value:\"1y\",children:\"1\\u5E74\"}),/*#__PURE__*/_jsx(Option,{value:\"2y\",children:\"2\\u5E74\"}),/*#__PURE__*/_jsx(Option,{value:\"5y\",children:\"5\\u5E74\"})]})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:()=>selectedStock&&fetchStockData(selectedStock.symbol),loading:loadingData,disabled:!selectedStock,style:{width:'100%'},children:\"\\u5237\\u65B0\\u6570\\u636E\"})})]})]})}),error&&/*#__PURE__*/_jsx(Alert,{message:\"\\u9519\\u8BEF\",description:error,type:\"error\",closable:true,onClose:()=>setError(null),style:{marginBottom:16}}),stockData&&/*#__PURE__*/_jsxs(Card,{title:\"\\u80A1\\u7968\\u4FE1\\u606F\",style:{marginBottom:16},children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F53\\u524D\\u4EF7\\u683C\",value:stockData.stock_info.current_price,precision:2,prefix:/*#__PURE__*/_jsx(DollarOutlined,{}),suffix:stockData.stock_info.currency})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4EF7\\u683C\\u53D8\\u5316\"}),/*#__PURE__*/_jsx(\"div\",{children:formatPriceChange(stockData.stock_info.price_change,stockData.stock_info.price_change_percent)})]})})]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u516C\\u53F8:\"}),/*#__PURE__*/_jsx(\"div\",{children:stockData.stock_info.company_name})]}),/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u4EA4\\u6613\\u6240:\"}),/*#__PURE__*/_jsx(\"div\",{children:stockData.stock_info.exchange})]}),/*#__PURE__*/_jsxs(Col,{span:8,children:[/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u6570\\u636E\\u70B9:\"}),/*#__PURE__*/_jsxs(\"div\",{children:[stockData.total_candles,\" \\u6839\\u8721\\u70DB\\u7EBF\"]})]})]})]}),/*#__PURE__*/_jsx(Card,{title:searchQuery?\"\\u641C\\u7D22\\u7ED3\\u679C \\\"\".concat(searchQuery,\"\\\"\"):\"热门股票\",children:/*#__PURE__*/_jsx(Spin,{spinning:searching,children:/*#__PURE__*/_jsx(List,{dataSource:searchResults,renderItem:stock=>/*#__PURE__*/_jsx(List.Item,{actions:[/*#__PURE__*/_jsx(Button,{type:(selectedStock===null||selectedStock===void 0?void 0:selectedStock.symbol)===stock.symbol?\"primary\":\"default\",icon:/*#__PURE__*/_jsx(StockOutlined,{}),onClick:()=>handleStockSelect(stock),loading:loadingData&&(selectedStock===null||selectedStock===void 0?void 0:selectedStock.symbol)===stock.symbol,children:(selectedStock===null||selectedStock===void 0?void 0:selectedStock.symbol)===stock.symbol?'已选择':'选择'})],children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Text,{strong:true,children:stock.symbol}),/*#__PURE__*/_jsx(Tag,{color:\"blue\",children:stock.exchange})]}),description:stock.name})})})})})]});};export default StockSelector;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Input", "Select", "<PERSON><PERSON>", "List", "Tag", "Space", "Typography", "Spin", "<PERSON><PERSON>", "Row", "Col", "Statistic", "Divider", "SearchOutlined", "StockOutlined", "RiseOutlined", "TrendingUpOutlined", "FallOutlined", "TrendingDownOutlined", "ReloadOutlined", "DollarOutlined", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Option", "Title", "Text", "StockSelector", "_ref", "onStockSelect", "loading", "searchQuery", "setSearch<PERSON>uery", "searchResults", "setSearchResults", "selectedStock", "setSelectedStock", "stockData", "setStockData", "period", "<PERSON><PERSON><PERSON><PERSON>", "searching", "setSearching", "loadingData", "setLoadingData", "error", "setError", "marketStatus", "setMarketStatus", "popularStocks", "symbol", "name", "exchange", "fetchMarketStatus", "response", "get", "data", "status", "console", "searchStocks", "query", "trim", "post", "results", "filtered", "filter", "stock", "toLowerCase", "includes", "fetchStockData", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "source", "stockInfo", "stock_info", "candles", "handleStockSelect", "handleSearchChange", "e", "value", "target", "handlePeriodChange", "newPeriod", "formatPriceChange", "change", "changePercent", "isPositive", "color", "icon", "style", "children", "toFixed", "message", "is_open", "type", "timezone", "marginBottom", "showIcon", "title", "direction", "width", "placeholder", "prefix", "onChange", "allowClear", "gutter", "span", "onClick", "disabled", "description", "closable", "onClose", "current_price", "precision", "suffix", "currency", "price_change", "price_change_percent", "company_name", "total_candles", "concat", "spinning", "dataSource", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "strong"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/StockSelector.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Input, \n  Select, \n  Button, \n  List, \n  Tag, \n  Space, \n  Typography, \n  Spin, \n  Alert,\n  Row,\n  Col,\n  Statistic,\n  Divider\n} from 'antd';\nimport {\n  SearchOutlined,\n  StockOutlined,\n  RiseOutlined as TrendingUpOutlined,\n  FallOutlined as TrendingDownOutlined,\n  ReloadOutlined,\n  DollarOutlined\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Option } = Select;\nconst { Title, Text } = Typography;\n\nconst StockSelector = ({ onStockSelect, loading }) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [selectedStock, setSelectedStock] = useState(null);\n  const [stockData, setStockData] = useState(null);\n  const [period, setPeriod] = useState('1y');\n  const [searching, setSearching] = useState(false);\n  const [loadingData, setLoadingData] = useState(false);\n  const [error, setError] = useState(null);\n  const [marketStatus, setMarketStatus] = useState(null);\n\n  // 热门股票列表\n  const popularStocks = [\n    { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ' },\n    { symbol: 'MSFT', name: 'Microsoft Corporation', exchange: 'NASDAQ' },\n    { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ' },\n    { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ' },\n    { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ' },\n    { symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ' },\n    { symbol: 'NVDA', name: 'NVIDIA Corporation', exchange: 'NASDAQ' },\n    { symbol: 'JPM', name: 'JPMorgan Chase & Co.', exchange: 'NYSE' }\n  ];\n\n  // 获取市场状态\n  useEffect(() => {\n    fetchMarketStatus();\n  }, []);\n\n  const fetchMarketStatus = async () => {\n    try {\n      const response = await axios.get('http://localhost:3000/api/v1/market/status');\n      if (response.data.status === 'success') {\n        setMarketStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取市场状态失败:', error);\n    }\n  };\n\n  // 搜索股票\n  const searchStocks = async (query) => {\n    if (!query.trim()) {\n      setSearchResults(popularStocks);\n      return;\n    }\n\n    setSearching(true);\n    try {\n      const response = await axios.post('http://localhost:3000/api/v1/stocks/search', {\n        query: query\n      });\n\n      if (response.data.status === 'success') {\n        setSearchResults(response.data.data.results);\n      } else {\n        setError('搜索失败');\n      }\n    } catch (error) {\n      console.error('搜索股票失败:', error);\n      setError('搜索服务暂时不可用');\n      // 使用本地过滤作为备选\n      const filtered = popularStocks.filter(stock => \n        stock.symbol.toLowerCase().includes(query.toLowerCase()) ||\n        stock.name.toLowerCase().includes(query.toLowerCase())\n      );\n      setSearchResults(filtered);\n    } finally {\n      setSearching(false);\n    }\n  };\n\n  // 获取股票数据\n  const fetchStockData = async (symbol, selectedPeriod = period) => {\n    setLoadingData(true);\n    setError(null);\n    \n    try {\n      const response = await axios.post('http://localhost:3000/api/v1/stocks/data', {\n        symbol: symbol,\n        period: selectedPeriod,\n        source: 'yahoo'\n      });\n\n      if (response.data.status === 'success') {\n        const data = response.data.data;\n        setStockData(data);\n        \n        // 通知父组件\n        if (onStockSelect) {\n          onStockSelect({\n            symbol: symbol,\n            stockInfo: data.stock_info,\n            candles: data.candles,\n            period: selectedPeriod\n          });\n        }\n      } else {\n        setError('获取股票数据失败');\n      }\n    } catch (error) {\n      console.error('获取股票数据失败:', error);\n      setError('股票数据服务暂时不可用');\n    } finally {\n      setLoadingData(false);\n    }\n  };\n\n  // 选择股票\n  const handleStockSelect = (stock) => {\n    setSelectedStock(stock);\n    fetchStockData(stock.symbol);\n  };\n\n  // 处理搜索输入\n  const handleSearchChange = (e) => {\n    const value = e.target.value;\n    setSearchQuery(value);\n    searchStocks(value);\n  };\n\n  // 处理周期变化\n  const handlePeriodChange = (newPeriod) => {\n    setPeriod(newPeriod);\n    if (selectedStock) {\n      fetchStockData(selectedStock.symbol, newPeriod);\n    }\n  };\n\n  // 格式化价格变化\n  const formatPriceChange = (change, changePercent) => {\n    const isPositive = change >= 0;\n    const color = isPositive ? '#52c41a' : '#ff4d4f';\n    const icon = isPositive ? <TrendingUpOutlined /> : <TrendingDownOutlined />;\n    \n    return (\n      <Space style={{ color }}>\n        {icon}\n        <Text style={{ color }}>\n          {isPositive ? '+' : ''}{change.toFixed(2)} ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)\n        </Text>\n      </Space>\n    );\n  };\n\n  // 初始化搜索结果\n  useEffect(() => {\n    setSearchResults(popularStocks);\n  }, []);\n\n  return (\n    <div>\n      {/* 市场状态 */}\n      {marketStatus && (\n        <Alert\n          message={\n            <Space>\n              <Text>市场状态:</Text>\n              <Tag color={marketStatus.is_open ? 'green' : 'red'}>\n                {marketStatus.is_open ? '开市' : '休市'}\n              </Tag>\n              <Text type=\"secondary\">{marketStatus.timezone}</Text>\n            </Space>\n          }\n          type={marketStatus.is_open ? 'success' : 'info'}\n          style={{ marginBottom: 16 }}\n          showIcon\n        />\n      )}\n\n      {/* 股票搜索 */}\n      <Card title=\"选择股票\" style={{ marginBottom: 16 }}>\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <Input\n            placeholder=\"搜索股票代码或公司名称 (如: AAPL, Apple)\"\n            prefix={<SearchOutlined />}\n            value={searchQuery}\n            onChange={handleSearchChange}\n            loading={searching}\n            allowClear\n          />\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Select\n                placeholder=\"选择时间周期\"\n                value={period}\n                onChange={handlePeriodChange}\n                style={{ width: '100%' }}\n              >\n                <Option value=\"1d\">1天</Option>\n                <Option value=\"5d\">5天</Option>\n                <Option value=\"1mo\">1个月</Option>\n                <Option value=\"3mo\">3个月</Option>\n                <Option value=\"6mo\">6个月</Option>\n                <Option value=\"1y\">1年</Option>\n                <Option value=\"2y\">2年</Option>\n                <Option value=\"5y\">5年</Option>\n              </Select>\n            </Col>\n            <Col span={12}>\n              <Button \n                icon={<ReloadOutlined />} \n                onClick={() => selectedStock && fetchStockData(selectedStock.symbol)}\n                loading={loadingData}\n                disabled={!selectedStock}\n                style={{ width: '100%' }}\n              >\n                刷新数据\n              </Button>\n            </Col>\n          </Row>\n        </Space>\n      </Card>\n\n      {/* 错误提示 */}\n      {error && (\n        <Alert\n          message=\"错误\"\n          description={error}\n          type=\"error\"\n          closable\n          onClose={() => setError(null)}\n          style={{ marginBottom: 16 }}\n        />\n      )}\n\n      {/* 当前选中的股票信息 */}\n      {stockData && (\n        <Card title=\"股票信息\" style={{ marginBottom: 16 }}>\n          <Row gutter={16}>\n            <Col span={12}>\n              <Statistic\n                title=\"当前价格\"\n                value={stockData.stock_info.current_price}\n                precision={2}\n                prefix={<DollarOutlined />}\n                suffix={stockData.stock_info.currency}\n              />\n            </Col>\n            <Col span={12}>\n              <div>\n                <Text type=\"secondary\">价格变化</Text>\n                <div>\n                  {formatPriceChange(\n                    stockData.stock_info.price_change,\n                    stockData.stock_info.price_change_percent\n                  )}\n                </div>\n              </div>\n            </Col>\n          </Row>\n          \n          <Divider />\n          \n          <Row gutter={16}>\n            <Col span={8}>\n              <Text type=\"secondary\">公司:</Text>\n              <div>{stockData.stock_info.company_name}</div>\n            </Col>\n            <Col span={8}>\n              <Text type=\"secondary\">交易所:</Text>\n              <div>{stockData.stock_info.exchange}</div>\n            </Col>\n            <Col span={8}>\n              <Text type=\"secondary\">数据点:</Text>\n              <div>{stockData.total_candles} 根蜡烛线</div>\n            </Col>\n          </Row>\n        </Card>\n      )}\n\n      {/* 股票搜索结果 */}\n      <Card title={searchQuery ? `搜索结果 \"${searchQuery}\"` : \"热门股票\"}>\n        <Spin spinning={searching}>\n          <List\n            dataSource={searchResults}\n            renderItem={(stock) => (\n              <List.Item\n                actions={[\n                  <Button\n                    type={selectedStock?.symbol === stock.symbol ? \"primary\" : \"default\"}\n                    icon={<StockOutlined />}\n                    onClick={() => handleStockSelect(stock)}\n                    loading={loadingData && selectedStock?.symbol === stock.symbol}\n                  >\n                    {selectedStock?.symbol === stock.symbol ? '已选择' : '选择'}\n                  </Button>\n                ]}\n              >\n                <List.Item.Meta\n                  title={\n                    <Space>\n                      <Text strong>{stock.symbol}</Text>\n                      <Tag color=\"blue\">{stock.exchange}</Tag>\n                    </Space>\n                  }\n                  description={stock.name}\n                />\n              </List.Item>\n            )}\n          />\n        </Spin>\n      </Card>\n    </div>\n  );\n};\n\nexport default StockSelector;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,KAAK,CACLC,UAAU,CACVC,IAAI,CACJC,KAAK,CACLC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,OAAO,KACF,MAAM,CACb,OACEC,cAAc,CACdC,aAAa,CACbC,YAAY,GAAI,CAAAC,kBAAkB,CAClCC,YAAY,GAAI,CAAAC,oBAAoB,CACpCC,cAAc,CACdC,cAAc,KACT,mBAAmB,CAC1B,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAEC,MAAO,CAAC,CAAGzB,MAAM,CACzB,KAAM,CAAE0B,KAAK,CAAEC,IAAK,CAAC,CAAGtB,UAAU,CAElC,KAAM,CAAAuB,aAAa,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,aAAa,CAAEC,OAAQ,CAAC,CAAAF,IAAA,CAC/C,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACwC,aAAa,CAAEC,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC0C,SAAS,CAAEC,YAAY,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAC4C,MAAM,CAAEC,SAAS,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC8C,SAAS,CAAEC,YAAY,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgD,WAAW,CAAEC,cAAc,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACkD,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CAEtD;AACA,KAAM,CAAAsD,aAAa,CAAG,CACpB,CAAEC,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,YAAY,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAC1D,CAAEF,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,uBAAuB,CAAEC,QAAQ,CAAE,QAAS,CAAC,CACrE,CAAEF,MAAM,CAAE,OAAO,CAAEC,IAAI,CAAE,eAAe,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAC9D,CAAEF,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAC/D,CAAEF,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,YAAY,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAC1D,CAAEF,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,QAAQ,CAAE,QAAS,CAAC,CACnE,CAAEF,MAAM,CAAE,MAAM,CAAEC,IAAI,CAAE,oBAAoB,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAClE,CAAEF,MAAM,CAAE,KAAK,CAAEC,IAAI,CAAE,sBAAsB,CAAEC,QAAQ,CAAE,MAAO,CAAC,CAClE,CAED;AACAxD,SAAS,CAAC,IAAM,CACdyD,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,4CAA4C,CAAC,CAC9E,GAAID,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAK,SAAS,CAAE,CACtCT,eAAe,CAACM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CACrC,CACF,CAAE,MAAOX,KAAK,CAAE,CACda,OAAO,CAACb,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAc,YAAY,CAAG,KAAO,CAAAC,KAAK,EAAK,CACpC,GAAI,CAACA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAE,CACjB3B,gBAAgB,CAACe,aAAa,CAAC,CAC/B,OACF,CAEAP,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CACF,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAAC2C,IAAI,CAAC,4CAA4C,CAAE,CAC9EF,KAAK,CAAEA,KACT,CAAC,CAAC,CAEF,GAAIN,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAK,SAAS,CAAE,CACtCvB,gBAAgB,CAACoB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,OAAO,CAAC,CAC9C,CAAC,IAAM,CACLjB,QAAQ,CAAC,MAAM,CAAC,CAClB,CACF,CAAE,MAAOD,KAAK,CAAE,CACda,OAAO,CAACb,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BC,QAAQ,CAAC,WAAW,CAAC,CACrB;AACA,KAAM,CAAAkB,QAAQ,CAAGf,aAAa,CAACgB,MAAM,CAACC,KAAK,EACzCA,KAAK,CAAChB,MAAM,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,KAAK,CAACO,WAAW,CAAC,CAAC,CAAC,EACxDD,KAAK,CAACf,IAAI,CAACgB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,KAAK,CAACO,WAAW,CAAC,CAAC,CACvD,CAAC,CACDjC,gBAAgB,CAAC8B,QAAQ,CAAC,CAC5B,CAAC,OAAS,CACRtB,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,cAAc,CAAG,cAAAA,CAAOnB,MAAM,CAA8B,IAA5B,CAAAoB,cAAc,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAGhC,MAAM,CAC3DK,cAAc,CAAC,IAAI,CAAC,CACpBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAAC2C,IAAI,CAAC,0CAA0C,CAAE,CAC5EZ,MAAM,CAAEA,MAAM,CACdX,MAAM,CAAE+B,cAAc,CACtBI,MAAM,CAAE,OACV,CAAC,CAAC,CAEF,GAAIpB,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAK,SAAS,CAAE,CACtC,KAAM,CAAAD,IAAI,CAAGF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC/BlB,YAAY,CAACkB,IAAI,CAAC,CAElB;AACA,GAAI3B,aAAa,CAAE,CACjBA,aAAa,CAAC,CACZqB,MAAM,CAAEA,MAAM,CACdyB,SAAS,CAAEnB,IAAI,CAACoB,UAAU,CAC1BC,OAAO,CAAErB,IAAI,CAACqB,OAAO,CACrBtC,MAAM,CAAE+B,cACV,CAAC,CAAC,CACJ,CACF,CAAC,IAAM,CACLxB,QAAQ,CAAC,UAAU,CAAC,CACtB,CACF,CAAE,MAAOD,KAAK,CAAE,CACda,OAAO,CAACb,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCC,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,OAAS,CACRF,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAkC,iBAAiB,CAAIZ,KAAK,EAAK,CACnC9B,gBAAgB,CAAC8B,KAAK,CAAC,CACvBG,cAAc,CAACH,KAAK,CAAChB,MAAM,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAA6B,kBAAkB,CAAIC,CAAC,EAAK,CAChC,KAAM,CAAAC,KAAK,CAAGD,CAAC,CAACE,MAAM,CAACD,KAAK,CAC5BjD,cAAc,CAACiD,KAAK,CAAC,CACrBtB,YAAY,CAACsB,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAE,kBAAkB,CAAIC,SAAS,EAAK,CACxC5C,SAAS,CAAC4C,SAAS,CAAC,CACpB,GAAIjD,aAAa,CAAE,CACjBkC,cAAc,CAAClC,aAAa,CAACe,MAAM,CAAEkC,SAAS,CAAC,CACjD,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAACC,MAAM,CAAEC,aAAa,GAAK,CACnD,KAAM,CAAAC,UAAU,CAAGF,MAAM,EAAI,CAAC,CAC9B,KAAM,CAAAG,KAAK,CAAGD,UAAU,CAAG,SAAS,CAAG,SAAS,CAChD,KAAM,CAAAE,IAAI,CAAGF,UAAU,cAAGnE,IAAA,CAACP,kBAAkB,GAAE,CAAC,cAAGO,IAAA,CAACL,oBAAoB,GAAE,CAAC,CAE3E,mBACEO,KAAA,CAACpB,KAAK,EAACwF,KAAK,CAAE,CAAEF,KAAM,CAAE,CAAAG,QAAA,EACrBF,IAAI,cACLnE,KAAA,CAACG,IAAI,EAACiE,KAAK,CAAE,CAAEF,KAAM,CAAE,CAAAG,QAAA,EACpBJ,UAAU,CAAG,GAAG,CAAG,EAAE,CAAEF,MAAM,CAACO,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,CAACL,UAAU,CAAG,GAAG,CAAG,EAAE,CAAED,aAAa,CAACM,OAAO,CAAC,CAAC,CAAC,CAAC,IAC/F,EAAM,CAAC,EACF,CAAC,CAEZ,CAAC,CAED;AACAjG,SAAS,CAAC,IAAM,CACdsC,gBAAgB,CAACe,aAAa,CAAC,CACjC,CAAC,CAAE,EAAE,CAAC,CAEN,mBACE1B,KAAA,QAAAqE,QAAA,EAEG7C,YAAY,eACX1B,IAAA,CAACf,KAAK,EACJwF,OAAO,cACLvE,KAAA,CAACpB,KAAK,EAAAyF,QAAA,eACJvE,IAAA,CAACK,IAAI,EAAAkE,QAAA,CAAC,2BAAK,CAAM,CAAC,cAClBvE,IAAA,CAACnB,GAAG,EAACuF,KAAK,CAAE1C,YAAY,CAACgD,OAAO,CAAG,OAAO,CAAG,KAAM,CAAAH,QAAA,CAChD7C,YAAY,CAACgD,OAAO,CAAG,IAAI,CAAG,IAAI,CAChC,CAAC,cACN1E,IAAA,CAACK,IAAI,EAACsE,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAE7C,YAAY,CAACkD,QAAQ,CAAO,CAAC,EAChD,CACR,CACDD,IAAI,CAAEjD,YAAY,CAACgD,OAAO,CAAG,SAAS,CAAG,MAAO,CAChDJ,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAC5BC,QAAQ,MACT,CACF,cAGD9E,IAAA,CAACxB,IAAI,EAACuG,KAAK,CAAC,0BAAM,CAACT,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAN,QAAA,cAC7CrE,KAAA,CAACpB,KAAK,EAACkG,SAAS,CAAC,UAAU,CAACV,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAO,CAAE,CAAAV,QAAA,eACnDvE,IAAA,CAACvB,KAAK,EACJyG,WAAW,CAAC,0FAA8B,CAC1CC,MAAM,cAAEnF,IAAA,CAACV,cAAc,GAAE,CAAE,CAC3BsE,KAAK,CAAElD,WAAY,CACnB0E,QAAQ,CAAE1B,kBAAmB,CAC7BjD,OAAO,CAAEW,SAAU,CACnBiE,UAAU,MACX,CAAC,cAEFnF,KAAA,CAAChB,GAAG,EAACoG,MAAM,CAAE,EAAG,CAAAf,QAAA,eACdvE,IAAA,CAACb,GAAG,EAACoG,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZrE,KAAA,CAACxB,MAAM,EACLwG,WAAW,CAAC,sCAAQ,CACpBtB,KAAK,CAAE1C,MAAO,CACdkE,QAAQ,CAAEtB,kBAAmB,CAC7BQ,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAO,CAAE,CAAAV,QAAA,eAEzBvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,IAAI,CAAAW,QAAA,CAAC,SAAE,CAAQ,CAAC,cAC9BvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,IAAI,CAAAW,QAAA,CAAC,SAAE,CAAQ,CAAC,cAC9BvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,KAAK,CAAAW,QAAA,CAAC,eAAG,CAAQ,CAAC,cAChCvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,KAAK,CAAAW,QAAA,CAAC,eAAG,CAAQ,CAAC,cAChCvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,KAAK,CAAAW,QAAA,CAAC,eAAG,CAAQ,CAAC,cAChCvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,IAAI,CAAAW,QAAA,CAAC,SAAE,CAAQ,CAAC,cAC9BvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,IAAI,CAAAW,QAAA,CAAC,SAAE,CAAQ,CAAC,cAC9BvE,IAAA,CAACG,MAAM,EAACyD,KAAK,CAAC,IAAI,CAAAW,QAAA,CAAC,SAAE,CAAQ,CAAC,EACxB,CAAC,CACN,CAAC,cACNvE,IAAA,CAACb,GAAG,EAACoG,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZvE,IAAA,CAACrB,MAAM,EACL0F,IAAI,cAAErE,IAAA,CAACJ,cAAc,GAAE,CAAE,CACzB4F,OAAO,CAAEA,CAAA,GAAM1E,aAAa,EAAIkC,cAAc,CAAClC,aAAa,CAACe,MAAM,CAAE,CACrEpB,OAAO,CAAEa,WAAY,CACrBmE,QAAQ,CAAE,CAAC3E,aAAc,CACzBwD,KAAK,CAAE,CAAEW,KAAK,CAAE,MAAO,CAAE,CAAAV,QAAA,CAC1B,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,CAGN/C,KAAK,eACJxB,IAAA,CAACf,KAAK,EACJwF,OAAO,CAAC,cAAI,CACZiB,WAAW,CAAElE,KAAM,CACnBmD,IAAI,CAAC,OAAO,CACZgB,QAAQ,MACRC,OAAO,CAAEA,CAAA,GAAMnE,QAAQ,CAAC,IAAI,CAAE,CAC9B6C,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAC7B,CACF,CAGA7D,SAAS,eACRd,KAAA,CAAC1B,IAAI,EAACuG,KAAK,CAAC,0BAAM,CAACT,KAAK,CAAE,CAAEO,YAAY,CAAE,EAAG,CAAE,CAAAN,QAAA,eAC7CrE,KAAA,CAAChB,GAAG,EAACoG,MAAM,CAAE,EAAG,CAAAf,QAAA,eACdvE,IAAA,CAACb,GAAG,EAACoG,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZvE,IAAA,CAACZ,SAAS,EACR2F,KAAK,CAAC,0BAAM,CACZnB,KAAK,CAAE5C,SAAS,CAACuC,UAAU,CAACsC,aAAc,CAC1CC,SAAS,CAAE,CAAE,CACbX,MAAM,cAAEnF,IAAA,CAACH,cAAc,GAAE,CAAE,CAC3BkG,MAAM,CAAE/E,SAAS,CAACuC,UAAU,CAACyC,QAAS,CACvC,CAAC,CACC,CAAC,cACNhG,IAAA,CAACb,GAAG,EAACoG,IAAI,CAAE,EAAG,CAAAhB,QAAA,cACZrE,KAAA,QAAAqE,QAAA,eACEvE,IAAA,CAACK,IAAI,EAACsE,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAC,0BAAI,CAAM,CAAC,cAClCvE,IAAA,QAAAuE,QAAA,CACGP,iBAAiB,CAChBhD,SAAS,CAACuC,UAAU,CAAC0C,YAAY,CACjCjF,SAAS,CAACuC,UAAU,CAAC2C,oBACvB,CAAC,CACE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENlG,IAAA,CAACX,OAAO,GAAE,CAAC,cAEXa,KAAA,CAAChB,GAAG,EAACoG,MAAM,CAAE,EAAG,CAAAf,QAAA,eACdrE,KAAA,CAACf,GAAG,EAACoG,IAAI,CAAE,CAAE,CAAAhB,QAAA,eACXvE,IAAA,CAACK,IAAI,EAACsE,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAC,eAAG,CAAM,CAAC,cACjCvE,IAAA,QAAAuE,QAAA,CAAMvD,SAAS,CAACuC,UAAU,CAAC4C,YAAY,CAAM,CAAC,EAC3C,CAAC,cACNjG,KAAA,CAACf,GAAG,EAACoG,IAAI,CAAE,CAAE,CAAAhB,QAAA,eACXvE,IAAA,CAACK,IAAI,EAACsE,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAC,qBAAI,CAAM,CAAC,cAClCvE,IAAA,QAAAuE,QAAA,CAAMvD,SAAS,CAACuC,UAAU,CAACxB,QAAQ,CAAM,CAAC,EACvC,CAAC,cACN7B,KAAA,CAACf,GAAG,EAACoG,IAAI,CAAE,CAAE,CAAAhB,QAAA,eACXvE,IAAA,CAACK,IAAI,EAACsE,IAAI,CAAC,WAAW,CAAAJ,QAAA,CAAC,qBAAI,CAAM,CAAC,cAClCrE,KAAA,QAAAqE,QAAA,EAAMvD,SAAS,CAACoF,aAAa,CAAC,2BAAK,EAAK,CAAC,EACtC,CAAC,EACH,CAAC,EACF,CACP,cAGDpG,IAAA,CAACxB,IAAI,EAACuG,KAAK,CAAErE,WAAW,+BAAA2F,MAAA,CAAY3F,WAAW,OAAM,MAAO,CAAA6D,QAAA,cAC1DvE,IAAA,CAAChB,IAAI,EAACsH,QAAQ,CAAElF,SAAU,CAAAmD,QAAA,cACxBvE,IAAA,CAACpB,IAAI,EACH2H,UAAU,CAAE3F,aAAc,CAC1B4F,UAAU,CAAG3D,KAAK,eAChB7C,IAAA,CAACpB,IAAI,CAAC6H,IAAI,EACRC,OAAO,CAAE,cACP1G,IAAA,CAACrB,MAAM,EACLgG,IAAI,CAAE,CAAA7D,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEe,MAAM,IAAKgB,KAAK,CAAChB,MAAM,CAAG,SAAS,CAAG,SAAU,CACrEwC,IAAI,cAAErE,IAAA,CAACT,aAAa,GAAE,CAAE,CACxBiG,OAAO,CAAEA,CAAA,GAAM/B,iBAAiB,CAACZ,KAAK,CAAE,CACxCpC,OAAO,CAAEa,WAAW,EAAI,CAAAR,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEe,MAAM,IAAKgB,KAAK,CAAChB,MAAO,CAAA0C,QAAA,CAE9D,CAAAzD,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEe,MAAM,IAAKgB,KAAK,CAAChB,MAAM,CAAG,KAAK,CAAG,IAAI,CAChD,CAAC,CACT,CAAA0C,QAAA,cAEFvE,IAAA,CAACpB,IAAI,CAAC6H,IAAI,CAACE,IAAI,EACb5B,KAAK,cACH7E,KAAA,CAACpB,KAAK,EAAAyF,QAAA,eACJvE,IAAA,CAACK,IAAI,EAACuG,MAAM,MAAArC,QAAA,CAAE1B,KAAK,CAAChB,MAAM,CAAO,CAAC,cAClC7B,IAAA,CAACnB,GAAG,EAACuF,KAAK,CAAC,MAAM,CAAAG,QAAA,CAAE1B,KAAK,CAACd,QAAQ,CAAM,CAAC,EACnC,CACR,CACD2D,WAAW,CAAE7C,KAAK,CAACf,IAAK,CACzB,CAAC,CACO,CACX,CACH,CAAC,CACE,CAAC,CACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}