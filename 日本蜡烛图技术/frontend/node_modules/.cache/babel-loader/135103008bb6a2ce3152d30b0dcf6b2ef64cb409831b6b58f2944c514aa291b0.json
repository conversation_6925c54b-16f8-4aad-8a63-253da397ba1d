{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Wind = createLucideIcon(\"Wind\", [[\"path\", {\n  d: \"M17.7 7.7a2.5 2.5 0 1 1 1.8 4.3H2\",\n  key: \"1k4u03\"\n}], [\"path\", {\n  d: \"M9.6 4.6A2 2 0 1 1 11 8H2\",\n  key: \"b7d0fd\"\n}], [\"path\", {\n  d: \"M12.6 19.4A2 2 0 1 0 14 16H2\",\n  key: \"1p5cb3\"\n}]]);\nexport { Wind as default };", "map": {"version": 3, "names": ["Wind", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/lucide-react/src/icons/wind.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Wind\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcuNyA3LjdhMi41IDIuNSAwIDEgMSAxLjggNC4zSDIiIC8+CiAgPHBhdGggZD0iTTkuNiA0LjZBMiAyIDAgMSAxIDExIDhIMiIgLz4KICA8cGF0aCBkPSJNMTIuNiAxOS40QTIgMiAwIDEgMCAxNCAxNkgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wind\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wind = createLucideIcon('Wind', [\n  ['path', { d: 'M17.7 7.7a2.5 2.5 0 1 1 1.8 4.3H2', key: '1k4u03' }],\n  ['path', { d: 'M9.6 4.6A2 2 0 1 1 11 8H2', key: 'b7d0fd' }],\n  ['path', { d: 'M12.6 19.4A2 2 0 1 0 14 16H2', key: '1p5cb3' }],\n]);\n\nexport default Wind;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}