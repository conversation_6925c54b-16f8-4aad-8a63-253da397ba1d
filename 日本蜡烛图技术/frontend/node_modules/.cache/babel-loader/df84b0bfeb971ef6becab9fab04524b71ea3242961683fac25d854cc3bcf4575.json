{"ast": null, "code": "export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}", "map": {"version": 3, "names": ["Invalid", "constructor", "reason", "explanation", "toMessage"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/impl/invalid.js"], "sourcesContent": ["export default class Invalid {\n  constructor(reason, explanation) {\n    this.reason = reason;\n    this.explanation = explanation;\n  }\n\n  toMessage() {\n    if (this.explanation) {\n      return `${this.reason}: ${this.explanation}`;\n    } else {\n      return this.reason;\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,MAAMA,OAAO,CAAC;EAC3BC,WAAWA,CAACC,MAAM,EAAEC,WAAW,EAAE;IAC/B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAChC;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACD,WAAW,EAAE;MACpB,OAAO,GAAG,IAAI,CAACD,MAAM,KAAK,IAAI,CAACC,WAAW,EAAE;IAC9C,CAAC,MAAM;MACL,OAAO,IAAI,CAACD,MAAM;IACpB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}