{"ast": null, "code": "/*!\n * @license\n * chartjs-chart-financial\n * http://chartjs.org/\n * Version: 0.2.1\n *\n * Copyright 2024 Chart.js Contributors\n * Released under the MIT license\n * https://github.com/chartjs/chartjs-chart-financial/blob/master/LICENSE.md\n */\nimport { BarElement, Chart, BarController, defaults as defaults$1 } from 'chart.js';\nimport { valueOrDefault, isNullOrUndef, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * Helper function to get the bounds of the bar regardless of the orientation\n * @param {Rectangle} bar the bar\n * @param {boolean} [useFinalPosition]\n * @return {object} bounds of the bar\n * @private\n */\nfunction getBarBounds(bar, useFinalPosition) {\n  const {\n    x,\n    y,\n    base,\n    width,\n    height\n  } = bar.getProps(['x', 'low', 'high', 'width', 'height'], useFinalPosition);\n  let left, right, top, bottom, half;\n  if (bar.horizontal) {\n    half = height / 2;\n    left = Math.min(x, base);\n    right = Math.max(x, base);\n    top = y - half;\n    bottom = y + half;\n  } else {\n    half = width / 2;\n    left = x - half;\n    right = x + half;\n    top = Math.min(y, base); // use min because 0 pixel at top of screen\n    bottom = Math.max(y, base);\n  }\n  return {\n    left,\n    top,\n    right,\n    bottom\n  };\n}\nfunction inRange(bar, x, y, useFinalPosition) {\n  const skipX = x === null;\n  const skipY = y === null;\n  const bounds = !bar || skipX && skipY ? false : getBarBounds(bar, useFinalPosition);\n  return bounds && (skipX || x >= bounds.left && x <= bounds.right) && (skipY || y >= bounds.top && y <= bounds.bottom);\n}\nclass FinancialElement extends BarElement {\n  static defaults = {\n    backgroundColors: {\n      up: 'rgba(75, 192, 192, 0.5)',\n      down: 'rgba(255, 99, 132, 0.5)',\n      unchanged: 'rgba(201, 203, 207, 0.5)'\n    },\n    borderColors: {\n      up: 'rgb(75, 192, 192)',\n      down: 'rgb(255, 99, 132)',\n      unchanged: 'rgb(201, 203, 207)'\n    }\n  };\n  height() {\n    return this.base - this.y;\n  }\n  inRange(mouseX, mouseY, useFinalPosition) {\n    return inRange(this, mouseX, mouseY, useFinalPosition);\n  }\n  inXRange(mouseX, useFinalPosition) {\n    return inRange(this, mouseX, null, useFinalPosition);\n  }\n  inYRange(mouseY, useFinalPosition) {\n    return inRange(this, null, mouseY, useFinalPosition);\n  }\n  getRange(axis) {\n    return axis === 'x' ? this.width / 2 : this.height / 2;\n  }\n  getCenterPoint(useFinalPosition) {\n    const {\n      x,\n      low,\n      high\n    } = this.getProps(['x', 'low', 'high'], useFinalPosition);\n    return {\n      x,\n      y: (high + low) / 2\n    };\n  }\n  tooltipPosition(useFinalPosition) {\n    const {\n      x,\n      open,\n      close\n    } = this.getProps(['x', 'open', 'close'], useFinalPosition);\n    return {\n      x,\n      y: (open + close) / 2\n    };\n  }\n}\nconst defaults = Chart.defaults;\nclass OhlcElement extends FinancialElement {\n  static id = 'ohlc';\n  static defaults = {\n    ...FinancialElement.defaults,\n    lineWidth: 2,\n    armLength: null,\n    armLengthRatio: 0.8\n  };\n  draw(ctx) {\n    const me = this;\n    const {\n      x,\n      open,\n      high,\n      low,\n      close\n    } = me;\n    const armLengthRatio = valueOrDefault(me.armLengthRatio, defaults.elements.ohlc.armLengthRatio);\n    let armLength = valueOrDefault(me.armLength, defaults.elements.ohlc.armLength);\n    if (armLength === null) {\n      // The width of an ohlc is affected by barPercentage and categoryPercentage\n      // This behavior is caused by extending controller.financial, which extends controller.bar\n      // barPercentage and categoryPercentage are now set to 1.0 (see controller.ohlc)\n      // and armLengthRatio is multipled by 0.5,\n      // so that when armLengthRatio=1.0, the arms from neighbour ohcl touch,\n      // and when armLengthRatio=0.0, ohcl are just vertical lines.\n      armLength = me.width * armLengthRatio * 0.5;\n    }\n    if (close < open) {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.up : undefined, defaults.elements.ohlc.borderColors.up);\n    } else if (close > open) {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.down : undefined, defaults.elements.ohlc.borderColors.down);\n    } else {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.unchanged : undefined, defaults.elements.ohlc.borderColors.unchanged);\n    }\n    ctx.lineWidth = valueOrDefault(me.lineWidth, defaults.elements.ohlc.lineWidth);\n    ctx.beginPath();\n    ctx.moveTo(x, high);\n    ctx.lineTo(x, low);\n    ctx.moveTo(x - armLength, open);\n    ctx.lineTo(x, open);\n    ctx.moveTo(x + armLength, close);\n    ctx.lineTo(x, close);\n    ctx.stroke();\n  }\n}\n\n/**\n * This class is based off controller.bar.js from the upstream Chart.js library\n */\nclass FinancialController extends BarController {\n  static overrides = {\n    label: '',\n    parsing: false,\n    hover: {\n      mode: 'label'\n    },\n    animations: {\n      numbers: {\n        type: 'number',\n        properties: ['x', 'y', 'base', 'width', 'open', 'high', 'low', 'close']\n      }\n    },\n    scales: {\n      x: {\n        type: 'timeseries',\n        offset: true,\n        ticks: {\n          major: {\n            enabled: true\n          },\n          source: 'data',\n          maxRotation: 0,\n          autoSkip: true,\n          autoSkipPadding: 75,\n          sampleSize: 100\n        }\n      },\n      y: {\n        type: 'linear'\n      }\n    },\n    plugins: {\n      tooltip: {\n        intersect: false,\n        mode: 'index',\n        callbacks: {\n          label(ctx) {\n            const point = ctx.parsed;\n            if (!isNullOrUndef(point.y)) {\n              return defaults$1.plugins.tooltip.callbacks.label(ctx);\n            }\n            const {\n              o,\n              h,\n              l,\n              c\n            } = point;\n            return `O: ${o}  H: ${h}  L: ${l}  C: ${c}`;\n          }\n        }\n      }\n    }\n  };\n  getLabelAndValue(index) {\n    const me = this;\n    const parsed = me.getParsed(index);\n    const axis = me._cachedMeta.iScale.axis;\n    const {\n      o,\n      h,\n      l,\n      c\n    } = parsed;\n    const value = `O: ${o}  H: ${h}  L: ${l}  C: ${c}`;\n    return {\n      label: `${me._cachedMeta.iScale.getLabelForValue(parsed[axis])}`,\n      value\n    };\n  }\n  getUserBounds(scale) {\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = scale.getUserBounds();\n    return {\n      min: minDefined ? min : Number.NEGATIVE_INFINITY,\n      max: maxDefined ? max : Number.POSITIVE_INFINITY\n    };\n  }\n\n  /**\n   * Implement this ourselves since it doesn't handle high and low values\n   * https://github.com/chartjs/Chart.js/issues/7328\n   * @protected\n   */\n  getMinMax(scale) {\n    const meta = this._cachedMeta;\n    const _parsed = meta._parsed;\n    const axis = meta.iScale.axis;\n    const otherScale = this._getOtherScale(scale);\n    const {\n      min: otherMin,\n      max: otherMax\n    } = this.getUserBounds(otherScale);\n    if (_parsed.length < 2) {\n      return {\n        min: 0,\n        max: 1\n      };\n    }\n    if (scale === meta.iScale) {\n      return {\n        min: _parsed[0][axis],\n        max: _parsed[_parsed.length - 1][axis]\n      };\n    }\n    const newParsedData = _parsed.filter(({\n      x\n    }) => x >= otherMin && x < otherMax);\n    let min = Number.POSITIVE_INFINITY;\n    let max = Number.NEGATIVE_INFINITY;\n    for (let i = 0; i < newParsedData.length; i++) {\n      const data = newParsedData[i];\n      min = Math.min(min, data.l);\n      max = Math.max(max, data.h);\n    }\n    return {\n      min,\n      max\n    };\n  }\n\n  /**\n   * @protected\n   */\n  calculateElementProperties(index, ruler, reset, options) {\n    const me = this;\n    const vscale = me._cachedMeta.vScale;\n    const base = vscale.getBasePixel();\n    const ipixels = me._calculateBarIndexPixels(index, ruler, options);\n    const data = me.chart.data.datasets[me.index].data[index];\n    const open = vscale.getPixelForValue(data.o);\n    const high = vscale.getPixelForValue(data.h);\n    const low = vscale.getPixelForValue(data.l);\n    const close = vscale.getPixelForValue(data.c);\n    return {\n      base: reset ? base : low,\n      x: ipixels.center,\n      y: (low + high) / 2,\n      width: ipixels.size,\n      open,\n      high,\n      low,\n      close\n    };\n  }\n  draw() {\n    const me = this;\n    const chart = me.chart;\n    const rects = me._cachedMeta.data;\n    clipArea(chart.ctx, chart.chartArea);\n    for (let i = 0; i < rects.length; ++i) {\n      rects[i].draw(me._ctx);\n    }\n    unclipArea(chart.ctx);\n  }\n}\nclass OhlcController extends FinancialController {\n  static id = 'ohlc';\n  static defaults = {\n    ...FinancialController.defaults,\n    dataElementType: OhlcElement.id,\n    datasets: {\n      barPercentage: 1.0,\n      categoryPercentage: 1.0\n    }\n  };\n  updateElements(elements, start, count, mode) {\n    const reset = mode === 'reset';\n    const ruler = this._getRuler();\n    const {\n      sharedOptions,\n      includeOptions\n    } = this._getSharedOptions(start, mode);\n    for (let i = start; i < start + count; i++) {\n      const options = sharedOptions || this.resolveDataElementOptions(i, mode);\n      const baseProperties = this.calculateElementProperties(i, ruler, reset, options);\n      if (includeOptions) {\n        baseProperties.options = options;\n      }\n      this.updateElement(elements[i], i, baseProperties, mode);\n    }\n  }\n}\nclass CandlestickElement extends FinancialElement {\n  static id = 'candlestick';\n  static defaults = {\n    ...FinancialElement.defaults,\n    borderWidth: 1\n  };\n  draw(ctx) {\n    const me = this;\n    const {\n      x,\n      open,\n      high,\n      low,\n      close\n    } = me;\n    let borderColors = me.options.borderColors;\n    if (typeof borderColors === 'string') {\n      borderColors = {\n        up: borderColors,\n        down: borderColors,\n        unchanged: borderColors\n      };\n    }\n    let borderColor;\n    if (close < open) {\n      borderColor = valueOrDefault(borderColors ? borderColors.up : undefined, defaults$1.elements.candlestick.borderColors.up);\n      ctx.fillStyle = valueOrDefault(me.options.backgroundColors ? me.options.backgroundColors.up : undefined, defaults$1.elements.candlestick.backgroundColors.up);\n    } else if (close > open) {\n      borderColor = valueOrDefault(borderColors ? borderColors.down : undefined, defaults$1.elements.candlestick.borderColors.down);\n      ctx.fillStyle = valueOrDefault(me.options.backgroundColors ? me.options.backgroundColors.down : undefined, defaults$1.elements.candlestick.backgroundColors.down);\n    } else {\n      borderColor = valueOrDefault(borderColors ? borderColors.unchanged : undefined, defaults$1.elements.candlestick.borderColors.unchanged);\n      ctx.fillStyle = valueOrDefault(me.backgroundColors ? me.backgroundColors.unchanged : undefined, defaults$1.elements.candlestick.backgroundColors.unchanged);\n    }\n    ctx.lineWidth = valueOrDefault(me.options.borderWidth, defaults$1.elements.candlestick.borderWidth);\n    ctx.strokeStyle = borderColor;\n    ctx.beginPath();\n    ctx.moveTo(x, high);\n    ctx.lineTo(x, Math.min(open, close));\n    ctx.moveTo(x, low);\n    ctx.lineTo(x, Math.max(open, close));\n    ctx.stroke();\n    ctx.fillRect(x - me.width / 2, close, me.width, open - close);\n    ctx.strokeRect(x - me.width / 2, close, me.width, open - close);\n    ctx.closePath();\n  }\n}\nclass CandlestickController extends FinancialController {\n  static id = 'candlestick';\n  static defaults = {\n    ...FinancialController.defaults,\n    dataElementType: CandlestickElement.id\n  };\n  static defaultRoutes = BarController.defaultRoutes;\n  updateElements(elements, start, count, mode) {\n    const reset = mode === 'reset';\n    const ruler = this._getRuler();\n    const {\n      sharedOptions,\n      includeOptions\n    } = this._getSharedOptions(start, mode);\n    for (let i = start; i < start + count; i++) {\n      const options = sharedOptions || this.resolveDataElementOptions(i, mode);\n      const baseProperties = this.calculateElementProperties(i, ruler, reset, options);\n      if (includeOptions) {\n        baseProperties.options = options;\n      }\n      this.updateElement(elements[i], i, baseProperties, mode);\n    }\n  }\n}\nexport { CandlestickController, CandlestickElement, OhlcController, OhlcElement };", "map": {"version": 3, "names": ["BarElement", "Chart", "BarController", "defaults", "defaults$1", "valueOrDefault", "isNullOrUndef", "clipArea", "unclipArea", "getBarBounds", "bar", "useFinalPosition", "x", "y", "base", "width", "height", "getProps", "left", "right", "top", "bottom", "half", "horizontal", "Math", "min", "max", "inRange", "skipX", "skipY", "bounds", "FinancialElement", "backgroundColors", "up", "down", "unchanged", "borderColors", "mouseX", "mouseY", "inXRange", "inYRange", "getRange", "axis", "getCenterPoint", "low", "high", "tooltipPosition", "open", "close", "OhlcElement", "id", "lineWidth", "<PERSON><PERSON><PERSON><PERSON>", "armLengthRatio", "draw", "ctx", "me", "elements", "ohlc", "strokeStyle", "options", "undefined", "beginPath", "moveTo", "lineTo", "stroke", "FinancialController", "overrides", "label", "parsing", "hover", "mode", "animations", "numbers", "type", "properties", "scales", "offset", "ticks", "major", "enabled", "source", "maxRotation", "autoSkip", "autoSkipPadding", "sampleSize", "plugins", "tooltip", "intersect", "callbacks", "point", "parsed", "o", "h", "l", "c", "getLabelAndValue", "index", "getParsed", "_cachedMeta", "iScale", "value", "getLabelForValue", "getUserBounds", "scale", "minDefined", "maxDefined", "Number", "NEGATIVE_INFINITY", "POSITIVE_INFINITY", "getMinMax", "meta", "_parsed", "otherScale", "_getOtherScale", "otherMin", "otherMax", "length", "newParsedData", "filter", "i", "data", "calculateElementProperties", "ruler", "reset", "vscale", "vScale", "getBasePixel", "ipixels", "_calculateBarIndexPixels", "chart", "datasets", "getPixelForValue", "center", "size", "rects", "chartArea", "_ctx", "OhlcController", "dataElementType", "barPercentage", "categoryPercentage", "updateElements", "start", "count", "_getRuler", "sharedOptions", "includeOptions", "_getSharedOptions", "resolveDataElementOptions", "baseProperties", "updateElement", "CandlestickElement", "borderWidth", "borderColor", "candlestick", "fillStyle", "fillRect", "strokeRect", "closePath", "CandlestickController", "defaultRoutes"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/chartjs-chart-financial/dist/chartjs-chart-financial.esm.js"], "sourcesContent": ["/*!\n * @license\n * chartjs-chart-financial\n * http://chartjs.org/\n * Version: 0.2.1\n *\n * Copyright 2024 Chart.js Contributors\n * Released under the MIT license\n * https://github.com/chartjs/chartjs-chart-financial/blob/master/LICENSE.md\n */\nimport { BarElement, Chart, BarController, defaults as defaults$1 } from 'chart.js';\nimport { valueOrDefault, isNullOrUndef, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * Helper function to get the bounds of the bar regardless of the orientation\n * @param {Rectangle} bar the bar\n * @param {boolean} [useFinalPosition]\n * @return {object} bounds of the bar\n * @private\n */\nfunction getBarBounds(bar, useFinalPosition) {\n  const {x, y, base, width, height} = bar.getProps(['x', 'low', 'high', 'width', 'height'], useFinalPosition);\n\n  let left, right, top, bottom, half;\n\n  if (bar.horizontal) {\n    half = height / 2;\n    left = Math.min(x, base);\n    right = Math.max(x, base);\n    top = y - half;\n    bottom = y + half;\n  } else {\n    half = width / 2;\n    left = x - half;\n    right = x + half;\n    top = Math.min(y, base); // use min because 0 pixel at top of screen\n    bottom = Math.max(y, base);\n  }\n\n  return {left, top, right, bottom};\n}\n\nfunction inRange(bar, x, y, useFinalPosition) {\n  const skipX = x === null;\n  const skipY = y === null;\n  const bounds = !bar || (skipX && skipY) ? false : getBarBounds(bar, useFinalPosition);\n\n  return bounds\n\t\t&& (skipX || x >= bounds.left && x <= bounds.right)\n\t\t&& (skipY || y >= bounds.top && y <= bounds.bottom);\n}\n\nclass FinancialElement extends BarElement {\n\n  static defaults = {\n    backgroundColors: {\n      up: 'rgba(75, 192, 192, 0.5)',\n      down: 'rgba(255, 99, 132, 0.5)',\n      unchanged: 'rgba(201, 203, 207, 0.5)',\n    },\n    borderColors: {\n      up: 'rgb(75, 192, 192)',\n      down: 'rgb(255, 99, 132)',\n      unchanged: 'rgb(201, 203, 207)',\n    }\n  };\n\n  height() {\n    return this.base - this.y;\n  }\n\n  inRange(mouseX, mouseY, useFinalPosition) {\n    return inRange(this, mouseX, mouseY, useFinalPosition);\n  }\n\n  inXRange(mouseX, useFinalPosition) {\n    return inRange(this, mouseX, null, useFinalPosition);\n  }\n\n  inYRange(mouseY, useFinalPosition) {\n    return inRange(this, null, mouseY, useFinalPosition);\n  }\n\n  getRange(axis) {\n    return axis === 'x' ? this.width / 2 : this.height / 2;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    const {x, low, high} = this.getProps(['x', 'low', 'high'], useFinalPosition);\n    return {\n      x,\n      y: (high + low) / 2\n    };\n  }\n\n  tooltipPosition(useFinalPosition) {\n    const {x, open, close} = this.getProps(['x', 'open', 'close'], useFinalPosition);\n    return {\n      x,\n      y: (open + close) / 2\n    };\n  }\n}\n\nconst defaults = Chart.defaults;\n\nclass OhlcElement extends FinancialElement {\n  static id = 'ohlc';\n\n  static defaults = {\n    ...FinancialElement.defaults,\n    lineWidth: 2,\n    armLength: null,\n    armLengthRatio: 0.8\n  };\n\n  draw(ctx) {\n    const me = this;\n\n    const {x, open, high, low, close} = me;\n\n    const armLengthRatio = valueOrDefault(me.armLengthRatio, defaults.elements.ohlc.armLengthRatio);\n    let armLength = valueOrDefault(me.armLength, defaults.elements.ohlc.armLength);\n    if (armLength === null) {\n      // The width of an ohlc is affected by barPercentage and categoryPercentage\n      // This behavior is caused by extending controller.financial, which extends controller.bar\n      // barPercentage and categoryPercentage are now set to 1.0 (see controller.ohlc)\n      // and armLengthRatio is multipled by 0.5,\n      // so that when armLengthRatio=1.0, the arms from neighbour ohcl touch,\n      // and when armLengthRatio=0.0, ohcl are just vertical lines.\n      armLength = me.width * armLengthRatio * 0.5;\n    }\n\n    if (close < open) {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.up : undefined, defaults.elements.ohlc.borderColors.up);\n    } else if (close > open) {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.down : undefined, defaults.elements.ohlc.borderColors.down);\n    } else {\n      ctx.strokeStyle = valueOrDefault(me.options.borderColors ? me.options.borderColors.unchanged : undefined, defaults.elements.ohlc.borderColors.unchanged);\n    }\n    ctx.lineWidth = valueOrDefault(me.lineWidth, defaults.elements.ohlc.lineWidth);\n\n    ctx.beginPath();\n    ctx.moveTo(x, high);\n    ctx.lineTo(x, low);\n    ctx.moveTo(x - armLength, open);\n    ctx.lineTo(x, open);\n    ctx.moveTo(x + armLength, close);\n    ctx.lineTo(x, close);\n    ctx.stroke();\n  }\n}\n\n/**\n * This class is based off controller.bar.js from the upstream Chart.js library\n */\nclass FinancialController extends BarController {\n\n  static overrides = {\n    label: '',\n\n    parsing: false,\n\n    hover: {\n      mode: 'label'\n    },\n    animations: {\n      numbers: {\n        type: 'number',\n        properties: ['x', 'y', 'base', 'width', 'open', 'high', 'low', 'close']\n      }\n    },\n\n    scales: {\n      x: {\n        type: 'timeseries',\n        offset: true,\n        ticks: {\n          major: {\n            enabled: true,\n          },\n          source: 'data',\n          maxRotation: 0,\n          autoSkip: true,\n          autoSkipPadding: 75,\n          sampleSize: 100\n        },\n      },\n      y: {\n        type: 'linear'\n      }\n    },\n\n    plugins: {\n      tooltip: {\n        intersect: false,\n        mode: 'index',\n        callbacks: {\n          label(ctx) {\n            const point = ctx.parsed;\n\n            if (!isNullOrUndef(point.y)) {\n              return defaults$1.plugins.tooltip.callbacks.label(ctx);\n            }\n\n            const {o, h, l, c} = point;\n\n            return `O: ${o}  H: ${h}  L: ${l}  C: ${c}`;\n          }\n        }\n      }\n    }\n  };\n\n  getLabelAndValue(index) {\n    const me = this;\n    const parsed = me.getParsed(index);\n    const axis = me._cachedMeta.iScale.axis;\n\n    const {o, h, l, c} = parsed;\n    const value = `O: ${o}  H: ${h}  L: ${l}  C: ${c}`;\n\n    return {\n      label: `${me._cachedMeta.iScale.getLabelForValue(parsed[axis])}`,\n      value\n    };\n  }\n\n  getUserBounds(scale) {\n    const {min, max, minDefined, maxDefined} = scale.getUserBounds();\n    return {\n      min: minDefined ? min : Number.NEGATIVE_INFINITY,\n      max: maxDefined ? max : Number.POSITIVE_INFINITY\n    };\n  }\n\n  /**\n   * Implement this ourselves since it doesn't handle high and low values\n   * https://github.com/chartjs/Chart.js/issues/7328\n   * @protected\n   */\n  getMinMax(scale) {\n    const meta = this._cachedMeta;\n    const _parsed = meta._parsed;\n    const axis = meta.iScale.axis;\n    const otherScale = this._getOtherScale(scale);\n    const {min: otherMin, max: otherMax} = this.getUserBounds(otherScale);\n\n    if (_parsed.length < 2) {\n      return {min: 0, max: 1};\n    }\n\n    if (scale === meta.iScale) {\n      return {min: _parsed[0][axis], max: _parsed[_parsed.length - 1][axis]};\n    }\n\n    const newParsedData = _parsed.filter(({x}) => x >= otherMin && x < otherMax);\n\n    let min = Number.POSITIVE_INFINITY;\n    let max = Number.NEGATIVE_INFINITY;\n    for (let i = 0; i < newParsedData.length; i++) {\n      const data = newParsedData[i];\n      min = Math.min(min, data.l);\n      max = Math.max(max, data.h);\n    }\n    return {min, max};\n  }\n\n  /**\n   * @protected\n   */\n  calculateElementProperties(index, ruler, reset, options) {\n    const me = this;\n    const vscale = me._cachedMeta.vScale;\n    const base = vscale.getBasePixel();\n    const ipixels = me._calculateBarIndexPixels(index, ruler, options);\n    const data = me.chart.data.datasets[me.index].data[index];\n    const open = vscale.getPixelForValue(data.o);\n    const high = vscale.getPixelForValue(data.h);\n    const low = vscale.getPixelForValue(data.l);\n    const close = vscale.getPixelForValue(data.c);\n\n    return {\n      base: reset ? base : low,\n      x: ipixels.center,\n      y: (low + high) / 2,\n      width: ipixels.size,\n      open,\n      high,\n      low,\n      close\n    };\n  }\n\n  draw() {\n    const me = this;\n    const chart = me.chart;\n    const rects = me._cachedMeta.data;\n    clipArea(chart.ctx, chart.chartArea);\n    for (let i = 0; i < rects.length; ++i) {\n      rects[i].draw(me._ctx);\n    }\n    unclipArea(chart.ctx);\n  }\n\n}\n\nclass OhlcController extends FinancialController {\n  static id = 'ohlc';\n\n  static defaults = {\n    ...FinancialController.defaults,\n    dataElementType: OhlcElement.id,\n    datasets: {\n      barPercentage: 1.0,\n      categoryPercentage: 1.0\n    }\n  };\n\n  updateElements(elements, start, count, mode) {\n    const reset = mode === 'reset';\n    const ruler = this._getRuler();\n    const {sharedOptions, includeOptions} = this._getSharedOptions(start, mode);\n\n    for (let i = start; i < start + count; i++) {\n      const options = sharedOptions || this.resolveDataElementOptions(i, mode);\n\n      const baseProperties = this.calculateElementProperties(i, ruler, reset, options);\n\n      if (includeOptions) {\n        baseProperties.options = options;\n      }\n      this.updateElement(elements[i], i, baseProperties, mode);\n    }\n  }\n\n}\n\nclass CandlestickElement extends FinancialElement {\n  static id = 'candlestick';\n\n  static defaults = {\n    ...FinancialElement.defaults,\n    borderWidth: 1,\n  };\n\n  draw(ctx) {\n    const me = this;\n\n    const {x, open, high, low, close} = me;\n\n    let borderColors = me.options.borderColors;\n    if (typeof borderColors === 'string') {\n      borderColors = {\n        up: borderColors,\n        down: borderColors,\n        unchanged: borderColors\n      };\n    }\n\n    let borderColor;\n    if (close < open) {\n      borderColor = valueOrDefault(borderColors ? borderColors.up : undefined, defaults$1.elements.candlestick.borderColors.up);\n      ctx.fillStyle = valueOrDefault(me.options.backgroundColors ? me.options.backgroundColors.up : undefined, defaults$1.elements.candlestick.backgroundColors.up);\n    } else if (close > open) {\n      borderColor = valueOrDefault(borderColors ? borderColors.down : undefined, defaults$1.elements.candlestick.borderColors.down);\n      ctx.fillStyle = valueOrDefault(me.options.backgroundColors ? me.options.backgroundColors.down : undefined, defaults$1.elements.candlestick.backgroundColors.down);\n    } else {\n      borderColor = valueOrDefault(borderColors ? borderColors.unchanged : undefined, defaults$1.elements.candlestick.borderColors.unchanged);\n      ctx.fillStyle = valueOrDefault(me.backgroundColors ? me.backgroundColors.unchanged : undefined, defaults$1.elements.candlestick.backgroundColors.unchanged);\n    }\n\n    ctx.lineWidth = valueOrDefault(me.options.borderWidth, defaults$1.elements.candlestick.borderWidth);\n    ctx.strokeStyle = borderColor;\n\n    ctx.beginPath();\n    ctx.moveTo(x, high);\n    ctx.lineTo(x, Math.min(open, close));\n    ctx.moveTo(x, low);\n    ctx.lineTo(x, Math.max(open, close));\n    ctx.stroke();\n    ctx.fillRect(x - me.width / 2, close, me.width, open - close);\n    ctx.strokeRect(x - me.width / 2, close, me.width, open - close);\n    ctx.closePath();\n  }\n}\n\nclass CandlestickController extends FinancialController {\n\n  static id = 'candlestick';\n\n  static defaults = {\n    ...FinancialController.defaults,\n    dataElementType: CandlestickElement.id\n  };\n\n  static defaultRoutes = BarController.defaultRoutes;\n\n  updateElements(elements, start, count, mode) {\n    const reset = mode === 'reset';\n    const ruler = this._getRuler();\n    const {sharedOptions, includeOptions} = this._getSharedOptions(start, mode);\n\n    for (let i = start; i < start + count; i++) {\n      const options = sharedOptions || this.resolveDataElementOptions(i, mode);\n\n      const baseProperties = this.calculateElementProperties(i, ruler, reset, options);\n\n      if (includeOptions) {\n        baseProperties.options = options;\n      }\n      this.updateElement(elements[i], i, baseProperties, mode);\n    }\n  }\n\n}\n\nexport { CandlestickController, CandlestickElement, OhlcController, OhlcElement };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,KAAK,EAAEC,aAAa,EAAEC,QAAQ,IAAIC,UAAU,QAAQ,UAAU;AACnF,SAASC,cAAc,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,kBAAkB;;AAEtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,GAAG,EAAEC,gBAAgB,EAAE;EAC3C,MAAM;IAACC,CAAC;IAAEC,CAAC;IAAEC,IAAI;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAEN,gBAAgB,CAAC;EAE3G,IAAIO,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI;EAElC,IAAIZ,GAAG,CAACa,UAAU,EAAE;IAClBD,IAAI,GAAGN,MAAM,GAAG,CAAC;IACjBE,IAAI,GAAGM,IAAI,CAACC,GAAG,CAACb,CAAC,EAAEE,IAAI,CAAC;IACxBK,KAAK,GAAGK,IAAI,CAACE,GAAG,CAACd,CAAC,EAAEE,IAAI,CAAC;IACzBM,GAAG,GAAGP,CAAC,GAAGS,IAAI;IACdD,MAAM,GAAGR,CAAC,GAAGS,IAAI;EACnB,CAAC,MAAM;IACLA,IAAI,GAAGP,KAAK,GAAG,CAAC;IAChBG,IAAI,GAAGN,CAAC,GAAGU,IAAI;IACfH,KAAK,GAAGP,CAAC,GAAGU,IAAI;IAChBF,GAAG,GAAGI,IAAI,CAACC,GAAG,CAACZ,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC;IACzBO,MAAM,GAAGG,IAAI,CAACE,GAAG,CAACb,CAAC,EAAEC,IAAI,CAAC;EAC5B;EAEA,OAAO;IAACI,IAAI;IAAEE,GAAG;IAAED,KAAK;IAAEE;EAAM,CAAC;AACnC;AAEA,SAASM,OAAOA,CAACjB,GAAG,EAAEE,CAAC,EAAEC,CAAC,EAAEF,gBAAgB,EAAE;EAC5C,MAAMiB,KAAK,GAAGhB,CAAC,KAAK,IAAI;EACxB,MAAMiB,KAAK,GAAGhB,CAAC,KAAK,IAAI;EACxB,MAAMiB,MAAM,GAAG,CAACpB,GAAG,IAAKkB,KAAK,IAAIC,KAAM,GAAG,KAAK,GAAGpB,YAAY,CAACC,GAAG,EAAEC,gBAAgB,CAAC;EAErF,OAAOmB,MAAM,KACTF,KAAK,IAAIhB,CAAC,IAAIkB,MAAM,CAACZ,IAAI,IAAIN,CAAC,IAAIkB,MAAM,CAACX,KAAK,CAAC,KAC/CU,KAAK,IAAIhB,CAAC,IAAIiB,MAAM,CAACV,GAAG,IAAIP,CAAC,IAAIiB,MAAM,CAACT,MAAM,CAAC;AACrD;AAEA,MAAMU,gBAAgB,SAAS/B,UAAU,CAAC;EAExC,OAAOG,QAAQ,GAAG;IAChB6B,gBAAgB,EAAE;MAChBC,EAAE,EAAE,yBAAyB;MAC7BC,IAAI,EAAE,yBAAyB;MAC/BC,SAAS,EAAE;IACb,CAAC;IACDC,YAAY,EAAE;MACZH,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,mBAAmB;MACzBC,SAAS,EAAE;IACb;EACF,CAAC;EAEDnB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,IAAI,GAAG,IAAI,CAACD,CAAC;EAC3B;EAEAc,OAAOA,CAACU,MAAM,EAAEC,MAAM,EAAE3B,gBAAgB,EAAE;IACxC,OAAOgB,OAAO,CAAC,IAAI,EAAEU,MAAM,EAAEC,MAAM,EAAE3B,gBAAgB,CAAC;EACxD;EAEA4B,QAAQA,CAACF,MAAM,EAAE1B,gBAAgB,EAAE;IACjC,OAAOgB,OAAO,CAAC,IAAI,EAAEU,MAAM,EAAE,IAAI,EAAE1B,gBAAgB,CAAC;EACtD;EAEA6B,QAAQA,CAACF,MAAM,EAAE3B,gBAAgB,EAAE;IACjC,OAAOgB,OAAO,CAAC,IAAI,EAAE,IAAI,EAAEW,MAAM,EAAE3B,gBAAgB,CAAC;EACtD;EAEA8B,QAAQA,CAACC,IAAI,EAAE;IACb,OAAOA,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC3B,KAAK,GAAG,CAAC,GAAG,IAAI,CAACC,MAAM,GAAG,CAAC;EACxD;EAEA2B,cAAcA,CAAChC,gBAAgB,EAAE;IAC/B,MAAM;MAACC,CAAC;MAAEgC,GAAG;MAAEC;IAAI,CAAC,GAAG,IAAI,CAAC5B,QAAQ,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,EAAEN,gBAAgB,CAAC;IAC5E,OAAO;MACLC,CAAC;MACDC,CAAC,EAAE,CAACgC,IAAI,GAAGD,GAAG,IAAI;IACpB,CAAC;EACH;EAEAE,eAAeA,CAACnC,gBAAgB,EAAE;IAChC,MAAM;MAACC,CAAC;MAAEmC,IAAI;MAAEC;IAAK,CAAC,GAAG,IAAI,CAAC/B,QAAQ,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,EAAEN,gBAAgB,CAAC;IAChF,OAAO;MACLC,CAAC;MACDC,CAAC,EAAE,CAACkC,IAAI,GAAGC,KAAK,IAAI;IACtB,CAAC;EACH;AACF;AAEA,MAAM7C,QAAQ,GAAGF,KAAK,CAACE,QAAQ;AAE/B,MAAM8C,WAAW,SAASlB,gBAAgB,CAAC;EACzC,OAAOmB,EAAE,GAAG,MAAM;EAElB,OAAO/C,QAAQ,GAAG;IAChB,GAAG4B,gBAAgB,CAAC5B,QAAQ;IAC5BgD,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAE;EAClB,CAAC;EAEDC,IAAIA,CAACC,GAAG,EAAE;IACR,MAAMC,EAAE,GAAG,IAAI;IAEf,MAAM;MAAC5C,CAAC;MAAEmC,IAAI;MAAEF,IAAI;MAAED,GAAG;MAAEI;IAAK,CAAC,GAAGQ,EAAE;IAEtC,MAAMH,cAAc,GAAGhD,cAAc,CAACmD,EAAE,CAACH,cAAc,EAAElD,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACL,cAAc,CAAC;IAC/F,IAAID,SAAS,GAAG/C,cAAc,CAACmD,EAAE,CAACJ,SAAS,EAAEjD,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACN,SAAS,CAAC;IAC9E,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB;MACA;MACA;MACA;MACA;MACA;MACAA,SAAS,GAAGI,EAAE,CAACzC,KAAK,GAAGsC,cAAc,GAAG,GAAG;IAC7C;IAEA,IAAIL,KAAK,GAAGD,IAAI,EAAE;MAChBQ,GAAG,CAACI,WAAW,GAAGtD,cAAc,CAACmD,EAAE,CAACI,OAAO,CAACxB,YAAY,GAAGoB,EAAE,CAACI,OAAO,CAACxB,YAAY,CAACH,EAAE,GAAG4B,SAAS,EAAE1D,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACtB,YAAY,CAACH,EAAE,CAAC;IAC5I,CAAC,MAAM,IAAIe,KAAK,GAAGD,IAAI,EAAE;MACvBQ,GAAG,CAACI,WAAW,GAAGtD,cAAc,CAACmD,EAAE,CAACI,OAAO,CAACxB,YAAY,GAAGoB,EAAE,CAACI,OAAO,CAACxB,YAAY,CAACF,IAAI,GAAG2B,SAAS,EAAE1D,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACtB,YAAY,CAACF,IAAI,CAAC;IAChJ,CAAC,MAAM;MACLqB,GAAG,CAACI,WAAW,GAAGtD,cAAc,CAACmD,EAAE,CAACI,OAAO,CAACxB,YAAY,GAAGoB,EAAE,CAACI,OAAO,CAACxB,YAAY,CAACD,SAAS,GAAG0B,SAAS,EAAE1D,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACtB,YAAY,CAACD,SAAS,CAAC;IAC1J;IACAoB,GAAG,CAACJ,SAAS,GAAG9C,cAAc,CAACmD,EAAE,CAACL,SAAS,EAAEhD,QAAQ,CAACsD,QAAQ,CAACC,IAAI,CAACP,SAAS,CAAC;IAE9EI,GAAG,CAACO,SAAS,CAAC,CAAC;IACfP,GAAG,CAACQ,MAAM,CAACnD,CAAC,EAAEiC,IAAI,CAAC;IACnBU,GAAG,CAACS,MAAM,CAACpD,CAAC,EAAEgC,GAAG,CAAC;IAClBW,GAAG,CAACQ,MAAM,CAACnD,CAAC,GAAGwC,SAAS,EAAEL,IAAI,CAAC;IAC/BQ,GAAG,CAACS,MAAM,CAACpD,CAAC,EAAEmC,IAAI,CAAC;IACnBQ,GAAG,CAACQ,MAAM,CAACnD,CAAC,GAAGwC,SAAS,EAAEJ,KAAK,CAAC;IAChCO,GAAG,CAACS,MAAM,CAACpD,CAAC,EAAEoC,KAAK,CAAC;IACpBO,GAAG,CAACU,MAAM,CAAC,CAAC;EACd;AACF;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,SAAShE,aAAa,CAAC;EAE9C,OAAOiE,SAAS,GAAG;IACjBC,KAAK,EAAE,EAAE;IAETC,OAAO,EAAE,KAAK;IAEdC,KAAK,EAAE;MACLC,IAAI,EAAE;IACR,CAAC;IACDC,UAAU,EAAE;MACVC,OAAO,EAAE;QACPC,IAAI,EAAE,QAAQ;QACdC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;MACxE;IACF,CAAC;IAEDC,MAAM,EAAE;MACNhE,CAAC,EAAE;QACD8D,IAAI,EAAE,YAAY;QAClBG,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE;UACLC,KAAK,EAAE;YACLC,OAAO,EAAE;UACX,CAAC;UACDC,MAAM,EAAE,MAAM;UACdC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE,EAAE;UACnBC,UAAU,EAAE;QACd;MACF,CAAC;MACDxE,CAAC,EAAE;QACD6D,IAAI,EAAE;MACR;IACF,CAAC;IAEDY,OAAO,EAAE;MACPC,OAAO,EAAE;QACPC,SAAS,EAAE,KAAK;QAChBjB,IAAI,EAAE,OAAO;QACbkB,SAAS,EAAE;UACTrB,KAAKA,CAACb,GAAG,EAAE;YACT,MAAMmC,KAAK,GAAGnC,GAAG,CAACoC,MAAM;YAExB,IAAI,CAACrF,aAAa,CAACoF,KAAK,CAAC7E,CAAC,CAAC,EAAE;cAC3B,OAAOT,UAAU,CAACkF,OAAO,CAACC,OAAO,CAACE,SAAS,CAACrB,KAAK,CAACb,GAAG,CAAC;YACxD;YAEA,MAAM;cAACqC,CAAC;cAAEC,CAAC;cAAEC,CAAC;cAAEC;YAAC,CAAC,GAAGL,KAAK;YAE1B,OAAO,MAAME,CAAC,QAAQC,CAAC,QAAQC,CAAC,QAAQC,CAAC,EAAE;UAC7C;QACF;MACF;IACF;EACF,CAAC;EAEDC,gBAAgBA,CAACC,KAAK,EAAE;IACtB,MAAMzC,EAAE,GAAG,IAAI;IACf,MAAMmC,MAAM,GAAGnC,EAAE,CAAC0C,SAAS,CAACD,KAAK,CAAC;IAClC,MAAMvD,IAAI,GAAGc,EAAE,CAAC2C,WAAW,CAACC,MAAM,CAAC1D,IAAI;IAEvC,MAAM;MAACkD,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,GAAGJ,MAAM;IAC3B,MAAMU,KAAK,GAAG,MAAMT,CAAC,QAAQC,CAAC,QAAQC,CAAC,QAAQC,CAAC,EAAE;IAElD,OAAO;MACL3B,KAAK,EAAE,GAAGZ,EAAE,CAAC2C,WAAW,CAACC,MAAM,CAACE,gBAAgB,CAACX,MAAM,CAACjD,IAAI,CAAC,CAAC,EAAE;MAChE2D;IACF,CAAC;EACH;EAEAE,aAAaA,CAACC,KAAK,EAAE;IACnB,MAAM;MAAC/E,GAAG;MAAEC,GAAG;MAAE+E,UAAU;MAAEC;IAAU,CAAC,GAAGF,KAAK,CAACD,aAAa,CAAC,CAAC;IAChE,OAAO;MACL9E,GAAG,EAAEgF,UAAU,GAAGhF,GAAG,GAAGkF,MAAM,CAACC,iBAAiB;MAChDlF,GAAG,EAAEgF,UAAU,GAAGhF,GAAG,GAAGiF,MAAM,CAACE;IACjC,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACEC,SAASA,CAACN,KAAK,EAAE;IACf,MAAMO,IAAI,GAAG,IAAI,CAACZ,WAAW;IAC7B,MAAMa,OAAO,GAAGD,IAAI,CAACC,OAAO;IAC5B,MAAMtE,IAAI,GAAGqE,IAAI,CAACX,MAAM,CAAC1D,IAAI;IAC7B,MAAMuE,UAAU,GAAG,IAAI,CAACC,cAAc,CAACV,KAAK,CAAC;IAC7C,MAAM;MAAC/E,GAAG,EAAE0F,QAAQ;MAAEzF,GAAG,EAAE0F;IAAQ,CAAC,GAAG,IAAI,CAACb,aAAa,CAACU,UAAU,CAAC;IAErE,IAAID,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;MACtB,OAAO;QAAC5F,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAC,CAAC;IACzB;IAEA,IAAI8E,KAAK,KAAKO,IAAI,CAACX,MAAM,EAAE;MACzB,OAAO;QAAC3E,GAAG,EAAEuF,OAAO,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC;QAAEhB,GAAG,EAAEsF,OAAO,CAACA,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC3E,IAAI;MAAC,CAAC;IACxE;IAEA,MAAM4E,aAAa,GAAGN,OAAO,CAACO,MAAM,CAAC,CAAC;MAAC3G;IAAC,CAAC,KAAKA,CAAC,IAAIuG,QAAQ,IAAIvG,CAAC,GAAGwG,QAAQ,CAAC;IAE5E,IAAI3F,GAAG,GAAGkF,MAAM,CAACE,iBAAiB;IAClC,IAAInF,GAAG,GAAGiF,MAAM,CAACC,iBAAiB;IAClC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;MAC7C,MAAMC,IAAI,GAAGH,aAAa,CAACE,CAAC,CAAC;MAC7B/F,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAEgG,IAAI,CAAC3B,CAAC,CAAC;MAC3BpE,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACA,GAAG,EAAE+F,IAAI,CAAC5B,CAAC,CAAC;IAC7B;IACA,OAAO;MAACpE,GAAG;MAAEC;IAAG,CAAC;EACnB;;EAEA;AACF;AACA;EACEgG,0BAA0BA,CAACzB,KAAK,EAAE0B,KAAK,EAAEC,KAAK,EAAEhE,OAAO,EAAE;IACvD,MAAMJ,EAAE,GAAG,IAAI;IACf,MAAMqE,MAAM,GAAGrE,EAAE,CAAC2C,WAAW,CAAC2B,MAAM;IACpC,MAAMhH,IAAI,GAAG+G,MAAM,CAACE,YAAY,CAAC,CAAC;IAClC,MAAMC,OAAO,GAAGxE,EAAE,CAACyE,wBAAwB,CAAChC,KAAK,EAAE0B,KAAK,EAAE/D,OAAO,CAAC;IAClE,MAAM6D,IAAI,GAAGjE,EAAE,CAAC0E,KAAK,CAACT,IAAI,CAACU,QAAQ,CAAC3E,EAAE,CAACyC,KAAK,CAAC,CAACwB,IAAI,CAACxB,KAAK,CAAC;IACzD,MAAMlD,IAAI,GAAG8E,MAAM,CAACO,gBAAgB,CAACX,IAAI,CAAC7B,CAAC,CAAC;IAC5C,MAAM/C,IAAI,GAAGgF,MAAM,CAACO,gBAAgB,CAACX,IAAI,CAAC5B,CAAC,CAAC;IAC5C,MAAMjD,GAAG,GAAGiF,MAAM,CAACO,gBAAgB,CAACX,IAAI,CAAC3B,CAAC,CAAC;IAC3C,MAAM9C,KAAK,GAAG6E,MAAM,CAACO,gBAAgB,CAACX,IAAI,CAAC1B,CAAC,CAAC;IAE7C,OAAO;MACLjF,IAAI,EAAE8G,KAAK,GAAG9G,IAAI,GAAG8B,GAAG;MACxBhC,CAAC,EAAEoH,OAAO,CAACK,MAAM;MACjBxH,CAAC,EAAE,CAAC+B,GAAG,GAAGC,IAAI,IAAI,CAAC;MACnB9B,KAAK,EAAEiH,OAAO,CAACM,IAAI;MACnBvF,IAAI;MACJF,IAAI;MACJD,GAAG;MACHI;IACF,CAAC;EACH;EAEAM,IAAIA,CAAA,EAAG;IACL,MAAME,EAAE,GAAG,IAAI;IACf,MAAM0E,KAAK,GAAG1E,EAAE,CAAC0E,KAAK;IACtB,MAAMK,KAAK,GAAG/E,EAAE,CAAC2C,WAAW,CAACsB,IAAI;IACjClH,QAAQ,CAAC2H,KAAK,CAAC3E,GAAG,EAAE2E,KAAK,CAACM,SAAS,CAAC;IACpC,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAAClB,MAAM,EAAE,EAAEG,CAAC,EAAE;MACrCe,KAAK,CAACf,CAAC,CAAC,CAAClE,IAAI,CAACE,EAAE,CAACiF,IAAI,CAAC;IACxB;IACAjI,UAAU,CAAC0H,KAAK,CAAC3E,GAAG,CAAC;EACvB;AAEF;AAEA,MAAMmF,cAAc,SAASxE,mBAAmB,CAAC;EAC/C,OAAOhB,EAAE,GAAG,MAAM;EAElB,OAAO/C,QAAQ,GAAG;IAChB,GAAG+D,mBAAmB,CAAC/D,QAAQ;IAC/BwI,eAAe,EAAE1F,WAAW,CAACC,EAAE;IAC/BiF,QAAQ,EAAE;MACRS,aAAa,EAAE,GAAG;MAClBC,kBAAkB,EAAE;IACtB;EACF,CAAC;EAEDC,cAAcA,CAACrF,QAAQ,EAAEsF,KAAK,EAAEC,KAAK,EAAEzE,IAAI,EAAE;IAC3C,MAAMqD,KAAK,GAAGrD,IAAI,KAAK,OAAO;IAC9B,MAAMoD,KAAK,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC;IAC9B,MAAM;MAACC,aAAa;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAACL,KAAK,EAAExE,IAAI,CAAC;IAE3E,KAAK,IAAIiD,CAAC,GAAGuB,KAAK,EAAEvB,CAAC,GAAGuB,KAAK,GAAGC,KAAK,EAAExB,CAAC,EAAE,EAAE;MAC1C,MAAM5D,OAAO,GAAGsF,aAAa,IAAI,IAAI,CAACG,yBAAyB,CAAC7B,CAAC,EAAEjD,IAAI,CAAC;MAExE,MAAM+E,cAAc,GAAG,IAAI,CAAC5B,0BAA0B,CAACF,CAAC,EAAEG,KAAK,EAAEC,KAAK,EAAEhE,OAAO,CAAC;MAEhF,IAAIuF,cAAc,EAAE;QAClBG,cAAc,CAAC1F,OAAO,GAAGA,OAAO;MAClC;MACA,IAAI,CAAC2F,aAAa,CAAC9F,QAAQ,CAAC+D,CAAC,CAAC,EAAEA,CAAC,EAAE8B,cAAc,EAAE/E,IAAI,CAAC;IAC1D;EACF;AAEF;AAEA,MAAMiF,kBAAkB,SAASzH,gBAAgB,CAAC;EAChD,OAAOmB,EAAE,GAAG,aAAa;EAEzB,OAAO/C,QAAQ,GAAG;IAChB,GAAG4B,gBAAgB,CAAC5B,QAAQ;IAC5BsJ,WAAW,EAAE;EACf,CAAC;EAEDnG,IAAIA,CAACC,GAAG,EAAE;IACR,MAAMC,EAAE,GAAG,IAAI;IAEf,MAAM;MAAC5C,CAAC;MAAEmC,IAAI;MAAEF,IAAI;MAAED,GAAG;MAAEI;IAAK,CAAC,GAAGQ,EAAE;IAEtC,IAAIpB,YAAY,GAAGoB,EAAE,CAACI,OAAO,CAACxB,YAAY;IAC1C,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpCA,YAAY,GAAG;QACbH,EAAE,EAAEG,YAAY;QAChBF,IAAI,EAAEE,YAAY;QAClBD,SAAS,EAAEC;MACb,CAAC;IACH;IAEA,IAAIsH,WAAW;IACf,IAAI1G,KAAK,GAAGD,IAAI,EAAE;MAChB2G,WAAW,GAAGrJ,cAAc,CAAC+B,YAAY,GAAGA,YAAY,CAACH,EAAE,GAAG4B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAACvH,YAAY,CAACH,EAAE,CAAC;MACzHsB,GAAG,CAACqG,SAAS,GAAGvJ,cAAc,CAACmD,EAAE,CAACI,OAAO,CAAC5B,gBAAgB,GAAGwB,EAAE,CAACI,OAAO,CAAC5B,gBAAgB,CAACC,EAAE,GAAG4B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAAC3H,gBAAgB,CAACC,EAAE,CAAC;IAC/J,CAAC,MAAM,IAAIe,KAAK,GAAGD,IAAI,EAAE;MACvB2G,WAAW,GAAGrJ,cAAc,CAAC+B,YAAY,GAAGA,YAAY,CAACF,IAAI,GAAG2B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAACvH,YAAY,CAACF,IAAI,CAAC;MAC7HqB,GAAG,CAACqG,SAAS,GAAGvJ,cAAc,CAACmD,EAAE,CAACI,OAAO,CAAC5B,gBAAgB,GAAGwB,EAAE,CAACI,OAAO,CAAC5B,gBAAgB,CAACE,IAAI,GAAG2B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAAC3H,gBAAgB,CAACE,IAAI,CAAC;IACnK,CAAC,MAAM;MACLwH,WAAW,GAAGrJ,cAAc,CAAC+B,YAAY,GAAGA,YAAY,CAACD,SAAS,GAAG0B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAACvH,YAAY,CAACD,SAAS,CAAC;MACvIoB,GAAG,CAACqG,SAAS,GAAGvJ,cAAc,CAACmD,EAAE,CAACxB,gBAAgB,GAAGwB,EAAE,CAACxB,gBAAgB,CAACG,SAAS,GAAG0B,SAAS,EAAEzD,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAAC3H,gBAAgB,CAACG,SAAS,CAAC;IAC7J;IAEAoB,GAAG,CAACJ,SAAS,GAAG9C,cAAc,CAACmD,EAAE,CAACI,OAAO,CAAC6F,WAAW,EAAErJ,UAAU,CAACqD,QAAQ,CAACkG,WAAW,CAACF,WAAW,CAAC;IACnGlG,GAAG,CAACI,WAAW,GAAG+F,WAAW;IAE7BnG,GAAG,CAACO,SAAS,CAAC,CAAC;IACfP,GAAG,CAACQ,MAAM,CAACnD,CAAC,EAAEiC,IAAI,CAAC;IACnBU,GAAG,CAACS,MAAM,CAACpD,CAAC,EAAEY,IAAI,CAACC,GAAG,CAACsB,IAAI,EAAEC,KAAK,CAAC,CAAC;IACpCO,GAAG,CAACQ,MAAM,CAACnD,CAAC,EAAEgC,GAAG,CAAC;IAClBW,GAAG,CAACS,MAAM,CAACpD,CAAC,EAAEY,IAAI,CAACE,GAAG,CAACqB,IAAI,EAAEC,KAAK,CAAC,CAAC;IACpCO,GAAG,CAACU,MAAM,CAAC,CAAC;IACZV,GAAG,CAACsG,QAAQ,CAACjJ,CAAC,GAAG4C,EAAE,CAACzC,KAAK,GAAG,CAAC,EAAEiC,KAAK,EAAEQ,EAAE,CAACzC,KAAK,EAAEgC,IAAI,GAAGC,KAAK,CAAC;IAC7DO,GAAG,CAACuG,UAAU,CAAClJ,CAAC,GAAG4C,EAAE,CAACzC,KAAK,GAAG,CAAC,EAAEiC,KAAK,EAAEQ,EAAE,CAACzC,KAAK,EAAEgC,IAAI,GAAGC,KAAK,CAAC;IAC/DO,GAAG,CAACwG,SAAS,CAAC,CAAC;EACjB;AACF;AAEA,MAAMC,qBAAqB,SAAS9F,mBAAmB,CAAC;EAEtD,OAAOhB,EAAE,GAAG,aAAa;EAEzB,OAAO/C,QAAQ,GAAG;IAChB,GAAG+D,mBAAmB,CAAC/D,QAAQ;IAC/BwI,eAAe,EAAEa,kBAAkB,CAACtG;EACtC,CAAC;EAED,OAAO+G,aAAa,GAAG/J,aAAa,CAAC+J,aAAa;EAElDnB,cAAcA,CAACrF,QAAQ,EAAEsF,KAAK,EAAEC,KAAK,EAAEzE,IAAI,EAAE;IAC3C,MAAMqD,KAAK,GAAGrD,IAAI,KAAK,OAAO;IAC9B,MAAMoD,KAAK,GAAG,IAAI,CAACsB,SAAS,CAAC,CAAC;IAC9B,MAAM;MAACC,aAAa;MAAEC;IAAc,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAACL,KAAK,EAAExE,IAAI,CAAC;IAE3E,KAAK,IAAIiD,CAAC,GAAGuB,KAAK,EAAEvB,CAAC,GAAGuB,KAAK,GAAGC,KAAK,EAAExB,CAAC,EAAE,EAAE;MAC1C,MAAM5D,OAAO,GAAGsF,aAAa,IAAI,IAAI,CAACG,yBAAyB,CAAC7B,CAAC,EAAEjD,IAAI,CAAC;MAExE,MAAM+E,cAAc,GAAG,IAAI,CAAC5B,0BAA0B,CAACF,CAAC,EAAEG,KAAK,EAAEC,KAAK,EAAEhE,OAAO,CAAC;MAEhF,IAAIuF,cAAc,EAAE;QAClBG,cAAc,CAAC1F,OAAO,GAAGA,OAAO;MAClC;MACA,IAAI,CAAC2F,aAAa,CAAC9F,QAAQ,CAAC+D,CAAC,CAAC,EAAEA,CAAC,EAAE8B,cAAc,EAAE/E,IAAI,CAAC;IAC1D;EACF;AAEF;AAEA,SAASyF,qBAAqB,EAAER,kBAAkB,EAAEd,cAAc,EAAEzF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}