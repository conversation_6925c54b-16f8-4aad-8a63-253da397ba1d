{"ast": null, "code": "import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, {\n    format,\n    locale\n  }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}", "map": {"version": 3, "names": ["formatOffset", "parseZoneInfo", "Zone", "singleton", "SystemZone", "instance", "type", "name", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "isUniversal", "offsetName", "ts", "format", "locale", "offset", "Date", "getTimezoneOffset", "equals", "otherZone", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/zones/systemZone.js"], "sourcesContent": ["import { formatOffset, parseZoneInfo } from \"../impl/util.js\";\nimport Zone from \"../zone.js\";\n\nlet singleton = null;\n\n/**\n * Represents the local zone for this JavaScript environment.\n * @implements {Zone}\n */\nexport default class SystemZone extends Zone {\n  /**\n   * Get a singleton instance of the local zone\n   * @return {SystemZone}\n   */\n  static get instance() {\n    if (singleton === null) {\n      singleton = new SystemZone();\n    }\n    return singleton;\n  }\n\n  /** @override **/\n  get type() {\n    return \"system\";\n  }\n\n  /** @override **/\n  get name() {\n    return new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName(ts, { format, locale }) {\n    return parseZoneInfo(ts, format, locale);\n  }\n\n  /** @override **/\n  formatOffset(ts, format) {\n    return formatOffset(this.offset(ts), format);\n  }\n\n  /** @override **/\n  offset(ts) {\n    return -new Date(ts).getTimezoneOffset();\n  }\n\n  /** @override **/\n  equals(otherZone) {\n    return otherZone.type === \"system\";\n  }\n\n  /** @override **/\n  get isValid() {\n    return true;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AAC7D,OAAOC,IAAI,MAAM,YAAY;AAE7B,IAAIC,SAAS,GAAG,IAAI;;AAEpB;AACA;AACA;AACA;AACA,eAAe,MAAMC,UAAU,SAASF,IAAI,CAAC;EAC3C;AACF;AACA;AACA;EACE,WAAWG,QAAQA,CAAA,EAAG;IACpB,IAAIF,SAAS,KAAK,IAAI,EAAE;MACtBA,SAAS,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC9B;IACA,OAAOD,SAAS;EAClB;;EAEA;EACA,IAAIG,IAAIA,CAAA,EAAG;IACT,OAAO,QAAQ;EACjB;;EAEA;EACA,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAIC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;EAC7D;;EAEA;EACA,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAO,KAAK;EACd;;EAEA;EACAC,UAAUA,CAACC,EAAE,EAAE;IAAEC,MAAM;IAAEC;EAAO,CAAC,EAAE;IACjC,OAAOf,aAAa,CAACa,EAAE,EAAEC,MAAM,EAAEC,MAAM,CAAC;EAC1C;;EAEA;EACAhB,YAAYA,CAACc,EAAE,EAAEC,MAAM,EAAE;IACvB,OAAOf,YAAY,CAAC,IAAI,CAACiB,MAAM,CAACH,EAAE,CAAC,EAAEC,MAAM,CAAC;EAC9C;;EAEA;EACAE,MAAMA,CAACH,EAAE,EAAE;IACT,OAAO,CAAC,IAAII,IAAI,CAACJ,EAAE,CAAC,CAACK,iBAAiB,CAAC,CAAC;EAC1C;;EAEA;EACAC,MAAMA,CAACC,SAAS,EAAE;IAChB,OAAOA,SAAS,CAACf,IAAI,KAAK,QAAQ;EACpC;;EAEA;EACA,IAAIgB,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}