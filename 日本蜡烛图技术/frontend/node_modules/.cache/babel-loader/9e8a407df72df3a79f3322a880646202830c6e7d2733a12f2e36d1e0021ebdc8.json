{"ast": null, "code": "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport { isUndefined, maybeArray, isDate, isNumber, bestBy, daysInMonth, daysInYear, isLeapYear, weeksInWeekYear, normalizeObject, roundTo, objToLocalTS, padStart } from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport { parseFromTokens, explainFromTokens, formatOptsToTokens, expandMacroTokens, TokenParser } from \"./impl/tokenParser.js\";\nimport { gregorianToWeek, weekToGregorian, gregorianToOrdinal, ordinalToGregorian, hasInvalidGregorianData, hasInvalidWeekData, hasInvalidOrdinalData, hasInvalidTimeData, usesLocalWeekValues, isoWeekdayToLocal } from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport { InvalidArgumentError, ConflictingSpecificationError, InvalidUnitError, InvalidDateTimeError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(dt.c, dt.loc.getMinDaysInFirstWeek(), dt.loc.getStartOfWeek());\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid\n  };\n  return new DateTime({\n    ...current,\n    ...alts,\n    old: current\n  });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n  const d = new Date(ts);\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds()\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day: Math.min(inst.c.day, daysInMonth(year, month)) + Math.trunc(dur.days) + Math.trunc(dur.weeks) * 7\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n  return {\n    ts,\n    o\n  };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const {\n    setZone,\n    zone\n  } = opts;\n  if (parsed && Object.keys(parsed).length !== 0 || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`));\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid ? Formatter.create(Locale.create(\"en-US\"), {\n    allowZ,\n    forceSimple: true\n  }).formatDateTimeFromString(dt, format) : null;\n}\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\nfunction toISOTime(o, extended, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\"weekYear\", \"weekNumber\", \"weekday\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\"\n  }[unit.toLowerCase()];\n  if (!normalized) throw new InvalidUnitError(unit);\n  return normalized;\n}\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n  const loc = Locale.fromObject(opts);\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n  return new DateTime({\n    ts,\n    zone,\n    loc,\n    o\n  });\n}\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = unit => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n    let invalid = config.invalid || (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) || (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({\n      year,\n      month,\n      day,\n      hour,\n      minute,\n      second,\n      millisecond\n    }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({\n      year,\n      month,\n      day,\n      hour,\n      minute,\n      second,\n      millisecond\n    }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options)\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(`fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`);\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options)\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options)\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const {\n      minDaysInFirstWeek,\n      startOfWeek\n    } = usesLocalWeekValues(normalized, loc);\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset) ? opts.specificOffset : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\"Can't mix weekYear/weekNumber units with year/month/day or ordinals\");\n    }\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n    const useWeekData = definiteWeekDef || normalized.weekday && !containsGregor;\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? hasInvalidOrdinalData(normalized) : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? ordinalToGregorian(normalized) : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\"mismatched weekday\", `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`);\n    }\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n    const {\n        locale = null,\n        numberingSystem = null\n      } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({\n        invalid\n      });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return o && o.isLuxonDateTime || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map(t => t ? t.val : null).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map(t => t.val).join(\"\");\n  }\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", {\n      locObj: this.loc\n    })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", {\n      locObj: this.loc\n    })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", {\n      locObj: this.loc\n    })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", {\n      locObj: this.loc\n    })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return this.offset > this.set({\n        month: 1,\n        day: 1\n      }).offset || this.offset > this.set({\n        month: 5\n      }).offset;\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (c1.hour === c2.hour && c1.minute === c2.minute && c1.second === c2.second && c1.millisecond === c2.millisecond) {\n      return [clone(this, {\n        ts: ts1\n      }), clone(this, {\n        ts: ts2\n      })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.localWeekYear, this.loc.getMinDaysInFirstWeek(), this.loc.getStartOfWeek()) : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const {\n      locale,\n      numberingSystem,\n      calendar\n    } = Formatter.create(this.loc.clone(opts), opts).resolvedOptions(this);\n    return {\n      locale,\n      numberingSystem,\n      outputCalendar: calendar\n    };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, {\n    keepLocalTime = false,\n    keepCalendarTime = false\n  } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, {\n        ts: newTS,\n        zone\n      });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({\n    locale,\n    numberingSystem,\n    outputCalendar\n  } = {}) {\n    const loc = this.loc.clone({\n      locale,\n      numberingSystem,\n      outputCalendar\n    });\n    return clone(this, {\n      loc\n    });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({\n      locale\n    });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const {\n      minDaysInFirstWeek,\n      startOfWeek\n    } = usesLocalWeekValues(normalized, this.loc);\n    const settingWeekStuff = !isUndefined(normalized.weekYear) || !isUndefined(normalized.weekNumber) || !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\"Can't mix weekYear/weekNumber units with year/month/day or ordinals\");\n    }\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian({\n        ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek),\n        ...normalized\n      }, minDaysInFirstWeek, startOfWeek);\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({\n        ...gregorianToOrdinal(this.c),\n        ...normalized\n      });\n    } else {\n      mixed = {\n        ...this.toObject(),\n        ...normalized\n      };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, {\n      ts,\n      o\n    });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, {\n    useLocaleWeeks = false\n  } = {}) {\n    if (!this.isValid) return this;\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const {\n          weekday\n        } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid ? this.plus({\n      [unit]: 1\n    }).startOf(unit, opts).minus(1) : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt) : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this) : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this) : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    const ext = format === \"extended\";\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string|null}\n   */\n  toISODate({\n    format = \"extended\"\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\"\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    let c = includePrefix ? \"T\" : \"\";\n    return c + toISOTime(this, format === \"extended\", suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({\n    includeOffset = true,\n    includeZone = false,\n    includeOffsetSpace = true\n  } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n    const base = {\n      ...this.c\n    };\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n    const durOpts = {\n      locale: this.locale,\n      numberingSystem: this.numberingSystem,\n      ...opts\n    };\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, {\n      keepLocalTime: true\n    });\n    return adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts);\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return this.isValid && other.isValid && this.valueOf() === other.valueOf() && this.zone.equals(other.zone) && this.loc.equals(other.loc);\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, {\n        zone: this.zone\n      }),\n      padding = options.padding ? this < base ? -options.padding : options.padding : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n    return diffRelative(options.base || DateTime.fromObject({}, {\n      zone: this.zone\n    }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, i => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, i => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const {\n        locale = null,\n        numberingSystem = null\n      } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const {\n        locale = null,\n        numberingSystem = null\n      } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\"fromFormatParser requires an input string and a format parser\");\n    }\n    const {\n        locale = null,\n        numberingSystem = null\n      } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(`fromFormatParser called with a locale of ${localeToUse}, ` + `but the format parser was created for ${formatParser.locale}`);\n    }\n    const {\n      result,\n      zone,\n      specificOffset,\n      invalidReason\n    } = formatParser.explainFromTokens(text);\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(result, zone, opts, `format ${formatParser.format}`, text, specificOffset);\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(`Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`);\n  }\n}", "map": {"version": 3, "names": ["Duration", "Interval", "Settings", "Info", "<PERSON><PERSON><PERSON>", "FixedOffsetZone", "Locale", "isUndefined", "maybeA<PERSON>y", "isDate", "isNumber", "bestBy", "daysInMonth", "daysInYear", "isLeapYear", "weeksInWeekYear", "normalizeObject", "roundTo", "objToLocalTS", "padStart", "normalizeZone", "diff", "parseRFC2822Date", "parseISODate", "parseHTTPDate", "parseSQL", "parseFromTokens", "explainFromTokens", "formatOptsToTokens", "expandMacroTokens", "Token<PERSON><PERSON><PERSON>", "gregorianToWeek", "weekT<PERSON><PERSON><PERSON><PERSON><PERSON>", "gregorianToOrdinal", "ordinalToGregorian", "hasInvalidGregorianData", "hasInvalidWeekData", "hasInvalidOrdinalData", "hasInvalidTimeData", "usesLocalWeekValues", "isoWeekdayToLocal", "Formats", "InvalidArgumentError", "ConflictingSpecificationError", "InvalidUnitError", "InvalidDateTimeError", "Invalid", "INVALID", "MAX_DATE", "unsupportedZone", "zone", "name", "possiblyCachedWeekData", "dt", "weekData", "c", "possiblyCachedLocalWeekData", "localWeekData", "loc", "getMinDaysInFirstWeek", "getStartOfWeek", "clone", "inst", "alts", "current", "ts", "o", "invalid", "DateTime", "old", "fixOffset", "localTS", "tz", "ut<PERSON><PERSON><PERSON><PERSON>", "o2", "offset", "o3", "Math", "min", "max", "tsToObj", "d", "Date", "year", "getUTCFullYear", "month", "getUTCMonth", "day", "getUTCDate", "hour", "getUTCHours", "minute", "getUTCMinutes", "second", "getUTCSeconds", "millisecond", "getUTCMilliseconds", "objToTS", "obj", "adjustTime", "dur", "oPre", "trunc", "years", "months", "quarters", "days", "weeks", "millisToAdd", "fromObject", "hours", "minutes", "seconds", "milliseconds", "as", "parseDataToDateTime", "parsed", "parsedZone", "opts", "format", "text", "specificOffset", "setZone", "Object", "keys", "length", "interpretationZone", "toTechFormat", "allowZ", "<PERSON><PERSON><PERSON><PERSON>", "create", "forceSimple", "formatDateTimeFromString", "toISODate", "extended", "longFormat", "toISOTime", "suppressSeconds", "suppressMilliseconds", "includeOffset", "extendedZone", "isOffsetFixed", "<PERSON><PERSON><PERSON><PERSON>", "defaultUnitValues", "defaultWeekUnitValues", "weekNumber", "weekday", "defaultOrdinalUnitValues", "ordinal", "orderedUnits", "orderedWeekUnits", "orderedOrdinalUnits", "normalizeUnit", "unit", "normalized", "quarter", "weekdays", "weeknumber", "weeksnumber", "weeknumbers", "weekyear", "weekyears", "toLowerCase", "normalizeUnitWithLocalWeeks", "guessOffsetForZone", "zoneOffsetTs", "undefined", "now", "type", "zoneName", "offsetGuess", "zoneOffsetGuessCache", "get", "set", "quickDT", "defaultZone", "u", "<PERSON><PERSON><PERSON><PERSON>", "diffRelative", "start", "end", "round", "calendary", "formatter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "differ", "<PERSON><PERSON><PERSON>", "startOf", "units", "count", "abs", "lastOpts", "argList", "args", "Array", "from", "slice", "Map", "constructor", "config", "Number", "isNaN", "unchanged", "equals", "ot", "_zone", "isLuxonDateTime", "local", "arguments", "utc", "utcInstance", "fromJSDate", "date", "options", "valueOf", "NaN", "zoneToUse", "fromMillis", "fromSeconds", "minDaysInFirstWeek", "startOfWeek", "tsNow", "containsOrdinal", "containsGregorYear", "containsGregorMD", "<PERSON><PERSON><PERSON><PERSON>", "definiteWeekDef", "weekYear", "useWeekData", "defaultValues", "objNow", "<PERSON><PERSON><PERSON><PERSON>", "v", "higherOrderInvalid", "gregorian", "tsFinal", "offsetFinal", "toISO", "fromISO", "vals", "fromRFC2822", "fromHTTP", "fromFormat", "fmt", "locale", "numberingSystem", "localeToUse", "fromOpts", "defaultToEN", "fromString", "fromSQL", "reason", "explanation", "throwOnInvalid", "isDateTime", "parseFormatForOpts", "formatOpts", "localeOpts", "tokenList", "map", "t", "val", "join", "expandFormat", "expanded", "parseFormat", "resetCache", "clear", "invalidReason", "invalidExplanation", "outputCalendar", "ceil", "isWeekend", "getWeekendDays", "includes", "localWeekday", "localWeekNumber", "localWeekYear", "monthShort", "locObj", "monthLong", "weekdayShort", "weekdayLong", "offsetNameShort", "offsetName", "offsetNameLong", "isUniversal", "isInDST", "getPossibleOffsets", "dayMs", "minuteMs", "oEarlier", "oLater", "o1", "ts1", "ts2", "c1", "c2", "isInLeapYear", "weeksInLocalWeekYear", "resolvedLocaleOptions", "calendar", "resolvedOptions", "toUTC", "instance", "toLocal", "keepLocalTime", "keepCalendarTime", "newTS", "as<PERSON>bj", "toObject", "reconfigure", "setLocale", "values", "settingWeekStuff", "mixed", "plus", "duration", "fromDurationLike", "minus", "negate", "useLocaleWeeks", "normalizedUnit", "q", "endOf", "toFormat", "redefaultToEN", "toLocaleString", "DATE_SHORT", "formatDateTime", "toLocaleParts", "formatDateTimeParts", "ext", "toISOWeekDate", "includePrefix", "toRFC2822", "toHTTP", "toSQLDate", "toSQLTime", "includeZone", "includeOffsetSpace", "toSQL", "toString", "Symbol", "for", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON>", "toUnixInteger", "floor", "toJSON", "toBSON", "toJSDate", "base", "includeConfig", "otherDateTime", "durOpts", "otherIsLater", "earlier", "later", "diffed", "diffNow", "until", "fromDateTimes", "inputMs", "adjustedToZone", "other", "toRelative", "padding", "isArray", "numeric", "toRelativeCalendar", "dateTimes", "every", "i", "fromFormatExplain", "fromStringExplain", "buildFormatParser", "fromFormatParser", "format<PERSON><PERSON>er", "result", "DATE_MED", "DATE_MED_WITH_WEEKDAY", "DATE_FULL", "DATE_HUGE", "TIME_SIMPLE", "TIME_WITH_SECONDS", "TIME_WITH_SHORT_OFFSET", "TIME_WITH_LONG_OFFSET", "TIME_24_SIMPLE", "TIME_24_WITH_SECONDS", "TIME_24_WITH_SHORT_OFFSET", "TIME_24_WITH_LONG_OFFSET", "DATETIME_SHORT", "DATETIME_SHORT_WITH_SECONDS", "DATETIME_MED", "DATETIME_MED_WITH_SECONDS", "DATETIME_MED_WITH_WEEKDAY", "DATETIME_FULL", "DATETIME_FULL_WITH_SECONDS", "DATETIME_HUGE", "DATETIME_HUGE_WITH_SECONDS", "friendlyDateTime", "dateTimeish"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/datetime.js"], "sourcesContent": ["import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone\n) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\n/**\n * @param {Zone} zone\n * @return {number}\n */\nfunction guessOffsetForZone(zone) {\n  if (zoneOffsetTs === undefined) {\n    zoneOffsetTs = Settings.now();\n  }\n\n  // Do not cache anything but IANA zones, because it is not safe to do so.\n  // Guessing an offset which is not present in the zone can cause wrong results from fixOffset\n  if (zone.type !== \"iana\") {\n    return zone.offset(zoneOffsetTs);\n  }\n  const zoneName = zone.name;\n  let offsetGuess = zoneOffsetGuessCache.get(zoneName);\n  if (offsetGuess === undefined) {\n    offsetGuess = zone.offset(zoneOffsetTs);\n    zoneOffsetGuessCache.set(zoneName, offsetGuess);\n  }\n  return offsetGuess;\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nconst zoneOffsetGuessCache = new Map();\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache.clear();\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string|null}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string|null}\n   */\n  toISODate({ format = \"extended\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    let c = includePrefix ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string|null}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (including milliseconds in the fractional part) of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval|DateTime}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SACEC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,eAAe,EACfC,eAAe,EACfC,OAAO,EACPC,YAAY,EACZC,QAAQ,QACH,gBAAgB;AACvB,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,IAAI,MAAM,gBAAgB;AACjC,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,uBAAuB;AAC/F,SACEC,eAAe,EACfC,iBAAiB,EACjBC,kBAAkB,EAClBC,iBAAiB,EACjBC,WAAW,QACN,uBAAuB;AAC9B,SACEC,eAAe,EACfC,eAAe,EACfC,kBAAkB,EAClBC,kBAAkB,EAClBC,uBAAuB,EACvBC,kBAAkB,EAClBC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,EACnBC,iBAAiB,QACZ,uBAAuB;AAC9B,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,SACEC,oBAAoB,EACpBC,6BAA6B,EAC7BC,gBAAgB,EAChBC,oBAAoB,QACf,aAAa;AACpB,OAAOC,OAAO,MAAM,mBAAmB;AAEvC,MAAMC,OAAO,GAAG,kBAAkB;AAClC,MAAMC,QAAQ,GAAG,OAAO;AAExB,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAIJ,OAAO,CAAC,kBAAkB,EAAE,aAAaI,IAAI,CAACC,IAAI,oBAAoB,CAAC;AACpF;;AAEA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,EAAE,EAAE;EAClC,IAAIA,EAAE,CAACC,QAAQ,KAAK,IAAI,EAAE;IACxBD,EAAE,CAACC,QAAQ,GAAGvB,eAAe,CAACsB,EAAE,CAACE,CAAC,CAAC;EACrC;EACA,OAAOF,EAAE,CAACC,QAAQ;AACpB;;AAEA;AACA;AACA;AACA,SAASE,2BAA2BA,CAACH,EAAE,EAAE;EACvC,IAAIA,EAAE,CAACI,aAAa,KAAK,IAAI,EAAE;IAC7BJ,EAAE,CAACI,aAAa,GAAG1B,eAAe,CAChCsB,EAAE,CAACE,CAAC,EACJF,EAAE,CAACK,GAAG,CAACC,qBAAqB,CAAC,CAAC,EAC9BN,EAAE,CAACK,GAAG,CAACE,cAAc,CAAC,CACxB,CAAC;EACH;EACA,OAAOP,EAAE,CAACI,aAAa;AACzB;;AAEA;AACA;AACA,SAASI,KAAKA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACzB,MAAMC,OAAO,GAAG;IACdC,EAAE,EAAEH,IAAI,CAACG,EAAE;IACXf,IAAI,EAAEY,IAAI,CAACZ,IAAI;IACfK,CAAC,EAAEO,IAAI,CAACP,CAAC;IACTW,CAAC,EAAEJ,IAAI,CAACI,CAAC;IACTR,GAAG,EAAEI,IAAI,CAACJ,GAAG;IACbS,OAAO,EAAEL,IAAI,CAACK;EAChB,CAAC;EACD,OAAO,IAAIC,QAAQ,CAAC;IAAE,GAAGJ,OAAO;IAAE,GAAGD,IAAI;IAAEM,GAAG,EAAEL;EAAQ,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA,SAASM,SAASA,CAACC,OAAO,EAAEL,CAAC,EAAEM,EAAE,EAAE;EACjC;EACA,IAAIC,QAAQ,GAAGF,OAAO,GAAGL,CAAC,GAAG,EAAE,GAAG,IAAI;;EAEtC;EACA,MAAMQ,EAAE,GAAGF,EAAE,CAACG,MAAM,CAACF,QAAQ,CAAC;;EAE9B;EACA,IAAIP,CAAC,KAAKQ,EAAE,EAAE;IACZ,OAAO,CAACD,QAAQ,EAAEP,CAAC,CAAC;EACtB;;EAEA;EACAO,QAAQ,IAAI,CAACC,EAAE,GAAGR,CAAC,IAAI,EAAE,GAAG,IAAI;;EAEhC;EACA,MAAMU,EAAE,GAAGJ,EAAE,CAACG,MAAM,CAACF,QAAQ,CAAC;EAC9B,IAAIC,EAAE,KAAKE,EAAE,EAAE;IACb,OAAO,CAACH,QAAQ,EAAEC,EAAE,CAAC;EACvB;;EAEA;EACA,OAAO,CAACH,OAAO,GAAGM,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAEC,IAAI,CAACE,GAAG,CAACL,EAAE,EAAEE,EAAE,CAAC,CAAC;AACnE;;AAEA;AACA,SAASI,OAAOA,CAACf,EAAE,EAAEU,MAAM,EAAE;EAC3BV,EAAE,IAAIU,MAAM,GAAG,EAAE,GAAG,IAAI;EAExB,MAAMM,CAAC,GAAG,IAAIC,IAAI,CAACjB,EAAE,CAAC;EAEtB,OAAO;IACLkB,IAAI,EAAEF,CAAC,CAACG,cAAc,CAAC,CAAC;IACxBC,KAAK,EAAEJ,CAAC,CAACK,WAAW,CAAC,CAAC,GAAG,CAAC;IAC1BC,GAAG,EAAEN,CAAC,CAACO,UAAU,CAAC,CAAC;IACnBC,IAAI,EAAER,CAAC,CAACS,WAAW,CAAC,CAAC;IACrBC,MAAM,EAAEV,CAAC,CAACW,aAAa,CAAC,CAAC;IACzBC,MAAM,EAAEZ,CAAC,CAACa,aAAa,CAAC,CAAC;IACzBC,WAAW,EAAEd,CAAC,CAACe,kBAAkB,CAAC;EACpC,CAAC;AACH;;AAEA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEvB,MAAM,EAAEzB,IAAI,EAAE;EAClC,OAAOoB,SAAS,CAACpD,YAAY,CAACgF,GAAG,CAAC,EAAEvB,MAAM,EAAEzB,IAAI,CAAC;AACnD;;AAEA;AACA,SAASiD,UAAUA,CAACrC,IAAI,EAAEsC,GAAG,EAAE;EAC7B,MAAMC,IAAI,GAAGvC,IAAI,CAACI,CAAC;IACjBiB,IAAI,GAAGrB,IAAI,CAACP,CAAC,CAAC4B,IAAI,GAAGN,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;IAC1ClB,KAAK,GAAGvB,IAAI,CAACP,CAAC,CAAC8B,KAAK,GAAGR,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACI,MAAM,CAAC,GAAG3B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACK,QAAQ,CAAC,GAAG,CAAC;IAC5ElD,CAAC,GAAG;MACF,GAAGO,IAAI,CAACP,CAAC;MACT4B,IAAI;MACJE,KAAK;MACLE,GAAG,EACDV,IAAI,CAACC,GAAG,CAAChB,IAAI,CAACP,CAAC,CAACgC,GAAG,EAAE3E,WAAW,CAACuE,IAAI,EAAEE,KAAK,CAAC,CAAC,GAC9CR,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACM,IAAI,CAAC,GACpB7B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACO,KAAK,CAAC,GAAG;IAC5B,CAAC;IACDC,WAAW,GAAG5G,QAAQ,CAAC6G,UAAU,CAAC;MAChCN,KAAK,EAAEH,GAAG,CAACG,KAAK,GAAG1B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;MACxCE,QAAQ,EAAEL,GAAG,CAACK,QAAQ,GAAG5B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACK,QAAQ,CAAC;MACjDD,MAAM,EAAEJ,GAAG,CAACI,MAAM,GAAG3B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACI,MAAM,CAAC;MAC3CG,KAAK,EAAEP,GAAG,CAACO,KAAK,GAAG9B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACO,KAAK,CAAC;MACxCD,IAAI,EAAEN,GAAG,CAACM,IAAI,GAAG7B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACM,IAAI,CAAC;MACrCI,KAAK,EAAEV,GAAG,CAACU,KAAK;MAChBC,OAAO,EAAEX,GAAG,CAACW,OAAO;MACpBC,OAAO,EAAEZ,GAAG,CAACY,OAAO;MACpBC,YAAY,EAAEb,GAAG,CAACa;IACpB,CAAC,CAAC,CAACC,EAAE,CAAC,cAAc,CAAC;IACrB3C,OAAO,GAAGrD,YAAY,CAACqC,CAAC,CAAC;EAE3B,IAAI,CAACU,EAAE,EAAEC,CAAC,CAAC,GAAGI,SAAS,CAACC,OAAO,EAAE8B,IAAI,EAAEvC,IAAI,CAACZ,IAAI,CAAC;EAEjD,IAAI0D,WAAW,KAAK,CAAC,EAAE;IACrB3C,EAAE,IAAI2C,WAAW;IACjB;IACA1C,CAAC,GAAGJ,IAAI,CAACZ,IAAI,CAACyB,MAAM,CAACV,EAAE,CAAC;EAC1B;EAEA,OAAO;IAAEA,EAAE;IAAEC;EAAE,CAAC;AAClB;;AAEA;AACA;AACA,SAASiD,mBAAmBA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,cAAc,EAAE;EACnF,MAAM;IAAEC,OAAO;IAAExE;EAAK,CAAC,GAAGoE,IAAI;EAC9B,IAAKF,MAAM,IAAIO,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAACS,MAAM,KAAK,CAAC,IAAKR,UAAU,EAAE;IAC9D,MAAMS,kBAAkB,GAAGT,UAAU,IAAInE,IAAI;MAC3CY,IAAI,GAAGM,QAAQ,CAACyC,UAAU,CAACO,MAAM,EAAE;QACjC,GAAGE,IAAI;QACPpE,IAAI,EAAE4E,kBAAkB;QACxBL;MACF,CAAC,CAAC;IACJ,OAAOC,OAAO,GAAG5D,IAAI,GAAGA,IAAI,CAAC4D,OAAO,CAACxE,IAAI,CAAC;EAC5C,CAAC,MAAM;IACL,OAAOkB,QAAQ,CAACD,OAAO,CACrB,IAAIrB,OAAO,CAAC,YAAY,EAAE,cAAc0E,IAAI,wBAAwBD,MAAM,EAAE,CAC9E,CAAC;EACH;AACF;;AAEA;AACA;AACA,SAASQ,YAAYA,CAAC1E,EAAE,EAAEkE,MAAM,EAAES,MAAM,GAAG,IAAI,EAAE;EAC/C,OAAO3E,EAAE,CAAC4E,OAAO,GACb7H,SAAS,CAAC8H,MAAM,CAAC5H,MAAM,CAAC4H,MAAM,CAAC,OAAO,CAAC,EAAE;IACvCF,MAAM;IACNG,WAAW,EAAE;EACf,CAAC,CAAC,CAACC,wBAAwB,CAAC/E,EAAE,EAAEkE,MAAM,CAAC,GACvC,IAAI;AACV;AAEA,SAASc,SAASA,CAACnE,CAAC,EAAEoE,QAAQ,EAAE;EAC9B,MAAMC,UAAU,GAAGrE,CAAC,CAACX,CAAC,CAAC4B,IAAI,GAAG,IAAI,IAAIjB,CAAC,CAACX,CAAC,CAAC4B,IAAI,GAAG,CAAC;EAClD,IAAI5B,CAAC,GAAG,EAAE;EACV,IAAIgF,UAAU,IAAIrE,CAAC,CAACX,CAAC,CAAC4B,IAAI,IAAI,CAAC,EAAE5B,CAAC,IAAI,GAAG;EACzCA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC4B,IAAI,EAAEoD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;EAE3C,IAAID,QAAQ,EAAE;IACZ/E,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC8B,KAAK,CAAC;IACxB9B,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACgC,GAAG,CAAC;EACxB,CAAC,MAAM;IACLhC,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC8B,KAAK,CAAC;IACxB9B,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACgC,GAAG,CAAC;EACxB;EACA,OAAOhC,CAAC;AACV;AAEA,SAASiF,SAASA,CAChBtE,CAAC,EACDoE,QAAQ,EACRG,eAAe,EACfC,oBAAoB,EACpBC,aAAa,EACbC,YAAY,EACZ;EACA,IAAIrF,CAAC,GAAGpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACkC,IAAI,CAAC;EAC1B,IAAI6C,QAAQ,EAAE;IACZ/E,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACoC,MAAM,CAAC;IACzB,IAAIzB,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI7B,CAAC,CAACX,CAAC,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAC4C,eAAe,EAAE;MACjElF,CAAC,IAAI,GAAG;IACV;EACF,CAAC,MAAM;IACLA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACoC,MAAM,CAAC;EAC3B;EAEA,IAAIzB,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI7B,CAAC,CAACX,CAAC,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAC4C,eAAe,EAAE;IACjElF,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACsC,MAAM,CAAC;IAEzB,IAAI3B,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI,CAAC2C,oBAAoB,EAAE;MAClDnF,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACwC,WAAW,EAAE,CAAC,CAAC;IACnC;EACF;EAEA,IAAI4C,aAAa,EAAE;IACjB,IAAIzE,CAAC,CAAC2E,aAAa,IAAI3E,CAAC,CAACS,MAAM,KAAK,CAAC,IAAI,CAACiE,YAAY,EAAE;MACtDrF,CAAC,IAAI,GAAG;IACV,CAAC,MAAM,IAAIW,CAAC,CAACA,CAAC,GAAG,CAAC,EAAE;MAClBX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAAC,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;MACpCX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAAC,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACLX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;MACnCX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;IACrC;EACF;EAEA,IAAI0E,YAAY,EAAE;IAChBrF,CAAC,IAAI,GAAG,GAAGW,CAAC,CAAChB,IAAI,CAAC4F,QAAQ,GAAG,GAAG;EAClC;EACA,OAAOvF,CAAC;AACV;;AAEA;AACA,MAAMwF,iBAAiB,GAAG;IACtB1D,KAAK,EAAE,CAAC;IACRE,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;EACDiD,qBAAqB,GAAG;IACtBC,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVzD,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;EACDoD,wBAAwB,GAAG;IACzBC,OAAO,EAAE,CAAC;IACV3D,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;;AAEH;AACA,MAAMsD,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;EACtFC,gBAAgB,GAAG,CACjB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,aAAa,CACd;EACDC,mBAAmB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;;AAEtF;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,MAAMC,UAAU,GAAG;IACjBvE,IAAI,EAAE,MAAM;IACZoB,KAAK,EAAE,MAAM;IACblB,KAAK,EAAE,OAAO;IACdmB,MAAM,EAAE,OAAO;IACfjB,GAAG,EAAE,KAAK;IACVmB,IAAI,EAAE,KAAK;IACXjB,IAAI,EAAE,MAAM;IACZqB,KAAK,EAAE,MAAM;IACbnB,MAAM,EAAE,QAAQ;IAChBoB,OAAO,EAAE,QAAQ;IACjB4C,OAAO,EAAE,SAAS;IAClBlD,QAAQ,EAAE,SAAS;IACnBZ,MAAM,EAAE,QAAQ;IAChBmB,OAAO,EAAE,QAAQ;IACjBjB,WAAW,EAAE,aAAa;IAC1BkB,YAAY,EAAE,aAAa;IAC3BiC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,YAAY;IACzBC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,UAAU;IACrBb,OAAO,EAAE;EACX,CAAC,CAACK,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC;EAErB,IAAI,CAACR,UAAU,EAAE,MAAM,IAAI9G,gBAAgB,CAAC6G,IAAI,CAAC;EAEjD,OAAOC,UAAU;AACnB;AAEA,SAASS,2BAA2BA,CAACV,IAAI,EAAE;EACzC,QAAQA,IAAI,CAACS,WAAW,CAAC,CAAC;IACxB,KAAK,cAAc;IACnB,KAAK,eAAe;MAClB,OAAO,cAAc;IACvB,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrB,OAAO,iBAAiB;IAC1B,KAAK,eAAe;IACpB,KAAK,gBAAgB;MACnB,OAAO,eAAe;IACxB;MACE,OAAOV,aAAa,CAACC,IAAI,CAAC;EAC9B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,kBAAkBA,CAAClH,IAAI,EAAE;EAChC,IAAImH,YAAY,KAAKC,SAAS,EAAE;IAC9BD,YAAY,GAAGnK,QAAQ,CAACqK,GAAG,CAAC,CAAC;EAC/B;;EAEA;EACA;EACA,IAAIrH,IAAI,CAACsH,IAAI,KAAK,MAAM,EAAE;IACxB,OAAOtH,IAAI,CAACyB,MAAM,CAAC0F,YAAY,CAAC;EAClC;EACA,MAAMI,QAAQ,GAAGvH,IAAI,CAACC,IAAI;EAC1B,IAAIuH,WAAW,GAAGC,oBAAoB,CAACC,GAAG,CAACH,QAAQ,CAAC;EACpD,IAAIC,WAAW,KAAKJ,SAAS,EAAE;IAC7BI,WAAW,GAAGxH,IAAI,CAACyB,MAAM,CAAC0F,YAAY,CAAC;IACvCM,oBAAoB,CAACE,GAAG,CAACJ,QAAQ,EAAEC,WAAW,CAAC;EACjD;EACA,OAAOA,WAAW;AACpB;;AAEA;AACA;AACA;AACA,SAASI,OAAOA,CAAC5E,GAAG,EAAEoB,IAAI,EAAE;EAC1B,MAAMpE,IAAI,GAAG9B,aAAa,CAACkG,IAAI,CAACpE,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;EAC3D,IAAI,CAAC7H,IAAI,CAAC+E,OAAO,EAAE;IACjB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACC,IAAI,CAAC,CAAC;EAChD;EAEA,MAAMQ,GAAG,GAAGpD,MAAM,CAACuG,UAAU,CAACS,IAAI,CAAC;EAEnC,IAAIrD,EAAE,EAAEC,CAAC;;EAET;EACA,IAAI,CAAC3D,WAAW,CAAC2F,GAAG,CAACf,IAAI,CAAC,EAAE;IAC1B,KAAK,MAAM6F,CAAC,IAAI3B,YAAY,EAAE;MAC5B,IAAI9I,WAAW,CAAC2F,GAAG,CAAC8E,CAAC,CAAC,CAAC,EAAE;QACvB9E,GAAG,CAAC8E,CAAC,CAAC,GAAGjC,iBAAiB,CAACiC,CAAC,CAAC;MAC/B;IACF;IAEA,MAAM7G,OAAO,GAAGhC,uBAAuB,CAAC+D,GAAG,CAAC,IAAI5D,kBAAkB,CAAC4D,GAAG,CAAC;IACvE,IAAI/B,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC;IAEA,MAAM8G,YAAY,GAAGb,kBAAkB,CAAClH,IAAI,CAAC;IAC7C,CAACe,EAAE,EAAEC,CAAC,CAAC,GAAG+B,OAAO,CAACC,GAAG,EAAE+E,YAAY,EAAE/H,IAAI,CAAC;EAC5C,CAAC,MAAM;IACLe,EAAE,GAAG/D,QAAQ,CAACqK,GAAG,CAAC,CAAC;EACrB;EAEA,OAAO,IAAInG,QAAQ,CAAC;IAAEH,EAAE;IAAEf,IAAI;IAAEQ,GAAG;IAAEQ;EAAE,CAAC,CAAC;AAC3C;AAEA,SAASgH,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE9D,IAAI,EAAE;EACtC,MAAM+D,KAAK,GAAG9K,WAAW,CAAC+G,IAAI,CAAC+D,KAAK,CAAC,GAAG,IAAI,GAAG/D,IAAI,CAAC+D,KAAK;IACvD9D,MAAM,GAAGA,CAAChE,CAAC,EAAEkG,IAAI,KAAK;MACpBlG,CAAC,GAAGtC,OAAO,CAACsC,CAAC,EAAE8H,KAAK,IAAI/D,IAAI,CAACgE,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;MACrD,MAAMC,SAAS,GAAGH,GAAG,CAAC1H,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,CAACkE,YAAY,CAAClE,IAAI,CAAC;MACxD,OAAOiE,SAAS,CAAChE,MAAM,CAAChE,CAAC,EAAEkG,IAAI,CAAC;IAClC,CAAC;IACDgC,MAAM,GAAIhC,IAAI,IAAK;MACjB,IAAInC,IAAI,CAACgE,SAAS,EAAE;QAClB,IAAI,CAACF,GAAG,CAACM,OAAO,CAACP,KAAK,EAAE1B,IAAI,CAAC,EAAE;UAC7B,OAAO2B,GAAG,CAACO,OAAO,CAAClC,IAAI,CAAC,CAACpI,IAAI,CAAC8J,KAAK,CAACQ,OAAO,CAAClC,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACmB,GAAG,CAACnB,IAAI,CAAC;QACpE,CAAC,MAAM,OAAO,CAAC;MACjB,CAAC,MAAM;QACL,OAAO2B,GAAG,CAAC/J,IAAI,CAAC8J,KAAK,EAAE1B,IAAI,CAAC,CAACmB,GAAG,CAACnB,IAAI,CAAC;MACxC;IACF,CAAC;EAEH,IAAInC,IAAI,CAACmC,IAAI,EAAE;IACb,OAAOlC,MAAM,CAACkE,MAAM,CAACnE,IAAI,CAACmC,IAAI,CAAC,EAAEnC,IAAI,CAACmC,IAAI,CAAC;EAC7C;EAEA,KAAK,MAAMA,IAAI,IAAInC,IAAI,CAACsE,KAAK,EAAE;IAC7B,MAAMC,KAAK,GAAGJ,MAAM,CAAChC,IAAI,CAAC;IAC1B,IAAI5E,IAAI,CAACiH,GAAG,CAACD,KAAK,CAAC,IAAI,CAAC,EAAE;MACxB,OAAOtE,MAAM,CAACsE,KAAK,EAAEpC,IAAI,CAAC;IAC5B;EACF;EACA,OAAOlC,MAAM,CAAC4D,KAAK,GAAGC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE9D,IAAI,CAACsE,KAAK,CAACtE,IAAI,CAACsE,KAAK,CAAC/D,MAAM,GAAG,CAAC,CAAC,CAAC;AACxE;AAEA,SAASkE,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAI1E,IAAI,GAAG,CAAC,CAAC;IACX2E,IAAI;EACN,IAAID,OAAO,CAACnE,MAAM,GAAG,CAAC,IAAI,OAAOmE,OAAO,CAACA,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;IACzEP,IAAI,GAAG0E,OAAO,CAACA,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC;IAClCoE,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,KAAK,CAAC,CAAC,EAAEJ,OAAO,CAACnE,MAAM,GAAG,CAAC,CAAC;EACzD,CAAC,MAAM;IACLoE,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC;EAC5B;EACA,OAAO,CAAC1E,IAAI,EAAE2E,IAAI,CAAC;AACrB;;AAEA;AACA;AACA;AACA,IAAI5B,YAAY;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,oBAAoB,GAAG,IAAI0B,GAAG,CAAC,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMjI,QAAQ,CAAC;EAC5B;AACF;AACA;EACEkI,WAAWA,CAACC,MAAM,EAAE;IAClB,MAAMrJ,IAAI,GAAGqJ,MAAM,CAACrJ,IAAI,IAAIhD,QAAQ,CAAC6K,WAAW;IAEhD,IAAI5G,OAAO,GACToI,MAAM,CAACpI,OAAO,KACbqI,MAAM,CAACC,KAAK,CAACF,MAAM,CAACtI,EAAE,CAAC,GAAG,IAAInB,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,KAC9D,CAACI,IAAI,CAAC+E,OAAO,GAAGhF,eAAe,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC;IAChD;AACJ;AACA;IACI,IAAI,CAACe,EAAE,GAAG1D,WAAW,CAACgM,MAAM,CAACtI,EAAE,CAAC,GAAG/D,QAAQ,CAACqK,GAAG,CAAC,CAAC,GAAGgC,MAAM,CAACtI,EAAE;IAE7D,IAAIV,CAAC,GAAG,IAAI;MACVW,CAAC,GAAG,IAAI;IACV,IAAI,CAACC,OAAO,EAAE;MACZ,MAAMuI,SAAS,GAAGH,MAAM,CAAClI,GAAG,IAAIkI,MAAM,CAAClI,GAAG,CAACJ,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIsI,MAAM,CAAClI,GAAG,CAACnB,IAAI,CAACyJ,MAAM,CAACzJ,IAAI,CAAC;MAEzF,IAAIwJ,SAAS,EAAE;QACb,CAACnJ,CAAC,EAAEW,CAAC,CAAC,GAAG,CAACqI,MAAM,CAAClI,GAAG,CAACd,CAAC,EAAEgJ,MAAM,CAAClI,GAAG,CAACH,CAAC,CAAC;MACvC,CAAC,MAAM;QACL;QACA;QACA,MAAM0I,EAAE,GAAGlM,QAAQ,CAAC6L,MAAM,CAACrI,CAAC,CAAC,IAAI,CAACqI,MAAM,CAAClI,GAAG,GAAGkI,MAAM,CAACrI,CAAC,GAAGhB,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACV,EAAE,CAAC;QAC9EV,CAAC,GAAGyB,OAAO,CAAC,IAAI,CAACf,EAAE,EAAE2I,EAAE,CAAC;QACxBzI,OAAO,GAAGqI,MAAM,CAACC,KAAK,CAAClJ,CAAC,CAAC4B,IAAI,CAAC,GAAG,IAAIrC,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI;QACpES,CAAC,GAAGY,OAAO,GAAG,IAAI,GAAGZ,CAAC;QACtBW,CAAC,GAAGC,OAAO,GAAG,IAAI,GAAGyI,EAAE;MACzB;IACF;;IAEA;AACJ;AACA;IACI,IAAI,CAACC,KAAK,GAAG3J,IAAI;IACjB;AACJ;AACA;IACI,IAAI,CAACQ,GAAG,GAAG6I,MAAM,CAAC7I,GAAG,IAAIpD,MAAM,CAAC4H,MAAM,CAAC,CAAC;IACxC;AACJ;AACA;IACI,IAAI,CAAC/D,OAAO,GAAGA,OAAO;IACtB;AACJ;AACA;IACI,IAAI,CAACb,QAAQ,GAAG,IAAI;IACpB;AACJ;AACA;IACI,IAAI,CAACG,aAAa,GAAG,IAAI;IACzB;AACJ;AACA;IACI,IAAI,CAACF,CAAC,GAAGA,CAAC;IACV;AACJ;AACA;IACI,IAAI,CAACW,CAAC,GAAGA,CAAC;IACV;AACJ;AACA;IACI,IAAI,CAAC4I,eAAe,GAAG,IAAI;EAC7B;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOvC,GAAGA,CAAA,EAAG;IACX,OAAO,IAAInG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO2I,KAAKA,CAAA,EAAG;IACb,MAAM,CAACzF,IAAI,EAAE2E,IAAI,CAAC,GAAGF,QAAQ,CAACiB,SAAS,CAAC;MACtC,CAAC7H,IAAI,EAAEE,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEE,MAAM,EAAEE,MAAM,EAAEE,WAAW,CAAC,GAAGkG,IAAI;IAC9D,OAAOnB,OAAO,CAAC;MAAE3F,IAAI;MAAEE,KAAK;MAAEE,GAAG;MAAEE,IAAI;MAAEE,MAAM;MAAEE,MAAM;MAAEE;IAAY,CAAC,EAAEuB,IAAI,CAAC;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO2F,GAAGA,CAAA,EAAG;IACX,MAAM,CAAC3F,IAAI,EAAE2E,IAAI,CAAC,GAAGF,QAAQ,CAACiB,SAAS,CAAC;MACtC,CAAC7H,IAAI,EAAEE,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEE,MAAM,EAAEE,MAAM,EAAEE,WAAW,CAAC,GAAGkG,IAAI;IAE9D3E,IAAI,CAACpE,IAAI,GAAG7C,eAAe,CAAC6M,WAAW;IACvC,OAAOpC,OAAO,CAAC;MAAE3F,IAAI;MAAEE,KAAK;MAAEE,GAAG;MAAEE,IAAI;MAAEE,MAAM;MAAEE,MAAM;MAAEE;IAAY,CAAC,EAAEuB,IAAI,CAAC;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAO6F,UAAUA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAMpJ,EAAE,GAAGxD,MAAM,CAAC2M,IAAI,CAAC,GAAGA,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGC,GAAG;IAC9C,IAAIf,MAAM,CAACC,KAAK,CAACxI,EAAE,CAAC,EAAE;MACpB,OAAOG,QAAQ,CAACD,OAAO,CAAC,eAAe,CAAC;IAC1C;IAEA,MAAMqJ,SAAS,GAAGpM,aAAa,CAACiM,OAAO,CAACnK,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;IACnE,IAAI,CAACyC,SAAS,CAACvF,OAAO,EAAE;MACtB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACuK,SAAS,CAAC,CAAC;IACrD;IAEA,OAAO,IAAIpJ,QAAQ,CAAC;MAClBH,EAAE,EAAEA,EAAE;MACNf,IAAI,EAAEsK,SAAS;MACf9J,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACwG,OAAO;IAChC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOI,UAAUA,CAACxG,YAAY,EAAEoG,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAAC3M,QAAQ,CAACuG,YAAY,CAAC,EAAE;MAC3B,MAAM,IAAIvE,oBAAoB,CAC5B,yDAAyD,OAAOuE,YAAY,eAAeA,YAAY,EACzG,CAAC;IACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAACjE,QAAQ,IAAIiE,YAAY,GAAGjE,QAAQ,EAAE;MAC9D;MACA,OAAOoB,QAAQ,CAACD,OAAO,CAAC,wBAAwB,CAAC;IACnD,CAAC,MAAM;MACL,OAAO,IAAIC,QAAQ,CAAC;QAClBH,EAAE,EAAEgD,YAAY;QAChB/D,IAAI,EAAE9B,aAAa,CAACiM,OAAO,CAACnK,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;QACvDrH,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACwG,OAAO;MAChC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOK,WAAWA,CAAC1G,OAAO,EAAEqG,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,IAAI,CAAC3M,QAAQ,CAACsG,OAAO,CAAC,EAAE;MACtB,MAAM,IAAItE,oBAAoB,CAAC,wCAAwC,CAAC;IAC1E,CAAC,MAAM;MACL,OAAO,IAAI0B,QAAQ,CAAC;QAClBH,EAAE,EAAE+C,OAAO,GAAG,IAAI;QAClB9D,IAAI,EAAE9B,aAAa,CAACiM,OAAO,CAACnK,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;QACvDrH,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACwG,OAAO;MAChC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOxG,UAAUA,CAACX,GAAG,EAAEoB,IAAI,GAAG,CAAC,CAAC,EAAE;IAChCpB,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,MAAMsH,SAAS,GAAGpM,aAAa,CAACkG,IAAI,CAACpE,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;IAChE,IAAI,CAACyC,SAAS,CAACvF,OAAO,EAAE;MACtB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACuK,SAAS,CAAC,CAAC;IACrD;IAEA,MAAM9J,GAAG,GAAGpD,MAAM,CAACuG,UAAU,CAACS,IAAI,CAAC;IACnC,MAAMoC,UAAU,GAAG1I,eAAe,CAACkF,GAAG,EAAEiE,2BAA2B,CAAC;IACpE,MAAM;MAAEwD,kBAAkB;MAAEC;IAAY,CAAC,GAAGrL,mBAAmB,CAACmH,UAAU,EAAEhG,GAAG,CAAC;IAEhF,MAAMmK,KAAK,GAAG3N,QAAQ,CAACqK,GAAG,CAAC,CAAC;MAC1BU,YAAY,GAAG,CAAC1K,WAAW,CAAC+G,IAAI,CAACG,cAAc,CAAC,GAC5CH,IAAI,CAACG,cAAc,GACnB+F,SAAS,CAAC7I,MAAM,CAACkJ,KAAK,CAAC;MAC3BC,eAAe,GAAG,CAACvN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC;MAClD2E,kBAAkB,GAAG,CAACxN,WAAW,CAACmJ,UAAU,CAACvE,IAAI,CAAC;MAClD6I,gBAAgB,GAAG,CAACzN,WAAW,CAACmJ,UAAU,CAACrE,KAAK,CAAC,IAAI,CAAC9E,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC;MACjF0I,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;MACvDE,eAAe,GAAGxE,UAAU,CAACyE,QAAQ,IAAIzE,UAAU,CAACT,UAAU;;IAEhE;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAACgF,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;MAC1D,MAAM,IAAIvL,6BAA6B,CACrC,qEACF,CAAC;IACH;IAEA,IAAIqL,gBAAgB,IAAIF,eAAe,EAAE;MACvC,MAAM,IAAInL,6BAA6B,CAAC,wCAAwC,CAAC;IACnF;IAEA,MAAMyL,WAAW,GAAGF,eAAe,IAAKxE,UAAU,CAACR,OAAO,IAAI,CAAC+E,cAAe;;IAE9E;IACA,IAAIrC,KAAK;MACPyC,aAAa;MACbC,MAAM,GAAGtJ,OAAO,CAAC6I,KAAK,EAAE5C,YAAY,CAAC;IACvC,IAAImD,WAAW,EAAE;MACfxC,KAAK,GAAGtC,gBAAgB;MACxB+E,aAAa,GAAGrF,qBAAqB;MACrCsF,MAAM,GAAGvM,eAAe,CAACuM,MAAM,EAAEX,kBAAkB,EAAEC,WAAW,CAAC;IACnE,CAAC,MAAM,IAAIE,eAAe,EAAE;MAC1BlC,KAAK,GAAGrC,mBAAmB;MAC3B8E,aAAa,GAAGlF,wBAAwB;MACxCmF,MAAM,GAAGrM,kBAAkB,CAACqM,MAAM,CAAC;IACrC,CAAC,MAAM;MACL1C,KAAK,GAAGvC,YAAY;MACpBgF,aAAa,GAAGtF,iBAAiB;IACnC;;IAEA;IACA,IAAIwF,UAAU,GAAG,KAAK;IACtB,KAAK,MAAMvD,CAAC,IAAIY,KAAK,EAAE;MACrB,MAAM4C,CAAC,GAAG9E,UAAU,CAACsB,CAAC,CAAC;MACvB,IAAI,CAACzK,WAAW,CAACiO,CAAC,CAAC,EAAE;QACnBD,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM,IAAIA,UAAU,EAAE;QACrB7E,UAAU,CAACsB,CAAC,CAAC,GAAGqD,aAAa,CAACrD,CAAC,CAAC;MAClC,CAAC,MAAM;QACLtB,UAAU,CAACsB,CAAC,CAAC,GAAGsD,MAAM,CAACtD,CAAC,CAAC;MAC3B;IACF;;IAEA;IACA,MAAMyD,kBAAkB,GAAGL,WAAW,GAChChM,kBAAkB,CAACsH,UAAU,EAAEiE,kBAAkB,EAAEC,WAAW,CAAC,GAC/DE,eAAe,GACfzL,qBAAqB,CAACqH,UAAU,CAAC,GACjCvH,uBAAuB,CAACuH,UAAU,CAAC;MACvCvF,OAAO,GAAGsK,kBAAkB,IAAInM,kBAAkB,CAACoH,UAAU,CAAC;IAEhE,IAAIvF,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC;;IAEA;IACA,MAAMuK,SAAS,GAAGN,WAAW,GACvBpM,eAAe,CAAC0H,UAAU,EAAEiE,kBAAkB,EAAEC,WAAW,CAAC,GAC5DE,eAAe,GACf5L,kBAAkB,CAACwH,UAAU,CAAC,GAC9BA,UAAU;MACd,CAACiF,OAAO,EAAEC,WAAW,CAAC,GAAG3I,OAAO,CAACyI,SAAS,EAAEzD,YAAY,EAAEuC,SAAS,CAAC;MACpE1J,IAAI,GAAG,IAAIM,QAAQ,CAAC;QAClBH,EAAE,EAAE0K,OAAO;QACXzL,IAAI,EAAEsK,SAAS;QACftJ,CAAC,EAAE0K,WAAW;QACdlL;MACF,CAAC,CAAC;;IAEJ;IACA,IAAIgG,UAAU,CAACR,OAAO,IAAI+E,cAAc,IAAI/H,GAAG,CAACgD,OAAO,KAAKpF,IAAI,CAACoF,OAAO,EAAE;MACxE,OAAO9E,QAAQ,CAACD,OAAO,CACrB,oBAAoB,EACpB,uCAAuCuF,UAAU,CAACR,OAAO,kBAAkBpF,IAAI,CAAC+K,KAAK,CAAC,CAAC,EACzF,CAAC;IACH;IAEA,IAAI,CAAC/K,IAAI,CAACmE,OAAO,EAAE;MACjB,OAAO7D,QAAQ,CAACD,OAAO,CAACL,IAAI,CAACK,OAAO,CAAC;IACvC;IAEA,OAAOL,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOgL,OAAOA,CAACtH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAM,CAACyH,IAAI,EAAE1H,UAAU,CAAC,GAAG9F,YAAY,CAACiG,IAAI,CAAC;IAC7C,OAAOL,mBAAmB,CAAC4H,IAAI,EAAE1H,UAAU,EAAEC,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOwH,WAAWA,CAACxH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAClC,MAAM,CAACyH,IAAI,EAAE1H,UAAU,CAAC,GAAG/F,gBAAgB,CAACkG,IAAI,CAAC;IACjD,OAAOL,mBAAmB,CAAC4H,IAAI,EAAE1H,UAAU,EAAEC,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOyH,QAAQA,CAACzH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAM,CAACyH,IAAI,EAAE1H,UAAU,CAAC,GAAG7F,aAAa,CAACgG,IAAI,CAAC;IAC9C,OAAOL,mBAAmB,CAAC4H,IAAI,EAAE1H,UAAU,EAAEC,IAAI,EAAE,MAAM,EAAEA,IAAI,CAAC;EAClE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO4H,UAAUA,CAAC1H,IAAI,EAAE2H,GAAG,EAAE7H,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI/G,WAAW,CAACiH,IAAI,CAAC,IAAIjH,WAAW,CAAC4O,GAAG,CAAC,EAAE;MACzC,MAAM,IAAIzM,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAM;QAAE0M,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAG/H,IAAI;MACpDgI,WAAW,GAAGhP,MAAM,CAACiP,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;MACF,CAACT,IAAI,EAAE1H,UAAU,EAAEI,cAAc,EAAEtD,OAAO,CAAC,GAAGzC,eAAe,CAAC4N,WAAW,EAAE9H,IAAI,EAAE2H,GAAG,CAAC;IACvF,IAAIhL,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAOgD,mBAAmB,CAAC4H,IAAI,EAAE1H,UAAU,EAAEC,IAAI,EAAE,UAAU6H,GAAG,EAAE,EAAE3H,IAAI,EAAEC,cAAc,CAAC;IAC3F;EACF;;EAEA;AACF;AACA;EACE,OAAOgI,UAAUA,CAACjI,IAAI,EAAE2H,GAAG,EAAE7H,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,OAAOlD,QAAQ,CAAC8K,UAAU,CAAC1H,IAAI,EAAE2H,GAAG,EAAE7H,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOoI,OAAOA,CAAClI,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAM,CAACyH,IAAI,EAAE1H,UAAU,CAAC,GAAG5F,QAAQ,CAAC+F,IAAI,CAAC;IACzC,OAAOL,mBAAmB,CAAC4H,IAAI,EAAE1H,UAAU,EAAEC,IAAI,EAAE,KAAK,EAAEE,IAAI,CAAC;EACjE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOrD,OAAOA,CAACwL,MAAM,EAAEC,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAACD,MAAM,EAAE;MACX,MAAM,IAAIjN,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAMyB,OAAO,GAAGwL,MAAM,YAAY7M,OAAO,GAAG6M,MAAM,GAAG,IAAI7M,OAAO,CAAC6M,MAAM,EAAEC,WAAW,CAAC;IAErF,IAAI1P,QAAQ,CAAC2P,cAAc,EAAE;MAC3B,MAAM,IAAIhN,oBAAoB,CAACsB,OAAO,CAAC;IACzC,CAAC,MAAM;MACL,OAAO,IAAIC,QAAQ,CAAC;QAAED;MAAQ,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAO2L,UAAUA,CAAC5L,CAAC,EAAE;IACnB,OAAQA,CAAC,IAAIA,CAAC,CAAC4I,eAAe,IAAK,KAAK;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOiD,kBAAkBA,CAACC,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IACrD,MAAMC,SAAS,GAAGtO,kBAAkB,CAACoO,UAAU,EAAE1P,MAAM,CAACuG,UAAU,CAACoJ,UAAU,CAAC,CAAC;IAC/E,OAAO,CAACC,SAAS,GAAG,IAAI,GAAGA,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAMA,CAAC,GAAGA,CAAC,CAACC,GAAG,GAAG,IAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC9E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,YAAYA,CAACpB,GAAG,EAAEc,UAAU,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMO,QAAQ,GAAG3O,iBAAiB,CAACzB,SAAS,CAACqQ,WAAW,CAACtB,GAAG,CAAC,EAAE7O,MAAM,CAACuG,UAAU,CAACoJ,UAAU,CAAC,CAAC;IAC7F,OAAOO,QAAQ,CAACL,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC5C;EAEA,OAAOI,UAAUA,CAAA,EAAG;IAClBrG,YAAY,GAAGC,SAAS;IACxBK,oBAAoB,CAACgG,KAAK,CAAC,CAAC;EAC9B;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE/F,GAAGA,CAACnB,IAAI,EAAE;IACR,OAAO,IAAI,CAACA,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxB,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9D,OAAO,KAAK,IAAI;EAC9B;;EAEA;AACF;AACA;AACA;EACE,IAAIyM,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACzM,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwL,MAAM,GAAG,IAAI;EAClD;;EAEA;AACF;AACA;AACA;EACE,IAAIkB,kBAAkBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC1M,OAAO,GAAG,IAAI,CAACA,OAAO,CAACyL,WAAW,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIR,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnH,OAAO,GAAG,IAAI,CAACvE,GAAG,CAAC0L,MAAM,GAAG,IAAI;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIC,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACpH,OAAO,GAAG,IAAI,CAACvE,GAAG,CAAC2L,eAAe,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIyB,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC7I,OAAO,GAAG,IAAI,CAACvE,GAAG,CAACoN,cAAc,GAAG,IAAI;EACtD;;EAEA;AACF;AACA;AACA;EACE,IAAI5N,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC2J,KAAK;EACnB;;EAEA;AACF;AACA;AACA;EACE,IAAIpC,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxC,OAAO,GAAG,IAAI,CAAC/E,IAAI,CAACC,IAAI,GAAG,IAAI;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIgC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC8C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAAC4B,IAAI,GAAGoI,GAAG;EACzC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI5D,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1B,OAAO,GAAGpD,IAAI,CAACkM,IAAI,CAAC,IAAI,CAACxN,CAAC,CAAC8B,KAAK,GAAG,CAAC,CAAC,GAAGkI,GAAG;EACzD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIlI,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAAC8B,KAAK,GAAGkI,GAAG;EAC1C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIhI,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACgC,GAAG,GAAGgI,GAAG;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI9H,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACkC,IAAI,GAAG8H,GAAG;EACzC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI5H,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACoC,MAAM,GAAG4H,GAAG;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI1H,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACsC,MAAM,GAAG0H,GAAG;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIxH,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACkC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACwC,WAAW,GAAGwH,GAAG;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIY,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClG,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC+K,QAAQ,GAAGZ,GAAG;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAItE,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChB,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC6F,UAAU,GAAGsE,GAAG;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIrE,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC8F,OAAO,GAAGqE,GAAG;EAClE;;EAEA;AACF;AACA;AACA;EACE,IAAIyD,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/I,OAAO,IAAI,IAAI,CAACvE,GAAG,CAACuN,cAAc,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAChI,OAAO,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIiI,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAClJ,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAAC0F,OAAO,GAAGqE,GAAG;EACvE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI6D,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACnJ,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAACyF,UAAU,GAAGsE,GAAG;EAC1E;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI8D,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpJ,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAAC2K,QAAQ,GAAGZ,GAAG;EACxE;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAInE,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnB,OAAO,GAAGhG,kBAAkB,CAAC,IAAI,CAACsB,CAAC,CAAC,CAAC6F,OAAO,GAAGmE,GAAG;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI+D,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACrJ,OAAO,GAAG9H,IAAI,CAACqG,MAAM,CAAC,OAAO,EAAE;MAAE+K,MAAM,EAAE,IAAI,CAAC7N;IAAI,CAAC,CAAC,CAAC,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;EACzF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAImM,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvJ,OAAO,GAAG9H,IAAI,CAACqG,MAAM,CAAC,MAAM,EAAE;MAAE+K,MAAM,EAAE,IAAI,CAAC7N;IAAI,CAAC,CAAC,CAAC,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;EACxF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIoM,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACxJ,OAAO,GAAG9H,IAAI,CAACyJ,QAAQ,CAAC,OAAO,EAAE;MAAE2H,MAAM,EAAE,IAAI,CAAC7N;IAAI,CAAC,CAAC,CAAC,IAAI,CAACwF,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7F;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIwI,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzJ,OAAO,GAAG9H,IAAI,CAACyJ,QAAQ,CAAC,MAAM,EAAE;MAAE2H,MAAM,EAAE,IAAI,CAAC7N;IAAI,CAAC,CAAC,CAAC,IAAI,CAACwF,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI;EAC5F;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIvE,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsD,OAAO,GAAG,CAAC,IAAI,CAAC/D,CAAC,GAAGqJ,GAAG;EACrC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIoE,eAAeA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC1J,OAAO,EAAE;MAChB,OAAO,IAAI,CAAC/E,IAAI,CAAC0O,UAAU,CAAC,IAAI,CAAC3N,EAAE,EAAE;QACnCsD,MAAM,EAAE,OAAO;QACf6H,MAAM,EAAE,IAAI,CAACA;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIyC,cAAcA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5J,OAAO,EAAE;MAChB,OAAO,IAAI,CAAC/E,IAAI,CAAC0O,UAAU,CAAC,IAAI,CAAC3N,EAAE,EAAE;QACnCsD,MAAM,EAAE,MAAM;QACd6H,MAAM,EAAE,IAAI,CAACA;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,IAAIvG,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACZ,OAAO,GAAG,IAAI,CAAC/E,IAAI,CAAC4O,WAAW,GAAG,IAAI;EACpD;;EAEA;AACF;AACA;AACA;EACE,IAAIC,OAAOA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAClJ,aAAa,EAAE;MACtB,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OACE,IAAI,CAAClE,MAAM,GAAG,IAAI,CAACkG,GAAG,CAAC;QAAExF,KAAK,EAAE,CAAC;QAAEE,GAAG,EAAE;MAAE,CAAC,CAAC,CAACZ,MAAM,IACnD,IAAI,CAACA,MAAM,GAAG,IAAI,CAACkG,GAAG,CAAC;QAAExF,KAAK,EAAE;MAAE,CAAC,CAAC,CAACV,MAAM;IAE/C;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEqN,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC/J,OAAO,IAAI,IAAI,CAACY,aAAa,EAAE;MACvC,OAAO,CAAC,IAAI,CAAC;IACf;IACA,MAAMoJ,KAAK,GAAG,QAAQ;IACtB,MAAMC,QAAQ,GAAG,KAAK;IACtB,MAAM3N,OAAO,GAAGrD,YAAY,CAAC,IAAI,CAACqC,CAAC,CAAC;IACpC,MAAM4O,QAAQ,GAAG,IAAI,CAACjP,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAG0N,KAAK,CAAC;IAClD,MAAMG,MAAM,GAAG,IAAI,CAAClP,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAG0N,KAAK,CAAC;IAEhD,MAAMI,EAAE,GAAG,IAAI,CAACnP,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAG4N,QAAQ,GAAGD,QAAQ,CAAC;IAC1D,MAAMxN,EAAE,GAAG,IAAI,CAACxB,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAG6N,MAAM,GAAGF,QAAQ,CAAC;IACxD,IAAIG,EAAE,KAAK3N,EAAE,EAAE;MACb,OAAO,CAAC,IAAI,CAAC;IACf;IACA,MAAM4N,GAAG,GAAG/N,OAAO,GAAG8N,EAAE,GAAGH,QAAQ;IACnC,MAAMK,GAAG,GAAGhO,OAAO,GAAGG,EAAE,GAAGwN,QAAQ;IACnC,MAAMM,EAAE,GAAGxN,OAAO,CAACsN,GAAG,EAAED,EAAE,CAAC;IAC3B,MAAMI,EAAE,GAAGzN,OAAO,CAACuN,GAAG,EAAE7N,EAAE,CAAC;IAC3B,IACE8N,EAAE,CAAC/M,IAAI,KAAKgN,EAAE,CAAChN,IAAI,IACnB+M,EAAE,CAAC7M,MAAM,KAAK8M,EAAE,CAAC9M,MAAM,IACvB6M,EAAE,CAAC3M,MAAM,KAAK4M,EAAE,CAAC5M,MAAM,IACvB2M,EAAE,CAACzM,WAAW,KAAK0M,EAAE,CAAC1M,WAAW,EACjC;MACA,OAAO,CAAClC,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAEqO;MAAI,CAAC,CAAC,EAAEzO,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAEsO;MAAI,CAAC,CAAC,CAAC;IAC7D;IACA,OAAO,CAAC,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,YAAYA,CAAA,EAAG;IACjB,OAAO5R,UAAU,CAAC,IAAI,CAACqE,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIvE,WAAWA,CAAA,EAAG;IAChB,OAAOA,WAAW,CAAC,IAAI,CAACuE,IAAI,EAAE,IAAI,CAACE,KAAK,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxE,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoH,OAAO,GAAGpH,UAAU,CAAC,IAAI,CAACsE,IAAI,CAAC,GAAGoI,GAAG;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIxM,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACkH,OAAO,GAAGlH,eAAe,CAAC,IAAI,CAACoN,QAAQ,CAAC,GAAGZ,GAAG;EAC5D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIoF,oBAAoBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAAC1K,OAAO,GACflH,eAAe,CACb,IAAI,CAACsQ,aAAa,EAClB,IAAI,CAAC3N,GAAG,CAACC,qBAAqB,CAAC,CAAC,EAChC,IAAI,CAACD,GAAG,CAACE,cAAc,CAAC,CAC1B,CAAC,GACD2J,GAAG;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEqF,qBAAqBA,CAACtL,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAM;MAAE8H,MAAM;MAAEC,eAAe;MAAEwD;IAAS,CAAC,GAAGzS,SAAS,CAAC8H,MAAM,CAC5D,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EACpBA,IACF,CAAC,CAACwL,eAAe,CAAC,IAAI,CAAC;IACvB,OAAO;MAAE1D,MAAM;MAAEC,eAAe;MAAEyB,cAAc,EAAE+B;IAAS,CAAC;EAC9D;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,KAAKA,CAACpO,MAAM,GAAG,CAAC,EAAE2C,IAAI,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACI,OAAO,CAACrH,eAAe,CAAC2S,QAAQ,CAACrO,MAAM,CAAC,EAAE2C,IAAI,CAAC;EAC7D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE2L,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACvL,OAAO,CAACxH,QAAQ,CAAC6K,WAAW,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErD,OAAOA,CAACxE,IAAI,EAAE;IAAEgQ,aAAa,GAAG,KAAK;IAAEC,gBAAgB,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACtEjQ,IAAI,GAAG9B,aAAa,CAAC8B,IAAI,EAAEhD,QAAQ,CAAC6K,WAAW,CAAC;IAChD,IAAI7H,IAAI,CAACyJ,MAAM,CAAC,IAAI,CAACzJ,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,CAACA,IAAI,CAAC+E,OAAO,EAAE;MACxB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACC,IAAI,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,IAAIkQ,KAAK,GAAG,IAAI,CAACnP,EAAE;MACnB,IAAIiP,aAAa,IAAIC,gBAAgB,EAAE;QACrC,MAAMzI,WAAW,GAAGxH,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACV,EAAE,CAAC;QACxC,MAAMoP,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7B,CAACF,KAAK,CAAC,GAAGnN,OAAO,CAACoN,KAAK,EAAE3I,WAAW,EAAExH,IAAI,CAAC;MAC7C;MACA,OAAOW,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAEmP,KAAK;QAAElQ;MAAK,CAAC,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEqQ,WAAWA,CAAC;IAAEnE,MAAM;IAAEC,eAAe;IAAEyB;EAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5D,MAAMpN,GAAG,GAAG,IAAI,CAACA,GAAG,CAACG,KAAK,CAAC;MAAEuL,MAAM;MAAEC,eAAe;MAAEyB;IAAe,CAAC,CAAC;IACvE,OAAOjN,KAAK,CAAC,IAAI,EAAE;MAAEH;IAAI,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE8P,SAASA,CAACpE,MAAM,EAAE;IAChB,OAAO,IAAI,CAACmE,WAAW,CAAC;MAAEnE;IAAO,CAAC,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvE,GAAGA,CAAC4I,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACxL,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMyB,UAAU,GAAG1I,eAAe,CAACyS,MAAM,EAAEtJ,2BAA2B,CAAC;IACvE,MAAM;MAAEwD,kBAAkB;MAAEC;IAAY,CAAC,GAAGrL,mBAAmB,CAACmH,UAAU,EAAE,IAAI,CAAChG,GAAG,CAAC;IAErF,MAAMgQ,gBAAgB,GAClB,CAACnT,WAAW,CAACmJ,UAAU,CAACyE,QAAQ,CAAC,IACjC,CAAC5N,WAAW,CAACmJ,UAAU,CAACT,UAAU,CAAC,IACnC,CAAC1I,WAAW,CAACmJ,UAAU,CAACR,OAAO,CAAC;MAClC4E,eAAe,GAAG,CAACvN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC;MAClD2E,kBAAkB,GAAG,CAACxN,WAAW,CAACmJ,UAAU,CAACvE,IAAI,CAAC;MAClD6I,gBAAgB,GAAG,CAACzN,WAAW,CAACmJ,UAAU,CAACrE,KAAK,CAAC,IAAI,CAAC9E,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC;MACjF0I,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;MACvDE,eAAe,GAAGxE,UAAU,CAACyE,QAAQ,IAAIzE,UAAU,CAACT,UAAU;IAEhE,IAAI,CAACgF,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;MAC1D,MAAM,IAAIvL,6BAA6B,CACrC,qEACF,CAAC;IACH;IAEA,IAAIqL,gBAAgB,IAAIF,eAAe,EAAE;MACvC,MAAM,IAAInL,6BAA6B,CAAC,wCAAwC,CAAC;IACnF;IAEA,IAAIgR,KAAK;IACT,IAAID,gBAAgB,EAAE;MACpBC,KAAK,GAAG3R,eAAe,CACrB;QAAE,GAAGD,eAAe,CAAC,IAAI,CAACwB,CAAC,EAAEoK,kBAAkB,EAAEC,WAAW,CAAC;QAAE,GAAGlE;MAAW,CAAC,EAC9EiE,kBAAkB,EAClBC,WACF,CAAC;IACH,CAAC,MAAM,IAAI,CAACrN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC,EAAE;MAC3CuK,KAAK,GAAGzR,kBAAkB,CAAC;QAAE,GAAGD,kBAAkB,CAAC,IAAI,CAACsB,CAAC,CAAC;QAAE,GAAGmG;MAAW,CAAC,CAAC;IAC9E,CAAC,MAAM;MACLiK,KAAK,GAAG;QAAE,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;QAAE,GAAG5J;MAAW,CAAC;;MAE7C;MACA;MACA,IAAInJ,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC,EAAE;QAC/BoO,KAAK,CAACpO,GAAG,GAAGV,IAAI,CAACC,GAAG,CAAClE,WAAW,CAAC+S,KAAK,CAACxO,IAAI,EAAEwO,KAAK,CAACtO,KAAK,CAAC,EAAEsO,KAAK,CAACpO,GAAG,CAAC;MACvE;IACF;IAEA,MAAM,CAACtB,EAAE,EAAEC,CAAC,CAAC,GAAG+B,OAAO,CAAC0N,KAAK,EAAE,IAAI,CAACzP,CAAC,EAAE,IAAI,CAAChB,IAAI,CAAC;IACjD,OAAOW,KAAK,CAAC,IAAI,EAAE;MAAEI,EAAE;MAAEC;IAAE,CAAC,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0P,IAAIA,CAACC,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAAC5L,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM7B,GAAG,GAAGpG,QAAQ,CAAC8T,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,OAAOhQ,KAAK,CAAC,IAAI,EAAEsC,UAAU,CAAC,IAAI,EAAEC,GAAG,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE2N,KAAKA,CAACF,QAAQ,EAAE;IACd,IAAI,CAAC,IAAI,CAAC5L,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM7B,GAAG,GAAGpG,QAAQ,CAAC8T,gBAAgB,CAACD,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAC;IACxD,OAAOnQ,KAAK,CAAC,IAAI,EAAEsC,UAAU,CAAC,IAAI,EAAEC,GAAG,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEuF,OAAOA,CAAClC,IAAI,EAAE;IAAEwK,cAAc,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7C,IAAI,CAAC,IAAI,CAAChM,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAM/D,CAAC,GAAG,CAAC,CAAC;MACVgQ,cAAc,GAAGlU,QAAQ,CAACwJ,aAAa,CAACC,IAAI,CAAC;IAC/C,QAAQyK,cAAc;MACpB,KAAK,OAAO;QACVhQ,CAAC,CAACmB,KAAK,GAAG,CAAC;MACb;MACA,KAAK,UAAU;MACf,KAAK,QAAQ;QACXnB,CAAC,CAACqB,GAAG,GAAG,CAAC;MACX;MACA,KAAK,OAAO;MACZ,KAAK,MAAM;QACTrB,CAAC,CAACuB,IAAI,GAAG,CAAC;MACZ;MACA,KAAK,OAAO;QACVvB,CAAC,CAACyB,MAAM,GAAG,CAAC;MACd;MACA,KAAK,SAAS;QACZzB,CAAC,CAAC2B,MAAM,GAAG,CAAC;MACd;MACA,KAAK,SAAS;QACZ3B,CAAC,CAAC6B,WAAW,GAAG,CAAC;QACjB;MACF,KAAK,cAAc;QACjB;MACF;IACF;IAEA,IAAImO,cAAc,KAAK,OAAO,EAAE;MAC9B,IAAID,cAAc,EAAE;QAClB,MAAMrG,WAAW,GAAG,IAAI,CAAClK,GAAG,CAACE,cAAc,CAAC,CAAC;QAC7C,MAAM;UAAEsF;QAAQ,CAAC,GAAG,IAAI;QACxB,IAAIA,OAAO,GAAG0E,WAAW,EAAE;UACzB1J,CAAC,CAAC+E,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,CAAC;QACpC;QACA/E,CAAC,CAACgF,OAAO,GAAG0E,WAAW;MACzB,CAAC,MAAM;QACL1J,CAAC,CAACgF,OAAO,GAAG,CAAC;MACf;IACF;IAEA,IAAIgL,cAAc,KAAK,UAAU,EAAE;MACjC,MAAMC,CAAC,GAAGtP,IAAI,CAACkM,IAAI,CAAC,IAAI,CAAC1L,KAAK,GAAG,CAAC,CAAC;MACnCnB,CAAC,CAACmB,KAAK,GAAG,CAAC8O,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3B;IAEA,OAAO,IAAI,CAACtJ,GAAG,CAAC3G,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkQ,KAAKA,CAAC3K,IAAI,EAAEnC,IAAI,EAAE;IAChB,OAAO,IAAI,CAACW,OAAO,GACf,IAAI,CAAC2L,IAAI,CAAC;MAAE,CAACnK,IAAI,GAAG;IAAE,CAAC,CAAC,CACrBkC,OAAO,CAAClC,IAAI,EAAEnC,IAAI,CAAC,CACnByM,KAAK,CAAC,CAAC,CAAC,GACX,IAAI;EACV;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,QAAQA,CAAClF,GAAG,EAAE7H,IAAI,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAAC4Q,aAAa,CAAChN,IAAI,CAAC,CAAC,CAACc,wBAAwB,CAAC,IAAI,EAAE+G,GAAG,CAAC,GAClFpM,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEwR,cAAcA,CAACvE,UAAU,GAAGvN,OAAO,CAAC+R,UAAU,EAAElN,IAAI,GAAG,CAAC,CAAC,EAAE;IACzD,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EAAE0I,UAAU,CAAC,CAACyE,cAAc,CAAC,IAAI,CAAC,GACvE1R,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2R,aAAaA,CAACpN,IAAI,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACqN,mBAAmB,CAAC,IAAI,CAAC,GACtE,EAAE;EACR;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9F,KAAKA,CAAC;IACJtH,MAAM,GAAG,UAAU;IACnBkB,eAAe,GAAG,KAAK;IACvBC,oBAAoB,GAAG,KAAK;IAC5BC,aAAa,GAAG,IAAI;IACpBC,YAAY,GAAG;EACjB,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,MAAM2M,GAAG,GAAGrN,MAAM,KAAK,UAAU;IAEjC,IAAIhE,CAAC,GAAG8E,SAAS,CAAC,IAAI,EAAEuM,GAAG,CAAC;IAC5BrR,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIiF,SAAS,CAAC,IAAI,EAAEoM,GAAG,EAAEnM,eAAe,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,YAAY,CAAC;IAC7F,OAAOrF,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE8E,SAASA,CAAC;IAAEd,MAAM,GAAG;EAAW,CAAC,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI,CAAC,IAAI,CAACU,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,OAAOI,SAAS,CAAC,IAAI,EAAEd,MAAM,KAAK,UAAU,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACEsN,aAAaA,CAAA,EAAG;IACd,OAAO9M,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACES,SAASA,CAAC;IACRE,oBAAoB,GAAG,KAAK;IAC5BD,eAAe,GAAG,KAAK;IACvBE,aAAa,GAAG,IAAI;IACpBmM,aAAa,GAAG,KAAK;IACrBlM,YAAY,GAAG,KAAK;IACpBrB,MAAM,GAAG;EACX,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAAC,IAAI,CAACU,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI1E,CAAC,GAAGuR,aAAa,GAAG,GAAG,GAAG,EAAE;IAChC,OACEvR,CAAC,GACDiF,SAAS,CACP,IAAI,EACJjB,MAAM,KAAK,UAAU,EACrBkB,eAAe,EACfC,oBAAoB,EACpBC,aAAa,EACbC,YACF,CAAC;EAEL;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEmM,SAASA,CAAA,EAAG;IACV,OAAOhN,YAAY,CAAC,IAAI,EAAE,+BAA+B,EAAE,KAAK,CAAC;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEiN,MAAMA,CAAA,EAAG;IACP,OAAOjN,YAAY,CAAC,IAAI,CAACgL,KAAK,CAAC,CAAC,EAAE,iCAAiC,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;EACEkC,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAChN,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IACA,OAAOI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6M,SAASA,CAAC;IAAEvM,aAAa,GAAG,IAAI;IAAEwM,WAAW,GAAG,KAAK;IAAEC,kBAAkB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACvF,IAAIjG,GAAG,GAAG,cAAc;IAExB,IAAIgG,WAAW,IAAIxM,aAAa,EAAE;MAChC,IAAIyM,kBAAkB,EAAE;QACtBjG,GAAG,IAAI,GAAG;MACZ;MACA,IAAIgG,WAAW,EAAE;QACfhG,GAAG,IAAI,GAAG;MACZ,CAAC,MAAM,IAAIxG,aAAa,EAAE;QACxBwG,GAAG,IAAI,IAAI;MACb;IACF;IAEA,OAAOpH,YAAY,CAAC,IAAI,EAAEoH,GAAG,EAAE,IAAI,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkG,KAAKA,CAAC/N,IAAI,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,OAAO,GAAG,IAAI,CAACgN,SAAS,CAAC,CAAC,IAAI,IAAI,CAACC,SAAS,CAAC5N,IAAI,CAAC,EAAE;EACtD;;EAEA;AACF;AACA;AACA;EACEgO,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrN,OAAO,GAAG,IAAI,CAAC4G,KAAK,CAAC,CAAC,GAAG9L,OAAO;EAC9C;;EAEA;AACF;AACA;AACA;EACE,CAACwS,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,IAAI;IAC3C,IAAI,IAAI,CAACvN,OAAO,EAAE;MAChB,OAAO,kBAAkB,IAAI,CAAC4G,KAAK,CAAC,CAAC,WAAW,IAAI,CAAC3L,IAAI,CAACC,IAAI,aAAa,IAAI,CAACiM,MAAM,IAAI;IAC5F,CAAC,MAAM;MACL,OAAO,+BAA+B,IAAI,CAACwB,aAAa,IAAI;IAC9D;EACF;;EAEA;AACF;AACA;AACA;EACEtD,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACmI,QAAQ,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEA,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACxN,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAGsJ,GAAG;EACrC;;EAEA;AACF;AACA;AACA;EACEmI,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACzN,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAG,IAAI,GAAGsJ,GAAG;EAC5C;;EAEA;AACF;AACA;AACA;EACEoI,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1N,OAAO,GAAGpD,IAAI,CAAC+Q,KAAK,CAAC,IAAI,CAAC3R,EAAE,GAAG,IAAI,CAAC,GAAGsJ,GAAG;EACxD;;EAEA;AACF;AACA;AACA;EACEsI,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAChH,KAAK,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACEiH,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEzC,QAAQA,CAAChM,IAAI,GAAG,CAAC,CAAC,EAAE;IAClB,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE,OAAO,CAAC,CAAC;IAE5B,MAAM+N,IAAI,GAAG;MAAE,GAAG,IAAI,CAACzS;IAAE,CAAC;IAE1B,IAAI+D,IAAI,CAAC2O,aAAa,EAAE;MACtBD,IAAI,CAAClF,cAAc,GAAG,IAAI,CAACA,cAAc;MACzCkF,IAAI,CAAC3G,eAAe,GAAG,IAAI,CAAC3L,GAAG,CAAC2L,eAAe;MAC/C2G,IAAI,CAAC5G,MAAM,GAAG,IAAI,CAAC1L,GAAG,CAAC0L,MAAM;IAC/B;IACA,OAAO4G,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACED,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI7Q,IAAI,CAAC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAGsJ,GAAG,CAAC;EAC/C;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElM,IAAIA,CAAC6U,aAAa,EAAEzM,IAAI,GAAG,cAAc,EAAEnC,IAAI,GAAG,CAAC,CAAC,EAAE;IACpD,IAAI,CAAC,IAAI,CAACW,OAAO,IAAI,CAACiO,aAAa,CAACjO,OAAO,EAAE;MAC3C,OAAOjI,QAAQ,CAACmE,OAAO,CAAC,wCAAwC,CAAC;IACnE;IAEA,MAAMgS,OAAO,GAAG;MAAE/G,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;MAAE,GAAG/H;IAAK,CAAC;IAEvF,MAAMsE,KAAK,GAAGpL,UAAU,CAACiJ,IAAI,CAAC,CAAC0G,GAAG,CAACnQ,QAAQ,CAACwJ,aAAa,CAAC;MACxD4M,YAAY,GAAGF,aAAa,CAAC5I,OAAO,CAAC,CAAC,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MACvD+I,OAAO,GAAGD,YAAY,GAAG,IAAI,GAAGF,aAAa;MAC7CI,KAAK,GAAGF,YAAY,GAAGF,aAAa,GAAG,IAAI;MAC3CK,MAAM,GAAGlV,IAAI,CAACgV,OAAO,EAAEC,KAAK,EAAE1K,KAAK,EAAEuK,OAAO,CAAC;IAE/C,OAAOC,YAAY,GAAGG,MAAM,CAACvC,MAAM,CAAC,CAAC,GAAGuC,MAAM;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAAC/M,IAAI,GAAG,cAAc,EAAEnC,IAAI,GAAG,CAAC,CAAC,EAAE;IACxC,OAAO,IAAI,CAACjG,IAAI,CAAC+C,QAAQ,CAACmG,GAAG,CAAC,CAAC,EAAEd,IAAI,EAAEnC,IAAI,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACEmP,KAAKA,CAACP,aAAa,EAAE;IACnB,OAAO,IAAI,CAACjO,OAAO,GAAGhI,QAAQ,CAACyW,aAAa,CAAC,IAAI,EAAER,aAAa,CAAC,GAAG,IAAI;EAC1E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExK,OAAOA,CAACwK,aAAa,EAAEzM,IAAI,EAAEnC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE,OAAO,KAAK;IAE/B,MAAM0O,OAAO,GAAGT,aAAa,CAAC5I,OAAO,CAAC,CAAC;IACvC,MAAMsJ,cAAc,GAAG,IAAI,CAAClP,OAAO,CAACwO,aAAa,CAAChT,IAAI,EAAE;MAAEgQ,aAAa,EAAE;IAAK,CAAC,CAAC;IAChF,OACE0D,cAAc,CAACjL,OAAO,CAAClC,IAAI,EAAEnC,IAAI,CAAC,IAAIqP,OAAO,IAAIA,OAAO,IAAIC,cAAc,CAACxC,KAAK,CAAC3K,IAAI,EAAEnC,IAAI,CAAC;EAEhG;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEqF,MAAMA,CAACkK,KAAK,EAAE;IACZ,OACE,IAAI,CAAC5O,OAAO,IACZ4O,KAAK,CAAC5O,OAAO,IACb,IAAI,CAACqF,OAAO,CAAC,CAAC,KAAKuJ,KAAK,CAACvJ,OAAO,CAAC,CAAC,IAClC,IAAI,CAACpK,IAAI,CAACyJ,MAAM,CAACkK,KAAK,CAAC3T,IAAI,CAAC,IAC5B,IAAI,CAACQ,GAAG,CAACiJ,MAAM,CAACkK,KAAK,CAACnT,GAAG,CAAC;EAE9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoT,UAAUA,CAACzJ,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAAC,IAAI,CAACpF,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM+N,IAAI,GAAG3I,OAAO,CAAC2I,IAAI,IAAI5R,QAAQ,CAACyC,UAAU,CAAC,CAAC,CAAC,EAAE;QAAE3D,IAAI,EAAE,IAAI,CAACA;MAAK,CAAC,CAAC;MACvE6T,OAAO,GAAG1J,OAAO,CAAC0J,OAAO,GAAI,IAAI,GAAGf,IAAI,GAAG,CAAC3I,OAAO,CAAC0J,OAAO,GAAG1J,OAAO,CAAC0J,OAAO,GAAI,CAAC;IACpF,IAAInL,KAAK,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;IACtE,IAAInC,IAAI,GAAG4D,OAAO,CAAC5D,IAAI;IACvB,IAAIyC,KAAK,CAAC8K,OAAO,CAAC3J,OAAO,CAAC5D,IAAI,CAAC,EAAE;MAC/BmC,KAAK,GAAGyB,OAAO,CAAC5D,IAAI;MACpBA,IAAI,GAAGa,SAAS;IAClB;IACA,OAAOY,YAAY,CAAC8K,IAAI,EAAE,IAAI,CAACpC,IAAI,CAACmD,OAAO,CAAC,EAAE;MAC5C,GAAG1J,OAAO;MACV4J,OAAO,EAAE,QAAQ;MACjBrL,KAAK;MACLnC;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyN,kBAAkBA,CAAC7J,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACpF,OAAO,EAAE,OAAO,IAAI;IAE9B,OAAOiD,YAAY,CAACmC,OAAO,CAAC2I,IAAI,IAAI5R,QAAQ,CAACyC,UAAU,CAAC,CAAC,CAAC,EAAE;MAAE3D,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,CAAC,EAAE,IAAI,EAAE;MACtF,GAAGmK,OAAO;MACV4J,OAAO,EAAE,MAAM;MACfrL,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;MAClCN,SAAS,EAAE;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOxG,GAAGA,CAAC,GAAGqS,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAChT,QAAQ,CAAC0L,UAAU,CAAC,EAAE;MACzC,MAAM,IAAIpN,oBAAoB,CAAC,yCAAyC,CAAC;IAC3E;IACA,OAAO/B,MAAM,CAACwW,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAAC/J,OAAO,CAAC,CAAC,EAAEzI,IAAI,CAACC,GAAG,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,GAAGA,CAAC,GAAGoS,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAChT,QAAQ,CAAC0L,UAAU,CAAC,EAAE;MACzC,MAAM,IAAIpN,oBAAoB,CAAC,yCAAyC,CAAC;IAC3E;IACA,OAAO/B,MAAM,CAACwW,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAAC/J,OAAO,CAAC,CAAC,EAAEzI,IAAI,CAACE,GAAG,CAAC;EACxD;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOuS,iBAAiBA,CAAC9P,IAAI,EAAE2H,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAChD,MAAM;QAAE+B,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAGhC,OAAO;MACvDiC,WAAW,GAAGhP,MAAM,CAACiP,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,OAAO7N,iBAAiB,CAAC2N,WAAW,EAAE9H,IAAI,EAAE2H,GAAG,CAAC;EAClD;;EAEA;AACF;AACA;EACE,OAAOoI,iBAAiBA,CAAC/P,IAAI,EAAE2H,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAChD,OAAOjJ,QAAQ,CAACkT,iBAAiB,CAAC9P,IAAI,EAAE2H,GAAG,EAAE9B,OAAO,CAAC;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOmK,iBAAiBA,CAACrI,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,MAAM;QAAE+B,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAGhC,OAAO;MACvDiC,WAAW,GAAGhP,MAAM,CAACiP,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,OAAO,IAAI1N,WAAW,CAACwN,WAAW,EAAEH,GAAG,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOsI,gBAAgBA,CAACjQ,IAAI,EAAEkQ,YAAY,EAAEpQ,IAAI,GAAG,CAAC,CAAC,EAAE;IACrD,IAAI/G,WAAW,CAACiH,IAAI,CAAC,IAAIjH,WAAW,CAACmX,YAAY,CAAC,EAAE;MAClD,MAAM,IAAIhV,oBAAoB,CAC5B,+DACF,CAAC;IACH;IACA,MAAM;QAAE0M,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAG/H,IAAI;MACpDgI,WAAW,GAAGhP,MAAM,CAACiP,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IAEJ,IAAI,CAACF,WAAW,CAAC3C,MAAM,CAAC+K,YAAY,CAACtI,MAAM,CAAC,EAAE;MAC5C,MAAM,IAAI1M,oBAAoB,CAC5B,4CAA4C4M,WAAW,IAAI,GACzD,yCAAyCoI,YAAY,CAACtI,MAAM,EAChE,CAAC;IACH;IAEA,MAAM;MAAEuI,MAAM;MAAEzU,IAAI;MAAEuE,cAAc;MAAEmJ;IAAc,CAAC,GAAG8G,YAAY,CAAC/V,iBAAiB,CAAC6F,IAAI,CAAC;IAE5F,IAAIoJ,aAAa,EAAE;MACjB,OAAOxM,QAAQ,CAACD,OAAO,CAACyM,aAAa,CAAC;IACxC,CAAC,MAAM;MACL,OAAOzJ,mBAAmB,CACxBwQ,MAAM,EACNzU,IAAI,EACJoE,IAAI,EACJ,UAAUoQ,YAAY,CAACnQ,MAAM,EAAE,EAC/BC,IAAI,EACJC,cACF,CAAC;IACH;EACF;;EAEA;;EAEA;AACF;AACA;AACA;EACE,WAAW+M,UAAUA,CAAA,EAAG;IACtB,OAAO/R,OAAO,CAAC+R,UAAU;EAC3B;;EAEA;AACF;AACA;AACA;EACE,WAAWoD,QAAQA,CAAA,EAAG;IACpB,OAAOnV,OAAO,CAACmV,QAAQ;EACzB;;EAEA;AACF;AACA;AACA;EACE,WAAWC,qBAAqBA,CAAA,EAAG;IACjC,OAAOpV,OAAO,CAACoV,qBAAqB;EACtC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,SAASA,CAAA,EAAG;IACrB,OAAOrV,OAAO,CAACqV,SAAS;EAC1B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,SAASA,CAAA,EAAG;IACrB,OAAOtV,OAAO,CAACsV,SAAS;EAC1B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,WAAWA,CAAA,EAAG;IACvB,OAAOvV,OAAO,CAACuV,WAAW;EAC5B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,iBAAiBA,CAAA,EAAG;IAC7B,OAAOxV,OAAO,CAACwV,iBAAiB;EAClC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,sBAAsBA,CAAA,EAAG;IAClC,OAAOzV,OAAO,CAACyV,sBAAsB;EACvC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,qBAAqBA,CAAA,EAAG;IACjC,OAAO1V,OAAO,CAAC0V,qBAAqB;EACtC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,cAAcA,CAAA,EAAG;IAC1B,OAAO3V,OAAO,CAAC2V,cAAc;EAC/B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,oBAAoBA,CAAA,EAAG;IAChC,OAAO5V,OAAO,CAAC4V,oBAAoB;EACrC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAO7V,OAAO,CAAC6V,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,wBAAwBA,CAAA,EAAG;IACpC,OAAO9V,OAAO,CAAC8V,wBAAwB;EACzC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,cAAcA,CAAA,EAAG;IAC1B,OAAO/V,OAAO,CAAC+V,cAAc;EAC/B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,2BAA2BA,CAAA,EAAG;IACvC,OAAOhW,OAAO,CAACgW,2BAA2B;EAC5C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,YAAYA,CAAA,EAAG;IACxB,OAAOjW,OAAO,CAACiW,YAAY;EAC7B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAOlW,OAAO,CAACkW,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAOnW,OAAO,CAACmW,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,aAAaA,CAAA,EAAG;IACzB,OAAOpW,OAAO,CAACoW,aAAa;EAC9B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,0BAA0BA,CAAA,EAAG;IACtC,OAAOrW,OAAO,CAACqW,0BAA0B;EAC3C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,aAAaA,CAAA,EAAG;IACzB,OAAOtW,OAAO,CAACsW,aAAa;EAC9B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,0BAA0BA,CAAA,EAAG;IACtC,OAAOvW,OAAO,CAACuW,0BAA0B;EAC3C;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,IAAI9U,QAAQ,CAAC0L,UAAU,CAACoJ,WAAW,CAAC,EAAE;IACpC,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAIA,WAAW,IAAIA,WAAW,CAAC5L,OAAO,IAAI5M,QAAQ,CAACwY,WAAW,CAAC5L,OAAO,CAAC,CAAC,CAAC,EAAE;IAChF,OAAOlJ,QAAQ,CAAC+I,UAAU,CAAC+L,WAAW,CAAC;EACzC,CAAC,MAAM,IAAIA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IACzD,OAAO9U,QAAQ,CAACyC,UAAU,CAACqS,WAAW,CAAC;EACzC,CAAC,MAAM;IACL,MAAM,IAAIxW,oBAAoB,CAC5B,8BAA8BwW,WAAW,aAAa,OAAOA,WAAW,EAC1E,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}