{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/src/components/CandlestickChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport annotationPlugin from 'chartjs-plugin-annotation';\nimport PatternAnnotation, { PATTERN_DETAILS, getSignalColor } from './PatternAnnotation';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables, CandlestickController, CandlestickElement, Ohlc<PERSON>ontroller, OhlcElement, annotationPlugin);\nconst {\n  Option\n} = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\n\n// 经典图表样式配置\nconst getClassicChartStyle = () => ({\n  backgroundColor: '#FFFFFF',\n  gridColor: '#E0E0E0',\n  textColor: '#333333',\n  borderColor: '#CCCCCC',\n  titleColor: '#000000'\n});\nconst getModernChartStyle = () => ({\n  backgroundColor: '#FAFAFA',\n  gridColor: '#F0F0F0',\n  textColor: '#666666',\n  borderColor: '#D9D9D9',\n  titleColor: '#1890FF'\n});\nconst CandlestickChart = ({\n  data,\n  patterns = []\n}) => {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    const filteredPatterns = selectedPattern === 'all' ? patterns : patterns.filter(p => p.pattern_name === selectedPattern);\n    const annotations = [];\n    filteredPatterns.forEach((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n      if (!startData || !endData) return;\n      const patternDetails = PATTERN_DETAILS[pattern.pattern_name];\n      const color = getSignalColor((patternDetails === null || patternDetails === void 0 ? void 0 : patternDetails.signal) || pattern.signal);\n      const patternName = (patternDetails === null || patternDetails === void 0 ? void 0 : patternDetails.chinese) || PATTERN_NAMES[pattern.pattern_name] || pattern.pattern_name;\n\n      // 添加背景高亮框\n      annotations.push({\n        type: 'box',\n        xMin: startData.timestamp ? new Date(startData.timestamp).getTime() : startIndex,\n        xMax: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yMin: Math.min(parseFloat(startData.low), parseFloat(endData.low)) * 0.998,\n        yMax: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.002,\n        backgroundColor: color + '15',\n        borderColor: color,\n        borderWidth: 1.5,\n        borderDash: [5, 5]\n      });\n\n      // 添加形态名称标签\n      annotations.push({\n        type: 'label',\n        xValue: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yValue: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.005,\n        backgroundColor: color,\n        borderColor: color,\n        borderWidth: 1,\n        borderRadius: 4,\n        color: '#FFFFFF',\n        content: [`${patternName}`, `置信度: ${(pattern.confidence * 100).toFixed(1)}%`],\n        font: {\n          size: 11,\n          weight: 'bold'\n        },\n        padding: 6,\n        position: 'center'\n      });\n    });\n    return annotations;\n  };\n\n  // 获取形态颜色\n  const getPatternColor = signal => {\n    switch (signal) {\n      case 'bullish':\n        return '#52c41a';\n      case 'bearish':\n        return '#ff4d4f';\n      case 'neutral':\n        return '#faad14';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    const styleConfig = classicStyle ? getClassicChartStyle() : getModernChartStyle();\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: d.c\n            })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [{\n          label: '日本蜡烛图',\n          data: candleData,\n          // 根据样式选择配置颜色\n          color: classicStyle ? {\n            up: '#000000',\n            // 阳线边框：黑色\n            down: '#000000',\n            // 阴线边框：黑色\n            unchanged: '#000000'\n          } : {\n            up: '#52c41a',\n            // 阳线边框：绿色\n            down: '#ff4d4f',\n            // 阴线边框：红色\n            unchanged: '#faad14'\n          },\n          backgroundColor: classicStyle ? {\n            up: '#FFFFFF',\n            // 阳线填充：白色\n            down: '#000000',\n            // 阴线填充：黑色\n            unchanged: '#FFFFFF'\n          } : {\n            up: '#52c41a20',\n            // 阳线填充：淡绿色\n            down: '#ff4d4f20',\n            // 阴线填充：淡红色\n            unchanged: '#faad1420'\n          },\n          borderColor: classicStyle ? {\n            up: '#000000',\n            down: '#000000',\n            unchanged: '#000000'\n          } : {\n            up: '#52c41a',\n            down: '#ff4d4f',\n            unchanged: '#faad14'\n          },\n          borderWidth: 1.5,\n          // 蜡烛图样式\n          candlestick: {\n            bodyWidth: 0.8,\n            wickWidth: 1\n          }\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd',\n                hour: 'HH:mm'\n              }\n            },\n            grid: {\n              display: true,\n              color: styleConfig.gridColor,\n              lineWidth: classicStyle ? 1 : 0.5,\n              drawBorder: true,\n              borderColor: styleConfig.borderColor,\n              borderWidth: 1\n            },\n            ticks: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 10 : 11,\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              maxTicksLimit: 10\n            },\n            title: {\n              display: classicStyle,\n              text: '时间',\n              color: styleConfig.textColor,\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            position: 'right',\n            grid: {\n              display: true,\n              color: styleConfig.gridColor,\n              lineWidth: classicStyle ? 1 : 0.5,\n              drawBorder: true,\n              borderColor: styleConfig.borderColor,\n              borderWidth: 1\n            },\n            ticks: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 10 : 11,\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              callback: function (value) {\n                return value.toFixed(2);\n              },\n              maxTicksLimit: 8\n            },\n            title: {\n              display: classicStyle,\n              text: '价格',\n              color: styleConfig.textColor,\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          }\n        },\n        plugins: {\n          title: {\n            display: classicStyle,\n            text: '日蜡烛线图',\n            color: styleConfig.titleColor,\n            font: {\n              size: 16,\n              weight: 'bold',\n              family: classicStyle ? 'serif' : 'Arial'\n            },\n            padding: {\n              top: 10,\n              bottom: 20\n            }\n          },\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 11 : 12,\n                weight: 'bold',\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              usePointStyle: true,\n              padding: 15\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: context => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString('zh-CN') : `第 ${dataIndex + 1} 根K线`;\n              },\n              label: context => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [`开盘: ${candle.o.toFixed(2)}`, `最高: ${candle.h.toFixed(2)}`, `最低: ${candle.l.toFixed(2)}`, `收盘: ${candle.c.toFixed(2)}`, `成交量: ${candle.v.toLocaleString()}`];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    chartInstance.current = new Chart(ctx, config);\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType, classicStyle]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u6570\\u636E\\uFF0C\\u8BF7\\u5148\\u4E0A\\u4F20\\u8721\\u70DB\\u56FE\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 16,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u56FE\\u8868\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartType,\n            onChange: setChartType,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"candlestick\",\n              children: \"\\u8721\\u70DB\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"line\",\n              children: \"\\u6298\\u7EBF\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u7ECF\\u5178\\u6837\\u5F0F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: classicStyle,\n            onChange: setClassicStyle,\n            checkedChildren: \"\\u9ED1\\u767D\",\n            unCheckedChildren: \"\\u5F69\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u663E\\u793A\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showPatterns,\n            onChange: setShowPatterns,\n            checkedChildren: \"\\u5F00\",\n            unCheckedChildren: \"\\u5173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), showPatterns && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u5F62\\u6001\\u8FC7\\u6EE4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedPattern,\n            onChange: setSelectedPattern,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u5168\\u90E8\\u5F62\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), getUniquePatterns().map(pattern => /*#__PURE__*/_jsxDEV(Option, {\n              value: pattern,\n              children: pattern\n            }, pattern, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: data.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this), patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u8BC6\\u522B\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: patterns.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '500px',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: chartRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this), showPatterns && patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginTop: 16\n      },\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u8BC6\\u522B\\u5230\\u7684\\u8721\\u70DB\\u56FE\\u5F62\\u6001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          children: [patterns.length, \"\\u4E2A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 13\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 8,\n          alignItems: 'center'\n        },\n        children: getUniquePatterns().map(patternName => {\n          const pattern = patterns.find(p => p.pattern_name === patternName);\n          if (!pattern) return null;\n          return /*#__PURE__*/_jsxDEV(PatternAnnotation, {\n            pattern: pattern,\n            showEnglish: true,\n            compact: false\n          }, patternName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16,\n          padding: '12px',\n          backgroundColor: '#fafafa',\n          borderRadius: '6px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: 16,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#52c41a',\n                fontWeight: 'bold'\n              },\n              children: [\"\\u770B\\u6DA8\\u5F62\\u6001: \", patterns.filter(p => {\n                const details = PATTERN_DETAILS[p.pattern_name];\n                return (details === null || details === void 0 ? void 0 : details.signal) === 'bullish' || p.signal === 'bullish';\n              }).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#ff4d4f',\n                fontWeight: 'bold'\n              },\n              children: [\"\\u770B\\u8DCC\\u5F62\\u6001: \", patterns.filter(p => {\n                const details = PATTERN_DETAILS[p.pattern_name];\n                return (details === null || details === void 0 ? void 0 : details.signal) === 'bearish' || p.signal === 'bearish';\n              }).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#722ed1',\n                fontWeight: 'bold'\n              },\n              children: [\"\\u53CD\\u8F6C\\u5F62\\u6001: \", patterns.filter(p => {\n                const details = PATTERN_DETAILS[p.pattern_name];\n                return (details === null || details === void 0 ? void 0 : details.signal) === 'reversal';\n              }).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#faad14',\n                fontWeight: 'bold'\n              },\n              children: [\"\\u9AD8\\u7F6E\\u4FE1\\u5EA6: \", patterns.filter(p => p.confidence >= 0.8).length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 425,\n    columnNumber: 5\n  }, this);\n};\n_s(CandlestickChart, \"e/7/0Cw5CRvQSoAkveSHJuxV6s4=\");\n_c = CandlestickChart;\nexport default CandlestickChart;\nvar _c;\n$RefreshReg$(_c, \"CandlestickChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "Select", "Switch", "<PERSON><PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Chart", "registerables", "CandlestickController", "CandlestickElement", "OhlcController", "OhlcElement", "annotationPlugin", "PatternAnnotation", "PATTERN_DETAILS", "getSignalColor", "jsxDEV", "_jsxDEV", "register", "Option", "PATTERN_NAMES", "getClassicChartStyle", "backgroundColor", "gridColor", "textColor", "borderColor", "titleColor", "getModernChartStyle", "CandlestickChart", "data", "patterns", "_s", "chartRef", "chartInstance", "showPatterns", "setShowPatterns", "selectedPatt<PERSON>", "setSelectedPattern", "chartType", "setChartType", "classicStyle", "setClassicStyle", "processCandleData", "length", "map", "candle", "index", "x", "timestamp", "Date", "getTime", "o", "parseFloat", "open", "h", "high", "l", "low", "c", "close", "v", "volume", "processPatternAnnotations", "filteredPatterns", "filter", "p", "pattern_name", "annotations", "for<PERSON>ach", "pattern", "startIndex", "start_index", "endIndex", "end_index", "startData", "endData", "patternDetails", "color", "signal", "patternName", "chinese", "push", "type", "xMin", "xMax", "yMin", "Math", "min", "yMax", "max", "borderWidth", "borderDash", "xValue", "yValue", "borderRadius", "content", "confidence", "toFixed", "font", "size", "weight", "padding", "position", "getPatternColor", "createChartConfig", "candleData", "styleConfig", "datasets", "label", "d", "y", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "time", "unit", "beginAtZero", "plugins", "legend", "display", "annotation", "up", "down", "unchanged", "candlestick", "bodyWidth", "wick<PERSON>idth", "interaction", "intersect", "mode", "displayFormats", "day", "hour", "grid", "lineWidth", "drawBorder", "ticks", "family", "maxTicksLimit", "title", "text", "callback", "value", "top", "bottom", "labels", "usePointStyle", "tooltip", "bodyColor", "callbacks", "context", "dataIndex", "toLocaleDateString", "toLocaleString", "current", "destroy", "ctx", "getContext", "config", "getUniquePatterns", "uniqueNames", "Set", "sort", "children", "style", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "alignItems", "gap", "flexWrap", "marginRight", "onChange", "width", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "height", "ref", "marginTop", "find", "showEnglish", "compact", "fontWeight", "details", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport annotationPlugin from 'chartjs-plugin-annotation';\nimport PatternAnnotation, { PATTERN_DETAILS, getSignalColor } from './PatternAnnotation';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement, annotationPlugin);\n\nconst { Option } = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\n\n// 经典图表样式配置\nconst getClassicChartStyle = () => ({\n  backgroundColor: '#FFFFFF',\n  gridColor: '#E0E0E0',\n  textColor: '#333333',\n  borderColor: '#CCCCCC',\n  titleColor: '#000000'\n});\n\nconst getModernChartStyle = () => ({\n  backgroundColor: '#FAFAFA',\n  gridColor: '#F0F0F0',\n  textColor: '#666666',\n  borderColor: '#D9D9D9',\n  titleColor: '#1890FF'\n});\n\nconst CandlestickChart = ({ data, patterns = [] }) => {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n\n    const filteredPatterns = selectedPattern === 'all'\n      ? patterns\n      : patterns.filter(p => p.pattern_name === selectedPattern);\n\n    const annotations = [];\n\n    filteredPatterns.forEach((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n\n      if (!startData || !endData) return;\n\n      const patternDetails = PATTERN_DETAILS[pattern.pattern_name];\n      const color = getSignalColor(patternDetails?.signal || pattern.signal);\n      const patternName = patternDetails?.chinese || PATTERN_NAMES[pattern.pattern_name] || pattern.pattern_name;\n\n      // 添加背景高亮框\n      annotations.push({\n        type: 'box',\n        xMin: startData.timestamp ? new Date(startData.timestamp).getTime() : startIndex,\n        xMax: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yMin: Math.min(parseFloat(startData.low), parseFloat(endData.low)) * 0.998,\n        yMax: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.002,\n        backgroundColor: color + '15',\n        borderColor: color,\n        borderWidth: 1.5,\n        borderDash: [5, 5]\n      });\n\n      // 添加形态名称标签\n      annotations.push({\n        type: 'label',\n        xValue: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yValue: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.005,\n        backgroundColor: color,\n        borderColor: color,\n        borderWidth: 1,\n        borderRadius: 4,\n        color: '#FFFFFF',\n        content: [`${patternName}`, `置信度: ${(pattern.confidence * 100).toFixed(1)}%`],\n        font: {\n          size: 11,\n          weight: 'bold'\n        },\n        padding: 6,\n        position: 'center'\n      });\n    });\n\n    return annotations;\n  };\n\n  // 获取形态颜色\n  const getPatternColor = (signal) => {\n    switch (signal) {\n      case 'bullish': return '#52c41a';\n      case 'bearish': return '#ff4d4f';\n      case 'neutral': return '#faad14';\n      default: return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    const styleConfig = classicStyle ? getClassicChartStyle() : getModernChartStyle();\n\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({ x: d.x, y: d.c })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [\n          {\n            label: '日本蜡烛图',\n            data: candleData,\n            // 根据样式选择配置颜色\n            color: classicStyle ? {\n              up: '#000000',    // 阳线边框：黑色\n              down: '#000000',  // 阴线边框：黑色\n              unchanged: '#000000'\n            } : {\n              up: '#52c41a',    // 阳线边框：绿色\n              down: '#ff4d4f',  // 阴线边框：红色\n              unchanged: '#faad14'\n            },\n            backgroundColor: classicStyle ? {\n              up: '#FFFFFF',    // 阳线填充：白色\n              down: '#000000',  // 阴线填充：黑色\n              unchanged: '#FFFFFF'\n            } : {\n              up: '#52c41a20',  // 阳线填充：淡绿色\n              down: '#ff4d4f20', // 阴线填充：淡红色\n              unchanged: '#faad1420'\n            },\n            borderColor: classicStyle ? {\n              up: '#000000',\n              down: '#000000',\n              unchanged: '#000000'\n            } : {\n              up: '#52c41a',\n              down: '#ff4d4f',\n              unchanged: '#faad14'\n            },\n            borderWidth: 1.5,\n            // 蜡烛图样式\n            candlestick: {\n              bodyWidth: 0.8,\n              wickWidth: 1\n            }\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd',\n                hour: 'HH:mm'\n              }\n            },\n            grid: {\n              display: true,\n              color: styleConfig.gridColor,\n              lineWidth: classicStyle ? 1 : 0.5,\n              drawBorder: true,\n              borderColor: styleConfig.borderColor,\n              borderWidth: 1\n            },\n            ticks: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 10 : 11,\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              maxTicksLimit: 10\n            },\n            title: {\n              display: classicStyle,\n              text: '时间',\n              color: styleConfig.textColor,\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            position: 'right',\n            grid: {\n              display: true,\n              color: styleConfig.gridColor,\n              lineWidth: classicStyle ? 1 : 0.5,\n              drawBorder: true,\n              borderColor: styleConfig.borderColor,\n              borderWidth: 1\n            },\n            ticks: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 10 : 11,\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              callback: function(value) {\n                return value.toFixed(2);\n              },\n              maxTicksLimit: 8\n            },\n            title: {\n              display: classicStyle,\n              text: '价格',\n              color: styleConfig.textColor,\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          }\n        },\n        plugins: {\n          title: {\n            display: classicStyle,\n            text: '日蜡烛线图',\n            color: styleConfig.titleColor,\n            font: {\n              size: 16,\n              weight: 'bold',\n              family: classicStyle ? 'serif' : 'Arial'\n            },\n            padding: {\n              top: 10,\n              bottom: 20\n            }\n          },\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: styleConfig.textColor,\n              font: {\n                size: classicStyle ? 11 : 12,\n                weight: 'bold',\n                family: classicStyle ? 'monospace' : 'Arial'\n              },\n              usePointStyle: true,\n              padding: 15\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: (context) => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ?\n                  new Date(candle.timestamp).toLocaleDateString('zh-CN') :\n                  `第 ${dataIndex + 1} 根K线`;\n              },\n              label: (context) => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [\n                  `开盘: ${candle.o.toFixed(2)}`,\n                  `最高: ${candle.h.toFixed(2)}`,\n                  `最低: ${candle.l.toFixed(2)}`,\n                  `收盘: ${candle.c.toFixed(2)}`,\n                  `成交量: ${candle.v.toLocaleString()}`\n                ];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    \n    chartInstance.current = new Chart(ctx, config);\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType, classicStyle]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n\n  if (!data || data.length === 0) {\n    return (\n      <Card>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <p>暂无数据，请先上传蜡烛图数据</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div>\n      {/* 控制面板 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>\n          <div>\n            <span style={{ marginRight: 8 }}>图表类型:</span>\n            <Select\n              value={chartType}\n              onChange={setChartType}\n              style={{ width: 120 }}\n            >\n              <Option value=\"candlestick\">蜡烛图</Option>\n              <Option value=\"line\">折线图</Option>\n            </Select>\n          </div>\n\n          <div>\n            <span style={{ marginRight: 8 }}>经典样式:</span>\n            <Switch\n              checked={classicStyle}\n              onChange={setClassicStyle}\n              checkedChildren=\"黑白\"\n              unCheckedChildren=\"彩色\"\n            />\n          </div>\n\n          <div>\n            <span style={{ marginRight: 8 }}>显示形态:</span>\n            <Switch\n              checked={showPatterns}\n              onChange={setShowPatterns}\n              checkedChildren=\"开\"\n              unCheckedChildren=\"关\"\n            />\n          </div>\n          \n          {showPatterns && (\n            <div>\n              <span style={{ marginRight: 8 }}>形态过滤:</span>\n              <Select \n                value={selectedPattern} \n                onChange={setSelectedPattern}\n                style={{ width: 150 }}\n              >\n                <Option value=\"all\">全部形态</Option>\n                {getUniquePatterns().map(pattern => (\n                  <Option key={pattern} value={pattern}>{pattern}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n          \n          <div>\n            <span style={{ marginRight: 8 }}>数据点数:</span>\n            <Tag color=\"blue\">{data.length}</Tag>\n          </div>\n          \n          {patterns && patterns.length > 0 && (\n            <div>\n              <span style={{ marginRight: 8 }}>识别形态:</span>\n              <Tag color=\"green\">{patterns.length}</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 图表区域 */}\n      <Card>\n        <div style={{ height: '500px', position: 'relative' }}>\n          <canvas ref={chartRef} />\n        </div>\n      </Card>\n\n      {/* 形态图例 */}\n      {showPatterns && patterns && patterns.length > 0 && (\n        <Card\n          size=\"small\"\n          style={{ marginTop: 16 }}\n          title={\n            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>\n              <span>识别到的蜡烛图形态</span>\n              <Tag color=\"blue\">{patterns.length}个</Tag>\n            </div>\n          }\n        >\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, alignItems: 'center' }}>\n            {getUniquePatterns().map(patternName => {\n              const pattern = patterns.find(p => p.pattern_name === patternName);\n              if (!pattern) return null;\n\n              return (\n                <PatternAnnotation\n                  key={patternName}\n                  pattern={pattern}\n                  showEnglish={true}\n                  compact={false}\n                />\n              );\n            })}\n          </div>\n\n          {/* 形态统计 */}\n          <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#fafafa', borderRadius: '6px' }}>\n            <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>\n              <div>\n                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>\n                  看涨形态: {patterns.filter(p => {\n                    const details = PATTERN_DETAILS[p.pattern_name];\n                    return details?.signal === 'bullish' || p.signal === 'bullish';\n                  }).length}\n                </span>\n              </div>\n              <div>\n                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>\n                  看跌形态: {patterns.filter(p => {\n                    const details = PATTERN_DETAILS[p.pattern_name];\n                    return details?.signal === 'bearish' || p.signal === 'bearish';\n                  }).length}\n                </span>\n              </div>\n              <div>\n                <span style={{ color: '#722ed1', fontWeight: 'bold' }}>\n                  反转形态: {patterns.filter(p => {\n                    const details = PATTERN_DETAILS[p.pattern_name];\n                    return details?.signal === 'reversal';\n                  }).length}\n                </span>\n              </div>\n              <div>\n                <span style={{ color: '#faad14', fontWeight: 'bold' }}>\n                  高置信度: {patterns.filter(p => p.confidence >= 0.8).length}\n                </span>\n              </div>\n            </div>\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default CandlestickChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AACjE,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AAChH,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAOC,iBAAiB,IAAIC,eAAe,EAAEC,cAAc,QAAQ,qBAAqB;AACxF,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAX,KAAK,CAACY,QAAQ,CAAC,GAAGX,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,CAAC;AAE1H,MAAM;EAAEO;AAAO,CAAC,GAAGlB,MAAM;;AAEzB;AACA,MAAMmB,aAAa,GAAG;EACpB,QAAQ,EAAE,KAAK;EACf,aAAa,EAAE,KAAK;EACpB,MAAM,EAAE,KAAK;EACb,kBAAkB,EAAE,OAAO;EAC3B,iBAAiB,EAAE,OAAO;EAC1B,gBAAgB,EAAE,OAAO;EACzB,cAAc,EAAE,KAAK;EACrB,UAAU,EAAE,OAAO;EACnB,mBAAmB,EAAE,MAAM;EAC3B,mBAAmB,EAAE,MAAM;EAC3B,kBAAkB,EAAE,MAAM;EAC1B,kBAAkB,EAAE,MAAM;EAC1B,gBAAgB,EAAE,MAAM;EACxB,gBAAgB,EAAE,MAAM;EACxB,cAAc,EAAE,MAAM;EACtB,cAAc,EAAE,MAAM;EACtB,iBAAiB,EAAE,MAAM;EACzB,cAAc,EAAE,KAAK;EACrB,cAAc,EAAE,KAAK;EACrB,mBAAmB,EAAE,OAAO;EAC5B,mBAAmB,EAAE,OAAO;EAC5B,sBAAsB,EAAE,QAAQ;EAChC,mBAAmB,EAAE,MAAM;EAC3B,sBAAsB,EAAE,MAAM;EAC9B,uBAAuB,EAAE,MAAM;EAC/B,eAAe,EAAE,IAAI;EACrB,iBAAiB,EAAE;AACrB,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAGA,CAAA,MAAO;EAClCC,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,MAAMC,mBAAmB,GAAGA,CAAA,MAAO;EACjCL,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,MAAME,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMmC,aAAa,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACb,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOd,IAAI,CAACe,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;MAClCC,CAAC,EAAEF,MAAM,CAACG,SAAS,GAAG,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGJ,KAAK;MAClEK,CAAC,EAAEC,UAAU,CAACP,MAAM,CAACQ,IAAI,CAAC;MAC1BC,CAAC,EAAEF,UAAU,CAACP,MAAM,CAACU,IAAI,CAAC;MAC1BC,CAAC,EAAEJ,UAAU,CAACP,MAAM,CAACY,GAAG,CAAC;MACzBC,CAAC,EAAEN,UAAU,CAACP,MAAM,CAACc,KAAK,CAAC;MAC3BC,CAAC,EAAER,UAAU,CAACP,MAAM,CAACgB,MAAM,IAAI,IAAI,CAAC;MACpCb,SAAS,EAAEH,MAAM,CAACG;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMc,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC5B,YAAY,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAElE,MAAMoB,gBAAgB,GAAG3B,eAAe,KAAK,KAAK,GAC9CN,QAAQ,GACRA,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK9B,eAAe,CAAC;IAE5D,MAAM+B,WAAW,GAAG,EAAE;IAEtBJ,gBAAgB,CAACK,OAAO,CAAC,CAACC,OAAO,EAAEvB,KAAK,KAAK;MAC3C,MAAMwB,UAAU,GAAGD,OAAO,CAACE,WAAW,IAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGH,OAAO,CAACI,SAAS,IAAIH,UAAU;;MAEhD;MACA,MAAMI,SAAS,GAAG7C,IAAI,CAACyC,UAAU,CAAC;MAClC,MAAMK,OAAO,GAAG9C,IAAI,CAAC2C,QAAQ,CAAC;MAE9B,IAAI,CAACE,SAAS,IAAI,CAACC,OAAO,EAAE;MAE5B,MAAMC,cAAc,GAAG9D,eAAe,CAACuD,OAAO,CAACH,YAAY,CAAC;MAC5D,MAAMW,KAAK,GAAG9D,cAAc,CAAC,CAAA6D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,MAAM,KAAIT,OAAO,CAACS,MAAM,CAAC;MACtE,MAAMC,WAAW,GAAG,CAAAH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEI,OAAO,KAAI5D,aAAa,CAACiD,OAAO,CAACH,YAAY,CAAC,IAAIG,OAAO,CAACH,YAAY;;MAE1G;MACAC,WAAW,CAACc,IAAI,CAAC;QACfC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAET,SAAS,CAAC1B,SAAS,GAAG,IAAIC,IAAI,CAACyB,SAAS,CAAC1B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGoB,UAAU;QAChFc,IAAI,EAAET,OAAO,CAAC3B,SAAS,GAAG,IAAIC,IAAI,CAAC0B,OAAO,CAAC3B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGsB,QAAQ;QAC1Ea,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACnC,UAAU,CAACsB,SAAS,CAACjB,GAAG,CAAC,EAAEL,UAAU,CAACuB,OAAO,CAAClB,GAAG,CAAC,CAAC,GAAG,KAAK;QAC1E+B,IAAI,EAAEF,IAAI,CAACG,GAAG,CAACrC,UAAU,CAACsB,SAAS,CAACnB,IAAI,CAAC,EAAEH,UAAU,CAACuB,OAAO,CAACpB,IAAI,CAAC,CAAC,GAAG,KAAK;QAC5EjC,eAAe,EAAEuD,KAAK,GAAG,IAAI;QAC7BpD,WAAW,EAAEoD,KAAK;QAClBa,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;MACnB,CAAC,CAAC;;MAEF;MACAxB,WAAW,CAACc,IAAI,CAAC;QACfC,IAAI,EAAE,OAAO;QACbU,MAAM,EAAEjB,OAAO,CAAC3B,SAAS,GAAG,IAAIC,IAAI,CAAC0B,OAAO,CAAC3B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGsB,QAAQ;QAC5EqB,MAAM,EAAEP,IAAI,CAACG,GAAG,CAACrC,UAAU,CAACsB,SAAS,CAACnB,IAAI,CAAC,EAAEH,UAAU,CAACuB,OAAO,CAACpB,IAAI,CAAC,CAAC,GAAG,KAAK;QAC9EjC,eAAe,EAAEuD,KAAK;QACtBpD,WAAW,EAAEoD,KAAK;QAClBa,WAAW,EAAE,CAAC;QACdI,YAAY,EAAE,CAAC;QACfjB,KAAK,EAAE,SAAS;QAChBkB,OAAO,EAAE,CAAC,GAAGhB,WAAW,EAAE,EAAE,QAAQ,CAACV,OAAO,CAAC2B,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAC7EC,IAAI,EAAE;UACJC,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE;QACV,CAAC;QACDC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOnC,WAAW;EACpB,CAAC;;EAED;EACA,MAAMoC,eAAe,GAAIzB,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAG/D,iBAAiB,CAAC,CAAC;IACtC,MAAMyB,WAAW,GAAGL,yBAAyB,CAAC,CAAC;IAC/C,MAAM4C,WAAW,GAAGlE,YAAY,GAAGnB,oBAAoB,CAAC,CAAC,GAAGM,mBAAmB,CAAC,CAAC;IAEjF,IAAIW,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO;QACL4C,IAAI,EAAE,MAAM;QACZrD,IAAI,EAAE;UACJ8E,QAAQ,EAAE,CAAC;YACTC,KAAK,EAAE,KAAK;YACZ/E,IAAI,EAAE4E,UAAU,CAAC7D,GAAG,CAACiE,CAAC,KAAK;cAAE9D,CAAC,EAAE8D,CAAC,CAAC9D,CAAC;cAAE+D,CAAC,EAAED,CAAC,CAACnD;YAAE,CAAC,CAAC,CAAC;YAC/CjC,WAAW,EAAE,SAAS;YACtBH,eAAe,EAAE,WAAW;YAC5ByF,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;UACX,CAAC;QACH,CAAC;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNrE,CAAC,EAAE;cACDmC,IAAI,EAAE,MAAM;cACZmC,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF,CAAC;YACDR,CAAC,EAAE;cACDS,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;YACX,CAAC;YACDC,UAAU,EAAE;cACVxD,WAAW,EAAEA;YACf;UACF;QACF;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLe,IAAI,EAAE,aAAa;MACnBrD,IAAI,EAAE;QACJ8E,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,OAAO;UACd/E,IAAI,EAAE4E,UAAU;UAChB;UACA5B,KAAK,EAAErC,YAAY,GAAG;YACpBoF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC;UACDxG,eAAe,EAAEkB,YAAY,GAAG;YAC9BoF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,WAAW;YAAG;YAClBC,IAAI,EAAE,WAAW;YAAE;YACnBC,SAAS,EAAE;UACb,CAAC;UACDrG,WAAW,EAAEe,YAAY,GAAG;YAC1BoF,EAAE,EAAE,SAAS;YACbC,IAAI,EAAE,SAAS;YACfC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,SAAS;YACbC,IAAI,EAAE,SAAS;YACfC,SAAS,EAAE;UACb,CAAC;UACDpC,WAAW,EAAE,GAAG;UAChB;UACAqC,WAAW,EAAE;YACXC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE;UACb;QACF,CAAC;MAEL,CAAC;MACDhB,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1Be,WAAW,EAAE;UACXC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE;QACR,CAAC;QACDhB,MAAM,EAAE;UACNrE,CAAC,EAAE;YACDmC,IAAI,EAAE,MAAM;YACZmC,IAAI,EAAE;cACJC,IAAI,EAAE,KAAK;cACXe,cAAc,EAAE;gBACdC,GAAG,EAAE,OAAO;gBACZC,IAAI,EAAE;cACR;YACF,CAAC;YACDC,IAAI,EAAE;cACJd,OAAO,EAAE,IAAI;cACb7C,KAAK,EAAE6B,WAAW,CAACnF,SAAS;cAC5BkH,SAAS,EAAEjG,YAAY,GAAG,CAAC,GAAG,GAAG;cACjCkG,UAAU,EAAE,IAAI;cAChBjH,WAAW,EAAEiF,WAAW,CAACjF,WAAW;cACpCiE,WAAW,EAAE;YACf,CAAC;YACDiD,KAAK,EAAE;cACL9D,KAAK,EAAE6B,WAAW,CAAClF,SAAS;cAC5B0E,IAAI,EAAE;gBACJC,IAAI,EAAE3D,YAAY,GAAG,EAAE,GAAG,EAAE;gBAC5BoG,MAAM,EAAEpG,YAAY,GAAG,WAAW,GAAG;cACvC,CAAC;cACDqG,aAAa,EAAE;YACjB,CAAC;YACDC,KAAK,EAAE;cACLpB,OAAO,EAAElF,YAAY;cACrBuG,IAAI,EAAE,IAAI;cACVlE,KAAK,EAAE6B,WAAW,CAAClF,SAAS;cAC5B0E,IAAI,EAAE;gBACJC,IAAI,EAAE,EAAE;gBACRC,MAAM,EAAE;cACV;YACF;UACF,CAAC;UACDU,CAAC,EAAE;YACDS,WAAW,EAAE,KAAK;YAClBjB,QAAQ,EAAE,OAAO;YACjBkC,IAAI,EAAE;cACJd,OAAO,EAAE,IAAI;cACb7C,KAAK,EAAE6B,WAAW,CAACnF,SAAS;cAC5BkH,SAAS,EAAEjG,YAAY,GAAG,CAAC,GAAG,GAAG;cACjCkG,UAAU,EAAE,IAAI;cAChBjH,WAAW,EAAEiF,WAAW,CAACjF,WAAW;cACpCiE,WAAW,EAAE;YACf,CAAC;YACDiD,KAAK,EAAE;cACL9D,KAAK,EAAE6B,WAAW,CAAClF,SAAS;cAC5B0E,IAAI,EAAE;gBACJC,IAAI,EAAE3D,YAAY,GAAG,EAAE,GAAG,EAAE;gBAC5BoG,MAAM,EAAEpG,YAAY,GAAG,WAAW,GAAG;cACvC,CAAC;cACDwG,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;gBACxB,OAAOA,KAAK,CAAChD,OAAO,CAAC,CAAC,CAAC;cACzB,CAAC;cACD4C,aAAa,EAAE;YACjB,CAAC;YACDC,KAAK,EAAE;cACLpB,OAAO,EAAElF,YAAY;cACrBuG,IAAI,EAAE,IAAI;cACVlE,KAAK,EAAE6B,WAAW,CAAClF,SAAS;cAC5B0E,IAAI,EAAE;gBACJC,IAAI,EAAE,EAAE;gBACRC,MAAM,EAAE;cACV;YACF;UACF;QACF,CAAC;QACDoB,OAAO,EAAE;UACPsB,KAAK,EAAE;YACLpB,OAAO,EAAElF,YAAY;YACrBuG,IAAI,EAAE,OAAO;YACblE,KAAK,EAAE6B,WAAW,CAAChF,UAAU;YAC7BwE,IAAI,EAAE;cACJC,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE,MAAM;cACdwC,MAAM,EAAEpG,YAAY,GAAG,OAAO,GAAG;YACnC,CAAC;YACD6D,OAAO,EAAE;cACP6C,GAAG,EAAE,EAAE;cACPC,MAAM,EAAE;YACV;UACF,CAAC;UACD1B,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACbpB,QAAQ,EAAE,KAAK;YACf8C,MAAM,EAAE;cACNvE,KAAK,EAAE6B,WAAW,CAAClF,SAAS;cAC5B0E,IAAI,EAAE;gBACJC,IAAI,EAAE3D,YAAY,GAAG,EAAE,GAAG,EAAE;gBAC5B4D,MAAM,EAAE,MAAM;gBACdwC,MAAM,EAAEpG,YAAY,GAAG,WAAW,GAAG;cACvC,CAAC;cACD6G,aAAa,EAAE,IAAI;cACnBhD,OAAO,EAAE;YACX;UACF,CAAC;UACDiD,OAAO,EAAE;YACPhI,eAAe,EAAE,oBAAoB;YACrCI,UAAU,EAAE,SAAS;YACrB6H,SAAS,EAAE,SAAS;YACpB9H,WAAW,EAAE,SAAS;YACtBiE,WAAW,EAAE,CAAC;YACd8D,SAAS,EAAE;cACTV,KAAK,EAAGW,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAM7G,MAAM,GAAG4D,UAAU,CAACiD,SAAS,CAAC;gBACpC,OAAO7G,MAAM,CAACG,SAAS,GACrB,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAAC2G,kBAAkB,CAAC,OAAO,CAAC,GACtD,KAAKD,SAAS,GAAG,CAAC,MAAM;cAC5B,CAAC;cACD9C,KAAK,EAAG6C,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAACC,SAAS;gBACnC,MAAM7G,MAAM,GAAG4D,UAAU,CAACiD,SAAS,CAAC;gBACpC,OAAO,CACL,OAAO7G,MAAM,CAACM,CAAC,CAAC8C,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOpD,MAAM,CAACS,CAAC,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOpD,MAAM,CAACW,CAAC,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOpD,MAAM,CAACa,CAAC,CAACuC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,QAAQpD,MAAM,CAACe,CAAC,CAACgG,cAAc,CAAC,CAAC,EAAE,CACpC;cACH;YACF;UACF,CAAC;UACDjC,UAAU,EAAE;YACVxD,WAAW,EAAEA;UACf;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACAtE,SAAS,CAAC,MAAM;IACd,IAAI,CAACmC,QAAQ,CAAC6H,OAAO,IAAI,CAAChI,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;;IAErD;IACA,IAAIV,aAAa,CAAC4H,OAAO,EAAE;MACzB5H,aAAa,CAAC4H,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;;IAEA;IACA,MAAMC,GAAG,GAAG/H,QAAQ,CAAC6H,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGzD,iBAAiB,CAAC,CAAC;IAElCvE,aAAa,CAAC4H,OAAO,GAAG,IAAIvJ,KAAK,CAACyJ,GAAG,EAAEE,MAAM,CAAC;IAE9C,OAAO,MAAM;MACX,IAAIhI,aAAa,CAAC4H,OAAO,EAAE;QACzB5H,aAAa,CAAC4H,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACjI,IAAI,EAAEC,QAAQ,EAAEI,YAAY,EAAEE,eAAe,EAAEE,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE5E;EACA,MAAM0H,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACpI,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACjD,MAAMwH,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACtI,QAAQ,CAACc,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACnE,OAAOiG,WAAW,CAACE,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED,IAAI,CAACxI,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACE1B,OAAA,CAACjB,IAAI;MAAAsK,QAAA,eACHrJ,OAAA;QAAKsJ,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEnE,OAAO,EAAE;QAAO,CAAE;QAAAiE,QAAA,eACnDrJ,OAAA;UAAAqJ,QAAA,EAAG;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE3J,OAAA;IAAAqJ,QAAA,gBAEErJ,OAAA,CAACjB,IAAI;MAACmG,IAAI,EAAC,OAAO;MAACoE,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eAC7CrJ,OAAA;QAAKsJ,KAAK,EAAE;UAAE7C,OAAO,EAAE,MAAM;UAAEoD,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAC/ErJ,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAAChB,MAAM;YACLgJ,KAAK,EAAE3G,SAAU;YACjB4I,QAAQ,EAAE3I,YAAa;YACvBgI,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAEtBrJ,OAAA,CAACE,MAAM;cAAC8H,KAAK,EAAC,aAAa;cAAAqB,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC3J,OAAA,CAACE,MAAM;cAAC8H,KAAK,EAAC,MAAM;cAAAqB,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN3J,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAACf,MAAM;YACLkL,OAAO,EAAE5I,YAAa;YACtB0I,QAAQ,EAAEzI,eAAgB;YAC1B4I,eAAe,EAAC,cAAI;YACpBC,iBAAiB,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3J,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAACf,MAAM;YACLkL,OAAO,EAAElJ,YAAa;YACtBgJ,QAAQ,EAAE/I,eAAgB;YAC1BkJ,eAAe,EAAC,QAAG;YACnBC,iBAAiB,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL1I,YAAY,iBACXjB,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAAChB,MAAM;YACLgJ,KAAK,EAAE7G,eAAgB;YACvB8I,QAAQ,EAAE7I,kBAAmB;YAC7BkI,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAEtBrJ,OAAA,CAACE,MAAM;cAAC8H,KAAK,EAAC,KAAK;cAAAqB,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCV,iBAAiB,CAAC,CAAC,CAACtH,GAAG,CAACyB,OAAO,iBAC9BpD,OAAA,CAACE,MAAM;cAAe8H,KAAK,EAAE5E,OAAQ;cAAAiG,QAAA,EAAEjG;YAAO,GAAjCA,OAAO;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAED3J,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAACb,GAAG;YAACyE,KAAK,EAAC,MAAM;YAAAyF,QAAA,EAAEzI,IAAI,CAACc;UAAM;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAEL9I,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9B1B,OAAA;UAAAqJ,QAAA,gBACErJ,OAAA;YAAMsJ,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C3J,OAAA,CAACb,GAAG;YAACyE,KAAK,EAAC,OAAO;YAAAyF,QAAA,EAAExI,QAAQ,CAACa;UAAM;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP3J,OAAA,CAACjB,IAAI;MAAAsK,QAAA,eACHrJ,OAAA;QAAKsJ,KAAK,EAAE;UAAEgB,MAAM,EAAE,OAAO;UAAEjF,QAAQ,EAAE;QAAW,CAAE;QAAAgE,QAAA,eACpDrJ,OAAA;UAAQuK,GAAG,EAAExJ;QAAS;UAAAyI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGN1I,YAAY,IAAIJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9C1B,OAAA,CAACjB,IAAI;MACHmG,IAAI,EAAC,OAAO;MACZoE,KAAK,EAAE;QAAEkB,SAAS,EAAE;MAAG,CAAE;MACzB3C,KAAK,eACH7H,OAAA;QAAKsJ,KAAK,EAAE;UAAE7C,OAAO,EAAE,MAAM;UAAEoD,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAC5DrJ,OAAA;UAAAqJ,QAAA,EAAM;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtB3J,OAAA,CAACb,GAAG;UAACyE,KAAK,EAAC,MAAM;UAAAyF,QAAA,GAAExI,QAAQ,CAACa,MAAM,EAAC,QAAC;QAAA;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CACN;MAAAN,QAAA,gBAEDrJ,OAAA;QAAKsJ,KAAK,EAAE;UAAE7C,OAAO,EAAE,MAAM;UAAEsD,QAAQ,EAAE,MAAM;UAAED,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAR,QAAA,EAC7EJ,iBAAiB,CAAC,CAAC,CAACtH,GAAG,CAACmC,WAAW,IAAI;UACtC,MAAMV,OAAO,GAAGvC,QAAQ,CAAC4J,IAAI,CAACzH,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKa,WAAW,CAAC;UAClE,IAAI,CAACV,OAAO,EAAE,OAAO,IAAI;UAEzB,oBACEpD,OAAA,CAACJ,iBAAiB;YAEhBwD,OAAO,EAAEA,OAAQ;YACjBsH,WAAW,EAAE,IAAK;YAClBC,OAAO,EAAE;UAAM,GAHV7G,WAAW;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIjB,CAAC;QAEN,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN3J,OAAA;QAAKsJ,KAAK,EAAE;UAAEkB,SAAS,EAAE,EAAE;UAAEpF,OAAO,EAAE,MAAM;UAAE/E,eAAe,EAAE,SAAS;UAAEwE,YAAY,EAAE;QAAM,CAAE;QAAAwE,QAAA,eAC9FrJ,OAAA;UAAKsJ,KAAK,EAAE;YAAE7C,OAAO,EAAE,MAAM;YAAEqD,GAAG,EAAE,EAAE;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACzDrJ,OAAA;YAAAqJ,QAAA,eACErJ,OAAA;cAAMsJ,KAAK,EAAE;gBAAE1F,KAAK,EAAE,SAAS;gBAAEgH,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,GAAC,4BAC/C,EAACxI,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAI;gBAC1B,MAAM6H,OAAO,GAAGhL,eAAe,CAACmD,CAAC,CAACC,YAAY,CAAC;gBAC/C,OAAO,CAAA4H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhH,MAAM,MAAK,SAAS,IAAIb,CAAC,CAACa,MAAM,KAAK,SAAS;cAChE,CAAC,CAAC,CAACnC,MAAM;YAAA;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3J,OAAA;YAAAqJ,QAAA,eACErJ,OAAA;cAAMsJ,KAAK,EAAE;gBAAE1F,KAAK,EAAE,SAAS;gBAAEgH,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,GAAC,4BAC/C,EAACxI,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAI;gBAC1B,MAAM6H,OAAO,GAAGhL,eAAe,CAACmD,CAAC,CAACC,YAAY,CAAC;gBAC/C,OAAO,CAAA4H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhH,MAAM,MAAK,SAAS,IAAIb,CAAC,CAACa,MAAM,KAAK,SAAS;cAChE,CAAC,CAAC,CAACnC,MAAM;YAAA;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3J,OAAA;YAAAqJ,QAAA,eACErJ,OAAA;cAAMsJ,KAAK,EAAE;gBAAE1F,KAAK,EAAE,SAAS;gBAAEgH,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,GAAC,4BAC/C,EAACxI,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAI;gBAC1B,MAAM6H,OAAO,GAAGhL,eAAe,CAACmD,CAAC,CAACC,YAAY,CAAC;gBAC/C,OAAO,CAAA4H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEhH,MAAM,MAAK,UAAU;cACvC,CAAC,CAAC,CAACnC,MAAM;YAAA;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3J,OAAA;YAAAqJ,QAAA,eACErJ,OAAA;cAAMsJ,KAAK,EAAE;gBAAE1F,KAAK,EAAE,SAAS;gBAAEgH,UAAU,EAAE;cAAO,CAAE;cAAAvB,QAAA,GAAC,4BAC/C,EAACxI,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC+B,UAAU,IAAI,GAAG,CAAC,CAACrD,MAAM;YAAA;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7I,EAAA,CAtfIH,gBAAgB;AAAAmK,EAAA,GAAhBnK,gBAAgB;AAwftB,eAAeA,gBAAgB;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}