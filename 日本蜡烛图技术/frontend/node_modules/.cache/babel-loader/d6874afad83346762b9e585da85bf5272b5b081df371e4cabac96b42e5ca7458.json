{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/src/components/CandlestickChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement);\nconst {\n  Option\n} = Select;\nconst CandlestickChart = ({\n  data,\n  patterns = []\n}) => {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    const filteredPatterns = selectedPattern === 'all' ? patterns : patterns.filter(p => p.pattern_name === selectedPattern);\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n      if (!startData || !endData) return null;\n      const color = getPatternColor(pattern.signal);\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = signal => {\n    switch (signal) {\n      case 'bullish':\n        return '#52c41a';\n      case 'bearish':\n        return '#ff4d4f';\n      case 'neutral':\n        return '#faad14';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: d.c\n            })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 蜡烛图配置（使用柱状图模拟）\n    return {\n      type: 'bar',\n      data: {\n        datasets: [{\n          label: '蜡烛图',\n          data: candleData.map(d => ({\n            x: d.x,\n            y: [d.l, d.o, d.c, d.h] // [low, open, close, high]\n          })),\n          backgroundColor: candleData.map(d => d.c >= d.o ? '#52c41a80' : '#ff4d4f80'),\n          borderColor: candleData.map(d => d.c >= d.o ? '#52c41a' : '#ff4d4f'),\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          x: {\n            type: 'category'\n          },\n          y: {\n            beginAtZero: false\n          }\n        },\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              title: context => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString() : `K线 ${dataIndex + 1}`;\n              },\n              label: context => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [`开盘: ${candle.o.toFixed(2)}`, `最高: ${candle.h.toFixed(2)}`, `最低: ${candle.l.toFixed(2)}`, `收盘: ${candle.c.toFixed(2)}`, `成交量: ${candle.v.toLocaleString()}`];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    chartInstance.current = new Chart(ctx, config);\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u6570\\u636E\\uFF0C\\u8BF7\\u5148\\u4E0A\\u4F20\\u8721\\u70DB\\u56FE\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 16,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u56FE\\u8868\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartType,\n            onChange: setChartType,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"candlestick\",\n              children: \"\\u8721\\u70DB\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"line\",\n              children: \"\\u6298\\u7EBF\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u663E\\u793A\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showPatterns,\n            onChange: setShowPatterns,\n            checkedChildren: \"\\u5F00\",\n            unCheckedChildren: \"\\u5173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), showPatterns && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u5F62\\u6001\\u8FC7\\u6EE4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedPattern,\n            onChange: setSelectedPattern,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u5168\\u90E8\\u5F62\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), getUniquePatterns().map(pattern => /*#__PURE__*/_jsxDEV(Option, {\n              value: pattern,\n              children: pattern\n            }, pattern, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: data.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u8BC6\\u522B\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: patterns.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '500px',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: chartRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), showPatterns && patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginRight: 16\n          },\n          children: \"\\u5F62\\u6001\\u56FE\\u4F8B:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), getUniquePatterns().map(patternName => {\n          const pattern = patterns.find(p => p.pattern_name === patternName);\n          if (!pattern) return null;\n          const color = getPatternColor(pattern.signal);\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: pattern.description,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange',\n              children: patternName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)\n          }, patternName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 17\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 226,\n    columnNumber: 5\n  }, this);\n};\n_s(CandlestickChart, \"llFm+VVjJ94NgTFKA+uyKbKbJEc=\");\n_c = CandlestickChart;\nexport default CandlestickChart;\nvar _c;\n$RefreshReg$(_c, \"CandlestickChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "Select", "Switch", "<PERSON><PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Chart", "registerables", "CandlestickController", "CandlestickElement", "OhlcController", "OhlcElement", "jsxDEV", "_jsxDEV", "register", "Option", "CandlestickChart", "data", "patterns", "_s", "chartRef", "chartInstance", "showPatterns", "setShowPatterns", "selectedPatt<PERSON>", "setSelectedPattern", "chartType", "setChartType", "processCandleData", "length", "map", "candle", "index", "x", "timestamp", "Date", "getTime", "o", "parseFloat", "open", "h", "high", "l", "low", "c", "close", "v", "volume", "processPatternAnnotations", "filteredPatterns", "filter", "p", "pattern_name", "pattern", "startIndex", "start_index", "endIndex", "end_index", "startData", "endData", "color", "getPatternColor", "signal", "type", "xMin", "xMax", "yMin", "Math", "min", "yMax", "max", "backgroundColor", "borderColor", "borderWidth", "label", "content", "confidence", "toFixed", "enabled", "position", "Boolean", "createChartConfig", "candleData", "annotations", "datasets", "d", "y", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "time", "unit", "beginAtZero", "plugins", "legend", "display", "annotation", "tooltip", "callbacks", "title", "context", "dataIndex", "toLocaleDateString", "toLocaleString", "current", "destroy", "ctx", "getContext", "config", "getUniquePatterns", "uniqueNames", "Set", "sort", "children", "style", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "marginBottom", "alignItems", "gap", "flexWrap", "marginRight", "value", "onChange", "width", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "height", "ref", "marginTop", "fontWeight", "patternName", "find", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement);\n\nconst { Option } = Select;\n\nconst CandlestickChart = ({ data, patterns = [] }) => {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    \n    const filteredPatterns = selectedPattern === 'all' \n      ? patterns \n      : patterns.filter(p => p.pattern_name === selectedPattern);\n\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n      \n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n\n      if (!startData || !endData) return null;\n\n      const color = getPatternColor(pattern.signal);\n\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = (signal) => {\n    switch (signal) {\n      case 'bullish': return '#52c41a';\n      case 'bearish': return '#ff4d4f';\n      case 'neutral': return '#faad14';\n      default: return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({ x: d.x, y: d.c })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 蜡烛图配置（使用柱状图模拟）\n    return {\n      type: 'bar',\n      data: {\n        datasets: [\n          {\n            label: '蜡烛图',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: [d.l, d.o, d.c, d.h] // [low, open, close, high]\n            })),\n            backgroundColor: candleData.map(d => d.c >= d.o ? '#52c41a80' : '#ff4d4f80'),\n            borderColor: candleData.map(d => d.c >= d.o ? '#52c41a' : '#ff4d4f'),\n            borderWidth: 1\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          x: {\n            type: 'category'\n          },\n          y: {\n            beginAtZero: false\n          }\n        },\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              title: (context) => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString() : `K线 ${dataIndex + 1}`;\n              },\n              label: (context) => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [\n                  `开盘: ${candle.o.toFixed(2)}`,\n                  `最高: ${candle.h.toFixed(2)}`,\n                  `最低: ${candle.l.toFixed(2)}`,\n                  `收盘: ${candle.c.toFixed(2)}`,\n                  `成交量: ${candle.v.toLocaleString()}`\n                ];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    \n    chartInstance.current = new Chart(ctx, config);\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n\n  if (!data || data.length === 0) {\n    return (\n      <Card>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <p>暂无数据，请先上传蜡烛图数据</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div>\n      {/* 控制面板 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>\n          <div>\n            <span style={{ marginRight: 8 }}>图表类型:</span>\n            <Select \n              value={chartType} \n              onChange={setChartType}\n              style={{ width: 120 }}\n            >\n              <Option value=\"candlestick\">蜡烛图</Option>\n              <Option value=\"line\">折线图</Option>\n            </Select>\n          </div>\n          \n          <div>\n            <span style={{ marginRight: 8 }}>显示形态:</span>\n            <Switch \n              checked={showPatterns} \n              onChange={setShowPatterns}\n              checkedChildren=\"开\"\n              unCheckedChildren=\"关\"\n            />\n          </div>\n          \n          {showPatterns && (\n            <div>\n              <span style={{ marginRight: 8 }}>形态过滤:</span>\n              <Select \n                value={selectedPattern} \n                onChange={setSelectedPattern}\n                style={{ width: 150 }}\n              >\n                <Option value=\"all\">全部形态</Option>\n                {getUniquePatterns().map(pattern => (\n                  <Option key={pattern} value={pattern}>{pattern}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n          \n          <div>\n            <span style={{ marginRight: 8 }}>数据点数:</span>\n            <Tag color=\"blue\">{data.length}</Tag>\n          </div>\n          \n          {patterns && patterns.length > 0 && (\n            <div>\n              <span style={{ marginRight: 8 }}>识别形态:</span>\n              <Tag color=\"green\">{patterns.length}</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 图表区域 */}\n      <Card>\n        <div style={{ height: '500px', position: 'relative' }}>\n          <canvas ref={chartRef} />\n        </div>\n      </Card>\n\n      {/* 形态图例 */}\n      {showPatterns && patterns && patterns.length > 0 && (\n        <Card size=\"small\" style={{ marginTop: 16 }}>\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>\n            <span style={{ fontWeight: 'bold', marginRight: 16 }}>形态图例:</span>\n            {getUniquePatterns().map(patternName => {\n              const pattern = patterns.find(p => p.pattern_name === patternName);\n              if (!pattern) return null;\n              \n              const color = getPatternColor(pattern.signal);\n              return (\n                <Tooltip key={patternName} title={pattern.description}>\n                  <Tag \n                    color={pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange'}\n                  >\n                    {patternName}\n                  </Tag>\n                </Tooltip>\n              );\n            })}\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default CandlestickChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AACjE,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AAChH,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAP,KAAK,CAACQ,QAAQ,CAAC,GAAGP,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,CAAC;AAExG,MAAM;EAAEI;AAAO,CAAC,GAAGd,MAAM;AAEzB,MAAMe,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGtB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMuB,aAAa,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,aAAa,CAAC;;EAEzD;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOZ,IAAI,CAACa,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;MAClCC,CAAC,EAAEF,MAAM,CAACG,SAAS,GAAG,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGJ,KAAK;MAClEK,CAAC,EAAEC,UAAU,CAACP,MAAM,CAACQ,IAAI,CAAC;MAC1BC,CAAC,EAAEF,UAAU,CAACP,MAAM,CAACU,IAAI,CAAC;MAC1BC,CAAC,EAAEJ,UAAU,CAACP,MAAM,CAACY,GAAG,CAAC;MACzBC,CAAC,EAAEN,UAAU,CAACP,MAAM,CAACc,KAAK,CAAC;MAC3BC,CAAC,EAAER,UAAU,CAACP,MAAM,CAACgB,MAAM,IAAI,IAAI,CAAC;MACpCb,SAAS,EAAEH,MAAM,CAACG;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMc,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC1B,YAAY,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAElE,MAAMoB,gBAAgB,GAAGzB,eAAe,KAAK,KAAK,GAC9CN,QAAQ,GACRA,QAAQ,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK5B,eAAe,CAAC;IAE5D,OAAOyB,gBAAgB,CAACnB,GAAG,CAAC,CAACuB,OAAO,EAAErB,KAAK,KAAK;MAC9C,MAAMsB,UAAU,GAAGD,OAAO,CAACE,WAAW,IAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGH,OAAO,CAACI,SAAS,IAAIH,UAAU;;MAEhD;MACA,MAAMI,SAAS,GAAGzC,IAAI,CAACqC,UAAU,CAAC;MAClC,MAAMK,OAAO,GAAG1C,IAAI,CAACuC,QAAQ,CAAC;MAE9B,IAAI,CAACE,SAAS,IAAI,CAACC,OAAO,EAAE,OAAO,IAAI;MAEvC,MAAMC,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;MAE7C,OAAO;QACLC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAEV,UAAU;QAChBW,IAAI,EAAET,QAAQ;QACdU,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACV,SAAS,CAACf,GAAG,EAAEgB,OAAO,CAAChB,GAAG,CAAC,GAAG,KAAK;QAClD0B,IAAI,EAAEF,IAAI,CAACG,GAAG,CAACZ,SAAS,CAACjB,IAAI,EAAEkB,OAAO,CAAClB,IAAI,CAAC,GAAG,KAAK;QACpD8B,eAAe,EAAEX,KAAK,GAAG,IAAI;QAC7BY,WAAW,EAAEZ,KAAK;QAClBa,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLC,OAAO,EAAE,GAAGtB,OAAO,CAACD,YAAY,KAAK,CAACC,OAAO,CAACuB,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;UAC9EC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE;QACZ;MACF,CAAC;IACH,CAAC,CAAC,CAAC7B,MAAM,CAAC8B,OAAO,CAAC;EACpB,CAAC;;EAED;EACA,MAAMnB,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAGtD,iBAAiB,CAAC,CAAC;IACtC,MAAMuD,WAAW,GAAGnC,yBAAyB,CAAC,CAAC;IAE/C,IAAItB,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO;QACLqC,IAAI,EAAE,MAAM;QACZ9C,IAAI,EAAE;UACJmE,QAAQ,EAAE,CAAC;YACTV,KAAK,EAAE,KAAK;YACZzD,IAAI,EAAEiE,UAAU,CAACpD,GAAG,CAACuD,CAAC,KAAK;cAAEpD,CAAC,EAAEoD,CAAC,CAACpD,CAAC;cAAEqD,CAAC,EAAED,CAAC,CAACzC;YAAE,CAAC,CAAC,CAAC;YAC/C4B,WAAW,EAAE,SAAS;YACtBD,eAAe,EAAE,WAAW;YAC5BgB,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;UACX,CAAC;QACH,CAAC;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACN3D,CAAC,EAAE;cACD8B,IAAI,EAAE,MAAM;cACZ8B,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF,CAAC;YACDR,CAAC,EAAE;cACDS,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;YACX,CAAC;YACDC,UAAU,EAAE;cACVhB,WAAW,EAAEA;YACf;UACF;QACF;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLpB,IAAI,EAAE,KAAK;MACX9C,IAAI,EAAE;QACJmE,QAAQ,EAAE,CACR;UACEV,KAAK,EAAE,KAAK;UACZzD,IAAI,EAAEiE,UAAU,CAACpD,GAAG,CAACuD,CAAC,KAAK;YACzBpD,CAAC,EAAEoD,CAAC,CAACpD,CAAC;YACNqD,CAAC,EAAE,CAACD,CAAC,CAAC3C,CAAC,EAAE2C,CAAC,CAAChD,CAAC,EAAEgD,CAAC,CAACzC,CAAC,EAAEyC,CAAC,CAAC7C,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC;UACH+B,eAAe,EAAEW,UAAU,CAACpD,GAAG,CAACuD,CAAC,IAAIA,CAAC,CAACzC,CAAC,IAAIyC,CAAC,CAAChD,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC;UAC5EmC,WAAW,EAAEU,UAAU,CAACpD,GAAG,CAACuD,CAAC,IAAIA,CAAC,CAACzC,CAAC,IAAIyC,CAAC,CAAChD,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;UACpEoC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;MACDgB,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;UACN3D,CAAC,EAAE;YACD8B,IAAI,EAAE;UACR,CAAC;UACDuB,CAAC,EAAE;YACDS,WAAW,EAAE;UACf;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;UACX,CAAC;UACDE,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAMzE,MAAM,GAAGmD,UAAU,CAACsB,SAAS,CAAC;gBACpC,OAAOzE,MAAM,CAACG,SAAS,GAAG,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACuE,kBAAkB,CAAC,CAAC,GAAG,MAAMD,SAAS,GAAG,CAAC,EAAE;cACnG,CAAC;cACD9B,KAAK,EAAG6B,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAACC,SAAS;gBACnC,MAAMzE,MAAM,GAAGmD,UAAU,CAACsB,SAAS,CAAC;gBACpC,OAAO,CACL,OAAOzE,MAAM,CAACM,CAAC,CAACwC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACS,CAAC,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACW,CAAC,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACa,CAAC,CAACiC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,QAAQ9C,MAAM,CAACe,CAAC,CAAC4D,cAAc,CAAC,CAAC,EAAE,CACpC;cACH;YACF;UACF,CAAC;UACDP,UAAU,EAAE;YACVhB,WAAW,EAAEA;UACf;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACAtF,SAAS,CAAC,MAAM;IACd,IAAI,CAACuB,QAAQ,CAACuF,OAAO,IAAI,CAAC1F,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;;IAErD;IACA,IAAIR,aAAa,CAACsF,OAAO,EAAE;MACzBtF,aAAa,CAACsF,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;;IAEA;IACA,MAAMC,GAAG,GAAGzF,QAAQ,CAACuF,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAG9B,iBAAiB,CAAC,CAAC;IAElC5D,aAAa,CAACsF,OAAO,GAAG,IAAIrG,KAAK,CAACuG,GAAG,EAAEE,MAAM,CAAC;IAE9C,OAAO,MAAM;MACX,IAAI1F,aAAa,CAACsF,OAAO,EAAE;QACzBtF,aAAa,CAACsF,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAAC3F,IAAI,EAAEC,QAAQ,EAAEI,YAAY,EAAEE,eAAe,EAAEE,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAMsF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC9F,QAAQ,IAAIA,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACjD,MAAMoF,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChG,QAAQ,CAACY,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACnE,OAAO6D,WAAW,CAACE,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED,IAAI,CAAClG,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACEhB,OAAA,CAACb,IAAI;MAAAoH,QAAA,eACHvG,OAAA;QAAKwG,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eACnDvG,OAAA;UAAAuG,QAAA,EAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE9G,OAAA;IAAAuG,QAAA,gBAEEvG,OAAA,CAACb,IAAI;MAAC4H,IAAI,EAAC,OAAO;MAACP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAT,QAAA,eAC7CvG,OAAA;QAAKwG,KAAK,EAAE;UAAEnB,OAAO,EAAE,MAAM;UAAE4B,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAC/EvG,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAMwG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9G,OAAA,CAACZ,MAAM;YACLiI,KAAK,EAAExG,SAAU;YACjByG,QAAQ,EAAExG,YAAa;YACvB0F,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAI,CAAE;YAAAhB,QAAA,gBAEtBvG,OAAA,CAACE,MAAM;cAACmH,KAAK,EAAC,aAAa;cAAAd,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9G,OAAA,CAACE,MAAM;cAACmH,KAAK,EAAC,MAAM;cAAAd,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9G,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAMwG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9G,OAAA,CAACX,MAAM;YACLmI,OAAO,EAAE/G,YAAa;YACtB6G,QAAQ,EAAE5G,eAAgB;YAC1B+G,eAAe,EAAC,QAAG;YACnBC,iBAAiB,EAAC;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELrG,YAAY,iBACXT,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAMwG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9G,OAAA,CAACZ,MAAM;YACLiI,KAAK,EAAE1G,eAAgB;YACvB2G,QAAQ,EAAE1G,kBAAmB;YAC7B4F,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAI,CAAE;YAAAhB,QAAA,gBAEtBvG,OAAA,CAACE,MAAM;cAACmH,KAAK,EAAC,KAAK;cAAAd,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCX,iBAAiB,CAAC,CAAC,CAAClF,GAAG,CAACuB,OAAO,iBAC9BxC,OAAA,CAACE,MAAM;cAAemH,KAAK,EAAE7E,OAAQ;cAAA+D,QAAA,EAAE/D;YAAO,GAAjCA,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAED9G,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAMwG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9G,OAAA,CAACT,GAAG;YAACwD,KAAK,EAAC,MAAM;YAAAwD,QAAA,EAAEnG,IAAI,CAACY;UAAM;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAELzG,QAAQ,IAAIA,QAAQ,CAACW,MAAM,GAAG,CAAC,iBAC9BhB,OAAA;UAAAuG,QAAA,gBACEvG,OAAA;YAAMwG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9G,OAAA,CAACT,GAAG;YAACwD,KAAK,EAAC,OAAO;YAAAwD,QAAA,EAAElG,QAAQ,CAACW;UAAM;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP9G,OAAA,CAACb,IAAI;MAAAoH,QAAA,eACHvG,OAAA;QAAKwG,KAAK,EAAE;UAAEmB,MAAM,EAAE,OAAO;UAAEzD,QAAQ,EAAE;QAAW,CAAE;QAAAqC,QAAA,eACpDvG,OAAA;UAAQ4H,GAAG,EAAErH;QAAS;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNrG,YAAY,IAAIJ,QAAQ,IAAIA,QAAQ,CAACW,MAAM,GAAG,CAAC,iBAC9ChB,OAAA,CAACb,IAAI;MAAC4H,IAAI,EAAC,OAAO;MAACP,KAAK,EAAE;QAAEqB,SAAS,EAAE;MAAG,CAAE;MAAAtB,QAAA,eAC1CvG,OAAA;QAAKwG,KAAK,EAAE;UAAEnB,OAAO,EAAE,MAAM;UAAE8B,QAAQ,EAAE,MAAM;UAAED,GAAG,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACxDvG,OAAA;UAAMwG,KAAK,EAAE;YAAEsB,UAAU,EAAE,MAAM;YAAEV,WAAW,EAAE;UAAG,CAAE;UAAAb,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjEX,iBAAiB,CAAC,CAAC,CAAClF,GAAG,CAAC8G,WAAW,IAAI;UACtC,MAAMvF,OAAO,GAAGnC,QAAQ,CAAC2H,IAAI,CAAC1F,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKwF,WAAW,CAAC;UAClE,IAAI,CAACvF,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMO,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;UAC7C,oBACEjD,OAAA,CAACV,OAAO;YAAmBmG,KAAK,EAAEjD,OAAO,CAACyF,WAAY;YAAA1B,QAAA,eACpDvG,OAAA,CAACT,GAAG;cACFwD,KAAK,EAAEP,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,OAAO,GAAGT,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;cAAAsD,QAAA,EAE/FwB;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GALMiB,WAAW;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMhB,CAAC;QAEd,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxG,EAAA,CA9SIH,gBAAgB;AAAA+H,EAAA,GAAhB/H,gBAAgB;AAgTtB,eAAeA,gBAAgB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}