{"ast": null, "code": "export var VIEW_BOX_SIZE = 100;\nexport var getCircleStyle = function getCircleStyle(perimeter, perimeterWithoutGap, offset, percent, rotateDeg, gapDegree, gapPosition, strokeColor, strokeLinecap, strokeWidth) {\n  var stepSpace = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : 0;\n  var offsetDeg = offset / 100 * 360 * ((360 - gapDegree) / 360);\n  var positionDeg = gapDegree === 0 ? 0 : {\n    bottom: 0,\n    top: 180,\n    left: 90,\n    right: -90\n  }[gapPosition];\n  var strokeDashoffset = (100 - percent) / 100 * perimeterWithoutGap;\n  // Fix percent accuracy when strokeLinecap is round\n  // https://github.com/ant-design/ant-design/issues/35009\n  if (strokeLinecap === 'round' && percent !== 100) {\n    strokeDashoffset += strokeWidth / 2;\n    // when percent is small enough (<= 1%), keep smallest value to avoid it's disappearance\n    if (strokeDashoffset >= perimeterWithoutGap) {\n      strokeDashoffset = perimeterWithoutGap - 0.01;\n    }\n  }\n  var halfSize = VIEW_BOX_SIZE / 2;\n  return {\n    stroke: typeof strokeColor === 'string' ? strokeColor : undefined,\n    strokeDasharray: \"\".concat(perimeterWithoutGap, \"px \").concat(perimeter),\n    strokeDashoffset: strokeDashoffset + stepSpace,\n    transform: \"rotate(\".concat(rotateDeg + offsetDeg + positionDeg, \"deg)\"),\n    transformOrigin: \"\".concat(halfSize, \"px \").concat(halfSize, \"px\"),\n    transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s',\n    fillOpacity: 0\n  };\n};", "map": {"version": 3, "names": ["VIEW_BOX_SIZE", "getCircleStyle", "perimeter", "perimeterWithoutGap", "offset", "percent", "rotateDeg", "gapDegree", "gapPosition", "strokeColor", "strokeLinecap", "strokeWidth", "stepSpace", "arguments", "length", "undefined", "offsetDeg", "positionDeg", "bottom", "top", "left", "right", "strokeDashoffset", "halfSize", "stroke", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "concat", "transform", "transform<PERSON><PERSON>in", "transition", "fillOpacity"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/rc-progress/es/Circle/util.js"], "sourcesContent": ["export var VIEW_BOX_SIZE = 100;\nexport var getCircleStyle = function getCircleStyle(perimeter, perimeterWithoutGap, offset, percent, rotateDeg, gapDegree, gapPosition, strokeColor, strokeLinecap, strokeWidth) {\n  var stepSpace = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : 0;\n  var offsetDeg = offset / 100 * 360 * ((360 - gapDegree) / 360);\n  var positionDeg = gapDegree === 0 ? 0 : {\n    bottom: 0,\n    top: 180,\n    left: 90,\n    right: -90\n  }[gapPosition];\n  var strokeDashoffset = (100 - percent) / 100 * perimeterWithoutGap;\n  // Fix percent accuracy when strokeLinecap is round\n  // https://github.com/ant-design/ant-design/issues/35009\n  if (strokeLinecap === 'round' && percent !== 100) {\n    strokeDashoffset += strokeWidth / 2;\n    // when percent is small enough (<= 1%), keep smallest value to avoid it's disappearance\n    if (strokeDashoffset >= perimeterWithoutGap) {\n      strokeDashoffset = perimeterWithoutGap - 0.01;\n    }\n  }\n  var halfSize = VIEW_BOX_SIZE / 2;\n  return {\n    stroke: typeof strokeColor === 'string' ? strokeColor : undefined,\n    strokeDasharray: \"\".concat(perimeterWithoutGap, \"px \").concat(perimeter),\n    strokeDashoffset: strokeDashoffset + stepSpace,\n    transform: \"rotate(\".concat(rotateDeg + offsetDeg + positionDeg, \"deg)\"),\n    transformOrigin: \"\".concat(halfSize, \"px \").concat(halfSize, \"px\"),\n    transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s',\n    fillOpacity: 0\n  };\n};"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG,GAAG;AAC9B,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,EAAE;EAC/K,IAAIC,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,EAAE,IAAID,SAAS,CAAC,EAAE,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC;EACxF,IAAIG,SAAS,GAAGZ,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,GAAGG,SAAS,IAAI,GAAG,CAAC;EAC9D,IAAIU,WAAW,GAAGV,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG;IACtCW,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,GAAG;IACRC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,CAAC;EACV,CAAC,CAACb,WAAW,CAAC;EACd,IAAIc,gBAAgB,GAAG,CAAC,GAAG,GAAGjB,OAAO,IAAI,GAAG,GAAGF,mBAAmB;EAClE;EACA;EACA,IAAIO,aAAa,KAAK,OAAO,IAAIL,OAAO,KAAK,GAAG,EAAE;IAChDiB,gBAAgB,IAAIX,WAAW,GAAG,CAAC;IACnC;IACA,IAAIW,gBAAgB,IAAInB,mBAAmB,EAAE;MAC3CmB,gBAAgB,GAAGnB,mBAAmB,GAAG,IAAI;IAC/C;EACF;EACA,IAAIoB,QAAQ,GAAGvB,aAAa,GAAG,CAAC;EAChC,OAAO;IACLwB,MAAM,EAAE,OAAOf,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGM,SAAS;IACjEU,eAAe,EAAE,EAAE,CAACC,MAAM,CAACvB,mBAAmB,EAAE,KAAK,CAAC,CAACuB,MAAM,CAACxB,SAAS,CAAC;IACxEoB,gBAAgB,EAAEA,gBAAgB,GAAGV,SAAS;IAC9Ce,SAAS,EAAE,SAAS,CAACD,MAAM,CAACpB,SAAS,GAAGU,SAAS,GAAGC,WAAW,EAAE,MAAM,CAAC;IACxEW,eAAe,EAAE,EAAE,CAACF,MAAM,CAACH,QAAQ,EAAE,KAAK,CAAC,CAACG,MAAM,CAACH,QAAQ,EAAE,IAAI,CAAC;IAClEM,UAAU,EAAE,0HAA0H;IACtIC,WAAW,EAAE;EACf,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}