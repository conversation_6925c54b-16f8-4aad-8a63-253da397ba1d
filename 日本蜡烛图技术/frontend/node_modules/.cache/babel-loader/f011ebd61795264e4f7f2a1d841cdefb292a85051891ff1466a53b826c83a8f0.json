{"ast": null, "code": "import axios from'axios';// API基础配置\nconst API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:3000/api/v1';const api=axios.create({baseURL:API_BASE_URL,timeout:30000,headers:{'Content-Type':'application/json'}});// 请求拦截器\napi.interceptors.request.use(config=>{var _config$method;console.log('API Request:',(_config$method=config.method)===null||_config$method===void 0?void 0:_config$method.toUpperCase(),config.url);return config;},error=>{return Promise.reject(error);});// 响应拦截器\napi.interceptors.response.use(response=>{console.log('API Response:',response.status,response.config.url);return response;},error=>{var _error$response,_error$response2;console.error('API Error:',(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.data);return Promise.reject(error);});/**\n * 分析蜡烛图形态\n * @param {Array} candles - 蜡烛线数据数组\n * @param {number} startIndex - 开始分析的索引\n * @param {number} endIndex - 结束分析的索引\n * @returns {Promise} 分析结果\n */export const analyzePatterns=async function(candles){let startIndex=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;let endIndex=arguments.length>2&&arguments[2]!==undefined?arguments[2]:null;try{const response=await api.post('/patterns/analyze',{candles:candles.map(candle=>({open:parseFloat(candle.open),high:parseFloat(candle.high),low:parseFloat(candle.low),close:parseFloat(candle.close),volume:parseFloat(candle.volume||1000),timestamp:candle.timestamp||new Date().toISOString()})),start_index:startIndex,end_index:endIndex});return response.data;}catch(error){var _error$response3,_error$response3$data;throw new Error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.detail)||'形态分析失败');}};/**\n * 获取支持的形态列表\n * @returns {Promise} 支持的形态列表\n */export const getSupportedPatterns=async()=>{try{const response=await api.get('/patterns/list');return response.data;}catch(error){var _error$response4,_error$response4$data;throw new Error(((_error$response4=error.response)===null||_error$response4===void 0?void 0:(_error$response4$data=_error$response4.data)===null||_error$response4$data===void 0?void 0:_error$response4$data.detail)||'获取形态列表失败');}};/**\n * 带过滤条件的形态分析\n * @param {Array} candles - 蜡烛线数据数组\n * @param {Object} filters - 过滤条件\n * @returns {Promise} 过滤后的分析结果\n */export const analyzeWithFilters=async function(candles){let filters=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{const params=new URLSearchParams();if(filters.minConfidence!==undefined){params.append('min_confidence',filters.minConfidence);}if(filters.patternTypes&&filters.patternTypes.length>0){filters.patternTypes.forEach(type=>{params.append('pattern_types',type);});}if(filters.signals&&filters.signals.length>0){filters.signals.forEach(signal=>{params.append('signals',signal);});}const response=await api.post(\"/patterns/filter?\".concat(params.toString()),{candles:candles.map(candle=>({open:parseFloat(candle.open),high:parseFloat(candle.high),low:parseFloat(candle.low),close:parseFloat(candle.close),volume:parseFloat(candle.volume||1000),timestamp:candle.timestamp||new Date().toISOString()}))});return response.data;}catch(error){var _error$response5,_error$response5$data;throw new Error(((_error$response5=error.response)===null||_error$response5===void 0?void 0:(_error$response5$data=_error$response5.data)===null||_error$response5$data===void 0?void 0:_error$response5$data.detail)||'过滤分析失败');}};/**\n * 获取形态统计信息\n * @returns {Promise} 统计信息\n */export const getPatternStatistics=async()=>{try{const response=await api.get('/patterns/statistics');return response.data;}catch(error){var _error$response6,_error$response6$data;throw new Error(((_error$response6=error.response)===null||_error$response6===void 0?void 0:(_error$response6$data=_error$response6.data)===null||_error$response6$data===void 0?void 0:_error$response6$data.detail)||'获取统计信息失败');}};/**\n * 健康检查\n * @returns {Promise} 健康状态\n */export const healthCheck=async()=>{try{const response=await api.get('/health');return response.data;}catch(error){throw new Error('服务不可用');}};export default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "_error$response", "_error$response2", "data", "analyzePatterns", "candles", "startIndex", "arguments", "length", "undefined", "endIndex", "post", "map", "candle", "open", "parseFloat", "high", "low", "close", "volume", "timestamp", "Date", "toISOString", "start_index", "end_index", "_error$response3", "_error$response3$data", "Error", "detail", "getSupportedPatterns", "get", "_error$response4", "_error$response4$data", "analyzeWithFilters", "filters", "params", "URLSearchParams", "minConfidence", "append", "patternTypes", "for<PERSON>ach", "type", "signals", "signal", "concat", "toString", "_error$response5", "_error$response5$data", "getPatternStatistics", "_error$response6", "_error$response6$data", "healthCheck"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// API基础配置\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    console.log('API Request:', config.method?.toUpperCase(), config.url);\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    console.log('API Response:', response.status, response.config.url);\n    return response;\n  },\n  (error) => {\n    console.error('API Error:', error.response?.status, error.response?.data);\n    return Promise.reject(error);\n  }\n);\n\n/**\n * 分析蜡烛图形态\n * @param {Array} candles - 蜡烛线数据数组\n * @param {number} startIndex - 开始分析的索引\n * @param {number} endIndex - 结束分析的索引\n * @returns {Promise} 分析结果\n */\nexport const analyzePatterns = async (candles, startIndex = 0, endIndex = null) => {\n  try {\n    const response = await api.post('/patterns/analyze', {\n      candles: candles.map(candle => ({\n        open: parseFloat(candle.open),\n        high: parseFloat(candle.high),\n        low: parseFloat(candle.low),\n        close: parseFloat(candle.close),\n        volume: parseFloat(candle.volume || 1000),\n        timestamp: candle.timestamp || new Date().toISOString(),\n      })),\n      start_index: startIndex,\n      end_index: endIndex,\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw new Error(error.response?.data?.detail || '形态分析失败');\n  }\n};\n\n/**\n * 获取支持的形态列表\n * @returns {Promise} 支持的形态列表\n */\nexport const getSupportedPatterns = async () => {\n  try {\n    const response = await api.get('/patterns/list');\n    return response.data;\n  } catch (error) {\n    throw new Error(error.response?.data?.detail || '获取形态列表失败');\n  }\n};\n\n/**\n * 带过滤条件的形态分析\n * @param {Array} candles - 蜡烛线数据数组\n * @param {Object} filters - 过滤条件\n * @returns {Promise} 过滤后的分析结果\n */\nexport const analyzeWithFilters = async (candles, filters = {}) => {\n  try {\n    const params = new URLSearchParams();\n    \n    if (filters.minConfidence !== undefined) {\n      params.append('min_confidence', filters.minConfidence);\n    }\n    \n    if (filters.patternTypes && filters.patternTypes.length > 0) {\n      filters.patternTypes.forEach(type => {\n        params.append('pattern_types', type);\n      });\n    }\n    \n    if (filters.signals && filters.signals.length > 0) {\n      filters.signals.forEach(signal => {\n        params.append('signals', signal);\n      });\n    }\n    \n    const response = await api.post(`/patterns/filter?${params.toString()}`, {\n      candles: candles.map(candle => ({\n        open: parseFloat(candle.open),\n        high: parseFloat(candle.high),\n        low: parseFloat(candle.low),\n        close: parseFloat(candle.close),\n        volume: parseFloat(candle.volume || 1000),\n        timestamp: candle.timestamp || new Date().toISOString(),\n      })),\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw new Error(error.response?.data?.detail || '过滤分析失败');\n  }\n};\n\n/**\n * 获取形态统计信息\n * @returns {Promise} 统计信息\n */\nexport const getPatternStatistics = async () => {\n  try {\n    const response = await api.get('/patterns/statistics');\n    return response.data;\n  } catch (error) {\n    throw new Error(error.response?.data?.detail || '获取统计信息失败');\n  }\n};\n\n/**\n * 健康检查\n * @returns {Promise} 健康状态\n */\nexport const healthCheck = async () => {\n  try {\n    const response = await api.get('/health');\n    return response.data;\n  } catch (error) {\n    throw new Error('服务不可用');\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AACA,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,8BAA8B,CAEpF,KAAM,CAAAC,GAAG,CAAGL,KAAK,CAACM,MAAM,CAAC,CACvBC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,EAAK,KAAAC,cAAA,CACVC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAAF,cAAA,CAAED,MAAM,CAACI,MAAM,UAAAH,cAAA,iBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAAEL,MAAM,CAACM,GAAG,CAAC,CACrE,MAAO,CAAAN,MAAM,CACf,CAAC,CACAO,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAf,GAAG,CAACK,YAAY,CAACa,QAAQ,CAACX,GAAG,CAC1BW,QAAQ,EAAK,CACZR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEO,QAAQ,CAACC,MAAM,CAAED,QAAQ,CAACV,MAAM,CAACM,GAAG,CAAC,CAClE,MAAO,CAAAI,QAAQ,CACjB,CAAC,CACAH,KAAK,EAAK,KAAAK,eAAA,CAAAC,gBAAA,CACTX,OAAO,CAACK,KAAK,CAAC,YAAY,EAAAK,eAAA,CAAEL,KAAK,CAACG,QAAQ,UAAAE,eAAA,iBAAdA,eAAA,CAAgBD,MAAM,EAAAE,gBAAA,CAAEN,KAAK,CAACG,QAAQ,UAAAG,gBAAA,iBAAdA,gBAAA,CAAgBC,IAAI,CAAC,CACzE,MAAO,CAAAN,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,eAAe,CAAG,cAAAA,CAAOC,OAAO,CAAsC,IAApC,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,QAAQ,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC5E,GAAI,CACF,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAlB,GAAG,CAAC8B,IAAI,CAAC,mBAAmB,CAAE,CACnDN,OAAO,CAAEA,OAAO,CAACO,GAAG,CAACC,MAAM,GAAK,CAC9BC,IAAI,CAAEC,UAAU,CAACF,MAAM,CAACC,IAAI,CAAC,CAC7BE,IAAI,CAAED,UAAU,CAACF,MAAM,CAACG,IAAI,CAAC,CAC7BC,GAAG,CAAEF,UAAU,CAACF,MAAM,CAACI,GAAG,CAAC,CAC3BC,KAAK,CAAEH,UAAU,CAACF,MAAM,CAACK,KAAK,CAAC,CAC/BC,MAAM,CAAEJ,UAAU,CAACF,MAAM,CAACM,MAAM,EAAI,IAAI,CAAC,CACzCC,SAAS,CAAEP,MAAM,CAACO,SAAS,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACxD,CAAC,CAAC,CAAC,CACHC,WAAW,CAAEjB,UAAU,CACvBkB,SAAS,CAAEd,QACb,CAAC,CAAC,CAEF,MAAO,CAAAX,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,KAAA6B,gBAAA,CAAAC,qBAAA,CACd,KAAM,IAAI,CAAAC,KAAK,CAAC,EAAAF,gBAAA,CAAA7B,KAAK,CAACG,QAAQ,UAAA0B,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBtB,IAAI,UAAAuB,qBAAA,iBAApBA,qBAAA,CAAsBE,MAAM,GAAI,QAAQ,CAAC,CAC3D,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CAC9C,GAAI,CACF,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAAlB,GAAG,CAACiD,GAAG,CAAC,gBAAgB,CAAC,CAChD,MAAO,CAAA/B,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,KAAAmC,gBAAA,CAAAC,qBAAA,CACd,KAAM,IAAI,CAAAL,KAAK,CAAC,EAAAI,gBAAA,CAAAnC,KAAK,CAACG,QAAQ,UAAAgC,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB5B,IAAI,UAAA6B,qBAAA,iBAApBA,qBAAA,CAAsBJ,MAAM,GAAI,UAAU,CAAC,CAC7D,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAK,kBAAkB,CAAG,cAAAA,CAAO5B,OAAO,CAAmB,IAAjB,CAAA6B,OAAO,CAAA3B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAC5D,GAAI,CACF,KAAM,CAAA4B,MAAM,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAEpC,GAAIF,OAAO,CAACG,aAAa,GAAK5B,SAAS,CAAE,CACvC0B,MAAM,CAACG,MAAM,CAAC,gBAAgB,CAAEJ,OAAO,CAACG,aAAa,CAAC,CACxD,CAEA,GAAIH,OAAO,CAACK,YAAY,EAAIL,OAAO,CAACK,YAAY,CAAC/B,MAAM,CAAG,CAAC,CAAE,CAC3D0B,OAAO,CAACK,YAAY,CAACC,OAAO,CAACC,IAAI,EAAI,CACnCN,MAAM,CAACG,MAAM,CAAC,eAAe,CAAEG,IAAI,CAAC,CACtC,CAAC,CAAC,CACJ,CAEA,GAAIP,OAAO,CAACQ,OAAO,EAAIR,OAAO,CAACQ,OAAO,CAAClC,MAAM,CAAG,CAAC,CAAE,CACjD0B,OAAO,CAACQ,OAAO,CAACF,OAAO,CAACG,MAAM,EAAI,CAChCR,MAAM,CAACG,MAAM,CAAC,SAAS,CAAEK,MAAM,CAAC,CAClC,CAAC,CAAC,CACJ,CAEA,KAAM,CAAA5C,QAAQ,CAAG,KAAM,CAAAlB,GAAG,CAAC8B,IAAI,qBAAAiC,MAAA,CAAqBT,MAAM,CAACU,QAAQ,CAAC,CAAC,EAAI,CACvExC,OAAO,CAAEA,OAAO,CAACO,GAAG,CAACC,MAAM,GAAK,CAC9BC,IAAI,CAAEC,UAAU,CAACF,MAAM,CAACC,IAAI,CAAC,CAC7BE,IAAI,CAAED,UAAU,CAACF,MAAM,CAACG,IAAI,CAAC,CAC7BC,GAAG,CAAEF,UAAU,CAACF,MAAM,CAACI,GAAG,CAAC,CAC3BC,KAAK,CAAEH,UAAU,CAACF,MAAM,CAACK,KAAK,CAAC,CAC/BC,MAAM,CAAEJ,UAAU,CAACF,MAAM,CAACM,MAAM,EAAI,IAAI,CAAC,CACzCC,SAAS,CAAEP,MAAM,CAACO,SAAS,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACxD,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAvB,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,KAAAkD,gBAAA,CAAAC,qBAAA,CACd,KAAM,IAAI,CAAApB,KAAK,CAAC,EAAAmB,gBAAA,CAAAlD,KAAK,CAACG,QAAQ,UAAA+C,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3C,IAAI,UAAA4C,qBAAA,iBAApBA,qBAAA,CAAsBnB,MAAM,GAAI,QAAQ,CAAC,CAC3D,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAoB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CAC9C,GAAI,CACF,KAAM,CAAAjD,QAAQ,CAAG,KAAM,CAAAlB,GAAG,CAACiD,GAAG,CAAC,sBAAsB,CAAC,CACtD,MAAO,CAAA/B,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,KAAAqD,gBAAA,CAAAC,qBAAA,CACd,KAAM,IAAI,CAAAvB,KAAK,CAAC,EAAAsB,gBAAA,CAAArD,KAAK,CAACG,QAAQ,UAAAkD,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB9C,IAAI,UAAA+C,qBAAA,iBAApBA,qBAAA,CAAsBtB,MAAM,GAAI,UAAU,CAAC,CAC7D,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAuB,WAAW,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF,KAAM,CAAApD,QAAQ,CAAG,KAAM,CAAAlB,GAAG,CAACiD,GAAG,CAAC,SAAS,CAAC,CACzC,MAAO,CAAA/B,QAAQ,CAACI,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACd,KAAM,IAAI,CAAA+B,KAAK,CAAC,OAAO,CAAC,CAC1B,CACF,CAAC,CAED,cAAe,CAAA9C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}