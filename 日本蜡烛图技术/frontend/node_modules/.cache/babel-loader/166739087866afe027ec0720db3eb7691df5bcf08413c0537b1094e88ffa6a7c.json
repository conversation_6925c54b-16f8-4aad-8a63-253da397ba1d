{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst VenetianMask = createLucideIcon(\"VenetianMask\", [[\"path\", {\n  d: \"M2 12a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V7h-5a8 8 0 0 0-5 2 8 8 0 0 0-5-2H2Z\",\n  key: \"1g6z3j\"\n}], [\"path\", {\n  d: \"M6 11c1.5 0 3 .5 3 2-2 0-3 0-3-2Z\",\n  key: \"c2lwnf\"\n}], [\"path\", {\n  d: \"M18 11c-1.5 0-3 .5-3 2 2 0 3 0 3-2Z\",\n  key: \"njd9zo\"\n}]]);\nexport { VenetianMask as default };", "map": {"version": 3, "names": ["VenetianMask", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/lucide-react/src/icons/venetian-mask.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name VenetianMask\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMmE1IDUgMCAwIDAgNSA1IDggOCAwIDAgMSA1IDIgOCA4IDAgMCAxIDUtMiA1IDUgMCAwIDAgNS01VjdoLTVhOCA4IDAgMCAwLTUgMiA4IDggMCAwIDAtNS0ySDJaIiAvPgogIDxwYXRoIGQ9Ik02IDExYzEuNSAwIDMgLjUgMyAyLTIgMC0zIDAtMy0yWiIgLz4KICA8cGF0aCBkPSJNMTggMTFjLTEuNSAwLTMgLjUtMyAyIDIgMCAzIDAgMy0yWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/venetian-mask\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VenetianMask = createLucideIcon('VenetianMask', [\n  [\n    'path',\n    {\n      d: 'M2 12a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V7h-5a8 8 0 0 0-5 2 8 8 0 0 0-5-2H2Z',\n      key: '1g6z3j',\n    },\n  ],\n  ['path', { d: 'M6 11c1.5 0 3 .5 3 2-2 0-3 0-3-2Z', key: 'c2lwnf' }],\n  ['path', { d: 'M18 11c-1.5 0-3 .5-3 2 2 0 3 0 3-2Z', key: 'njd9zo' }],\n]);\n\nexport default VenetianMask;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,EACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}