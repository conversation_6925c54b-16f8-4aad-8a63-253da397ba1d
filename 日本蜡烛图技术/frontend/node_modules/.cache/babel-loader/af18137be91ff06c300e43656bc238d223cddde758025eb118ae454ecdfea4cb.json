{"ast": null, "code": "import _objectSpread from \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"color\", \"size\", \"strokeWidth\", \"absoluteStrokeWidth\", \"className\", \"children\"];\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport { forwardRef, createElement } from 'react';\nimport defaultAttributes from './defaultAttributes.js';\nconst toKebabCase = string => string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase().trim();\nconst createLucideIcon = (iconName, iconNode) => {\n  const Component = forwardRef((_ref, ref) => {\n    let {\n        color = \"currentColor\",\n        size = 24,\n        strokeWidth = 2,\n        absoluteStrokeWidth,\n        className = \"\",\n        children\n      } = _ref,\n      rest = _objectWithoutProperties(_ref, _excluded);\n    return createElement(\"svg\", _objectSpread(_objectSpread({\n      ref\n    }, defaultAttributes), {}, {\n      width: size,\n      height: size,\n      stroke: color,\n      strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      className: [\"lucide\", \"lucide-\".concat(toKebabCase(iconName)), className].join(\" \")\n    }, rest), [...iconNode.map(_ref2 => {\n      let [tag, attrs] = _ref2;\n      return createElement(tag, attrs);\n    }), ...(Array.isArray(children) ? children : [children])]);\n  });\n  Component.displayName = \"\".concat(iconName);\n  return Component;\n};\nexport { createLucideIcon as default, toKebabCase };", "map": {"version": 3, "names": ["toKebabCase", "string", "replace", "toLowerCase", "trim", "createLucideIcon", "iconName", "iconNode", "Component", "forwardRef", "_ref", "ref", "color", "size", "strokeWidth", "absoluteStrokeWidth", "className", "children", "rest", "_objectWithoutProperties", "_excluded", "createElement", "_objectSpread", "defaultAttributes", "width", "height", "stroke", "Number", "concat", "join", "map", "_ref2", "tag", "attrs", "Array", "isArray", "displayName"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import {\n  forwardRef,\n  createElement,\n  ReactSVG,\n  SVGProps,\n  ForwardRefExoticComponent,\n  RefAttributes,\n} from 'react';\nimport defaultAttributes from './defaultAttributes';\n\nexport type IconNode = [elementName: keyof ReactSVG, attrs: Record<string, string>][];\n\nexport type SVGAttributes = Partial<SVGProps<SVGSVGElement>>;\ntype ComponentAttributes = RefAttributes<SVGSVGElement> & SVGAttributes;\n\nexport interface LucideProps extends ComponentAttributes {\n  size?: string | number;\n  absoluteStrokeWidth?: boolean;\n}\n\nexport type LucideIcon = ForwardRefExoticComponent<LucideProps>;\n/**\n * Converts string to KebabCase\n * Copied from scripts/helper. If anyone knows how to properly import it here\n * then please fix it.\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string\n    .replace(/([a-z0-9])([A-Z])/g, '$1-$2')\n    .toLowerCase()\n    .trim();\n\nconst createLucideIcon = (iconName: string, iconNode: IconNode): LucideIcon => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(\n    ({ color = 'currentColor', size = 24, strokeWidth = 2, absoluteStrokeWidth, className = '', children, ...rest }, ref) =>\n      createElement(\n        'svg',\n        {\n          ref,\n          ...defaultAttributes,\n          width: size,\n          height: size,\n          stroke: color,\n          strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n          className: ['lucide', `lucide-${toKebabCase(iconName)}`, className].join(' '),\n          ...rest,\n        },\n        [\n          ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n          ...(Array.isArray(children) ? children : [children]),\n        ]\n      )\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "mappings": ";;;;;;;;;;;;AA6Ba,MAAAA,WAAA,GAAeC,MAAA,IAC1BA,MACG,CAAAC,OAAA,CAAQ,sBAAsB,OAAO,EACrCC,WAAY,GACZC,IAAK;AAEJ,MAAAC,gBAAA,GAAmBA,CAACC,QAAA,EAAkBC,QAAmC;EAC7E,MAAMC,SAAY,GAAAC,UAAA,CAChB,CAAAC,IAAA,EAAiHC,GAC/G;IAAA,IADD;QAAEC,KAAQ;QAAgBC,IAAA,GAAO,EAAI;QAAAC,WAAA,GAAc,CAAG;QAAAC,mBAAA;QAAqBC,SAAY;QAAIC;MAAa,IAAAP,IAAA;MAAAQ,IAAA,GAAAC,wBAAA,CAAAT,IAAA,EAAAU,SAAA;IAAA,OACvGC,aAAA,CACE,OAAAC,aAAA,CAAAA,aAAA;MAEEX;IAAA,GACGY,iBAAA;MACHC,KAAO,EAAAX,IAAA;MACPY,MAAQ,EAAAZ,IAAA;MACRa,MAAQ,EAAAd,KAAA;MACRE,WAAA,EAAaC,mBAAA,GAAsBY,MAAO,CAAAb,WAAW,IAAI,EAAK,GAAAa,MAAA,CAAOd,IAAI,CAAI,GAAAC,WAAA;MAC7EE,SAAA,EAAW,CAAC,oBAAAY,MAAA,CAAoB5B,WAAA,CAAYM,QAAQ,CAAK,GAAAU,SAAS,CAAE,CAAAa,IAAA,CAAK,GAAG;IAAA,GACzEX,IAAA,GAEL,CACE,GAAGX,QAAS,CAAAuB,GAAA,CAAIC,KAAA;MAAA,IAAC,CAACC,GAAK,EAAAC,KAAK,CAAM,GAAAF,KAAA;MAAA,OAAAV,aAAA,CAAcW,GAAK,EAAAC,KAAK,CAAC;IAAA,IAC3D,IAAIC,KAAM,CAAAC,OAAA,CAAQlB,QAAQ,CAAI,GAAAA,QAAA,GAAW,CAACA,QAAQ,GAEtD;EAAA,EACJ;EAEAT,SAAA,CAAU4B,WAAA,MAAAR,MAAA,CAAiBtB,QAAA;EAEpB,OAAAE,SAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}