{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Row,Col,Badge,Timeline,Button,Spin,Alert,Tabs,Progress}from'antd';import{RobotOutlined,MessageOutlined,BarChartOutlined,ClockCircleOutlined,CheckCircleOutlined,ExclamationCircleOutlined,SyncOutlined}from'@ant-design/icons';import'./AgentWorkspace.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{TabPane}=Tabs;const AgentWorkspace=()=>{var _llmStats$totalTokens,_llmStats$totalCost;const[agents,setAgents]=useState([]);const[messages,setMessages]=useState([]);const[workflows,setWorkflows]=useState([]);const[llmStats,setLlmStats]=useState({});const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// 模拟数据（实际应该从API获取）\nuseEffect(()=>{// 只在组件首次挂载时加载数据\nif(agents.length===0){loadMockData();}// 完全移除自动刷新功能\n// 用户可以通过手动刷新按钮更新数据\n},[]);// 空依赖数组确保只执行一次\nconst loadMockData=()=>{setLoading(true);// 模拟API调用延迟\nsetTimeout(()=>{setAgents([{id:'technical_analyst_001',name:'技术分析师',role:'technical_analyst',status:'active',analysisCount:15,accuracy:0.85,lastActivity:'2分钟前'},{id:'bullish_researcher_001',name:'看涨研究员',role:'bullish_researcher',status:'active',analysisCount:12,accuracy:0.78,lastActivity:'1分钟前'},{id:'bearish_researcher_001',name:'看跌研究员',role:'bearish_researcher',status:'active',analysisCount:10,accuracy:0.82,lastActivity:'3分钟前'},{id:'trader_001',name:'交易员',role:'trader',status:'busy',analysisCount:8,accuracy:0.90,lastActivity:'刚刚'},{id:'risk_manager_001',name:'风险管理',role:'risk_manager',status:'idle',analysisCount:5,accuracy:0.95,lastActivity:'5分钟前'}]);setMessages([{id:1,from:'technical_analyst_001',to:['bullish_researcher_001','bearish_researcher_001'],type:'analysis_report',content:'发现启明星形态，建议关注反转信号',timestamp:'14:32:15',status:'delivered'},{id:2,from:'bullish_researcher_001',to:['trader_001'],type:'research_opinion',content:'看涨观点：技术面支持突破，建议买入',timestamp:'14:33:22',status:'delivered'},{id:3,from:'bearish_researcher_001',to:['trader_001'],type:'research_opinion',content:'看跌观点：仍需关注风险，建议谨慎',timestamp:'14:33:45',status:'delivered'},{id:4,from:'trader_001',to:['risk_manager_001'],type:'trading_signal',content:'交易决策：BUY，置信度80%',timestamp:'14:35:10',status:'processing'}]);setWorkflows([{id:'workflow_001',name:'市场分析工作流',symbol:'AAPL',status:'running',progress:0.8,stages:[{name:'技术分析',status:'completed',duration:'2分钟'},{name:'研究观点',status:'completed',duration:'3分钟'},{name:'辩论讨论',status:'completed',duration:'5分钟'},{name:'交易决策',status:'running',duration:'进行中'},{name:'风险管理',status:'pending',duration:'等待中'}]}]);setLlmStats({provider:'Mock Provider',totalRequests:45,totalTokens:12500,totalCost:0.0,avgResponseTime:1.2});setLoading(false);setError(null);},1000);};const getStatusColor=status=>{switch(status){case'active':return'green';case'busy':return'orange';case'idle':return'blue';case'error':return'red';default:return'default';}};const getStatusIcon=status=>{switch(status){case'completed':return/*#__PURE__*/_jsx(CheckCircleOutlined,{style:{color:'green'}});case'running':return/*#__PURE__*/_jsx(SyncOutlined,{spin:true,style:{color:'blue'}});case'pending':return/*#__PURE__*/_jsx(ClockCircleOutlined,{style:{color:'gray'}});case'error':return/*#__PURE__*/_jsx(ExclamationCircleOutlined,{style:{color:'red'}});default:return/*#__PURE__*/_jsx(ClockCircleOutlined,{});}};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"agent-workspace-loading\",children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u52A0\\u8F7D\\u667A\\u80FD\\u4F53\\u5DE5\\u4F5C\\u53F0...\"})]});}if(error){return/*#__PURE__*/_jsx(Alert,{message:\"\\u52A0\\u8F7D\\u5931\\u8D25\",description:error,type:\"error\",showIcon:true,action:/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:loadMockData,children:\"\\u91CD\\u8BD5\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"agent-workspace\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"workspace-header\",children:[/*#__PURE__*/_jsxs(\"h2\",{children:[/*#__PURE__*/_jsx(RobotOutlined,{}),\" \\u667A\\u80FD\\u4F53\\u5DE5\\u4F5C\\u53F0\"]}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(SyncOutlined,{}),onClick:loadMockData,loading:loading,children:\"\\u5237\\u65B0\"})]}),/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"1\",className:\"workspace-tabs\",children:[/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(RobotOutlined,{}),\"\\u667A\\u80FD\\u4F53\\u72B6\\u6001\"]}),children:/*#__PURE__*/_jsx(Row,{gutter:[16,16],children:agents.map(agent=>/*#__PURE__*/_jsx(Col,{xs:24,sm:12,lg:8,children:/*#__PURE__*/_jsxs(Card,{className:\"agent-card\",title:/*#__PURE__*/_jsx(\"div\",{className:\"agent-title\",children:/*#__PURE__*/_jsx(Badge,{status:getStatusColor(agent.status),text:agent.name})}),extra:/*#__PURE__*/_jsx(Badge,{count:agent.analysisCount}),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"agent-info\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u89D2\\u8272:\"}),\" \",agent.role]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(Badge,{status:getStatusColor(agent.status),text:agent.status})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u51C6\\u786E\\u7387:\"}),\" \",(agent.accuracy*100).toFixed(1),\"%\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6700\\u540E\\u6D3B\\u52A8:\"}),\" \",agent.lastActivity]})]}),/*#__PURE__*/_jsx(Progress,{percent:agent.accuracy*100,size:\"small\",status:agent.accuracy>0.8?'success':'normal'})]})},agent.id))})},\"1\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(MessageOutlined,{}),\"\\u6D88\\u606F\\u6D41\"]}),children:/*#__PURE__*/_jsx(Card,{title:\"\\u667A\\u80FD\\u4F53\\u901A\\u4FE1\",className:\"message-flow-card\",children:/*#__PURE__*/_jsx(Timeline,{children:messages.map(message=>/*#__PURE__*/_jsx(Timeline.Item,{dot:getStatusIcon(message.status),children:/*#__PURE__*/_jsxs(\"div\",{className:\"message-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"message-header\",children:[/*#__PURE__*/_jsx(\"strong\",{children:message.from}),\" \\u2192 \",message.to.join(', '),/*#__PURE__*/_jsx(\"span\",{className:\"message-time\",children:message.timestamp})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"message-content\",children:[/*#__PURE__*/_jsx(Badge,{color:\"blue\",text:message.type}),/*#__PURE__*/_jsx(\"p\",{children:message.content})]})]})},message.id))})})},\"2\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(BarChartOutlined,{}),\"\\u5DE5\\u4F5C\\u6D41\"]}),children:workflows.map(workflow=>/*#__PURE__*/_jsxs(Card,{title:\"\".concat(workflow.name,\" - \").concat(workflow.symbol),className:\"workflow-card\",extra:/*#__PURE__*/_jsx(Badge,{status:getStatusColor(workflow.status),text:workflow.status}),children:[/*#__PURE__*/_jsx(\"div\",{className:\"workflow-progress\",children:/*#__PURE__*/_jsx(Progress,{percent:workflow.progress*100,status:workflow.status==='running'?'active':'success'})}),/*#__PURE__*/_jsx(Timeline,{className:\"workflow-timeline\",children:workflow.stages.map((stage,index)=>/*#__PURE__*/_jsx(Timeline.Item,{dot:getStatusIcon(stage.status),children:/*#__PURE__*/_jsxs(\"div\",{className:\"stage-item\",children:[/*#__PURE__*/_jsx(\"strong\",{children:stage.name}),/*#__PURE__*/_jsx(\"span\",{className:\"stage-duration\",children:stage.duration})]})},index))})]},workflow.id))},\"3\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(SyncOutlined,{}),\"LLM\\u7EDF\\u8BA1\"]}),children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsxs(Card,{title:\"LLM\\u4F7F\\u7528\\u7EDF\\u8BA1\",className:\"stats-card\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u63D0\\u4F9B\\u5546:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:llmStats.provider})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u603B\\u8BF7\\u6C42\\u6570:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:llmStats.totalRequests})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u603BToken\\u6570:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:(_llmStats$totalTokens=llmStats.totalTokens)===null||_llmStats$totalTokens===void 0?void 0:_llmStats$totalTokens.toLocaleString()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u603B\\u6210\\u672C:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-value\",children:[\"$\",(_llmStats$totalCost=llmStats.totalCost)===null||_llmStats$totalCost===void 0?void 0:_llmStats$totalCost.toFixed(4)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u5E73\\u5747\\u54CD\\u5E94\\u65F6\\u95F4:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-value\",children:[llmStats.avgResponseTime,\"\\u79D2\"]})]})]})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u7CFB\\u7EDF\\u72B6\\u6001\",className:\"stats-card\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"system-status\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(Badge,{status:\"success\",text:\"\\u667A\\u80FD\\u4F53\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-detail\",children:\"\\u6B63\\u5E38\\u8FD0\\u884C\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(Badge,{status:\"success\",text:\"LLM\\u670D\\u52A1\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-detail\",children:\"\\u8FDE\\u63A5\\u6B63\\u5E38\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(Badge,{status:\"success\",text:\"\\u901A\\u4FE1\\u4E2D\\u5FC3\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-detail\",children:\"\\u8FD0\\u884C\\u4E2D\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-item\",children:[/*#__PURE__*/_jsx(Badge,{status:\"warning\",text:\"\\u5B9E\\u65F6\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-detail\",children:\"\\u6A21\\u62DF\\u6A21\\u5F0F\"})]})]})})})]})},\"4\")]})]});};export default AgentWorkspace;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Badge", "Timeline", "<PERSON><PERSON>", "Spin", "<PERSON><PERSON>", "Tabs", "Progress", "RobotOutlined", "MessageOutlined", "BarChartOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "SyncOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "TabPane", "AgentWorkspace", "_llmStats$totalTokens", "_llmStats$totalCost", "agents", "setAgents", "messages", "setMessages", "workflows", "setWorkflows", "llmStats", "setLlmStats", "loading", "setLoading", "error", "setError", "length", "loadMockData", "setTimeout", "id", "name", "role", "status", "analysisCount", "accuracy", "lastActivity", "from", "to", "type", "content", "timestamp", "symbol", "progress", "stages", "duration", "provider", "totalRequests", "totalTokens", "totalCost", "avgResponseTime", "getStatusColor", "getStatusIcon", "style", "color", "spin", "className", "children", "size", "message", "description", "showIcon", "action", "onClick", "icon", "defaultActiveKey", "tab", "gutter", "map", "agent", "xs", "sm", "lg", "title", "text", "extra", "count", "toFixed", "percent", "<PERSON><PERSON>", "dot", "join", "workflow", "concat", "stage", "index", "toLocaleString"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Badge, Timeline, Button, Spin, Alert, Tabs, Progress } from 'antd';\nimport { \n  RobotOutlined, \n  MessageOutlined, \n  BarChartOutlined, \n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SyncOutlined\n} from '@ant-design/icons';\nimport './AgentWorkspace.css';\n\nconst { TabPane } = Tabs;\n\nconst AgentWorkspace = () => {\n  const [agents, setAgents] = useState([]);\n  const [messages, setMessages] = useState([]);\n  const [workflows, setWorkflows] = useState([]);\n  const [llmStats, setLlmStats] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // 模拟数据（实际应该从API获取）\n  useEffect(() => {\n    // 只在组件首次挂载时加载数据\n    if (agents.length === 0) {\n      loadMockData();\n    }\n\n    // 完全移除自动刷新功能\n    // 用户可以通过手动刷新按钮更新数据\n  }, []); // 空依赖数组确保只执行一次\n\n  const loadMockData = () => {\n    setLoading(true);\n    \n    // 模拟API调用延迟\n    setTimeout(() => {\n      setAgents([\n        {\n          id: 'technical_analyst_001',\n          name: '技术分析师',\n          role: 'technical_analyst',\n          status: 'active',\n          analysisCount: 15,\n          accuracy: 0.85,\n          lastActivity: '2分钟前'\n        },\n        {\n          id: 'bullish_researcher_001',\n          name: '看涨研究员',\n          role: 'bullish_researcher',\n          status: 'active',\n          analysisCount: 12,\n          accuracy: 0.78,\n          lastActivity: '1分钟前'\n        },\n        {\n          id: 'bearish_researcher_001',\n          name: '看跌研究员',\n          role: 'bearish_researcher',\n          status: 'active',\n          analysisCount: 10,\n          accuracy: 0.82,\n          lastActivity: '3分钟前'\n        },\n        {\n          id: 'trader_001',\n          name: '交易员',\n          role: 'trader',\n          status: 'busy',\n          analysisCount: 8,\n          accuracy: 0.90,\n          lastActivity: '刚刚'\n        },\n        {\n          id: 'risk_manager_001',\n          name: '风险管理',\n          role: 'risk_manager',\n          status: 'idle',\n          analysisCount: 5,\n          accuracy: 0.95,\n          lastActivity: '5分钟前'\n        }\n      ]);\n\n      setMessages([\n        {\n          id: 1,\n          from: 'technical_analyst_001',\n          to: ['bullish_researcher_001', 'bearish_researcher_001'],\n          type: 'analysis_report',\n          content: '发现启明星形态，建议关注反转信号',\n          timestamp: '14:32:15',\n          status: 'delivered'\n        },\n        {\n          id: 2,\n          from: 'bullish_researcher_001',\n          to: ['trader_001'],\n          type: 'research_opinion',\n          content: '看涨观点：技术面支持突破，建议买入',\n          timestamp: '14:33:22',\n          status: 'delivered'\n        },\n        {\n          id: 3,\n          from: 'bearish_researcher_001',\n          to: ['trader_001'],\n          type: 'research_opinion',\n          content: '看跌观点：仍需关注风险，建议谨慎',\n          timestamp: '14:33:45',\n          status: 'delivered'\n        },\n        {\n          id: 4,\n          from: 'trader_001',\n          to: ['risk_manager_001'],\n          type: 'trading_signal',\n          content: '交易决策：BUY，置信度80%',\n          timestamp: '14:35:10',\n          status: 'processing'\n        }\n      ]);\n\n      setWorkflows([\n        {\n          id: 'workflow_001',\n          name: '市场分析工作流',\n          symbol: 'AAPL',\n          status: 'running',\n          progress: 0.8,\n          stages: [\n            { name: '技术分析', status: 'completed', duration: '2分钟' },\n            { name: '研究观点', status: 'completed', duration: '3分钟' },\n            { name: '辩论讨论', status: 'completed', duration: '5分钟' },\n            { name: '交易决策', status: 'running', duration: '进行中' },\n            { name: '风险管理', status: 'pending', duration: '等待中' }\n          ]\n        }\n      ]);\n\n      setLlmStats({\n        provider: 'Mock Provider',\n        totalRequests: 45,\n        totalTokens: 12500,\n        totalCost: 0.0,\n        avgResponseTime: 1.2\n      });\n\n      setLoading(false);\n      setError(null);\n    }, 1000);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active': return 'green';\n      case 'busy': return 'orange';\n      case 'idle': return 'blue';\n      case 'error': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'completed': return <CheckCircleOutlined style={{ color: 'green' }} />;\n      case 'running': return <SyncOutlined spin style={{ color: 'blue' }} />;\n      case 'pending': return <ClockCircleOutlined style={{ color: 'gray' }} />;\n      case 'error': return <ExclamationCircleOutlined style={{ color: 'red' }} />;\n      default: return <ClockCircleOutlined />;\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"agent-workspace-loading\">\n        <Spin size=\"large\" />\n        <p>加载智能体工作台...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"加载失败\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <Button size=\"small\" onClick={loadMockData}>\n            重试\n          </Button>\n        }\n      />\n    );\n  }\n\n  return (\n    <div className=\"agent-workspace\">\n      <div className=\"workspace-header\">\n        <h2>\n          <RobotOutlined /> 智能体工作台\n        </h2>\n        <Button \n          icon={<SyncOutlined />} \n          onClick={loadMockData}\n          loading={loading}\n        >\n          刷新\n        </Button>\n      </div>\n\n      <Tabs defaultActiveKey=\"1\" className=\"workspace-tabs\">\n        <TabPane tab={<span><RobotOutlined />智能体状态</span>} key=\"1\">\n          <Row gutter={[16, 16]}>\n            {agents.map(agent => (\n              <Col xs={24} sm={12} lg={8} key={agent.id}>\n                <Card \n                  className=\"agent-card\"\n                  title={\n                    <div className=\"agent-title\">\n                      <Badge \n                        status={getStatusColor(agent.status)} \n                        text={agent.name}\n                      />\n                    </div>\n                  }\n                  extra={<Badge count={agent.analysisCount} />}\n                >\n                  <div className=\"agent-info\">\n                    <p><strong>角色:</strong> {agent.role}</p>\n                    <p><strong>状态:</strong> \n                      <Badge \n                        status={getStatusColor(agent.status)} \n                        text={agent.status}\n                      />\n                    </p>\n                    <p><strong>准确率:</strong> {(agent.accuracy * 100).toFixed(1)}%</p>\n                    <p><strong>最后活动:</strong> {agent.lastActivity}</p>\n                  </div>\n                  <Progress \n                    percent={agent.accuracy * 100} \n                    size=\"small\"\n                    status={agent.accuracy > 0.8 ? 'success' : 'normal'}\n                  />\n                </Card>\n              </Col>\n            ))}\n          </Row>\n        </TabPane>\n\n        <TabPane tab={<span><MessageOutlined />消息流</span>} key=\"2\">\n          <Card title=\"智能体通信\" className=\"message-flow-card\">\n            <Timeline>\n              {messages.map(message => (\n                <Timeline.Item\n                  key={message.id}\n                  dot={getStatusIcon(message.status)}\n                >\n                  <div className=\"message-item\">\n                    <div className=\"message-header\">\n                      <strong>{message.from}</strong> → {message.to.join(', ')}\n                      <span className=\"message-time\">{message.timestamp}</span>\n                    </div>\n                    <div className=\"message-content\">\n                      <Badge color=\"blue\" text={message.type} />\n                      <p>{message.content}</p>\n                    </div>\n                  </div>\n                </Timeline.Item>\n              ))}\n            </Timeline>\n          </Card>\n        </TabPane>\n\n        <TabPane tab={<span><BarChartOutlined />工作流</span>} key=\"3\">\n          {workflows.map(workflow => (\n            <Card \n              key={workflow.id}\n              title={`${workflow.name} - ${workflow.symbol}`}\n              className=\"workflow-card\"\n              extra={\n                <Badge \n                  status={getStatusColor(workflow.status)} \n                  text={workflow.status}\n                />\n              }\n            >\n              <div className=\"workflow-progress\">\n                <Progress \n                  percent={workflow.progress * 100} \n                  status={workflow.status === 'running' ? 'active' : 'success'}\n                />\n              </div>\n              \n              <Timeline className=\"workflow-timeline\">\n                {workflow.stages.map((stage, index) => (\n                  <Timeline.Item\n                    key={index}\n                    dot={getStatusIcon(stage.status)}\n                  >\n                    <div className=\"stage-item\">\n                      <strong>{stage.name}</strong>\n                      <span className=\"stage-duration\">{stage.duration}</span>\n                    </div>\n                  </Timeline.Item>\n                ))}\n              </Timeline>\n            </Card>\n          ))}\n        </TabPane>\n\n        <TabPane tab={<span><SyncOutlined />LLM统计</span>} key=\"4\">\n          <Row gutter={[16, 16]}>\n            <Col xs={24} lg={12}>\n              <Card title=\"LLM使用统计\" className=\"stats-card\">\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">提供商:</span>\n                  <span className=\"stat-value\">{llmStats.provider}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">总请求数:</span>\n                  <span className=\"stat-value\">{llmStats.totalRequests}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">总Token数:</span>\n                  <span className=\"stat-value\">{llmStats.totalTokens?.toLocaleString()}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">总成本:</span>\n                  <span className=\"stat-value\">${llmStats.totalCost?.toFixed(4)}</span>\n                </div>\n                <div className=\"stat-item\">\n                  <span className=\"stat-label\">平均响应时间:</span>\n                  <span className=\"stat-value\">{llmStats.avgResponseTime}秒</span>\n                </div>\n              </Card>\n            </Col>\n            \n            <Col xs={24} lg={12}>\n              <Card title=\"系统状态\" className=\"stats-card\">\n                <div className=\"system-status\">\n                  <div className=\"status-item\">\n                    <Badge status=\"success\" text=\"智能体系统\" />\n                    <span className=\"status-detail\">正常运行</span>\n                  </div>\n                  <div className=\"status-item\">\n                    <Badge status=\"success\" text=\"LLM服务\" />\n                    <span className=\"status-detail\">连接正常</span>\n                  </div>\n                  <div className=\"status-item\">\n                    <Badge status=\"success\" text=\"通信中心\" />\n                    <span className=\"status-detail\">运行中</span>\n                  </div>\n                  <div className=\"status-item\">\n                    <Badge status=\"warning\" text=\"实时数据\" />\n                    <span className=\"status-detail\">模拟模式</span>\n                  </div>\n                </div>\n              </Card>\n            </Col>\n          </Row>\n        </TabPane>\n      </Tabs>\n    </div>\n  );\n};\n\nexport default AgentWorkspace;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,GAAG,CAAEC,GAAG,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,CAAEC,QAAQ,KAAQ,MAAM,CAC3F,OACEC,aAAa,CACbC,eAAe,CACfC,gBAAgB,CAChBC,mBAAmB,CACnBC,mBAAmB,CACnBC,yBAAyB,CACzBC,YAAY,KACP,mBAAmB,CAC1B,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAEC,OAAQ,CAAC,CAAGb,IAAI,CAExB,KAAM,CAAAc,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,mBAAA,CAC3B,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiC,QAAQ,CAAEC,WAAW,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5C,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,KAAK,CAAEC,QAAQ,CAAC,CAAGtC,QAAQ,CAAC,IAAI,CAAC,CAExC;AACAC,SAAS,CAAC,IAAM,CACd;AACA,GAAI0B,MAAM,CAACY,MAAM,GAAK,CAAC,CAAE,CACvBC,YAAY,CAAC,CAAC,CAChB,CAEA;AACA;AACF,CAAC,CAAE,EAAE,CAAC,CAAE;AAER,KAAM,CAAAA,YAAY,CAAGA,CAAA,GAAM,CACzBJ,UAAU,CAAC,IAAI,CAAC,CAEhB;AACAK,UAAU,CAAC,IAAM,CACfb,SAAS,CAAC,CACR,CACEc,EAAE,CAAE,uBAAuB,CAC3BC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,mBAAmB,CACzBC,MAAM,CAAE,QAAQ,CAChBC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,MAChB,CAAC,CACD,CACEN,EAAE,CAAE,wBAAwB,CAC5BC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,oBAAoB,CAC1BC,MAAM,CAAE,QAAQ,CAChBC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,MAChB,CAAC,CACD,CACEN,EAAE,CAAE,wBAAwB,CAC5BC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,oBAAoB,CAC1BC,MAAM,CAAE,QAAQ,CAChBC,aAAa,CAAE,EAAE,CACjBC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,MAChB,CAAC,CACD,CACEN,EAAE,CAAE,YAAY,CAChBC,IAAI,CAAE,KAAK,CACXC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,MAAM,CACdC,aAAa,CAAE,CAAC,CAChBC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,IAChB,CAAC,CACD,CACEN,EAAE,CAAE,kBAAkB,CACtBC,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,cAAc,CACpBC,MAAM,CAAE,MAAM,CACdC,aAAa,CAAE,CAAC,CAChBC,QAAQ,CAAE,IAAI,CACdC,YAAY,CAAE,MAChB,CAAC,CACF,CAAC,CAEFlB,WAAW,CAAC,CACV,CACEY,EAAE,CAAE,CAAC,CACLO,IAAI,CAAE,uBAAuB,CAC7BC,EAAE,CAAE,CAAC,wBAAwB,CAAE,wBAAwB,CAAC,CACxDC,IAAI,CAAE,iBAAiB,CACvBC,OAAO,CAAE,kBAAkB,CAC3BC,SAAS,CAAE,UAAU,CACrBR,MAAM,CAAE,WACV,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLO,IAAI,CAAE,wBAAwB,CAC9BC,EAAE,CAAE,CAAC,YAAY,CAAC,CAClBC,IAAI,CAAE,kBAAkB,CACxBC,OAAO,CAAE,mBAAmB,CAC5BC,SAAS,CAAE,UAAU,CACrBR,MAAM,CAAE,WACV,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLO,IAAI,CAAE,wBAAwB,CAC9BC,EAAE,CAAE,CAAC,YAAY,CAAC,CAClBC,IAAI,CAAE,kBAAkB,CACxBC,OAAO,CAAE,kBAAkB,CAC3BC,SAAS,CAAE,UAAU,CACrBR,MAAM,CAAE,WACV,CAAC,CACD,CACEH,EAAE,CAAE,CAAC,CACLO,IAAI,CAAE,YAAY,CAClBC,EAAE,CAAE,CAAC,kBAAkB,CAAC,CACxBC,IAAI,CAAE,gBAAgB,CACtBC,OAAO,CAAE,iBAAiB,CAC1BC,SAAS,CAAE,UAAU,CACrBR,MAAM,CAAE,YACV,CAAC,CACF,CAAC,CAEFb,YAAY,CAAC,CACX,CACEU,EAAE,CAAE,cAAc,CAClBC,IAAI,CAAE,SAAS,CACfW,MAAM,CAAE,MAAM,CACdT,MAAM,CAAE,SAAS,CACjBU,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,CACN,CAAEb,IAAI,CAAE,MAAM,CAAEE,MAAM,CAAE,WAAW,CAAEY,QAAQ,CAAE,KAAM,CAAC,CACtD,CAAEd,IAAI,CAAE,MAAM,CAAEE,MAAM,CAAE,WAAW,CAAEY,QAAQ,CAAE,KAAM,CAAC,CACtD,CAAEd,IAAI,CAAE,MAAM,CAAEE,MAAM,CAAE,WAAW,CAAEY,QAAQ,CAAE,KAAM,CAAC,CACtD,CAAEd,IAAI,CAAE,MAAM,CAAEE,MAAM,CAAE,SAAS,CAAEY,QAAQ,CAAE,KAAM,CAAC,CACpD,CAAEd,IAAI,CAAE,MAAM,CAAEE,MAAM,CAAE,SAAS,CAAEY,QAAQ,CAAE,KAAM,CAAC,CAExD,CAAC,CACF,CAAC,CAEFvB,WAAW,CAAC,CACVwB,QAAQ,CAAE,eAAe,CACzBC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,KAAK,CAClBC,SAAS,CAAE,GAAG,CACdC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEF1B,UAAU,CAAC,KAAK,CAAC,CACjBE,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAyB,cAAc,CAAIlB,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,QAAQ,CAAE,MAAO,OAAO,CAC7B,IAAK,MAAM,CAAE,MAAO,QAAQ,CAC5B,IAAK,MAAM,CAAE,MAAO,MAAM,CAC1B,IAAK,OAAO,CAAE,MAAO,KAAK,CAC1B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAmB,aAAa,CAAInB,MAAM,EAAK,CAChC,OAAQA,MAAM,EACZ,IAAK,WAAW,CAAE,mBAAOzB,IAAA,CAACJ,mBAAmB,EAACiD,KAAK,CAAE,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAE,CAAC,CAC3E,IAAK,SAAS,CAAE,mBAAO9C,IAAA,CAACF,YAAY,EAACiD,IAAI,MAACF,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACtE,IAAK,SAAS,CAAE,mBAAO9C,IAAA,CAACL,mBAAmB,EAACkD,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CACxE,IAAK,OAAO,CAAE,mBAAO9C,IAAA,CAACH,yBAAyB,EAACgD,KAAK,CAAE,CAAEC,KAAK,CAAE,KAAM,CAAE,CAAE,CAAC,CAC3E,QAAS,mBAAO9C,IAAA,CAACL,mBAAmB,GAAE,CAAC,CACzC,CACF,CAAC,CAED,GAAIoB,OAAO,CAAE,CACX,mBACEb,KAAA,QAAK8C,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCjD,IAAA,CAACZ,IAAI,EAAC8D,IAAI,CAAC,OAAO,CAAE,CAAC,cACrBlD,IAAA,MAAAiD,QAAA,CAAG,qDAAW,CAAG,CAAC,EACf,CAAC,CAEV,CAEA,GAAIhC,KAAK,CAAE,CACT,mBACEjB,IAAA,CAACX,KAAK,EACJ8D,OAAO,CAAC,0BAAM,CACdC,WAAW,CAAEnC,KAAM,CACnBc,IAAI,CAAC,OAAO,CACZsB,QAAQ,MACRC,MAAM,cACJtD,IAAA,CAACb,MAAM,EAAC+D,IAAI,CAAC,OAAO,CAACK,OAAO,CAAEnC,YAAa,CAAA6B,QAAA,CAAC,cAE5C,CAAQ,CACT,CACF,CAAC,CAEN,CAEA,mBACE/C,KAAA,QAAK8C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B/C,KAAA,QAAK8C,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B/C,KAAA,OAAA+C,QAAA,eACEjD,IAAA,CAACR,aAAa,GAAE,CAAC,wCACnB,EAAI,CAAC,cACLQ,IAAA,CAACb,MAAM,EACLqE,IAAI,cAAExD,IAAA,CAACF,YAAY,GAAE,CAAE,CACvByD,OAAO,CAAEnC,YAAa,CACtBL,OAAO,CAAEA,OAAQ,CAAAkC,QAAA,CAClB,cAED,CAAQ,CAAC,EACN,CAAC,cAEN/C,KAAA,CAACZ,IAAI,EAACmE,gBAAgB,CAAC,GAAG,CAACT,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eACnDjD,IAAA,CAACG,OAAO,EAACuD,GAAG,cAAExD,KAAA,SAAA+C,QAAA,eAAMjD,IAAA,CAACR,aAAa,GAAE,CAAC,iCAAK,EAAM,CAAE,CAAAyD,QAAA,cAChDjD,IAAA,CAACjB,GAAG,EAAC4E,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAV,QAAA,CACnB1C,MAAM,CAACqD,GAAG,CAACC,KAAK,eACf7D,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAf,QAAA,cACzB/C,KAAA,CAACpB,IAAI,EACHkE,SAAS,CAAC,YAAY,CACtBiB,KAAK,cACHjE,IAAA,QAAKgD,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BjD,IAAA,CAACf,KAAK,EACJwC,MAAM,CAAEkB,cAAc,CAACkB,KAAK,CAACpC,MAAM,CAAE,CACrCyC,IAAI,CAAEL,KAAK,CAACtC,IAAK,CAClB,CAAC,CACC,CACN,CACD4C,KAAK,cAAEnE,IAAA,CAACf,KAAK,EAACmF,KAAK,CAAEP,KAAK,CAACnC,aAAc,CAAE,CAAE,CAAAuB,QAAA,eAE7C/C,KAAA,QAAK8C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB/C,KAAA,MAAA+C,QAAA,eAAGjD,IAAA,WAAAiD,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACY,KAAK,CAACrC,IAAI,EAAI,CAAC,cACxCtB,KAAA,MAAA+C,QAAA,eAAGjD,IAAA,WAAAiD,QAAA,CAAQ,eAAG,CAAQ,CAAC,cACrBjD,IAAA,CAACf,KAAK,EACJwC,MAAM,CAAEkB,cAAc,CAACkB,KAAK,CAACpC,MAAM,CAAE,CACrCyC,IAAI,CAAEL,KAAK,CAACpC,MAAO,CACpB,CAAC,EACD,CAAC,cACJvB,KAAA,MAAA+C,QAAA,eAAGjD,IAAA,WAAAiD,QAAA,CAAQ,qBAAI,CAAQ,CAAC,IAAC,CAAC,CAACY,KAAK,CAAClC,QAAQ,CAAG,GAAG,EAAE0C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAG,CAAC,cACjEnE,KAAA,MAAA+C,QAAA,eAAGjD,IAAA,WAAAiD,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACY,KAAK,CAACjC,YAAY,EAAI,CAAC,EAC/C,CAAC,cACN5B,IAAA,CAACT,QAAQ,EACP+E,OAAO,CAAET,KAAK,CAAClC,QAAQ,CAAG,GAAI,CAC9BuB,IAAI,CAAC,OAAO,CACZzB,MAAM,CAAEoC,KAAK,CAAClC,QAAQ,CAAG,GAAG,CAAG,SAAS,CAAG,QAAS,CACrD,CAAC,EACE,CAAC,EA7BwBkC,KAAK,CAACvC,EA8BlC,CACN,CAAC,CACC,CAAC,EAnC+C,GAoC9C,CAAC,cAEVtB,IAAA,CAACG,OAAO,EAACuD,GAAG,cAAExD,KAAA,SAAA+C,QAAA,eAAMjD,IAAA,CAACP,eAAe,GAAE,CAAC,qBAAG,EAAM,CAAE,CAAAwD,QAAA,cAChDjD,IAAA,CAAClB,IAAI,EAACmF,KAAK,CAAC,gCAAO,CAACjB,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAC/CjD,IAAA,CAACd,QAAQ,EAAA+D,QAAA,CACNxC,QAAQ,CAACmD,GAAG,CAACT,OAAO,eACnBnD,IAAA,CAACd,QAAQ,CAACqF,IAAI,EAEZC,GAAG,CAAE5B,aAAa,CAACO,OAAO,CAAC1B,MAAM,CAAE,CAAAwB,QAAA,cAEnC/C,KAAA,QAAK8C,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B/C,KAAA,QAAK8C,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjD,IAAA,WAAAiD,QAAA,CAASE,OAAO,CAACtB,IAAI,CAAS,CAAC,WAAG,CAACsB,OAAO,CAACrB,EAAE,CAAC2C,IAAI,CAAC,IAAI,CAAC,cACxDzE,IAAA,SAAMgD,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAEE,OAAO,CAAClB,SAAS,CAAO,CAAC,EACtD,CAAC,cACN/B,KAAA,QAAK8C,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BjD,IAAA,CAACf,KAAK,EAAC6D,KAAK,CAAC,MAAM,CAACoB,IAAI,CAAEf,OAAO,CAACpB,IAAK,CAAE,CAAC,cAC1C/B,IAAA,MAAAiD,QAAA,CAAIE,OAAO,CAACnB,OAAO,CAAI,CAAC,EACrB,CAAC,EACH,CAAC,EAZDmB,OAAO,CAAC7B,EAaA,CAChB,CAAC,CACM,CAAC,CACP,CAAC,EArB8C,GAsB9C,CAAC,cAEVtB,IAAA,CAACG,OAAO,EAACuD,GAAG,cAAExD,KAAA,SAAA+C,QAAA,eAAMjD,IAAA,CAACN,gBAAgB,GAAE,CAAC,qBAAG,EAAM,CAAE,CAAAuD,QAAA,CAChDtC,SAAS,CAACiD,GAAG,CAACc,QAAQ,eACrBxE,KAAA,CAACpB,IAAI,EAEHmF,KAAK,IAAAU,MAAA,CAAKD,QAAQ,CAACnD,IAAI,QAAAoD,MAAA,CAAMD,QAAQ,CAACxC,MAAM,CAAG,CAC/Cc,SAAS,CAAC,eAAe,CACzBmB,KAAK,cACHnE,IAAA,CAACf,KAAK,EACJwC,MAAM,CAAEkB,cAAc,CAAC+B,QAAQ,CAACjD,MAAM,CAAE,CACxCyC,IAAI,CAAEQ,QAAQ,CAACjD,MAAO,CACvB,CACF,CAAAwB,QAAA,eAEDjD,IAAA,QAAKgD,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCjD,IAAA,CAACT,QAAQ,EACP+E,OAAO,CAAEI,QAAQ,CAACvC,QAAQ,CAAG,GAAI,CACjCV,MAAM,CAAEiD,QAAQ,CAACjD,MAAM,GAAK,SAAS,CAAG,QAAQ,CAAG,SAAU,CAC9D,CAAC,CACC,CAAC,cAENzB,IAAA,CAACd,QAAQ,EAAC8D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CACpCyB,QAAQ,CAACtC,MAAM,CAACwB,GAAG,CAAC,CAACgB,KAAK,CAAEC,KAAK,gBAChC7E,IAAA,CAACd,QAAQ,CAACqF,IAAI,EAEZC,GAAG,CAAE5B,aAAa,CAACgC,KAAK,CAACnD,MAAM,CAAE,CAAAwB,QAAA,cAEjC/C,KAAA,QAAK8C,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBjD,IAAA,WAAAiD,QAAA,CAAS2B,KAAK,CAACrD,IAAI,CAAS,CAAC,cAC7BvB,IAAA,SAAMgD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE2B,KAAK,CAACvC,QAAQ,CAAO,CAAC,EACrD,CAAC,EANDwC,KAOQ,CAChB,CAAC,CACM,CAAC,GA7BNH,QAAQ,CAACpD,EA8BV,CACP,CAAC,EAlCoD,GAmC/C,CAAC,cAEVtB,IAAA,CAACG,OAAO,EAACuD,GAAG,cAAExD,KAAA,SAAA+C,QAAA,eAAMjD,IAAA,CAACF,YAAY,GAAE,CAAC,kBAAK,EAAM,CAAE,CAAAmD,QAAA,cAC/C/C,KAAA,CAACnB,GAAG,EAAC4E,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAV,QAAA,eACpBjD,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAf,QAAA,cAClB/C,KAAA,CAACpB,IAAI,EAACmF,KAAK,CAAC,6BAAS,CAACjB,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC1C/C,KAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAI,CAAM,CAAC,cACxCjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpC,QAAQ,CAACyB,QAAQ,CAAO,CAAC,EACpD,CAAC,cACNpC,KAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzCjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEpC,QAAQ,CAAC0B,aAAa,CAAO,CAAC,EACzD,CAAC,cACNrC,KAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAQ,CAAM,CAAC,cAC5CjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAA5C,qBAAA,CAAEQ,QAAQ,CAAC2B,WAAW,UAAAnC,qBAAA,iBAApBA,qBAAA,CAAsByE,cAAc,CAAC,CAAC,CAAO,CAAC,EACzE,CAAC,cACN5E,KAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,qBAAI,CAAM,CAAC,cACxC/C,KAAA,SAAM8C,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAC,GAAC,EAAA3C,mBAAA,CAACO,QAAQ,CAAC4B,SAAS,UAAAnC,mBAAA,iBAAlBA,mBAAA,CAAoB+D,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAClE,CAAC,cACNnE,KAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjD,IAAA,SAAMgD,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uCAAO,CAAM,CAAC,cAC3C/C,KAAA,SAAM8C,SAAS,CAAC,YAAY,CAAAC,QAAA,EAAEpC,QAAQ,CAAC6B,eAAe,CAAC,QAAC,EAAM,CAAC,EAC5D,CAAC,EACF,CAAC,CACJ,CAAC,cAEN1C,IAAA,CAAChB,GAAG,EAAC8E,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAAf,QAAA,cAClBjD,IAAA,CAAClB,IAAI,EAACmF,KAAK,CAAC,0BAAM,CAACjB,SAAS,CAAC,YAAY,CAAAC,QAAA,cACvC/C,KAAA,QAAK8C,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/C,KAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjD,IAAA,CAACf,KAAK,EAACwC,MAAM,CAAC,SAAS,CAACyC,IAAI,CAAC,gCAAO,CAAE,CAAC,cACvClE,IAAA,SAAMgD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,EACxC,CAAC,cACN/C,KAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjD,IAAA,CAACf,KAAK,EAACwC,MAAM,CAAC,SAAS,CAACyC,IAAI,CAAC,iBAAO,CAAE,CAAC,cACvClE,IAAA,SAAMgD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,EACxC,CAAC,cACN/C,KAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjD,IAAA,CAACf,KAAK,EAACwC,MAAM,CAAC,SAAS,CAACyC,IAAI,CAAC,0BAAM,CAAE,CAAC,cACtClE,IAAA,SAAMgD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,EACvC,CAAC,cACN/C,KAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjD,IAAA,CAACf,KAAK,EAACwC,MAAM,CAAC,SAAS,CAACyC,IAAI,CAAC,0BAAM,CAAE,CAAC,cACtClE,IAAA,SAAMgD,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,EACxC,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,EACH,CAAC,EAjD8C,GAkD7C,CAAC,EACN,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}