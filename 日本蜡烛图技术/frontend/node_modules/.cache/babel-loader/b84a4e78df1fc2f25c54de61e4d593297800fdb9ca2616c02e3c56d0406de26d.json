{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/src/components/CandlestickChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport annotationPlugin from 'chartjs-plugin-annotation';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement, annotationPlugin);\nconst {\n  Option\n} = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\nconst CandlestickChart = ({\n  data,\n  patterns = []\n}) => {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    const filteredPatterns = selectedPattern === 'all' ? patterns : patterns.filter(p => p.pattern_name === selectedPattern);\n    const annotations = [];\n    filteredPatterns.forEach((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n      if (!startData || !endData) return;\n      const color = getPatternColor(pattern.signal);\n      const patternName = PATTERN_NAMES[pattern.pattern_name] || pattern.pattern_name;\n\n      // 添加背景高亮框\n      annotations.push({\n        type: 'box',\n        xMin: startData.timestamp ? new Date(startData.timestamp).getTime() : startIndex,\n        xMax: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yMin: Math.min(parseFloat(startData.low), parseFloat(endData.low)) * 0.998,\n        yMax: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.002,\n        backgroundColor: color + '15',\n        borderColor: color,\n        borderWidth: 1.5,\n        borderDash: [5, 5]\n      });\n\n      // 添加形态名称标签\n      annotations.push({\n        type: 'label',\n        xValue: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yValue: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.005,\n        backgroundColor: color,\n        borderColor: color,\n        borderWidth: 1,\n        borderRadius: 4,\n        color: '#FFFFFF',\n        content: [`${patternName}`, `置信度: ${(pattern.confidence * 100).toFixed(1)}%`],\n        font: {\n          size: 11,\n          weight: 'bold'\n        },\n        padding: 6,\n        position: 'center'\n      });\n    });\n    return annotations;\n  };\n\n  // 获取形态颜色\n  const getPatternColor = signal => {\n    switch (signal) {\n      case 'bullish':\n        return '#52c41a';\n      case 'bearish':\n        return '#ff4d4f';\n      case 'neutral':\n        return '#faad14';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: d.c\n            })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [{\n          label: '日本蜡烛图',\n          data: candleData,\n          // 根据样式选择配置颜色\n          color: classicStyle ? {\n            up: '#000000',\n            // 阳线边框：黑色\n            down: '#000000',\n            // 阴线边框：黑色\n            unchanged: '#000000'\n          } : {\n            up: '#52c41a',\n            // 阳线边框：绿色\n            down: '#ff4d4f',\n            // 阴线边框：红色\n            unchanged: '#faad14'\n          },\n          backgroundColor: classicStyle ? {\n            up: '#FFFFFF',\n            // 阳线填充：白色\n            down: '#000000',\n            // 阴线填充：黑色\n            unchanged: '#FFFFFF'\n          } : {\n            up: '#52c41a20',\n            // 阳线填充：淡绿色\n            down: '#ff4d4f20',\n            // 阴线填充：淡红色\n            unchanged: '#faad1420'\n          },\n          borderColor: classicStyle ? {\n            up: '#000000',\n            down: '#000000',\n            unchanged: '#000000'\n          } : {\n            up: '#52c41a',\n            down: '#ff4d4f',\n            unchanged: '#faad14'\n          },\n          borderWidth: 1.5,\n          // 蜡烛图样式\n          candlestick: {\n            bodyWidth: 0.8,\n            wickWidth: 1\n          }\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd'\n              }\n            },\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              },\n              callback: function (value) {\n                return value.toFixed(2);\n              }\n            }\n          }\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: '#333333',\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: context => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString('zh-CN') : `第 ${dataIndex + 1} 根K线`;\n              },\n              label: context => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [`开盘: ${candle.o.toFixed(2)}`, `最高: ${candle.h.toFixed(2)}`, `最低: ${candle.l.toFixed(2)}`, `收盘: ${candle.c.toFixed(2)}`, `成交量: ${candle.v.toLocaleString()}`];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    chartInstance.current = new Chart(ctx, config);\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType, classicStyle]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u6570\\u636E\\uFF0C\\u8BF7\\u5148\\u4E0A\\u4F20\\u8721\\u70DB\\u56FE\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 16,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u56FE\\u8868\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartType,\n            onChange: setChartType,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"candlestick\",\n              children: \"\\u8721\\u70DB\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"line\",\n              children: \"\\u6298\\u7EBF\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u7ECF\\u5178\\u6837\\u5F0F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: classicStyle,\n            onChange: setClassicStyle,\n            checkedChildren: \"\\u9ED1\\u767D\",\n            unCheckedChildren: \"\\u5F69\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u663E\\u793A\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showPatterns,\n            onChange: setShowPatterns,\n            checkedChildren: \"\\u5F00\",\n            unCheckedChildren: \"\\u5173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), showPatterns && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u5F62\\u6001\\u8FC7\\u6EE4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedPattern,\n            onChange: setSelectedPattern,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u5168\\u90E8\\u5F62\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), getUniquePatterns().map(pattern => /*#__PURE__*/_jsxDEV(Option, {\n              value: pattern,\n              children: pattern\n            }, pattern, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: data.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u8BC6\\u522B\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: patterns.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '500px',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: chartRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this), showPatterns && patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginRight: 16\n          },\n          children: \"\\u5F62\\u6001\\u56FE\\u4F8B:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this), getUniquePatterns().map(patternName => {\n          const pattern = patterns.find(p => p.pattern_name === patternName);\n          if (!pattern) return null;\n          const chineseName = PATTERN_NAMES[patternName] || patternName;\n          const color = getPatternColor(pattern.signal);\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `${chineseName} (${patternName}) - ${pattern.description || '经典蜡烛图形态'}`,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange',\n              style: {\n                margin: '2px'\n              },\n              children: chineseName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 19\n            }, this)\n          }, patternName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 17\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 358,\n    columnNumber: 5\n  }, this);\n};\n_s(CandlestickChart, \"e/7/0Cw5CRvQSoAkveSHJuxV6s4=\");\n_c = CandlestickChart;\nexport default CandlestickChart;\nvar _c;\n$RefreshReg$(_c, \"CandlestickChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "Select", "Switch", "<PERSON><PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Chart", "registerables", "CandlestickController", "CandlestickElement", "OhlcController", "OhlcElement", "annotationPlugin", "jsxDEV", "_jsxDEV", "register", "Option", "PATTERN_NAMES", "CandlestickChart", "data", "patterns", "_s", "chartRef", "chartInstance", "showPatterns", "setShowPatterns", "selectedPatt<PERSON>", "setSelectedPattern", "chartType", "setChartType", "classicStyle", "setClassicStyle", "processCandleData", "length", "map", "candle", "index", "x", "timestamp", "Date", "getTime", "o", "parseFloat", "open", "h", "high", "l", "low", "c", "close", "v", "volume", "processPatternAnnotations", "filteredPatterns", "filter", "p", "pattern_name", "annotations", "for<PERSON>ach", "pattern", "startIndex", "start_index", "endIndex", "end_index", "startData", "endData", "color", "getPatternColor", "signal", "patternName", "push", "type", "xMin", "xMax", "yMin", "Math", "min", "yMax", "max", "backgroundColor", "borderColor", "borderWidth", "borderDash", "xValue", "yValue", "borderRadius", "content", "confidence", "toFixed", "font", "size", "weight", "padding", "position", "createChartConfig", "candleData", "datasets", "label", "d", "y", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "time", "unit", "beginAtZero", "plugins", "legend", "display", "annotation", "up", "down", "unchanged", "candlestick", "bodyWidth", "wick<PERSON>idth", "interaction", "intersect", "mode", "displayFormats", "day", "grid", "lineWidth", "ticks", "callback", "value", "labels", "tooltip", "titleColor", "bodyColor", "callbacks", "title", "context", "dataIndex", "toLocaleDateString", "toLocaleString", "current", "destroy", "ctx", "getContext", "config", "getUniquePatterns", "uniqueNames", "Set", "sort", "children", "style", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "alignItems", "gap", "flexWrap", "marginRight", "onChange", "width", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "height", "ref", "marginTop", "fontWeight", "find", "chineseName", "description", "margin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport annotationPlugin from 'chartjs-plugin-annotation';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement, annotationPlugin);\n\nconst { Option } = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\n\nconst CandlestickChart = ({ data, patterns = [] }) => {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n\n    const filteredPatterns = selectedPattern === 'all'\n      ? patterns\n      : patterns.filter(p => p.pattern_name === selectedPattern);\n\n    const annotations = [];\n\n    filteredPatterns.forEach((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n\n      if (!startData || !endData) return;\n\n      const color = getPatternColor(pattern.signal);\n      const patternName = PATTERN_NAMES[pattern.pattern_name] || pattern.pattern_name;\n\n      // 添加背景高亮框\n      annotations.push({\n        type: 'box',\n        xMin: startData.timestamp ? new Date(startData.timestamp).getTime() : startIndex,\n        xMax: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yMin: Math.min(parseFloat(startData.low), parseFloat(endData.low)) * 0.998,\n        yMax: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.002,\n        backgroundColor: color + '15',\n        borderColor: color,\n        borderWidth: 1.5,\n        borderDash: [5, 5]\n      });\n\n      // 添加形态名称标签\n      annotations.push({\n        type: 'label',\n        xValue: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,\n        yValue: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.005,\n        backgroundColor: color,\n        borderColor: color,\n        borderWidth: 1,\n        borderRadius: 4,\n        color: '#FFFFFF',\n        content: [`${patternName}`, `置信度: ${(pattern.confidence * 100).toFixed(1)}%`],\n        font: {\n          size: 11,\n          weight: 'bold'\n        },\n        padding: 6,\n        position: 'center'\n      });\n    });\n\n    return annotations;\n  };\n\n  // 获取形态颜色\n  const getPatternColor = (signal) => {\n    switch (signal) {\n      case 'bullish': return '#52c41a';\n      case 'bearish': return '#ff4d4f';\n      case 'neutral': return '#faad14';\n      default: return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({ x: d.x, y: d.c })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [\n          {\n            label: '日本蜡烛图',\n            data: candleData,\n            // 根据样式选择配置颜色\n            color: classicStyle ? {\n              up: '#000000',    // 阳线边框：黑色\n              down: '#000000',  // 阴线边框：黑色\n              unchanged: '#000000'\n            } : {\n              up: '#52c41a',    // 阳线边框：绿色\n              down: '#ff4d4f',  // 阴线边框：红色\n              unchanged: '#faad14'\n            },\n            backgroundColor: classicStyle ? {\n              up: '#FFFFFF',    // 阳线填充：白色\n              down: '#000000',  // 阴线填充：黑色\n              unchanged: '#FFFFFF'\n            } : {\n              up: '#52c41a20',  // 阳线填充：淡绿色\n              down: '#ff4d4f20', // 阴线填充：淡红色\n              unchanged: '#faad1420'\n            },\n            borderColor: classicStyle ? {\n              up: '#000000',\n              down: '#000000',\n              unchanged: '#000000'\n            } : {\n              up: '#52c41a',\n              down: '#ff4d4f',\n              unchanged: '#faad14'\n            },\n            borderWidth: 1.5,\n            // 蜡烛图样式\n            candlestick: {\n              bodyWidth: 0.8,\n              wickWidth: 1\n            }\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd'\n              }\n            },\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              },\n              callback: function(value) {\n                return value.toFixed(2);\n              }\n            }\n          }\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: '#333333',\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: (context) => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ?\n                  new Date(candle.timestamp).toLocaleDateString('zh-CN') :\n                  `第 ${dataIndex + 1} 根K线`;\n              },\n              label: (context) => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [\n                  `开盘: ${candle.o.toFixed(2)}`,\n                  `最高: ${candle.h.toFixed(2)}`,\n                  `最低: ${candle.l.toFixed(2)}`,\n                  `收盘: ${candle.c.toFixed(2)}`,\n                  `成交量: ${candle.v.toLocaleString()}`\n                ];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    \n    chartInstance.current = new Chart(ctx, config);\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType, classicStyle]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n\n  if (!data || data.length === 0) {\n    return (\n      <Card>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <p>暂无数据，请先上传蜡烛图数据</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div>\n      {/* 控制面板 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>\n          <div>\n            <span style={{ marginRight: 8 }}>图表类型:</span>\n            <Select\n              value={chartType}\n              onChange={setChartType}\n              style={{ width: 120 }}\n            >\n              <Option value=\"candlestick\">蜡烛图</Option>\n              <Option value=\"line\">折线图</Option>\n            </Select>\n          </div>\n\n          <div>\n            <span style={{ marginRight: 8 }}>经典样式:</span>\n            <Switch\n              checked={classicStyle}\n              onChange={setClassicStyle}\n              checkedChildren=\"黑白\"\n              unCheckedChildren=\"彩色\"\n            />\n          </div>\n\n          <div>\n            <span style={{ marginRight: 8 }}>显示形态:</span>\n            <Switch\n              checked={showPatterns}\n              onChange={setShowPatterns}\n              checkedChildren=\"开\"\n              unCheckedChildren=\"关\"\n            />\n          </div>\n          \n          {showPatterns && (\n            <div>\n              <span style={{ marginRight: 8 }}>形态过滤:</span>\n              <Select \n                value={selectedPattern} \n                onChange={setSelectedPattern}\n                style={{ width: 150 }}\n              >\n                <Option value=\"all\">全部形态</Option>\n                {getUniquePatterns().map(pattern => (\n                  <Option key={pattern} value={pattern}>{pattern}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n          \n          <div>\n            <span style={{ marginRight: 8 }}>数据点数:</span>\n            <Tag color=\"blue\">{data.length}</Tag>\n          </div>\n          \n          {patterns && patterns.length > 0 && (\n            <div>\n              <span style={{ marginRight: 8 }}>识别形态:</span>\n              <Tag color=\"green\">{patterns.length}</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 图表区域 */}\n      <Card>\n        <div style={{ height: '500px', position: 'relative' }}>\n          <canvas ref={chartRef} />\n        </div>\n      </Card>\n\n      {/* 形态图例 */}\n      {showPatterns && patterns && patterns.length > 0 && (\n        <Card size=\"small\" style={{ marginTop: 16 }}>\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>\n            <span style={{ fontWeight: 'bold', marginRight: 16 }}>形态图例:</span>\n            {getUniquePatterns().map(patternName => {\n              const pattern = patterns.find(p => p.pattern_name === patternName);\n              if (!pattern) return null;\n\n              const chineseName = PATTERN_NAMES[patternName] || patternName;\n              const color = getPatternColor(pattern.signal);\n              return (\n                <Tooltip key={patternName} title={`${chineseName} (${patternName}) - ${pattern.description || '经典蜡烛图形态'}`}>\n                  <Tag\n                    color={pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange'}\n                    style={{ margin: '2px' }}\n                  >\n                    {chineseName}\n                  </Tag>\n                </Tooltip>\n              );\n            })}\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default CandlestickChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AACjE,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AAChH,OAAOC,gBAAgB,MAAM,2BAA2B;AACxD,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAR,KAAK,CAACS,QAAQ,CAAC,GAAGR,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,CAAC;AAE1H,MAAM;EAAEI;AAAO,CAAC,GAAGf,MAAM;;AAEzB;AACA,MAAMgB,aAAa,GAAG;EACpB,QAAQ,EAAE,KAAK;EACf,aAAa,EAAE,KAAK;EACpB,MAAM,EAAE,KAAK;EACb,kBAAkB,EAAE,OAAO;EAC3B,iBAAiB,EAAE,OAAO;EAC1B,gBAAgB,EAAE,OAAO;EACzB,cAAc,EAAE,KAAK;EACrB,UAAU,EAAE,OAAO;EACnB,mBAAmB,EAAE,MAAM;EAC3B,mBAAmB,EAAE,MAAM;EAC3B,kBAAkB,EAAE,MAAM;EAC1B,kBAAkB,EAAE,MAAM;EAC1B,gBAAgB,EAAE,MAAM;EACxB,gBAAgB,EAAE,MAAM;EACxB,cAAc,EAAE,MAAM;EACtB,cAAc,EAAE,MAAM;EACtB,iBAAiB,EAAE,MAAM;EACzB,cAAc,EAAE,KAAK;EACrB,cAAc,EAAE,KAAK;EACrB,mBAAmB,EAAE,OAAO;EAC5B,mBAAmB,EAAE,OAAO;EAC5B,sBAAsB,EAAE,QAAQ;EAChC,mBAAmB,EAAE,MAAM;EAC3B,sBAAsB,EAAE,MAAM;EAC9B,uBAAuB,EAAE,MAAM;EAC/B,eAAe,EAAE,IAAI;EACrB,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMyB,aAAa,GAAGzB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMiC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACb,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOd,IAAI,CAACe,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;MAClCC,CAAC,EAAEF,MAAM,CAACG,SAAS,GAAG,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGJ,KAAK;MAClEK,CAAC,EAAEC,UAAU,CAACP,MAAM,CAACQ,IAAI,CAAC;MAC1BC,CAAC,EAAEF,UAAU,CAACP,MAAM,CAACU,IAAI,CAAC;MAC1BC,CAAC,EAAEJ,UAAU,CAACP,MAAM,CAACY,GAAG,CAAC;MACzBC,CAAC,EAAEN,UAAU,CAACP,MAAM,CAACc,KAAK,CAAC;MAC3BC,CAAC,EAAER,UAAU,CAACP,MAAM,CAACgB,MAAM,IAAI,IAAI,CAAC;MACpCb,SAAS,EAAEH,MAAM,CAACG;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMc,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC5B,YAAY,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAElE,MAAMoB,gBAAgB,GAAG3B,eAAe,KAAK,KAAK,GAC9CN,QAAQ,GACRA,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK9B,eAAe,CAAC;IAE5D,MAAM+B,WAAW,GAAG,EAAE;IAEtBJ,gBAAgB,CAACK,OAAO,CAAC,CAACC,OAAO,EAAEvB,KAAK,KAAK;MAC3C,MAAMwB,UAAU,GAAGD,OAAO,CAACE,WAAW,IAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGH,OAAO,CAACI,SAAS,IAAIH,UAAU;;MAEhD;MACA,MAAMI,SAAS,GAAG7C,IAAI,CAACyC,UAAU,CAAC;MAClC,MAAMK,OAAO,GAAG9C,IAAI,CAAC2C,QAAQ,CAAC;MAE9B,IAAI,CAACE,SAAS,IAAI,CAACC,OAAO,EAAE;MAE5B,MAAMC,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;MAC7C,MAAMC,WAAW,GAAGpD,aAAa,CAAC0C,OAAO,CAACH,YAAY,CAAC,IAAIG,OAAO,CAACH,YAAY;;MAE/E;MACAC,WAAW,CAACa,IAAI,CAAC;QACfC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAER,SAAS,CAAC1B,SAAS,GAAG,IAAIC,IAAI,CAACyB,SAAS,CAAC1B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGoB,UAAU;QAChFa,IAAI,EAAER,OAAO,CAAC3B,SAAS,GAAG,IAAIC,IAAI,CAAC0B,OAAO,CAAC3B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGsB,QAAQ;QAC1EY,IAAI,EAAEC,IAAI,CAACC,GAAG,CAAClC,UAAU,CAACsB,SAAS,CAACjB,GAAG,CAAC,EAAEL,UAAU,CAACuB,OAAO,CAAClB,GAAG,CAAC,CAAC,GAAG,KAAK;QAC1E8B,IAAI,EAAEF,IAAI,CAACG,GAAG,CAACpC,UAAU,CAACsB,SAAS,CAACnB,IAAI,CAAC,EAAEH,UAAU,CAACuB,OAAO,CAACpB,IAAI,CAAC,CAAC,GAAG,KAAK;QAC5EkC,eAAe,EAAEb,KAAK,GAAG,IAAI;QAC7Bc,WAAW,EAAEd,KAAK;QAClBe,WAAW,EAAE,GAAG;QAChBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;MACnB,CAAC,CAAC;;MAEF;MACAzB,WAAW,CAACa,IAAI,CAAC;QACfC,IAAI,EAAE,OAAO;QACbY,MAAM,EAAElB,OAAO,CAAC3B,SAAS,GAAG,IAAIC,IAAI,CAAC0B,OAAO,CAAC3B,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGsB,QAAQ;QAC5EsB,MAAM,EAAET,IAAI,CAACG,GAAG,CAACpC,UAAU,CAACsB,SAAS,CAACnB,IAAI,CAAC,EAAEH,UAAU,CAACuB,OAAO,CAACpB,IAAI,CAAC,CAAC,GAAG,KAAK;QAC9EkC,eAAe,EAAEb,KAAK;QACtBc,WAAW,EAAEd,KAAK;QAClBe,WAAW,EAAE,CAAC;QACdI,YAAY,EAAE,CAAC;QACfnB,KAAK,EAAE,SAAS;QAChBoB,OAAO,EAAE,CAAC,GAAGjB,WAAW,EAAE,EAAE,QAAQ,CAACV,OAAO,CAAC4B,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QAC7EC,IAAI,EAAE;UACJC,IAAI,EAAE,EAAE;UACRC,MAAM,EAAE;QACV,CAAC;QACDC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOpC,WAAW;EACpB,CAAC;;EAED;EACA,MAAMU,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAG/D,iBAAiB,CAAC,CAAC;IACtC,MAAMyB,WAAW,GAAGL,yBAAyB,CAAC,CAAC;IAE/C,IAAIxB,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO;QACL2C,IAAI,EAAE,MAAM;QACZpD,IAAI,EAAE;UACJ6E,QAAQ,EAAE,CAAC;YACTC,KAAK,EAAE,KAAK;YACZ9E,IAAI,EAAE4E,UAAU,CAAC7D,GAAG,CAACgE,CAAC,KAAK;cAAE7D,CAAC,EAAE6D,CAAC,CAAC7D,CAAC;cAAE8D,CAAC,EAAED,CAAC,CAAClD;YAAE,CAAC,CAAC,CAAC;YAC/CgC,WAAW,EAAE,SAAS;YACtBD,eAAe,EAAE,WAAW;YAC5BqB,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;UACX,CAAC;QACH,CAAC;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNpE,CAAC,EAAE;cACDkC,IAAI,EAAE,MAAM;cACZmC,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF,CAAC;YACDR,CAAC,EAAE;cACDS,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;YACX,CAAC;YACDC,UAAU,EAAE;cACVvD,WAAW,EAAEA;YACf;UACF;QACF;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLc,IAAI,EAAE,aAAa;MACnBpD,IAAI,EAAE;QACJ6E,QAAQ,EAAE,CACR;UACEC,KAAK,EAAE,OAAO;UACd9E,IAAI,EAAE4E,UAAU;UAChB;UACA7B,KAAK,EAAEpC,YAAY,GAAG;YACpBmF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC;UACDpC,eAAe,EAAEjD,YAAY,GAAG;YAC9BmF,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,WAAW;YAAG;YAClBC,IAAI,EAAE,WAAW;YAAE;YACnBC,SAAS,EAAE;UACb,CAAC;UACDnC,WAAW,EAAElD,YAAY,GAAG;YAC1BmF,EAAE,EAAE,SAAS;YACbC,IAAI,EAAE,SAAS;YACfC,SAAS,EAAE;UACb,CAAC,GAAG;YACFF,EAAE,EAAE,SAAS;YACbC,IAAI,EAAE,SAAS;YACfC,SAAS,EAAE;UACb,CAAC;UACDlC,WAAW,EAAE,GAAG;UAChB;UACAmC,WAAW,EAAE;YACXC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE;UACb;QACF,CAAC;MAEL,CAAC;MACDhB,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1Be,WAAW,EAAE;UACXC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE;QACR,CAAC;QACDhB,MAAM,EAAE;UACNpE,CAAC,EAAE;YACDkC,IAAI,EAAE,MAAM;YACZmC,IAAI,EAAE;cACJC,IAAI,EAAE,KAAK;cACXe,cAAc,EAAE;gBACdC,GAAG,EAAE;cACP;YACF,CAAC;YACDC,IAAI,EAAE;cACJb,OAAO,EAAE,IAAI;cACb7C,KAAK,EAAE,SAAS;cAChB2D,SAAS,EAAE;YACb,CAAC;YACDC,KAAK,EAAE;cACL5D,KAAK,EAAE,SAAS;cAChBuB,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF;UACF,CAAC;UACDS,CAAC,EAAE;YACDS,WAAW,EAAE,KAAK;YAClBgB,IAAI,EAAE;cACJb,OAAO,EAAE,IAAI;cACb7C,KAAK,EAAE,SAAS;cAChB2D,SAAS,EAAE;YACb,CAAC;YACDC,KAAK,EAAE;cACL5D,KAAK,EAAE,SAAS;cAChBuB,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR,CAAC;cACDqC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;gBACxB,OAAOA,KAAK,CAACxC,OAAO,CAAC,CAAC,CAAC;cACzB;YACF;UACF;QACF,CAAC;QACDqB,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACblB,QAAQ,EAAE,KAAK;YACfoC,MAAM,EAAE;cACN/D,KAAK,EAAE,SAAS;cAChBuB,IAAI,EAAE;gBACJC,IAAI,EAAE,EAAE;gBACRC,MAAM,EAAE;cACV;YACF;UACF,CAAC;UACDuC,OAAO,EAAE;YACPnD,eAAe,EAAE,oBAAoB;YACrCoD,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpBpD,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdoD,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAMrG,MAAM,GAAG4D,UAAU,CAACyC,SAAS,CAAC;gBACpC,OAAOrG,MAAM,CAACG,SAAS,GACrB,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACmG,kBAAkB,CAAC,OAAO,CAAC,GACtD,KAAKD,SAAS,GAAG,CAAC,MAAM;cAC5B,CAAC;cACDvC,KAAK,EAAGsC,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAACC,SAAS;gBACnC,MAAMrG,MAAM,GAAG4D,UAAU,CAACyC,SAAS,CAAC;gBACpC,OAAO,CACL,OAAOrG,MAAM,CAACM,CAAC,CAAC+C,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOrD,MAAM,CAACS,CAAC,CAAC4C,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOrD,MAAM,CAACW,CAAC,CAAC0C,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAOrD,MAAM,CAACa,CAAC,CAACwC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,QAAQrD,MAAM,CAACe,CAAC,CAACwF,cAAc,CAAC,CAAC,EAAE,CACpC;cACH;YACF;UACF,CAAC;UACD1B,UAAU,EAAE;YACVvD,WAAW,EAAEA;UACf;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA5D,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,QAAQ,CAACqH,OAAO,IAAI,CAACxH,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;;IAErD;IACA,IAAIV,aAAa,CAACoH,OAAO,EAAE;MACzBpH,aAAa,CAACoH,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;;IAEA;IACA,MAAMC,GAAG,GAAGvH,QAAQ,CAACqH,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGjD,iBAAiB,CAAC,CAAC;IAElCvE,aAAa,CAACoH,OAAO,GAAG,IAAIrI,KAAK,CAACuI,GAAG,EAAEE,MAAM,CAAC;IAE9C,OAAO,MAAM;MACX,IAAIxH,aAAa,CAACoH,OAAO,EAAE;QACzBpH,aAAa,CAACoH,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACzH,IAAI,EAAEC,QAAQ,EAAEI,YAAY,EAAEE,eAAe,EAAEE,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAE5E;EACA,MAAMkH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC5H,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACjD,MAAMgH,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9H,QAAQ,CAACc,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACnE,OAAOyF,WAAW,CAACE,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED,IAAI,CAAChI,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACEnB,OAAA,CAACd,IAAI;MAAAoJ,QAAA,eACHtI,OAAA;QAAKuI,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAE1D,OAAO,EAAE;QAAO,CAAE;QAAAwD,QAAA,eACnDtI,OAAA;UAAAsI,QAAA,EAAG;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE5I,OAAA;IAAAsI,QAAA,gBAEEtI,OAAA,CAACd,IAAI;MAAC0F,IAAI,EAAC,OAAO;MAAC2D,KAAK,EAAE;QAAEM,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eAC7CtI,OAAA;QAAKuI,KAAK,EAAE;UAAEtC,OAAO,EAAE,MAAM;UAAE6C,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAC/EtI,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACb,MAAM;YACL+H,KAAK,EAAEpG,SAAU;YACjBoI,QAAQ,EAAEnI,YAAa;YACvBwH,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAEtBtI,OAAA,CAACE,MAAM;cAACgH,KAAK,EAAC,aAAa;cAAAoB,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC5I,OAAA,CAACE,MAAM;cAACgH,KAAK,EAAC,MAAM;cAAAoB,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN5I,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACZ,MAAM;YACLgK,OAAO,EAAEpI,YAAa;YACtBkI,QAAQ,EAAEjI,eAAgB;YAC1BoI,eAAe,EAAC,cAAI;YACpBC,iBAAiB,EAAC;UAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN5I,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACZ,MAAM;YACLgK,OAAO,EAAE1I,YAAa;YACtBwI,QAAQ,EAAEvI,eAAgB;YAC1B0I,eAAe,EAAC,QAAG;YACnBC,iBAAiB,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELlI,YAAY,iBACXV,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACb,MAAM;YACL+H,KAAK,EAAEtG,eAAgB;YACvBsI,QAAQ,EAAErI,kBAAmB;YAC7B0H,KAAK,EAAE;cAAEY,KAAK,EAAE;YAAI,CAAE;YAAAb,QAAA,gBAEtBtI,OAAA,CAACE,MAAM;cAACgH,KAAK,EAAC,KAAK;cAAAoB,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCV,iBAAiB,CAAC,CAAC,CAAC9G,GAAG,CAACyB,OAAO,iBAC9B7C,OAAA,CAACE,MAAM;cAAegH,KAAK,EAAErE,OAAQ;cAAAyF,QAAA,EAAEzF;YAAO,GAAjCA,OAAO;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAED5I,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACV,GAAG;YAAC8D,KAAK,EAAC,MAAM;YAAAkF,QAAA,EAAEjI,IAAI,CAACc;UAAM;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAELtI,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9BnB,OAAA;UAAAsI,QAAA,gBACEtI,OAAA;YAAMuI,KAAK,EAAE;cAAEU,WAAW,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C5I,OAAA,CAACV,GAAG;YAAC8D,KAAK,EAAC,OAAO;YAAAkF,QAAA,EAAEhI,QAAQ,CAACa;UAAM;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5I,OAAA,CAACd,IAAI;MAAAoJ,QAAA,eACHtI,OAAA;QAAKuI,KAAK,EAAE;UAAEgB,MAAM,EAAE,OAAO;UAAExE,QAAQ,EAAE;QAAW,CAAE;QAAAuD,QAAA,eACpDtI,OAAA;UAAQwJ,GAAG,EAAEhJ;QAAS;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNlI,YAAY,IAAIJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9CnB,OAAA,CAACd,IAAI;MAAC0F,IAAI,EAAC,OAAO;MAAC2D,KAAK,EAAE;QAAEkB,SAAS,EAAE;MAAG,CAAE;MAAAnB,QAAA,eAC1CtI,OAAA;QAAKuI,KAAK,EAAE;UAAEtC,OAAO,EAAE,MAAM;UAAE+C,QAAQ,EAAE,MAAM;UAAED,GAAG,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACxDtI,OAAA;UAAMuI,KAAK,EAAE;YAAEmB,UAAU,EAAE,MAAM;YAAET,WAAW,EAAE;UAAG,CAAE;UAAAX,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjEV,iBAAiB,CAAC,CAAC,CAAC9G,GAAG,CAACmC,WAAW,IAAI;UACtC,MAAMV,OAAO,GAAGvC,QAAQ,CAACqJ,IAAI,CAAClH,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKa,WAAW,CAAC;UAClE,IAAI,CAACV,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAM+G,WAAW,GAAGzJ,aAAa,CAACoD,WAAW,CAAC,IAAIA,WAAW;UAC7D,MAAMH,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;UAC7C,oBACEtD,OAAA,CAACX,OAAO;YAAmBmI,KAAK,EAAE,GAAGoC,WAAW,KAAKrG,WAAW,OAAOV,OAAO,CAACgH,WAAW,IAAI,SAAS,EAAG;YAAAvB,QAAA,eACxGtI,OAAA,CAACV,GAAG;cACF8D,KAAK,EAAEP,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,OAAO,GAAGT,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;cAChGiF,KAAK,EAAE;gBAAEuB,MAAM,EAAE;cAAM,CAAE;cAAAxB,QAAA,EAExBsB;YAAW;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GANMrF,WAAW;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOhB,CAAC;QAEd,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrI,EAAA,CA9ZIH,gBAAgB;AAAA2J,EAAA,GAAhB3J,gBAAgB;AAgatB,eAAeA,gBAAgB;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}