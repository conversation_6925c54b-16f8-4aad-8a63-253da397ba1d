{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/src/components/CandlestickChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag } from 'antd';\nimport { Chart, registerables } from 'chart.js';\n\n// 注册Chart.js组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables);\nconst {\n  Option\n} = Select;\nconst CandlestickChart = ({\n  data,\n  patterns = []\n}) => {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n    return data.map((candle, index) => ({\n      x: index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    const filteredPatterns = selectedPattern === 'all' ? patterns : patterns.filter(p => p.pattern_name === selectedPattern);\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n      if (!startData || !endData) return null;\n      const color = getPatternColor(pattern.signal);\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = signal => {\n    switch (signal) {\n      case 'bullish':\n        return '#52c41a';\n      case 'bearish':\n        return '#ff4d4f';\n      case 'neutral':\n        return '#faad14';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: d.c\n            })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 蜡烛图配置（使用柱状图模拟）\n    return {\n      type: 'bar',\n      data: {\n        datasets: [{\n          label: '蜡烛图',\n          data: candleData.map(d => ({\n            x: d.x,\n            y: [d.l, d.o, d.c, d.h] // [low, open, close, high]\n          })),\n          backgroundColor: candleData.map(d => d.c >= d.o ? '#52c41a80' : '#ff4d4f80'),\n          borderColor: candleData.map(d => d.c >= d.o ? '#52c41a' : '#ff4d4f'),\n          borderWidth: 1\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          x: {\n            type: 'category'\n          },\n          y: {\n            beginAtZero: false\n          }\n        },\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              title: context => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString() : `K线 ${dataIndex + 1}`;\n              },\n              label: context => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [`开盘: ${candle.o.toFixed(2)}`, `最高: ${candle.h.toFixed(2)}`, `最低: ${candle.l.toFixed(2)}`, `收盘: ${candle.c.toFixed(2)}`, `成交量: ${candle.v.toLocaleString()}`];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    chartInstance.current = new Chart(ctx, config);\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u6570\\u636E\\uFF0C\\u8BF7\\u5148\\u4E0A\\u4F20\\u8721\\u70DB\\u56FE\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 16,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u56FE\\u8868\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartType,\n            onChange: setChartType,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"candlestick\",\n              children: \"\\u8721\\u70DB\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"line\",\n              children: \"\\u6298\\u7EBF\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u663E\\u793A\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showPatterns,\n            onChange: setShowPatterns,\n            checkedChildren: \"\\u5F00\",\n            unCheckedChildren: \"\\u5173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), showPatterns && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u5F62\\u6001\\u8FC7\\u6EE4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedPattern,\n            onChange: setSelectedPattern,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u5168\\u90E8\\u5F62\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), getUniquePatterns().map(pattern => /*#__PURE__*/_jsxDEV(Option, {\n              value: pattern,\n              children: pattern\n            }, pattern, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: data.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u8BC6\\u522B\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: patterns.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '500px',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: chartRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), showPatterns && patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginRight: 16\n          },\n          children: \"\\u5F62\\u6001\\u56FE\\u4F8B:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this), getUniquePatterns().map(patternName => {\n          const pattern = patterns.find(p => p.pattern_name === patternName);\n          if (!pattern) return null;\n          const color = getPatternColor(pattern.signal);\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: pattern.description,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange',\n              children: patternName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this)\n          }, patternName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 5\n  }, this);\n};\n_s(CandlestickChart, \"llFm+VVjJ94NgTFKA+uyKbKbJEc=\");\n_c = CandlestickChart;\nexport default CandlestickChart;\nvar _c;\n$RefreshReg$(_c, \"CandlestickChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "Select", "Switch", "<PERSON><PERSON><PERSON>", "Tag", "Chart", "registerables", "jsxDEV", "_jsxDEV", "register", "Option", "CandlestickChart", "data", "patterns", "_s", "chartRef", "chartInstance", "showPatterns", "setShowPatterns", "selectedPatt<PERSON>", "setSelectedPattern", "chartType", "setChartType", "processCandleData", "length", "map", "candle", "index", "x", "o", "parseFloat", "open", "h", "high", "l", "low", "c", "close", "v", "volume", "timestamp", "processPatternAnnotations", "filteredPatterns", "filter", "p", "pattern_name", "pattern", "startIndex", "start_index", "endIndex", "end_index", "startData", "endData", "color", "getPatternColor", "signal", "type", "xMin", "xMax", "yMin", "Math", "min", "yMax", "max", "backgroundColor", "borderColor", "borderWidth", "label", "content", "confidence", "toFixed", "enabled", "position", "Boolean", "createChartConfig", "candleData", "annotations", "datasets", "d", "y", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "time", "unit", "beginAtZero", "plugins", "legend", "display", "annotation", "tooltip", "callbacks", "title", "context", "dataIndex", "Date", "toLocaleDateString", "toLocaleString", "current", "destroy", "ctx", "getContext", "config", "getUniquePatterns", "uniqueNames", "Set", "sort", "children", "style", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "marginBottom", "alignItems", "gap", "flexWrap", "marginRight", "value", "onChange", "width", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "height", "ref", "marginTop", "fontWeight", "patternName", "find", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag } from 'antd';\nimport { Chart, registerables } from 'chart.js';\n\n// 注册Chart.js组件\nChart.register(...registerables);\n\nconst { Option } = Select;\n\nconst CandlestickChart = ({ data, patterns = [] }) => {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n\n    return data.map((candle, index) => ({\n      x: index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    \n    const filteredPatterns = selectedPattern === 'all' \n      ? patterns \n      : patterns.filter(p => p.pattern_name === selectedPattern);\n\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n      \n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n\n      if (!startData || !endData) return null;\n\n      const color = getPatternColor(pattern.signal);\n\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = (signal) => {\n    switch (signal) {\n      case 'bullish': return '#52c41a';\n      case 'bearish': return '#ff4d4f';\n      case 'neutral': return '#faad14';\n      default: return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({ x: d.x, y: d.c })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 蜡烛图配置（使用柱状图模拟）\n    return {\n      type: 'bar',\n      data: {\n        datasets: [\n          {\n            label: '蜡烛图',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: [d.l, d.o, d.c, d.h] // [low, open, close, high]\n            })),\n            backgroundColor: candleData.map(d => d.c >= d.o ? '#52c41a80' : '#ff4d4f80'),\n            borderColor: candleData.map(d => d.c >= d.o ? '#52c41a' : '#ff4d4f'),\n            borderWidth: 1\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        scales: {\n          x: {\n            type: 'category'\n          },\n          y: {\n            beginAtZero: false\n          }\n        },\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            callbacks: {\n              title: (context) => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString() : `K线 ${dataIndex + 1}`;\n              },\n              label: (context) => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [\n                  `开盘: ${candle.o.toFixed(2)}`,\n                  `最高: ${candle.h.toFixed(2)}`,\n                  `最低: ${candle.l.toFixed(2)}`,\n                  `收盘: ${candle.c.toFixed(2)}`,\n                  `成交量: ${candle.v.toLocaleString()}`\n                ];\n              }\n            }\n          },\n          annotation: {\n            annotations: annotations\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    \n    chartInstance.current = new Chart(ctx, config);\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n\n  if (!data || data.length === 0) {\n    return (\n      <Card>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <p>暂无数据，请先上传蜡烛图数据</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div>\n      {/* 控制面板 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>\n          <div>\n            <span style={{ marginRight: 8 }}>图表类型:</span>\n            <Select \n              value={chartType} \n              onChange={setChartType}\n              style={{ width: 120 }}\n            >\n              <Option value=\"candlestick\">蜡烛图</Option>\n              <Option value=\"line\">折线图</Option>\n            </Select>\n          </div>\n          \n          <div>\n            <span style={{ marginRight: 8 }}>显示形态:</span>\n            <Switch \n              checked={showPatterns} \n              onChange={setShowPatterns}\n              checkedChildren=\"开\"\n              unCheckedChildren=\"关\"\n            />\n          </div>\n          \n          {showPatterns && (\n            <div>\n              <span style={{ marginRight: 8 }}>形态过滤:</span>\n              <Select \n                value={selectedPattern} \n                onChange={setSelectedPattern}\n                style={{ width: 150 }}\n              >\n                <Option value=\"all\">全部形态</Option>\n                {getUniquePatterns().map(pattern => (\n                  <Option key={pattern} value={pattern}>{pattern}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n          \n          <div>\n            <span style={{ marginRight: 8 }}>数据点数:</span>\n            <Tag color=\"blue\">{data.length}</Tag>\n          </div>\n          \n          {patterns && patterns.length > 0 && (\n            <div>\n              <span style={{ marginRight: 8 }}>识别形态:</span>\n              <Tag color=\"green\">{patterns.length}</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 图表区域 */}\n      <Card>\n        <div style={{ height: '500px', position: 'relative' }}>\n          <canvas ref={chartRef} />\n        </div>\n      </Card>\n\n      {/* 形态图例 */}\n      {showPatterns && patterns && patterns.length > 0 && (\n        <Card size=\"small\" style={{ marginTop: 16 }}>\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>\n            <span style={{ fontWeight: 'bold', marginRight: 16 }}>形态图例:</span>\n            {getUniquePatterns().map(patternName => {\n              const pattern = patterns.find(p => p.pattern_name === patternName);\n              if (!pattern) return null;\n              \n              const color = getPatternColor(pattern.signal);\n              return (\n                <Tooltip key={patternName} title={pattern.description}>\n                  <Tag \n                    color={pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange'}\n                  >\n                    {patternName}\n                  </Tag>\n                </Tooltip>\n              );\n            })}\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default CandlestickChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,QAAQ,MAAM;AACzD,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAEhC,MAAM;EAAEI;AAAO,CAAC,GAAGT,MAAM;AAEzB,MAAMU,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkB,aAAa,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,aAAa,CAAC;;EAEzD;EACA,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACX,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOZ,IAAI,CAACa,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;MAClCC,CAAC,EAAED,KAAK;MACRE,CAAC,EAAEC,UAAU,CAACJ,MAAM,CAACK,IAAI,CAAC;MAC1BC,CAAC,EAAEF,UAAU,CAACJ,MAAM,CAACO,IAAI,CAAC;MAC1BC,CAAC,EAAEJ,UAAU,CAACJ,MAAM,CAACS,GAAG,CAAC;MACzBC,CAAC,EAAEN,UAAU,CAACJ,MAAM,CAACW,KAAK,CAAC;MAC3BC,CAAC,EAAER,UAAU,CAACJ,MAAM,CAACa,MAAM,IAAI,IAAI,CAAC;MACpCC,SAAS,EAAEd,MAAM,CAACc;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACxB,YAAY,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAElE,MAAMkB,gBAAgB,GAAGvB,eAAe,KAAK,KAAK,GAC9CN,QAAQ,GACRA,QAAQ,CAAC8B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK1B,eAAe,CAAC;IAE5D,OAAOuB,gBAAgB,CAACjB,GAAG,CAAC,CAACqB,OAAO,EAAEnB,KAAK,KAAK;MAC9C,MAAMoB,UAAU,GAAGD,OAAO,CAACE,WAAW,IAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGH,OAAO,CAACI,SAAS,IAAIH,UAAU;;MAEhD;MACA,MAAMI,SAAS,GAAGvC,IAAI,CAACmC,UAAU,CAAC;MAClC,MAAMK,OAAO,GAAGxC,IAAI,CAACqC,QAAQ,CAAC;MAE9B,IAAI,CAACE,SAAS,IAAI,CAACC,OAAO,EAAE,OAAO,IAAI;MAEvC,MAAMC,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;MAE7C,OAAO;QACLC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAEV,UAAU;QAChBW,IAAI,EAAET,QAAQ;QACdU,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACV,SAAS,CAAChB,GAAG,EAAEiB,OAAO,CAACjB,GAAG,CAAC,GAAG,KAAK;QAClD2B,IAAI,EAAEF,IAAI,CAACG,GAAG,CAACZ,SAAS,CAAClB,IAAI,EAAEmB,OAAO,CAACnB,IAAI,CAAC,GAAG,KAAK;QACpD+B,eAAe,EAAEX,KAAK,GAAG,IAAI;QAC7BY,WAAW,EAAEZ,KAAK;QAClBa,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLC,OAAO,EAAE,GAAGtB,OAAO,CAACD,YAAY,KAAK,CAACC,OAAO,CAACuB,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;UAC9EC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE;QACZ;MACF,CAAC;IACH,CAAC,CAAC,CAAC7B,MAAM,CAAC8B,OAAO,CAAC;EACpB,CAAC;;EAED;EACA,MAAMnB,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAGpD,iBAAiB,CAAC,CAAC;IACtC,MAAMqD,WAAW,GAAGnC,yBAAyB,CAAC,CAAC;IAE/C,IAAIpB,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO;QACLmC,IAAI,EAAE,MAAM;QACZ5C,IAAI,EAAE;UACJiE,QAAQ,EAAE,CAAC;YACTV,KAAK,EAAE,KAAK;YACZvD,IAAI,EAAE+D,UAAU,CAAClD,GAAG,CAACqD,CAAC,KAAK;cAAElD,CAAC,EAAEkD,CAAC,CAAClD,CAAC;cAAEmD,CAAC,EAAED,CAAC,CAAC1C;YAAE,CAAC,CAAC,CAAC;YAC/C6B,WAAW,EAAE,SAAS;YACtBD,eAAe,EAAE,WAAW;YAC5BgB,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;UACX,CAAC;QACH,CAAC;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACNzD,CAAC,EAAE;cACD4B,IAAI,EAAE,MAAM;cACZ8B,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF,CAAC;YACDR,CAAC,EAAE;cACDS,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;YACX,CAAC;YACDC,UAAU,EAAE;cACVhB,WAAW,EAAEA;YACf;UACF;QACF;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLpB,IAAI,EAAE,KAAK;MACX5C,IAAI,EAAE;QACJiE,QAAQ,EAAE,CACR;UACEV,KAAK,EAAE,KAAK;UACZvD,IAAI,EAAE+D,UAAU,CAAClD,GAAG,CAACqD,CAAC,KAAK;YACzBlD,CAAC,EAAEkD,CAAC,CAAClD,CAAC;YACNmD,CAAC,EAAE,CAACD,CAAC,CAAC5C,CAAC,EAAE4C,CAAC,CAACjD,CAAC,EAAEiD,CAAC,CAAC1C,CAAC,EAAE0C,CAAC,CAAC9C,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC;UACHgC,eAAe,EAAEW,UAAU,CAAClD,GAAG,CAACqD,CAAC,IAAIA,CAAC,CAAC1C,CAAC,IAAI0C,CAAC,CAACjD,CAAC,GAAG,WAAW,GAAG,WAAW,CAAC;UAC5EoC,WAAW,EAAEU,UAAU,CAAClD,GAAG,CAACqD,CAAC,IAAIA,CAAC,CAAC1C,CAAC,IAAI0C,CAAC,CAACjD,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;UACpEqC,WAAW,EAAE;QACf,CAAC;MAEL,CAAC;MACDgB,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;UACNzD,CAAC,EAAE;YACD4B,IAAI,EAAE;UACR,CAAC;UACDuB,CAAC,EAAE;YACDS,WAAW,EAAE;UACf;QACF,CAAC;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE;UACX,CAAC;UACDE,OAAO,EAAE;YACPC,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAMvE,MAAM,GAAGiD,UAAU,CAACsB,SAAS,CAAC;gBACpC,OAAOvE,MAAM,CAACc,SAAS,GAAG,IAAI0D,IAAI,CAACxE,MAAM,CAACc,SAAS,CAAC,CAAC2D,kBAAkB,CAAC,CAAC,GAAG,MAAMF,SAAS,GAAG,CAAC,EAAE;cACnG,CAAC;cACD9B,KAAK,EAAG6B,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAACC,SAAS;gBACnC,MAAMvE,MAAM,GAAGiD,UAAU,CAACsB,SAAS,CAAC;gBACpC,OAAO,CACL,OAAOvE,MAAM,CAACG,CAAC,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO5C,MAAM,CAACM,CAAC,CAACsC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO5C,MAAM,CAACQ,CAAC,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO5C,MAAM,CAACU,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,QAAQ5C,MAAM,CAACY,CAAC,CAAC8D,cAAc,CAAC,CAAC,EAAE,CACpC;cACH;YACF;UACF,CAAC;UACDR,UAAU,EAAE;YACVhB,WAAW,EAAEA;UACf;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACA/E,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,QAAQ,CAACsF,OAAO,IAAI,CAACzF,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;;IAErD;IACA,IAAIR,aAAa,CAACqF,OAAO,EAAE;MACzBrF,aAAa,CAACqF,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;;IAEA;IACA,MAAMC,GAAG,GAAGxF,QAAQ,CAACsF,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAG/B,iBAAiB,CAAC,CAAC;IAElC1D,aAAa,CAACqF,OAAO,GAAG,IAAIhG,KAAK,CAACkG,GAAG,EAAEE,MAAM,CAAC;IAE9C,OAAO,MAAM;MACX,IAAIzF,aAAa,CAACqF,OAAO,EAAE;QACzBrF,aAAa,CAACqF,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAAC1F,IAAI,EAAEC,QAAQ,EAAEI,YAAY,EAAEE,eAAe,EAAEE,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAMqF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC7F,QAAQ,IAAIA,QAAQ,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACjD,MAAMmF,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC/F,QAAQ,CAACY,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACnE,OAAO8D,WAAW,CAACE,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED,IAAI,CAACjG,IAAI,IAAIA,IAAI,CAACY,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACEhB,OAAA,CAACR,IAAI;MAAA8G,QAAA,eACHtG,OAAA;QAAKuG,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eACnDtG,OAAA;UAAAsG,QAAA,EAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACE7G,OAAA;IAAAsG,QAAA,gBAEEtG,OAAA,CAACR,IAAI;MAACsH,IAAI,EAAC,OAAO;MAACP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAG,CAAE;MAAAT,QAAA,eAC7CtG,OAAA;QAAKuG,KAAK,EAAE;UAAEpB,OAAO,EAAE,MAAM;UAAE6B,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAC/EtG,OAAA;UAAAsG,QAAA,gBACEtG,OAAA;YAAMuG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7G,OAAA,CAACP,MAAM;YACL2H,KAAK,EAAEvG,SAAU;YACjBwG,QAAQ,EAAEvG,YAAa;YACvByF,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAI,CAAE;YAAAhB,QAAA,gBAEtBtG,OAAA,CAACE,MAAM;cAACkH,KAAK,EAAC,aAAa;cAAAd,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7G,OAAA,CAACE,MAAM;cAACkH,KAAK,EAAC,MAAM;cAAAd,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7G,OAAA;UAAAsG,QAAA,gBACEtG,OAAA;YAAMuG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7G,OAAA,CAACN,MAAM;YACL6H,OAAO,EAAE9G,YAAa;YACtB4G,QAAQ,EAAE3G,eAAgB;YAC1B8G,eAAe,EAAC,QAAG;YACnBC,iBAAiB,EAAC;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELpG,YAAY,iBACXT,OAAA;UAAAsG,QAAA,gBACEtG,OAAA;YAAMuG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7G,OAAA,CAACP,MAAM;YACL2H,KAAK,EAAEzG,eAAgB;YACvB0G,QAAQ,EAAEzG,kBAAmB;YAC7B2F,KAAK,EAAE;cAAEe,KAAK,EAAE;YAAI,CAAE;YAAAhB,QAAA,gBAEtBtG,OAAA,CAACE,MAAM;cAACkH,KAAK,EAAC,KAAK;cAAAd,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCX,iBAAiB,CAAC,CAAC,CAACjF,GAAG,CAACqB,OAAO,iBAC9BtC,OAAA,CAACE,MAAM;cAAekH,KAAK,EAAE9E,OAAQ;cAAAgE,QAAA,EAAEhE;YAAO,GAAjCA,OAAO;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAED7G,OAAA;UAAAsG,QAAA,gBACEtG,OAAA;YAAMuG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7G,OAAA,CAACJ,GAAG;YAACiD,KAAK,EAAC,MAAM;YAAAyD,QAAA,EAAElG,IAAI,CAACY;UAAM;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAELxG,QAAQ,IAAIA,QAAQ,CAACW,MAAM,GAAG,CAAC,iBAC9BhB,OAAA;UAAAsG,QAAA,gBACEtG,OAAA;YAAMuG,KAAK,EAAE;cAAEY,WAAW,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C7G,OAAA,CAACJ,GAAG;YAACiD,KAAK,EAAC,OAAO;YAAAyD,QAAA,EAAEjG,QAAQ,CAACW;UAAM;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP7G,OAAA,CAACR,IAAI;MAAA8G,QAAA,eACHtG,OAAA;QAAKuG,KAAK,EAAE;UAAEmB,MAAM,EAAE,OAAO;UAAE1D,QAAQ,EAAE;QAAW,CAAE;QAAAsC,QAAA,eACpDtG,OAAA;UAAQ2H,GAAG,EAAEpH;QAAS;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNpG,YAAY,IAAIJ,QAAQ,IAAIA,QAAQ,CAACW,MAAM,GAAG,CAAC,iBAC9ChB,OAAA,CAACR,IAAI;MAACsH,IAAI,EAAC,OAAO;MAACP,KAAK,EAAE;QAAEqB,SAAS,EAAE;MAAG,CAAE;MAAAtB,QAAA,eAC1CtG,OAAA;QAAKuG,KAAK,EAAE;UAAEpB,OAAO,EAAE,MAAM;UAAE+B,QAAQ,EAAE,MAAM;UAAED,GAAG,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACxDtG,OAAA;UAAMuG,KAAK,EAAE;YAAEsB,UAAU,EAAE,MAAM;YAAEV,WAAW,EAAE;UAAG,CAAE;UAAAb,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjEX,iBAAiB,CAAC,CAAC,CAACjF,GAAG,CAAC6G,WAAW,IAAI;UACtC,MAAMxF,OAAO,GAAGjC,QAAQ,CAAC0H,IAAI,CAAC3F,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKyF,WAAW,CAAC;UAClE,IAAI,CAACxF,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMO,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;UAC7C,oBACE/C,OAAA,CAACL,OAAO;YAAmB4F,KAAK,EAAEjD,OAAO,CAAC0F,WAAY;YAAA1B,QAAA,eACpDtG,OAAA,CAACJ,GAAG;cACFiD,KAAK,EAAEP,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,OAAO,GAAGT,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;cAAAuD,QAAA,EAE/FwB;YAAW;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GALMiB,WAAW;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMhB,CAAC;QAEd,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvG,EAAA,CA9SIH,gBAAgB;AAAA8H,EAAA,GAAhB9H,gBAAgB;AAgTtB,eAAeA,gBAAgB;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}