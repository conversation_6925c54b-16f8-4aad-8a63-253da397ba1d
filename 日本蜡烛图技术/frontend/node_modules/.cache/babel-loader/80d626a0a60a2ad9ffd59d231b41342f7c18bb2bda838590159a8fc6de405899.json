{"ast": null, "code": "import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}", "map": {"version": 3, "names": ["Zone", "InvalidZone", "constructor", "zoneName", "type", "name", "isUniversal", "offsetName", "formatOffset", "offset", "NaN", "equals", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/zones/invalidZone.js"], "sourcesContent": ["import Zone from \"../zone.js\";\n\n/**\n * A zone that failed to parse. You should never need to instantiate this.\n * @implements {Zone}\n */\nexport default class InvalidZone extends Zone {\n  constructor(zoneName) {\n    super();\n    /**  @private */\n    this.zoneName = zoneName;\n  }\n\n  /** @override **/\n  get type() {\n    return \"invalid\";\n  }\n\n  /** @override **/\n  get name() {\n    return this.zoneName;\n  }\n\n  /** @override **/\n  get isUniversal() {\n    return false;\n  }\n\n  /** @override **/\n  offsetName() {\n    return null;\n  }\n\n  /** @override **/\n  formatOffset() {\n    return \"\";\n  }\n\n  /** @override **/\n  offset() {\n    return NaN;\n  }\n\n  /** @override **/\n  equals() {\n    return false;\n  }\n\n  /** @override **/\n  get isValid() {\n    return false;\n  }\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA;AACA;AACA;AACA,eAAe,MAAMC,WAAW,SAASD,IAAI,CAAC;EAC5CE,WAAWA,CAACC,QAAQ,EAAE;IACpB,KAAK,CAAC,CAAC;IACP;IACA,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;;EAEA;EACA,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,SAAS;EAClB;;EAEA;EACA,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,QAAQ;EACtB;;EAEA;EACA,IAAIG,WAAWA,CAAA,EAAG;IAChB,OAAO,KAAK;EACd;;EAEA;EACAC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI;EACb;;EAEA;EACAC,YAAYA,CAAA,EAAG;IACb,OAAO,EAAE;EACX;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,OAAOC,GAAG;EACZ;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,KAAK;EACd;;EAEA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACZ,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}