{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport OrderContext from \"./Context\";\nvar EMPTY_LIST = [];\n\n/**\n * Will add `div` to document. Nest call will keep order\n * @param render Render DOM in document\n */\nexport default function useDom(render, debug) {\n  var _React$useState = React.useState(function () {\n      if (!canUseDom()) {\n        return null;\n      }\n      var defaultEle = document.createElement('div');\n      if (process.env.NODE_ENV !== 'production' && debug) {\n        defaultEle.setAttribute('data-debug', debug);\n      }\n      return defaultEle;\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    ele = _React$useState2[0];\n\n  // ========================== Order ==========================\n  var appendedRef = React.useRef(false);\n  var queueCreate = React.useContext(OrderContext);\n  var _React$useState3 = React.useState(EMPTY_LIST),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    queue = _React$useState4[0],\n    setQueue = _React$useState4[1];\n  var mergedQueueCreate = queueCreate || (appendedRef.current ? undefined : function (appendFn) {\n    setQueue(function (origin) {\n      var newQueue = [appendFn].concat(_toConsumableArray(origin));\n      return newQueue;\n    });\n  });\n\n  // =========================== DOM ===========================\n  function append() {\n    if (!ele.parentElement) {\n      document.body.appendChild(ele);\n    }\n    appendedRef.current = true;\n  }\n  function cleanup() {\n    var _ele$parentElement;\n    (_ele$parentElement = ele.parentElement) === null || _ele$parentElement === void 0 ? void 0 : _ele$parentElement.removeChild(ele);\n    appendedRef.current = false;\n  }\n  useLayoutEffect(function () {\n    if (render) {\n      if (queueCreate) {\n        queueCreate(append);\n      } else {\n        append();\n      }\n    } else {\n      cleanup();\n    }\n    return cleanup;\n  }, [render]);\n  useLayoutEffect(function () {\n    if (queue.length) {\n      queue.forEach(function (appendFn) {\n        return appendFn();\n      });\n      setQueue(EMPTY_LIST);\n    }\n  }, [queue]);\n  return [ele, mergedQueueCreate];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "React", "useLayoutEffect", "canUseDom", "OrderContext", "EMPTY_LIST", "useDom", "render", "debug", "_React$useState", "useState", "defaultEle", "document", "createElement", "process", "env", "NODE_ENV", "setAttribute", "_React$useState2", "ele", "appendedRef", "useRef", "queueCreate", "useContext", "_React$useState3", "_React$useState4", "queue", "setQueue", "mergedQueueCreate", "current", "undefined", "appendFn", "origin", "newQueue", "concat", "append", "parentElement", "body", "append<PERSON><PERSON><PERSON>", "cleanup", "_ele$parentElement", "<PERSON><PERSON><PERSON><PERSON>", "length", "for<PERSON>ach"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/@rc-component/portal/es/useDom.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nimport OrderContext from \"./Context\";\nvar EMPTY_LIST = [];\n\n/**\n * Will add `div` to document. Nest call will keep order\n * @param render Render DOM in document\n */\nexport default function useDom(render, debug) {\n  var _React$useState = React.useState(function () {\n      if (!canUseDom()) {\n        return null;\n      }\n      var defaultEle = document.createElement('div');\n      if (process.env.NODE_ENV !== 'production' && debug) {\n        defaultEle.setAttribute('data-debug', debug);\n      }\n      return defaultEle;\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    ele = _React$useState2[0];\n\n  // ========================== Order ==========================\n  var appendedRef = React.useRef(false);\n  var queueCreate = React.useContext(OrderContext);\n  var _React$useState3 = React.useState(EMPTY_LIST),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    queue = _React$useState4[0],\n    setQueue = _React$useState4[1];\n  var mergedQueueCreate = queueCreate || (appendedRef.current ? undefined : function (appendFn) {\n    setQueue(function (origin) {\n      var newQueue = [appendFn].concat(_toConsumableArray(origin));\n      return newQueue;\n    });\n  });\n\n  // =========================== DOM ===========================\n  function append() {\n    if (!ele.parentElement) {\n      document.body.appendChild(ele);\n    }\n    appendedRef.current = true;\n  }\n  function cleanup() {\n    var _ele$parentElement;\n    (_ele$parentElement = ele.parentElement) === null || _ele$parentElement === void 0 ? void 0 : _ele$parentElement.removeChild(ele);\n    appendedRef.current = false;\n  }\n  useLayoutEffect(function () {\n    if (render) {\n      if (queueCreate) {\n        queueCreate(append);\n      } else {\n        append();\n      }\n    } else {\n      cleanup();\n    }\n    return cleanup;\n  }, [render]);\n  useLayoutEffect(function () {\n    if (queue.length) {\n      queue.forEach(function (appendFn) {\n        return appendFn();\n      });\n      setQueue(EMPTY_LIST);\n    }\n  }, [queue]);\n  return [ele, mergedQueueCreate];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,YAAY,MAAM,WAAW;AACpC,IAAIC,UAAU,GAAG,EAAE;;AAEnB;AACA;AACA;AACA;AACA,eAAe,SAASC,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC5C,IAAIC,eAAe,GAAGR,KAAK,CAACS,QAAQ,CAAC,YAAY;MAC7C,IAAI,CAACP,SAAS,CAAC,CAAC,EAAE;QAChB,OAAO,IAAI;MACb;MACA,IAAIQ,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC9C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIR,KAAK,EAAE;QAClDG,UAAU,CAACM,YAAY,CAAC,YAAY,EAAET,KAAK,CAAC;MAC9C;MACA,OAAOG,UAAU;IACnB,CAAC,CAAC;IACFO,gBAAgB,GAAGlB,cAAc,CAACS,eAAe,EAAE,CAAC,CAAC;IACrDU,GAAG,GAAGD,gBAAgB,CAAC,CAAC,CAAC;;EAE3B;EACA,IAAIE,WAAW,GAAGnB,KAAK,CAACoB,MAAM,CAAC,KAAK,CAAC;EACrC,IAAIC,WAAW,GAAGrB,KAAK,CAACsB,UAAU,CAACnB,YAAY,CAAC;EAChD,IAAIoB,gBAAgB,GAAGvB,KAAK,CAACS,QAAQ,CAACL,UAAU,CAAC;IAC/CoB,gBAAgB,GAAGzB,cAAc,CAACwB,gBAAgB,EAAE,CAAC,CAAC;IACtDE,KAAK,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3BE,QAAQ,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIG,iBAAiB,GAAGN,WAAW,KAAKF,WAAW,CAACS,OAAO,GAAGC,SAAS,GAAG,UAAUC,QAAQ,EAAE;IAC5FJ,QAAQ,CAAC,UAAUK,MAAM,EAAE;MACzB,IAAIC,QAAQ,GAAG,CAACF,QAAQ,CAAC,CAACG,MAAM,CAACnC,kBAAkB,CAACiC,MAAM,CAAC,CAAC;MAC5D,OAAOC,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,SAASE,MAAMA,CAAA,EAAG;IAChB,IAAI,CAAChB,GAAG,CAACiB,aAAa,EAAE;MACtBxB,QAAQ,CAACyB,IAAI,CAACC,WAAW,CAACnB,GAAG,CAAC;IAChC;IACAC,WAAW,CAACS,OAAO,GAAG,IAAI;EAC5B;EACA,SAASU,OAAOA,CAAA,EAAG;IACjB,IAAIC,kBAAkB;IACtB,CAACA,kBAAkB,GAAGrB,GAAG,CAACiB,aAAa,MAAM,IAAI,IAAII,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,WAAW,CAACtB,GAAG,CAAC;IACjIC,WAAW,CAACS,OAAO,GAAG,KAAK;EAC7B;EACA3B,eAAe,CAAC,YAAY;IAC1B,IAAIK,MAAM,EAAE;MACV,IAAIe,WAAW,EAAE;QACfA,WAAW,CAACa,MAAM,CAAC;MACrB,CAAC,MAAM;QACLA,MAAM,CAAC,CAAC;MACV;IACF,CAAC,MAAM;MACLI,OAAO,CAAC,CAAC;IACX;IACA,OAAOA,OAAO;EAChB,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;EACZL,eAAe,CAAC,YAAY;IAC1B,IAAIwB,KAAK,CAACgB,MAAM,EAAE;MAChBhB,KAAK,CAACiB,OAAO,CAAC,UAAUZ,QAAQ,EAAE;QAChC,OAAOA,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACFJ,QAAQ,CAACtB,UAAU,CAAC;IACtB;EACF,CAAC,EAAE,CAACqB,KAAK,CAAC,CAAC;EACX,OAAO,CAACP,GAAG,EAAES,iBAAiB,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}