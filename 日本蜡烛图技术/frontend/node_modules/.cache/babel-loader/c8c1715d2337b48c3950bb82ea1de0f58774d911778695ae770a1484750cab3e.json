{"ast": null, "code": "/*!\n* chartjs-plugin-annotation v3.1.0\n* https://www.chartjs.org/chartjs-plugin-annotation/index\n * (c) 2024 chartjs-plugin-annotation Contributors\n * Released under the MIT License\n */\nimport { Element, DoughnutController, defaults, Animations, Chart } from 'chart.js';\nimport { distanceBetweenPoints, toRadians, isObject, valueOrDefault, defined, isFunction, callback, isArray, toFont, addRoundedRectPath, toTRBLCorners, QUARTER_PI, PI, HALF_PI, TWO_THIRDS_PI, TAU, isNumber, RAD_PER_DEG, toPadding, isFinite, getAngleFromPoint, toDegrees, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * @typedef { import(\"chart.js\").ChartEvent } ChartEvent\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst interaction = {\n  modes: {\n    /**\n     * Point mode returns all elements that hit test based on the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    point(visibleElements, event) {\n      return filterElements(visibleElements, event, {\n        intersect: true\n      });\n    },\n    /**\n     * Nearest mode returns the element closest to the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found (only 1 element)\n     */\n    nearest(visibleElements, event, options) {\n      return getNearestItem(visibleElements, event, options);\n    },\n    /**\n     * x mode returns the elements that hit-test at the current x coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    x(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {\n        intersect: options.intersect,\n        axis: 'x'\n      });\n    },\n    /**\n     * y mode returns the elements that hit-test at the current y coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    y(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {\n        intersect: options.intersect,\n        axis: 'y'\n      });\n    }\n  }\n};\n\n/**\n * Returns all elements that hit test based on the event position\n * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n * @param {ChartEvent} event - the event we are find things at\n * @param {Object} options - interaction options to use\n * @return {AnnotationElement[]} - elements that are found\n */\nfunction getElements(visibleElements, event, options) {\n  const mode = interaction.modes[options.mode] || interaction.modes.nearest;\n  return mode(visibleElements, event, options);\n}\nfunction inRangeByAxis(element, event, axis) {\n  if (axis !== 'x' && axis !== 'y') {\n    return element.inRange(event.x, event.y, 'x', true) || element.inRange(event.x, event.y, 'y', true);\n  }\n  return element.inRange(event.x, event.y, axis, true);\n}\nfunction getPointByAxis(event, center, axis) {\n  if (axis === 'x') {\n    return {\n      x: event.x,\n      y: center.y\n    };\n  } else if (axis === 'y') {\n    return {\n      x: center.x,\n      y: event.y\n    };\n  }\n  return center;\n}\nfunction filterElements(visibleElements, event, options) {\n  return visibleElements.filter(element => options.intersect ? element.inRange(event.x, event.y) : inRangeByAxis(element, event, options.axis));\n}\nfunction getNearestItem(visibleElements, event, options) {\n  let minDistance = Number.POSITIVE_INFINITY;\n  return filterElements(visibleElements, event, options).reduce((nearestItems, element) => {\n    const center = element.getCenterPoint();\n    const evenPoint = getPointByAxis(event, center, options.axis);\n    const distance = distanceBetweenPoints(event, evenPoint);\n    if (distance < minDistance) {\n      nearestItems = [element];\n      minDistance = distance;\n    } else if (distance === minDistance) {\n      // Can have multiple items at the same distance in which case we sort by size\n      nearestItems.push(element);\n    }\n    return nearestItems;\n  }, []).sort((a, b) => a._index - b._index).slice(0, 1); // return only the top item;\n}\n\n/**\n * @typedef {import('chart.js').Point} Point\n */\n\n/**\n * Rotate a `point` relative to `center` point by `angle`\n * @param {Point} point - the point to rotate\n * @param {Point} center - center point for rotation\n * @param {number} angle - angle for rotation, in radians\n * @returns {Point} rotated point\n */\nfunction rotated(point, center, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const cx = center.x;\n  const cy = center.y;\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\nconst isOlderPart = (act, req) => req > act || act.length > req.length && act.slice(0, req.length) === req;\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').InteractionAxis } InteractionAxis\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst EPSILON = 0.001;\nconst clamp = (x, from, to) => Math.min(to, Math.max(from, x));\n\n/**\n * @param {{value: number, start: number, end: number}} limit\n * @param {number} hitSize\n * @returns {boolean}\n */\nconst inLimit = (limit, hitSize) => limit.value >= limit.start - hitSize && limit.value <= limit.end + hitSize;\n\n/**\n * @param {Object} obj\n * @param {number} from\n * @param {number} to\n * @returns {Object}\n */\nfunction clampAll(obj, from, to) {\n  for (const key of Object.keys(obj)) {\n    obj[key] = clamp(obj[key], from, to);\n  }\n  return obj;\n}\n\n/**\n * @param {Point} point\n * @param {Point} center\n * @param {number} radius\n * @param {number} hitSize\n * @returns {boolean}\n */\nfunction inPointRange(point, center, radius, hitSize) {\n  if (!point || !center || radius <= 0) {\n    return false;\n  }\n  return Math.pow(point.x - center.x, 2) + Math.pow(point.y - center.y, 2) <= Math.pow(radius + hitSize, 2);\n}\n\n/**\n * @param {Point} point\n * @param {{x: number, y: number, x2: number, y2: number}} rect\n * @param {InteractionAxis} axis\n * @param {{borderWidth: number, hitTolerance: number}} hitsize\n * @returns {boolean}\n */\nfunction inBoxRange(point, {\n  x,\n  y,\n  x2,\n  y2\n}, axis, {\n  borderWidth,\n  hitTolerance\n}) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const inRangeX = point.x >= x - hitSize - EPSILON && point.x <= x2 + hitSize + EPSILON;\n  const inRangeY = point.y >= y - hitSize - EPSILON && point.y <= y2 + hitSize + EPSILON;\n  if (axis === 'x') {\n    return inRangeX;\n  } else if (axis === 'y') {\n    return inRangeY;\n  }\n  return inRangeX && inRangeY;\n}\n\n/**\n * @param {Point} point\n * @param {rect: {x: number, y: number, x2: number, y2: number}, center: {x: number, y: number}} element\n * @param {InteractionAxis} axis\n * @param {{rotation: number, borderWidth: number, hitTolerance: number}}\n * @returns {boolean}\n */\nfunction inLabelRange(point, {\n  rect,\n  center\n}, axis, {\n  rotation,\n  borderWidth,\n  hitTolerance\n}) {\n  const rotPoint = rotated(point, center, toRadians(-rotation));\n  return inBoxRange(rotPoint, rect, axis, {\n    borderWidth,\n    hitTolerance\n  });\n}\n\n/**\n * @param {AnnotationElement} element\n * @param {boolean} useFinalPosition\n * @returns {Point}\n */\nfunction getElementCenterPoint(element, useFinalPosition) {\n  const {\n    centerX,\n    centerY\n  } = element.getProps(['centerX', 'centerY'], useFinalPosition);\n  return {\n    x: centerX,\n    y: centerY\n  };\n}\n\n/**\n * @param {string} pkg\n * @param {string} min\n * @param {string} ver\n * @param {boolean} [strict=true]\n * @returns {boolean}\n */\nfunction requireVersion(pkg, min, ver, strict = true) {\n  const parts = ver.split('.');\n  let i = 0;\n  for (const req of min.split('.')) {\n    const act = parts[i++];\n    if (parseInt(req, 10) < parseInt(act, 10)) {\n      break;\n    }\n    if (isOlderPart(act, req)) {\n      if (strict) {\n        throw new Error(`${pkg} v${ver} is not supported. v${min} or newer is required.`);\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nconst isPercentString = s => typeof s === 'string' && s.endsWith('%');\nconst toPercent = s => parseFloat(s) / 100;\nconst toPositivePercent = s => clamp(toPercent(s), 0, 1);\nconst boxAppering = (x, y) => ({\n  x,\n  y,\n  x2: x,\n  y2: y,\n  width: 0,\n  height: 0\n});\nconst defaultInitAnimation = {\n  box: properties => boxAppering(properties.centerX, properties.centerY),\n  doughnutLabel: properties => boxAppering(properties.centerX, properties.centerY),\n  ellipse: properties => ({\n    centerX: properties.centerX,\n    centerY: properties.centerX,\n    radius: 0,\n    width: 0,\n    height: 0\n  }),\n  label: properties => boxAppering(properties.centerX, properties.centerY),\n  line: properties => boxAppering(properties.x, properties.y),\n  point: properties => ({\n    centerX: properties.centerX,\n    centerY: properties.centerY,\n    radius: 0,\n    width: 0,\n    height: 0\n  }),\n  polygon: properties => boxAppering(properties.centerX, properties.centerY)\n};\n\n/**\n * @typedef { import('chart.js').FontSpec } FontSpec\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').Padding } Padding\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n * @typedef { import('../../types/options').AnnotationPointCoordinates } AnnotationPointCoordinates\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/label').LabelPositionObject } LabelPositionObject\n */\n\n/**\n * @param {number} size\n * @param {number|string} position\n * @returns {number}\n */\nfunction getRelativePosition(size, position) {\n  if (position === 'start') {\n    return 0;\n  }\n  if (position === 'end') {\n    return size;\n  }\n  if (isPercentString(position)) {\n    return toPositivePercent(position) * size;\n  }\n  return size / 2;\n}\n\n/**\n * @param {number} size\n * @param {number|string} value\n * @param {boolean} [positivePercent=true]\n * @returns {number}\n */\nfunction getSize(size, value, positivePercent = true) {\n  if (typeof value === 'number') {\n    return value;\n  } else if (isPercentString(value)) {\n    return (positivePercent ? toPositivePercent(value) : toPercent(value)) * size;\n  }\n  return size;\n}\n\n/**\n * @param {{x: number, width: number}} size\n * @param {CoreLabelOptions} options\n * @returns {number}\n */\nfunction calculateTextAlignment(size, options) {\n  const {\n    x,\n    width\n  } = size;\n  const textAlign = options.textAlign;\n  if (textAlign === 'center') {\n    return x + width / 2;\n  } else if (textAlign === 'end' || textAlign === 'right') {\n    return x + width;\n  }\n  return x;\n}\n\n/**\n * @param {Point} point\n * @param {{height: number, width: number}} labelSize\n * @param {{borderWidth: number, position: {LabelPositionObject|string}, xAdjust: number, yAdjust: number}} options\n * @param {Padding|undefined} padding\n * @returns {{x: number, y: number, x2: number, y2: number, height: number, width: number, centerX: number, centerY: number}}\n */\nfunction measureLabelRectangle(point, labelSize, {\n  borderWidth,\n  position,\n  xAdjust,\n  yAdjust\n}, padding) {\n  const hasPadding = isObject(padding);\n  const width = labelSize.width + (hasPadding ? padding.width : 0) + borderWidth;\n  const height = labelSize.height + (hasPadding ? padding.height : 0) + borderWidth;\n  const positionObj = toPosition(position);\n  const x = calculateLabelPosition$1(point.x, width, xAdjust, positionObj.x);\n  const y = calculateLabelPosition$1(point.y, height, yAdjust, positionObj.y);\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2\n  };\n}\n\n/**\n * @param {LabelPositionObject|string} value\n * @param {string|number} defaultValue\n * @returns {LabelPositionObject}\n */\nfunction toPosition(value, defaultValue = 'center') {\n  if (isObject(value)) {\n    return {\n      x: valueOrDefault(value.x, defaultValue),\n      y: valueOrDefault(value.y, defaultValue)\n    };\n  }\n  value = valueOrDefault(value, defaultValue);\n  return {\n    x: value,\n    y: value\n  };\n}\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {boolean}\n */\nconst shouldFit = (options, fitRatio) => options && options.autoFit && fitRatio < 1;\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {FontSpec[]}\n */\nfunction toFonts(options, fitRatio) {\n  const optFont = options.font;\n  const fonts = isArray(optFont) ? optFont : [optFont];\n  if (shouldFit(options, fitRatio)) {\n    return fonts.map(function (f) {\n      const font = toFont(f);\n      font.size = Math.floor(f.size * fitRatio);\n      font.lineHeight = f.lineHeight;\n      return toFont(font);\n    });\n  }\n  return fonts.map(f => toFont(f));\n}\n\n/**\n * @param {AnnotationPointCoordinates} options\n * @returns {boolean}\n */\nfunction isBoundToPoint(options) {\n  return options && (defined(options.xValue) || defined(options.yValue));\n}\nfunction calculateLabelPosition$1(start, size, adjust = 0, position) {\n  return start - getRelativePosition(size, position) + adjust;\n}\n\n/**\n * @param {Chart} chart\n * @param {AnnotationBoxModel} properties\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationElement}\n */\nfunction initAnimationProperties(chart, properties, options) {\n  const initAnim = options.init;\n  if (!initAnim) {\n    return;\n  } else if (initAnim === true) {\n    return applyDefault(properties, options);\n  }\n  return execCallback(chart, properties, options);\n}\n\n/**\n * @param {Object} options\n * @param {Array} hooks\n * @param {Object} hooksContainer\n * @returns {boolean}\n */\nfunction loadHooks(options, hooks, hooksContainer) {\n  let activated = false;\n  hooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      activated = true;\n      hooksContainer[hook] = options[hook];\n    } else if (defined(hooksContainer[hook])) {\n      delete hooksContainer[hook];\n    }\n  });\n  return activated;\n}\nfunction applyDefault(properties, options) {\n  const type = options.type || 'line';\n  return defaultInitAnimation[type](properties);\n}\nfunction execCallback(chart, properties, options) {\n  const result = callback(options.init, [{\n    chart,\n    properties,\n    options\n  }]);\n  if (result === true) {\n    return applyDefault(properties, options);\n  } else if (isObject(result)) {\n    return result;\n  }\n}\nconst widthCache = new Map();\nconst notRadius = radius => isNaN(radius) || radius <= 0;\nconst fontsKey = fonts => fonts.reduce(function (prev, item) {\n  prev += item.string;\n  return prev;\n}, '');\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n */\n\n/**\n * Determine if content is an image or a canvas.\n * @param {*} content\n * @returns boolean|undefined\n * @todo move this function to chart.js helpers\n */\nfunction isImageOrCanvas(content) {\n  if (content && typeof content === 'object') {\n    const type = content.toString();\n    return type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]';\n  }\n}\n\n/**\n * Set the translation on the canvas if the rotation must be applied.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {Point} point - the point of translation\n * @param {number} rotation - rotation (in degrees) to apply\n */\nfunction translate(ctx, {\n  x,\n  y\n}, rotation) {\n  if (rotation) {\n    ctx.translate(x, y);\n    ctx.rotate(toRadians(rotation));\n    ctx.translate(-x, -y);\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n * @returns {boolean|undefined}\n */\nfunction setBorderStyle(ctx, options) {\n  if (options && options.borderWidth) {\n    ctx.lineCap = options.borderCapStyle || 'butt';\n    ctx.setLineDash(options.borderDash);\n    ctx.lineDashOffset = options.borderDashOffset;\n    ctx.lineJoin = options.borderJoinStyle || 'miter';\n    ctx.lineWidth = options.borderWidth;\n    ctx.strokeStyle = options.borderColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n */\nfunction setShadowStyle(ctx, options) {\n  ctx.shadowColor = options.backgroundShadowColor;\n  ctx.shadowBlur = options.shadowBlur;\n  ctx.shadowOffsetX = options.shadowOffsetX;\n  ctx.shadowOffsetY = options.shadowOffsetY;\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {CoreLabelOptions} options\n * @returns {{width: number, height: number}}\n */\nfunction measureLabelSize(ctx, options) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    const size = {\n      width: getSize(content.width, options.width),\n      height: getSize(content.height, options.height)\n    };\n    return size;\n  }\n  const fonts = toFonts(options);\n  const strokeWidth = options.textStrokeWidth;\n  const lines = isArray(content) ? content : [content];\n  const mapKey = lines.join() + fontsKey(fonts) + strokeWidth + (ctx._measureText ? '-spriting' : '');\n  if (!widthCache.has(mapKey)) {\n    widthCache.set(mapKey, calculateLabelSize(ctx, lines, fonts, strokeWidth));\n  }\n  return widthCache.get(mapKey);\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {Object} options\n */\nfunction drawBox(ctx, rect, options) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  ctx.save();\n  setShadowStyle(ctx, options);\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  addRoundedRectPath(ctx, {\n    x,\n    y,\n    w: width,\n    h: height,\n    radius: clampAll(toTRBLCorners(options.borderRadius), 0, Math.min(width, height) / 2)\n  });\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n */\nfunction drawLabel(ctx, rect, options, fitRatio) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    ctx.save();\n    ctx.globalAlpha = getOpacity(options.opacity, content.style.opacity);\n    ctx.drawImage(content, rect.x, rect.y, rect.width, rect.height);\n    ctx.restore();\n    return;\n  }\n  const labels = isArray(content) ? content : [content];\n  const fonts = toFonts(options, fitRatio);\n  const optColor = options.color;\n  const colors = isArray(optColor) ? optColor : [optColor];\n  const x = calculateTextAlignment(rect, options);\n  const y = rect.y + options.textStrokeWidth / 2;\n  ctx.save();\n  ctx.textBaseline = 'middle';\n  ctx.textAlign = options.textAlign;\n  if (setTextStrokeStyle(ctx, options)) {\n    applyLabelDecoration(ctx, {\n      x,\n      y\n    }, labels, fonts);\n  }\n  applyLabelContent(ctx, {\n    x,\n    y\n  }, labels, {\n    fonts,\n    colors\n  });\n  ctx.restore();\n}\nfunction setTextStrokeStyle(ctx, options) {\n  if (options.textStrokeWidth > 0) {\n    // https://stackoverflow.com/questions/13627111/drawing-text-with-an-outer-stroke-with-html5s-canvas\n    ctx.lineJoin = 'round';\n    ctx.miterLimit = 2;\n    ctx.lineWidth = options.textStrokeWidth;\n    ctx.strokeStyle = options.textStrokeColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{radius: number, options: PointAnnotationOptions}} element\n * @param {number} x\n * @param {number} y\n */\nfunction drawPoint(ctx, element, x, y) {\n  const {\n    radius,\n    options\n  } = element;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (isImageOrCanvas(style)) {\n    ctx.save();\n    ctx.translate(x, y);\n    ctx.rotate(rad);\n    ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n    ctx.restore();\n    return;\n  }\n  if (notRadius(radius)) {\n    return;\n  }\n  drawPointStyle(ctx, {\n    x,\n    y,\n    radius,\n    rotation,\n    style,\n    rad\n  });\n}\nfunction drawPointStyle(ctx, {\n  x,\n  y,\n  radius,\n  rotation,\n  style,\n  rad\n}) {\n  let xOffset, yOffset, size, cornerRadius;\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      ctx.arc(x, y, radius, 0, TAU);\n      ctx.closePath();\n      break;\n    case 'triangle':\n      ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        ctx.rect(x - size, y - size, 2 * size, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'star':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      rad += QUARTER_PI;\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n    case 'line':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n      break;\n  }\n  ctx.fill();\n}\nfunction calculateLabelSize(ctx, lines, fonts, strokeWidth) {\n  ctx.save();\n  const count = lines.length;\n  let width = 0;\n  let height = strokeWidth;\n  for (let i = 0; i < count; i++) {\n    const font = fonts[Math.min(i, fonts.length - 1)];\n    ctx.font = font.string;\n    const text = lines[i];\n    width = Math.max(width, ctx.measureText(text).width + strokeWidth);\n    height += font.lineHeight;\n  }\n  ctx.restore();\n  return {\n    width,\n    height\n  };\n}\nfunction applyLabelDecoration(ctx, {\n  x,\n  y\n}, labels, fonts) {\n  ctx.beginPath();\n  let lhs = 0;\n  labels.forEach(function (l, i) {\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.font = f.string;\n    ctx.strokeText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n  });\n  ctx.stroke();\n}\nfunction applyLabelContent(ctx, {\n  x,\n  y\n}, labels, {\n  fonts,\n  colors\n}) {\n  let lhs = 0;\n  labels.forEach(function (l, i) {\n    const c = colors[Math.min(i, colors.length - 1)];\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.beginPath();\n    ctx.font = f.string;\n    ctx.fillStyle = c;\n    ctx.fillText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n    ctx.fill();\n  });\n}\nfunction getOpacity(value, elementValue) {\n  const opacity = isNumber(value) ? value : elementValue;\n  return isNumber(opacity) ? clamp(opacity, 0, 1) : 1;\n}\nconst positions = ['left', 'bottom', 'top', 'right'];\n\n/**\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\n/**\n * Drawa the callout component for labels.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {AnnotationElement} element - the label element\n */\nfunction drawCallout(ctx, element) {\n  const {\n    pointX,\n    pointY,\n    options\n  } = element;\n  const callout = options.callout;\n  const calloutPosition = callout && callout.display && resolveCalloutPosition(element, callout);\n  if (!calloutPosition || isPointInRange(element, callout, calloutPosition)) {\n    return;\n  }\n  ctx.save();\n  ctx.beginPath();\n  const stroke = setBorderStyle(ctx, callout);\n  if (!stroke) {\n    return ctx.restore();\n  }\n  const {\n    separatorStart,\n    separatorEnd\n  } = getCalloutSeparatorCoord(element, calloutPosition);\n  const {\n    sideStart,\n    sideEnd\n  } = getCalloutSideCoord(element, calloutPosition, separatorStart);\n  if (callout.margin > 0 || options.borderWidth === 0) {\n    ctx.moveTo(separatorStart.x, separatorStart.y);\n    ctx.lineTo(separatorEnd.x, separatorEnd.y);\n  }\n  ctx.moveTo(sideStart.x, sideStart.y);\n  ctx.lineTo(sideEnd.x, sideEnd.y);\n  const rotatedPoint = rotated({\n    x: pointX,\n    y: pointY\n  }, element.getCenterPoint(), toRadians(-element.rotation));\n  ctx.lineTo(rotatedPoint.x, rotatedPoint.y);\n  ctx.stroke();\n  ctx.restore();\n}\nfunction getCalloutSeparatorCoord(element, position) {\n  const {\n    x,\n    y,\n    x2,\n    y2\n  } = element;\n  const adjust = getCalloutSeparatorAdjust(element, position);\n  let separatorStart, separatorEnd;\n  if (position === 'left' || position === 'right') {\n    separatorStart = {\n      x: x + adjust,\n      y\n    };\n    separatorEnd = {\n      x: separatorStart.x,\n      y: y2\n    };\n  } else {\n    //  position 'top' or 'bottom'\n    separatorStart = {\n      x,\n      y: y + adjust\n    };\n    separatorEnd = {\n      x: x2,\n      y: separatorStart.y\n    };\n  }\n  return {\n    separatorStart,\n    separatorEnd\n  };\n}\nfunction getCalloutSeparatorAdjust(element, position) {\n  const {\n    width,\n    height,\n    options\n  } = element;\n  const adjust = options.callout.margin + options.borderWidth / 2;\n  if (position === 'right') {\n    return width + adjust;\n  } else if (position === 'bottom') {\n    return height + adjust;\n  }\n  return -adjust;\n}\nfunction getCalloutSideCoord(element, position, separatorStart) {\n  const {\n    y,\n    width,\n    height,\n    options\n  } = element;\n  const start = options.callout.start;\n  const side = getCalloutSideAdjust(position, options.callout);\n  let sideStart, sideEnd;\n  if (position === 'left' || position === 'right') {\n    sideStart = {\n      x: separatorStart.x,\n      y: y + getSize(height, start)\n    };\n    sideEnd = {\n      x: sideStart.x + side,\n      y: sideStart.y\n    };\n  } else {\n    //  position 'top' or 'bottom'\n    sideStart = {\n      x: separatorStart.x + getSize(width, start),\n      y: separatorStart.y\n    };\n    sideEnd = {\n      x: sideStart.x,\n      y: sideStart.y + side\n    };\n  }\n  return {\n    sideStart,\n    sideEnd\n  };\n}\nfunction getCalloutSideAdjust(position, options) {\n  const side = options.side;\n  if (position === 'left' || position === 'top') {\n    return -side;\n  }\n  return side;\n}\nfunction resolveCalloutPosition(element, options) {\n  const position = options.position;\n  if (positions.includes(position)) {\n    return position;\n  }\n  return resolveCalloutAutoPosition(element, options);\n}\nfunction resolveCalloutAutoPosition(element, options) {\n  const {\n    x,\n    y,\n    x2,\n    y2,\n    width,\n    height,\n    pointX,\n    pointY,\n    centerX,\n    centerY,\n    rotation\n  } = element;\n  const center = {\n    x: centerX,\n    y: centerY\n  };\n  const start = options.start;\n  const xAdjust = getSize(width, start);\n  const yAdjust = getSize(height, start);\n  const xPoints = [x, x + xAdjust, x + xAdjust, x2];\n  const yPoints = [y + yAdjust, y2, y, y2];\n  const result = [];\n  for (let index = 0; index < 4; index++) {\n    const rotatedPoint = rotated({\n      x: xPoints[index],\n      y: yPoints[index]\n    }, center, toRadians(rotation));\n    result.push({\n      position: positions[index],\n      distance: distanceBetweenPoints(rotatedPoint, {\n        x: pointX,\n        y: pointY\n      })\n    });\n  }\n  return result.sort((a, b) => a.distance - b.distance)[0].position;\n}\nfunction isPointInRange(element, callout, position) {\n  const {\n    pointX,\n    pointY\n  } = element;\n  const margin = callout.margin;\n  let x = pointX;\n  let y = pointY;\n  if (position === 'left') {\n    x += margin;\n  } else if (position === 'right') {\n    x -= margin;\n  } else if (position === 'top') {\n    y += margin;\n  } else if (position === 'bottom') {\n    y -= margin;\n  }\n  return element.inRange(x, y);\n}\nconst limitedLineScale = {\n  xScaleID: {\n    min: 'xMin',\n    max: 'xMax',\n    start: 'left',\n    end: 'right',\n    startProp: 'x',\n    endProp: 'x2'\n  },\n  yScaleID: {\n    min: 'yMin',\n    max: 'yMax',\n    start: 'bottom',\n    end: 'top',\n    startProp: 'y',\n    endProp: 'y2'\n  }\n};\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import(\"chart.js\").Point } Point\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n * @typedef { import('../../types/options').LineAnnotationOptions } LineAnnotationOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n * @typedef { import('../../types/options').PolygonAnnotationOptions } PolygonAnnotationOptions\n */\n\n/**\n * @param {Scale} scale\n * @param {number|string} value\n * @param {number} fallback\n * @returns {number}\n */\nfunction scaleValue(scale, value, fallback) {\n  value = typeof value === 'number' ? value : scale.parse(value);\n  return isFinite(value) ? scale.getPixelForValue(value) : fallback;\n}\n\n/**\n * Search the scale defined in chartjs by the axis related to the annotation options key.\n * @param {{ [key: string]: Scale }} scales\n * @param {CoreAnnotationOptions} options\n * @param {string} key\n * @returns {string}\n */\nfunction retrieveScaleID(scales, options, key) {\n  const scaleID = options[key];\n  if (scaleID || key === 'scaleID') {\n    return scaleID;\n  }\n  const axis = key.charAt(0);\n  const axes = Object.values(scales).filter(scale => scale.axis && scale.axis === axis);\n  if (axes.length) {\n    return axes[0].id;\n  }\n  return axis;\n}\n\n/**\n * @param {Scale} scale\n * @param {{min: number, max: number, start: number, end: number}} options\n * @returns {{start: number, end: number}|undefined}\n */\nfunction getDimensionByScale(scale, options) {\n  if (scale) {\n    const reverse = scale.options.reverse;\n    const start = scaleValue(scale, options.min, reverse ? options.end : options.start);\n    const end = scaleValue(scale, options.max, reverse ? options.start : options.end);\n    return {\n      start,\n      end\n    };\n  }\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {Point}\n */\nfunction getChartPoint(chart, options) {\n  const {\n    chartArea,\n    scales\n  } = chart;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n  let x = chartArea.width / 2;\n  let y = chartArea.height / 2;\n  if (xScale) {\n    x = scaleValue(xScale, options.xValue, xScale.left + xScale.width / 2);\n  }\n  if (yScale) {\n    y = scaleValue(yScale, options.yValue, yScale.top + yScale.height / 2);\n  }\n  return {\n    x,\n    y\n  };\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxProperties(chart, options) {\n  const scales = chart.scales;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n  if (!xScale && !yScale) {\n    return {};\n  }\n  let {\n    left: x,\n    right: x2\n  } = xScale || chart.chartArea;\n  let {\n    top: y,\n    bottom: y2\n  } = yScale || chart.chartArea;\n  const xDim = getChartDimensionByScale(xScale, {\n    min: options.xMin,\n    max: options.xMax,\n    start: x,\n    end: x2\n  });\n  x = xDim.start;\n  x2 = xDim.end;\n  const yDim = getChartDimensionByScale(yScale, {\n    min: options.yMin,\n    max: options.yMax,\n    start: y2,\n    end: y\n  });\n  y = yDim.start;\n  y2 = yDim.end;\n  return {\n    x,\n    y,\n    x2,\n    y2,\n    width: x2 - x,\n    height: y2 - y,\n    centerX: x + (x2 - x) / 2,\n    centerY: y + (y2 - y) / 2\n  };\n}\n\n/**\n * @param {Chart} chart\n * @param {PointAnnotationOptions|PolygonAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolvePointProperties(chart, options) {\n  if (!isBoundToPoint(options)) {\n    const box = resolveBoxProperties(chart, options);\n    let radius = options.radius;\n    if (!radius || isNaN(radius)) {\n      radius = Math.min(box.width, box.height) / 2;\n      options.radius = radius;\n    }\n    const size = radius * 2;\n    const adjustCenterX = box.centerX + options.xAdjust;\n    const adjustCenterY = box.centerY + options.yAdjust;\n    return {\n      x: adjustCenterX - radius,\n      y: adjustCenterY - radius,\n      x2: adjustCenterX + radius,\n      y2: adjustCenterY + radius,\n      centerX: adjustCenterX,\n      centerY: adjustCenterY,\n      width: size,\n      height: size,\n      radius\n    };\n  }\n  return getChartCircle(chart, options);\n}\n/**\n * @param {Chart} chart\n * @param {LineAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveLineProperties(chart, options) {\n  const {\n    scales,\n    chartArea\n  } = chart;\n  const scale = scales[options.scaleID];\n  const area = {\n    x: chartArea.left,\n    y: chartArea.top,\n    x2: chartArea.right,\n    y2: chartArea.bottom\n  };\n  if (scale) {\n    resolveFullLineProperties(scale, area, options);\n  } else {\n    resolveLimitedLineProperties(scales, area, options);\n  }\n  return area;\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @param {boolean} [centerBased=false]\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxAndLabelProperties(chart, options) {\n  const properties = resolveBoxProperties(chart, options);\n  properties.initProperties = initAnimationProperties(chart, properties, options);\n  properties.elements = [{\n    type: 'label',\n    optionScope: 'label',\n    properties: resolveLabelElementProperties$1(chart, properties, options),\n    initProperties: properties.initProperties\n  }];\n  return properties;\n}\nfunction getChartCircle(chart, options) {\n  const point = getChartPoint(chart, options);\n  const size = options.radius * 2;\n  return {\n    x: point.x - options.radius + options.xAdjust,\n    y: point.y - options.radius + options.yAdjust,\n    x2: point.x + options.radius + options.xAdjust,\n    y2: point.y + options.radius + options.yAdjust,\n    centerX: point.x + options.xAdjust,\n    centerY: point.y + options.yAdjust,\n    radius: options.radius,\n    width: size,\n    height: size\n  };\n}\nfunction getChartDimensionByScale(scale, options) {\n  const result = getDimensionByScale(scale, options) || options;\n  return {\n    start: Math.min(result.start, result.end),\n    end: Math.max(result.start, result.end)\n  };\n}\nfunction resolveFullLineProperties(scale, area, options) {\n  const min = scaleValue(scale, options.value, NaN);\n  const max = scaleValue(scale, options.endValue, min);\n  if (scale.isHorizontal()) {\n    area.x = min;\n    area.x2 = max;\n  } else {\n    area.y = min;\n    area.y2 = max;\n  }\n}\nfunction resolveLimitedLineProperties(scales, area, options) {\n  for (const scaleId of Object.keys(limitedLineScale)) {\n    const scale = scales[retrieveScaleID(scales, options, scaleId)];\n    if (scale) {\n      const {\n        min,\n        max,\n        start,\n        end,\n        startProp,\n        endProp\n      } = limitedLineScale[scaleId];\n      const dim = getDimensionByScale(scale, {\n        min: options[min],\n        max: options[max],\n        start: scale[start],\n        end: scale[end]\n      });\n      area[startProp] = dim.start;\n      area[endProp] = dim.end;\n    }\n  }\n}\nfunction calculateX({\n  properties,\n  options\n}, labelSize, position, padding) {\n  const {\n    x: start,\n    x2: end,\n    width: size\n  } = properties;\n  return calculatePosition({\n    start,\n    end,\n    size,\n    borderWidth: options.borderWidth\n  }, {\n    position: position.x,\n    padding: {\n      start: padding.left,\n      end: padding.right\n    },\n    adjust: options.label.xAdjust,\n    size: labelSize.width\n  });\n}\nfunction calculateY({\n  properties,\n  options\n}, labelSize, position, padding) {\n  const {\n    y: start,\n    y2: end,\n    height: size\n  } = properties;\n  return calculatePosition({\n    start,\n    end,\n    size,\n    borderWidth: options.borderWidth\n  }, {\n    position: position.y,\n    padding: {\n      start: padding.top,\n      end: padding.bottom\n    },\n    adjust: options.label.yAdjust,\n    size: labelSize.height\n  });\n}\nfunction calculatePosition(boxOpts, labelOpts) {\n  const {\n    start,\n    end,\n    borderWidth\n  } = boxOpts;\n  const {\n    position,\n    padding: {\n      start: padStart,\n      end: padEnd\n    },\n    adjust\n  } = labelOpts;\n  const availableSize = end - borderWidth - start - padStart - padEnd - labelOpts.size;\n  return start + borderWidth / 2 + adjust + getRelativePosition(availableSize, position);\n}\nfunction resolveLabelElementProperties$1(chart, properties, options) {\n  const label = options.label;\n  label.backgroundColor = 'transparent';\n  label.callout.display = false;\n  const position = toPosition(label.position);\n  const padding = toPadding(label.padding);\n  const labelSize = measureLabelSize(chart.ctx, label);\n  const x = calculateX({\n    properties,\n    options\n  }, labelSize, position, padding);\n  const y = calculateY({\n    properties,\n    options\n  }, labelSize, position, padding);\n  const width = labelSize.width + padding.width;\n  const height = labelSize.height + padding.height;\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2,\n    rotation: label.rotation\n  };\n}\nconst moveHooks = ['enter', 'leave'];\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\nconst eventHooks = moveHooks.concat('click');\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateListeners(chart, state, options) {\n  state.listened = loadHooks(options, eventHooks, state.listeners);\n  state.moveListened = false;\n  moveHooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      state.moveListened = true;\n    }\n  });\n  if (!state.listened || !state.moveListened) {\n    state.annotations.forEach(scope => {\n      if (!state.listened && isFunction(scope.click)) {\n        state.listened = true;\n      }\n      if (!state.moveListened) {\n        moveHooks.forEach(hook => {\n          if (isFunction(scope[hook])) {\n            state.listened = true;\n            state.moveListened = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {ChartEvent} event\n * @param {AnnotationPluginOptions} options\n * @return {boolean|undefined}\n */\nfunction handleEvent(state, event, options) {\n  if (state.listened) {\n    switch (event.type) {\n      case 'mousemove':\n      case 'mouseout':\n        return handleMoveEvents(state, event, options);\n      case 'click':\n        return handleClickEvents(state, event, options);\n    }\n  }\n}\nfunction handleMoveEvents(state, event, options) {\n  if (!state.moveListened) {\n    return;\n  }\n  let elements;\n  if (event.type === 'mousemove') {\n    elements = getElements(state.visibleElements, event, options.interaction);\n  } else {\n    elements = [];\n  }\n  const previous = state.hovered;\n  state.hovered = elements;\n  const context = {\n    state,\n    event\n  };\n  let changed = dispatchMoveEvents(context, 'leave', previous, elements);\n  return dispatchMoveEvents(context, 'enter', elements, previous) || changed;\n}\nfunction dispatchMoveEvents({\n  state,\n  event\n}, hook, elements, checkElements) {\n  let changed;\n  for (const element of elements) {\n    if (checkElements.indexOf(element) < 0) {\n      changed = dispatchEvent(element.options[hook] || state.listeners[hook], element, event) || changed;\n    }\n  }\n  return changed;\n}\nfunction handleClickEvents(state, event, options) {\n  const listeners = state.listeners;\n  const elements = getElements(state.visibleElements, event, options.interaction);\n  let changed;\n  for (const element of elements) {\n    changed = dispatchEvent(element.options.click || listeners.click, element, event) || changed;\n  }\n  return changed;\n}\nfunction dispatchEvent(handler, element, event) {\n  return callback(handler, [element.$context, event]) === true;\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst elementHooks = ['afterDraw', 'beforeDraw'];\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateHooks(chart, state, options) {\n  const visibleElements = state.visibleElements;\n  state.hooked = loadHooks(options, elementHooks, state.hooks);\n  if (!state.hooked) {\n    visibleElements.forEach(scope => {\n      if (!state.hooked) {\n        elementHooks.forEach(hook => {\n          if (isFunction(scope.options[hook])) {\n            state.hooked = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {AnnotationElement} element\n * @param {string} hook\n */\nfunction invokeHook(state, element, hook) {\n  if (state.hooked) {\n    const callbackHook = element.options[hook] || state.hooks[hook];\n    return callback(callbackHook, [element.$context]);\n  }\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n */\n\n/**\n * @param {Chart} chart\n * @param {Scale} scale\n * @param {CoreAnnotationOptions[]} annotations\n */\nfunction adjustScaleRange(chart, scale, annotations) {\n  const range = getScaleLimits(chart.scales, scale, annotations);\n  let changed = changeScaleLimit(scale, range, 'min', 'suggestedMin');\n  changed = changeScaleLimit(scale, range, 'max', 'suggestedMax') || changed;\n  if (changed && isFunction(scale.handleTickRangeOptions)) {\n    scale.handleTickRangeOptions();\n  }\n}\n\n/**\n * @param {CoreAnnotationOptions[]} annotations\n * @param {{ [key: string]: Scale }} scales\n */\nfunction verifyScaleOptions(annotations, scales) {\n  for (const annotation of annotations) {\n    verifyScaleIDs(annotation, scales);\n  }\n}\nfunction changeScaleLimit(scale, range, limit, suggestedLimit) {\n  if (isFinite(range[limit]) && !scaleLimitDefined(scale.options, limit, suggestedLimit)) {\n    const changed = scale[limit] !== range[limit];\n    scale[limit] = range[limit];\n    return changed;\n  }\n}\nfunction scaleLimitDefined(scaleOptions, limit, suggestedLimit) {\n  return defined(scaleOptions[limit]) || defined(scaleOptions[suggestedLimit]);\n}\nfunction verifyScaleIDs(annotation, scales) {\n  for (const key of ['scaleID', 'xScaleID', 'yScaleID']) {\n    const scaleID = retrieveScaleID(scales, annotation, key);\n    if (scaleID && !scales[scaleID] && verifyProperties(annotation, key)) {\n      console.warn(`No scale found with id '${scaleID}' for annotation '${annotation.id}'`);\n    }\n  }\n}\nfunction verifyProperties(annotation, key) {\n  if (key === 'scaleID') {\n    return true;\n  }\n  const axis = key.charAt(0);\n  for (const prop of ['Min', 'Max', 'Value']) {\n    if (defined(annotation[axis + prop])) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction getScaleLimits(scales, scale, annotations) {\n  const axis = scale.axis;\n  const scaleID = scale.id;\n  const scaleIDOption = axis + 'ScaleID';\n  const limits = {\n    min: valueOrDefault(scale.min, Number.NEGATIVE_INFINITY),\n    max: valueOrDefault(scale.max, Number.POSITIVE_INFINITY)\n  };\n  for (const annotation of annotations) {\n    if (annotation.scaleID === scaleID) {\n      updateLimits(annotation, scale, ['value', 'endValue'], limits);\n    } else if (retrieveScaleID(scales, annotation, scaleIDOption) === scaleID) {\n      updateLimits(annotation, scale, [axis + 'Min', axis + 'Max', axis + 'Value'], limits);\n    }\n  }\n  return limits;\n}\nfunction updateLimits(annotation, scale, props, limits) {\n  for (const prop of props) {\n    const raw = annotation[prop];\n    if (defined(raw)) {\n      const value = scale.parse(raw);\n      limits.min = Math.min(limits.min, value);\n      limits.max = Math.max(limits.max, value);\n    }\n  }\n}\nclass BoxAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {\n      x,\n      y\n    } = rotated({\n      x: mouseX,\n      y: mouseY\n    }, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    return inBoxRange({\n      x,\n      y\n    }, this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis, this.options);\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.options.rotation);\n    drawBox(ctx, this, this.options);\n    ctx.restore();\n  }\n  get label() {\n    return this.elements && this.elements[0];\n  }\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n}\nBoxAnnotation.id = 'boxAnnotation';\nBoxAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'transparent',\n    borderWidth: 0,\n    callout: {\n      display: false\n    },\n    color: 'black',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: undefined,\n    textAlign: 'start',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\nBoxAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\nBoxAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\nclass DoughnutLabelAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange({\n      x: mouseX,\n      y: mouseY\n    }, {\n      rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition),\n      center: this.getCenterPoint(useFinalPosition)\n    }, axis, {\n      rotation: this.rotation,\n      borderWidth: 0,\n      hitTolerance: this.options.hitTolerance\n    });\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const options = this.options;\n    if (!options.display || !options.content) {\n      return;\n    }\n    drawBackground(ctx, this);\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawLabel(ctx, this, options, this._fitRatio);\n    ctx.restore();\n  }\n  resolveElementProperties(chart, options) {\n    const meta = getDatasetMeta(chart, options);\n    if (!meta) {\n      return {};\n    }\n    const {\n      controllerMeta,\n      point,\n      radius\n    } = getControllerMeta(chart, options, meta);\n    let labelSize = measureLabelSize(chart.ctx, options);\n    const _fitRatio = getFitRatio(labelSize, radius);\n    if (shouldFit(options, _fitRatio)) {\n      labelSize = {\n        width: labelSize.width * _fitRatio,\n        height: labelSize.height * _fitRatio\n      };\n    }\n    const {\n      position,\n      xAdjust,\n      yAdjust\n    } = options;\n    const boxSize = measureLabelRectangle(point, labelSize, {\n      borderWidth: 0,\n      position,\n      xAdjust,\n      yAdjust\n    });\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      ...boxSize,\n      ...controllerMeta,\n      rotation: options.rotation,\n      _fitRatio\n    };\n  }\n}\nDoughnutLabelAnnotation.id = 'doughnutLabelAnnotation';\nDoughnutLabelAnnotation.defaults = {\n  autoFit: true,\n  autoHide: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  spacing: 1,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  yAdjust: 0\n};\nDoughnutLabelAnnotation.defaultRoutes = {};\nfunction getDatasetMeta(chart, options) {\n  return chart.getSortedVisibleDatasetMetas().reduce(function (result, value) {\n    const controller = value.controller;\n    if (controller instanceof DoughnutController && isControllerVisible(chart, options, value.data) && (!result || controller.innerRadius < result.controller.innerRadius) && controller.options.circumference >= 90) {\n      return value;\n    }\n    return result;\n  }, undefined);\n}\nfunction isControllerVisible(chart, options, elements) {\n  if (!options.autoHide) {\n    return true;\n  }\n  for (let i = 0; i < elements.length; i++) {\n    if (!elements[i].hidden && chart.getDataVisibility(i)) {\n      return true;\n    }\n  }\n}\nfunction getControllerMeta({\n  chartArea\n}, options, meta) {\n  const {\n    left,\n    top,\n    right,\n    bottom\n  } = chartArea;\n  const {\n    innerRadius,\n    offsetX,\n    offsetY\n  } = meta.controller;\n  const x = (left + right) / 2 + offsetX;\n  const y = (top + bottom) / 2 + offsetY;\n  const square = {\n    left: Math.max(x - innerRadius, left),\n    right: Math.min(x + innerRadius, right),\n    top: Math.max(y - innerRadius, top),\n    bottom: Math.min(y + innerRadius, bottom)\n  };\n  const point = {\n    x: (square.left + square.right) / 2,\n    y: (square.top + square.bottom) / 2\n  };\n  const space = options.spacing + options.borderWidth / 2;\n  const _radius = innerRadius - space;\n  const _counterclockwise = point.y > y;\n  const side = _counterclockwise ? top + space : bottom - space;\n  const angles = getAngles(side, x, y, _radius);\n  const controllerMeta = {\n    _centerX: x,\n    _centerY: y,\n    _radius,\n    _counterclockwise,\n    ...angles\n  };\n  return {\n    controllerMeta,\n    point,\n    radius: Math.min(innerRadius, Math.min(square.right - square.left, square.bottom - square.top) / 2)\n  };\n}\nfunction getFitRatio({\n  width,\n  height\n}, radius) {\n  const hypo = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));\n  return radius * 2 / hypo;\n}\nfunction getAngles(y, centerX, centerY, radius) {\n  const yk2 = Math.pow(centerY - y, 2);\n  const r2 = Math.pow(radius, 2);\n  const b = centerX * -2;\n  const c = Math.pow(centerX, 2) + yk2 - r2;\n  const delta = Math.pow(b, 2) - 4 * c;\n  if (delta <= 0) {\n    return {\n      _startAngle: 0,\n      _endAngle: TAU\n    };\n  }\n  const start = (-b - Math.sqrt(delta)) / 2;\n  const end = (-b + Math.sqrt(delta)) / 2;\n  return {\n    _startAngle: getAngleFromPoint({\n      x: centerX,\n      y: centerY\n    }, {\n      x: start,\n      y\n    }).angle,\n    _endAngle: getAngleFromPoint({\n      x: centerX,\n      y: centerY\n    }, {\n      x: end,\n      y\n    }).angle\n  };\n}\nfunction drawBackground(ctx, element) {\n  const {\n    _centerX,\n    _centerY,\n    _radius,\n    _startAngle,\n    _endAngle,\n    _counterclockwise,\n    options\n  } = element;\n  ctx.save();\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  ctx.arc(_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise);\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.stroke();\n  }\n  ctx.restore();\n}\nclass LabelAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange({\n      x: mouseX,\n      y: mouseY\n    }, {\n      rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition),\n      center: this.getCenterPoint(useFinalPosition)\n    }, axis, {\n      rotation: this.rotation,\n      borderWidth: this.options.borderWidth,\n      hitTolerance: this.options.hitTolerance\n    });\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const options = this.options;\n    const visible = !defined(this._visible) || this._visible;\n    if (!options.display || !options.content || !visible) {\n      return;\n    }\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawCallout(ctx, this);\n    drawBox(ctx, this, options);\n    drawLabel(ctx, getLabelSize(this), options);\n    ctx.restore();\n  }\n  resolveElementProperties(chart, options) {\n    let point;\n    if (!isBoundToPoint(options)) {\n      const {\n        centerX,\n        centerY\n      } = resolveBoxProperties(chart, options);\n      point = {\n        x: centerX,\n        y: centerY\n      };\n    } else {\n      point = getChartPoint(chart, options);\n    }\n    const padding = toPadding(options.padding);\n    const labelSize = measureLabelSize(chart.ctx, options);\n    const boxSize = measureLabelRectangle(point, labelSize, options, padding);\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      pointX: point.x,\n      pointY: point.y,\n      ...boxSize,\n      rotation: options.rotation\n    };\n  }\n}\nLabelAnnotation.id = 'labelAnnotation';\nLabelAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  callout: {\n    borderCapStyle: 'butt',\n    borderColor: undefined,\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderWidth: 1,\n    display: false,\n    margin: 5,\n    position: 'auto',\n    side: 5,\n    start: '50%'\n  },\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  padding: 6,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\nLabelAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\nfunction getLabelSize({\n  x,\n  y,\n  width,\n  height,\n  options\n}) {\n  const hBorderWidth = options.borderWidth / 2;\n  const padding = toPadding(options.padding);\n  return {\n    x: x + padding.left + hBorderWidth,\n    y: y + padding.top + hBorderWidth,\n    width: width - padding.left - padding.right - options.borderWidth,\n    height: height - padding.top - padding.bottom - options.borderWidth\n  };\n}\nconst pointInLine = (p1, p2, t) => ({\n  x: p1.x + t * (p2.x - p1.x),\n  y: p1.y + t * (p2.y - p1.y)\n});\nconst interpolateX = (y, p1, p2) => pointInLine(p1, p2, Math.abs((y - p1.y) / (p2.y - p1.y))).x;\nconst interpolateY = (x, p1, p2) => pointInLine(p1, p2, Math.abs((x - p1.x) / (p2.x - p1.x))).y;\nconst sqr = v => v * v;\nconst rangeLimit = (mouseX, mouseY, {\n  x,\n  y,\n  x2,\n  y2\n}, axis) => axis === 'y' ? {\n  start: Math.min(y, y2),\n  end: Math.max(y, y2),\n  value: mouseY\n} : {\n  start: Math.min(x, x2),\n  end: Math.max(x, x2),\n  value: mouseX\n};\n// http://www.independent-software.com/determining-coordinates-on-a-html-canvas-bezier-curve.html\nconst coordInCurve = (start, cp, end, t) => (1 - t) * (1 - t) * start + 2 * (1 - t) * t * cp + t * t * end;\nconst pointInCurve = (start, cp, end, t) => ({\n  x: coordInCurve(start.x, cp.x, end.x, t),\n  y: coordInCurve(start.y, cp.y, end.y, t)\n});\nconst coordAngleInCurve = (start, cp, end, t) => 2 * (1 - t) * (cp - start) + 2 * t * (end - cp);\nconst angleInCurve = (start, cp, end, t) => -Math.atan2(coordAngleInCurve(start.x, cp.x, end.x, t), coordAngleInCurve(start.y, cp.y, end.y, t)) + 0.5 * PI;\nclass LineAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      const point = {\n        mouseX,\n        mouseY\n      };\n      const {\n        path,\n        ctx\n      } = this;\n      if (path) {\n        setBorderStyle(ctx, this.options);\n        ctx.lineWidth += this.options.hitTolerance;\n        const {\n          chart\n        } = this.$context;\n        const mx = mouseX * chart.currentDevicePixelRatio;\n        const my = mouseY * chart.currentDevicePixelRatio;\n        const result = ctx.isPointInStroke(path, mx, my) || isOnLabel(this, point, useFinalPosition);\n        ctx.restore();\n        return result;\n      }\n      const epsilon = sqr(hitSize);\n      return intersects(this, point, epsilon, useFinalPosition) || isOnLabel(this, point, useFinalPosition);\n    }\n    return inAxisRange(this, {\n      mouseX,\n      mouseY\n    }, axis, {\n      hitSize,\n      useFinalPosition\n    });\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const {\n      x,\n      y,\n      x2,\n      y2,\n      cp,\n      options\n    } = this;\n    ctx.save();\n    if (!setBorderStyle(ctx, options)) {\n      // no border width, then line is not drawn\n      return ctx.restore();\n    }\n    setShadowStyle(ctx, options);\n    const length = Math.sqrt(Math.pow(x2 - x, 2) + Math.pow(y2 - y, 2));\n    if (options.curve && cp) {\n      drawCurve(ctx, this, cp, length);\n      return ctx.restore();\n    }\n    const {\n      startOpts,\n      endOpts,\n      startAdjust,\n      endAdjust\n    } = getArrowHeads(this);\n    const angle = Math.atan2(y2 - y, x2 - x);\n    ctx.translate(x, y);\n    ctx.rotate(angle);\n    ctx.beginPath();\n    ctx.moveTo(0 + startAdjust, 0);\n    ctx.lineTo(length - endAdjust, 0);\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n    drawArrowHead(ctx, 0, startAdjust, startOpts);\n    drawArrowHead(ctx, length, -endAdjust, endOpts);\n    ctx.restore();\n  }\n  get label() {\n    return this.elements && this.elements[0];\n  }\n  resolveElementProperties(chart, options) {\n    const area = resolveLineProperties(chart, options);\n    const {\n      x,\n      y,\n      x2,\n      y2\n    } = area;\n    const inside = isLineInArea(area, chart.chartArea);\n    const properties = inside ? limitLineToArea({\n      x,\n      y\n    }, {\n      x: x2,\n      y: y2\n    }, chart.chartArea) : {\n      x,\n      y,\n      x2,\n      y2,\n      width: Math.abs(x2 - x),\n      height: Math.abs(y2 - y)\n    };\n    properties.centerX = (x2 + x) / 2;\n    properties.centerY = (y2 + y) / 2;\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    if (options.curve) {\n      const p1 = {\n        x: properties.x,\n        y: properties.y\n      };\n      const p2 = {\n        x: properties.x2,\n        y: properties.y2\n      };\n      properties.cp = getControlPoint(properties, options, distanceBetweenPoints(p1, p2));\n    }\n    const labelProperties = resolveLabelElementProperties(chart, properties, options.label);\n    // additonal prop to manage zoom/pan\n    labelProperties._visible = inside;\n    properties.elements = [{\n      type: 'label',\n      optionScope: 'label',\n      properties: labelProperties,\n      initProperties: properties.initProperties\n    }];\n    return properties;\n  }\n}\nLineAnnotation.id = 'lineAnnotation';\nconst arrowHeadsDefaults = {\n  backgroundColor: undefined,\n  backgroundShadowColor: undefined,\n  borderColor: undefined,\n  borderDash: undefined,\n  borderDashOffset: undefined,\n  borderShadowColor: undefined,\n  borderWidth: undefined,\n  display: undefined,\n  fill: undefined,\n  length: undefined,\n  shadowBlur: undefined,\n  shadowOffsetX: undefined,\n  shadowOffsetY: undefined,\n  width: undefined\n};\nLineAnnotation.defaults = {\n  adjustScaleRange: true,\n  arrowHeads: {\n    display: false,\n    end: Object.assign({}, arrowHeadsDefaults),\n    fill: false,\n    length: 12,\n    start: Object.assign({}, arrowHeadsDefaults),\n    width: 6\n  },\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 2,\n  curve: false,\n  controlPoint: {\n    y: '-50%'\n  },\n  display: true,\n  endValue: undefined,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'rgba(0,0,0,0.8)',\n    backgroundShadowColor: 'transparent',\n    borderCapStyle: 'butt',\n    borderColor: 'black',\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderRadius: 6,\n    borderShadowColor: 'transparent',\n    borderWidth: 0,\n    callout: Object.assign({}, LabelAnnotation.defaults.callout),\n    color: '#fff',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: 0,\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    textAlign: 'center',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  scaleID: undefined,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  value: undefined,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\nLineAnnotation.descriptors = {\n  arrowHeads: {\n    start: {\n      _fallback: true\n    },\n    end: {\n      _fallback: true\n    },\n    _fallback: true\n  }\n};\nLineAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\nfunction inAxisRange(element, {\n  mouseX,\n  mouseY\n}, axis, {\n  hitSize,\n  useFinalPosition\n}) {\n  const limit = rangeLimit(mouseX, mouseY, element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis);\n  return inLimit(limit, hitSize) || isOnLabel(element, {\n    mouseX,\n    mouseY\n  }, useFinalPosition, axis);\n}\nfunction isLineInArea({\n  x,\n  y,\n  x2,\n  y2\n}, {\n  top,\n  right,\n  bottom,\n  left\n}) {\n  return !(x < left && x2 < left || x > right && x2 > right || y < top && y2 < top || y > bottom && y2 > bottom);\n}\nfunction limitPointToArea({\n  x,\n  y\n}, p2, {\n  top,\n  right,\n  bottom,\n  left\n}) {\n  if (x < left) {\n    y = interpolateY(left, {\n      x,\n      y\n    }, p2);\n    x = left;\n  }\n  if (x > right) {\n    y = interpolateY(right, {\n      x,\n      y\n    }, p2);\n    x = right;\n  }\n  if (y < top) {\n    x = interpolateX(top, {\n      x,\n      y\n    }, p2);\n    y = top;\n  }\n  if (y > bottom) {\n    x = interpolateX(bottom, {\n      x,\n      y\n    }, p2);\n    y = bottom;\n  }\n  return {\n    x,\n    y\n  };\n}\nfunction limitLineToArea(p1, p2, area) {\n  const {\n    x,\n    y\n  } = limitPointToArea(p1, p2, area);\n  const {\n    x: x2,\n    y: y2\n  } = limitPointToArea(p2, p1, area);\n  return {\n    x,\n    y,\n    x2,\n    y2,\n    width: Math.abs(x2 - x),\n    height: Math.abs(y2 - y)\n  };\n}\nfunction intersects(element, {\n  mouseX,\n  mouseY\n}, epsilon = EPSILON, useFinalPosition) {\n  // Adapted from https://stackoverflow.com/a/6853926/25507\n  const {\n    x: x1,\n    y: y1,\n    x2,\n    y2\n  } = element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const lenSq = sqr(dx) + sqr(dy);\n  const t = lenSq === 0 ? -1 : ((mouseX - x1) * dx + (mouseY - y1) * dy) / lenSq;\n  let xx, yy;\n  if (t < 0) {\n    xx = x1;\n    yy = y1;\n  } else if (t > 1) {\n    xx = x2;\n    yy = y2;\n  } else {\n    xx = x1 + t * dx;\n    yy = y1 + t * dy;\n  }\n  return sqr(mouseX - xx) + sqr(mouseY - yy) <= epsilon;\n}\nfunction isOnLabel(element, {\n  mouseX,\n  mouseY\n}, useFinalPosition, axis) {\n  const label = element.label;\n  return label.options.display && label.inRange(mouseX, mouseY, axis, useFinalPosition);\n}\nfunction resolveLabelElementProperties(chart, properties, options) {\n  const borderWidth = options.borderWidth;\n  const padding = toPadding(options.padding);\n  const textSize = measureLabelSize(chart.ctx, options);\n  const width = textSize.width + padding.width + borderWidth;\n  const height = textSize.height + padding.height + borderWidth;\n  return calculateLabelPosition(properties, options, {\n    width,\n    height,\n    padding\n  }, chart.chartArea);\n}\nfunction calculateAutoRotation(properties) {\n  const {\n    x,\n    y,\n    x2,\n    y2\n  } = properties;\n  const rotation = Math.atan2(y2 - y, x2 - x);\n  // Flip the rotation if it goes > PI/2 or < -PI/2, so label stays upright\n  return rotation > PI / 2 ? rotation - PI : rotation < PI / -2 ? rotation + PI : rotation;\n}\nfunction calculateLabelPosition(properties, label, sizes, chartArea) {\n  const {\n    width,\n    height,\n    padding\n  } = sizes;\n  const {\n    xAdjust,\n    yAdjust\n  } = label;\n  const p1 = {\n    x: properties.x,\n    y: properties.y\n  };\n  const p2 = {\n    x: properties.x2,\n    y: properties.y2\n  };\n  const rotation = label.rotation === 'auto' ? calculateAutoRotation(properties) : toRadians(label.rotation);\n  const size = rotatedSize(width, height, rotation);\n  const t = calculateT(properties, label, {\n    labelSize: size,\n    padding\n  }, chartArea);\n  const pt = properties.cp ? pointInCurve(p1, properties.cp, p2, t) : pointInLine(p1, p2, t);\n  const xCoordinateSizes = {\n    size: size.w,\n    min: chartArea.left,\n    max: chartArea.right,\n    padding: padding.left\n  };\n  const yCoordinateSizes = {\n    size: size.h,\n    min: chartArea.top,\n    max: chartArea.bottom,\n    padding: padding.top\n  };\n  const centerX = adjustLabelCoordinate(pt.x, xCoordinateSizes) + xAdjust;\n  const centerY = adjustLabelCoordinate(pt.y, yCoordinateSizes) + yAdjust;\n  return {\n    x: centerX - width / 2,\n    y: centerY - height / 2,\n    x2: centerX + width / 2,\n    y2: centerY + height / 2,\n    centerX,\n    centerY,\n    pointX: pt.x,\n    pointY: pt.y,\n    width,\n    height,\n    rotation: toDegrees(rotation)\n  };\n}\nfunction rotatedSize(width, height, rotation) {\n  const cos = Math.cos(rotation);\n  const sin = Math.sin(rotation);\n  return {\n    w: Math.abs(width * cos) + Math.abs(height * sin),\n    h: Math.abs(width * sin) + Math.abs(height * cos)\n  };\n}\nfunction calculateT(properties, label, sizes, chartArea) {\n  let t;\n  const space = spaceAround(properties, chartArea);\n  if (label.position === 'start') {\n    t = calculateTAdjust({\n      w: properties.x2 - properties.x,\n      h: properties.y2 - properties.y\n    }, sizes, label, space);\n  } else if (label.position === 'end') {\n    t = 1 - calculateTAdjust({\n      w: properties.x - properties.x2,\n      h: properties.y - properties.y2\n    }, sizes, label, space);\n  } else {\n    t = getRelativePosition(1, label.position);\n  }\n  return t;\n}\nfunction calculateTAdjust(lineSize, sizes, label, space) {\n  const {\n    labelSize,\n    padding\n  } = sizes;\n  const lineW = lineSize.w * space.dx;\n  const lineH = lineSize.h * space.dy;\n  const x = lineW > 0 && (labelSize.w / 2 + padding.left - space.x) / lineW;\n  const y = lineH > 0 && (labelSize.h / 2 + padding.top - space.y) / lineH;\n  return clamp(Math.max(x, y), 0, 0.25);\n}\nfunction spaceAround(properties, chartArea) {\n  const {\n    x,\n    x2,\n    y,\n    y2\n  } = properties;\n  const t = Math.min(y, y2) - chartArea.top;\n  const l = Math.min(x, x2) - chartArea.left;\n  const b = chartArea.bottom - Math.max(y, y2);\n  const r = chartArea.right - Math.max(x, x2);\n  return {\n    x: Math.min(l, r),\n    y: Math.min(t, b),\n    dx: l <= r ? 1 : -1,\n    dy: t <= b ? 1 : -1\n  };\n}\nfunction adjustLabelCoordinate(coordinate, labelSizes) {\n  const {\n    size,\n    min,\n    max,\n    padding\n  } = labelSizes;\n  const halfSize = size / 2;\n  if (size > max - min) {\n    // if it does not fit, display as much as possible\n    return (max + min) / 2;\n  }\n  if (min >= coordinate - padding - halfSize) {\n    coordinate = min + padding + halfSize;\n  }\n  if (max <= coordinate + padding + halfSize) {\n    coordinate = max - padding - halfSize;\n  }\n  return coordinate;\n}\nfunction getArrowHeads(line) {\n  const options = line.options;\n  const arrowStartOpts = options.arrowHeads && options.arrowHeads.start;\n  const arrowEndOpts = options.arrowHeads && options.arrowHeads.end;\n  return {\n    startOpts: arrowStartOpts,\n    endOpts: arrowEndOpts,\n    startAdjust: getLineAdjust(line, arrowStartOpts),\n    endAdjust: getLineAdjust(line, arrowEndOpts)\n  };\n}\nfunction getLineAdjust(line, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return 0;\n  }\n  const {\n    length,\n    width\n  } = arrowOpts;\n  const adjust = line.options.borderWidth / 2;\n  const p1 = {\n    x: length,\n    y: width + adjust\n  };\n  const p2 = {\n    x: 0,\n    y: adjust\n  };\n  return Math.abs(interpolateX(0, p1, p2));\n}\nfunction drawArrowHead(ctx, offset, adjust, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  const {\n    length,\n    width,\n    fill,\n    backgroundColor,\n    borderColor\n  } = arrowOpts;\n  const arrowOffsetX = Math.abs(offset - length) + adjust;\n  ctx.beginPath();\n  setShadowStyle(ctx, arrowOpts);\n  setBorderStyle(ctx, arrowOpts);\n  ctx.moveTo(arrowOffsetX, -width);\n  ctx.lineTo(offset + adjust, 0);\n  ctx.lineTo(arrowOffsetX, width);\n  if (fill === true) {\n    ctx.fillStyle = backgroundColor || borderColor;\n    ctx.closePath();\n    ctx.fill();\n    ctx.shadowColor = 'transparent';\n  } else {\n    ctx.shadowColor = arrowOpts.borderShadowColor;\n  }\n  ctx.stroke();\n}\nfunction getControlPoint(properties, options, distance) {\n  const {\n    x,\n    y,\n    x2,\n    y2,\n    centerX,\n    centerY\n  } = properties;\n  const angle = Math.atan2(y2 - y, x2 - x);\n  const cp = toPosition(options.controlPoint, 0);\n  const point = {\n    x: centerX + getSize(distance, cp.x, false),\n    y: centerY + getSize(distance, cp.y, false)\n  };\n  return rotated(point, {\n    x: centerX,\n    y: centerY\n  }, angle);\n}\nfunction drawArrowHeadOnCurve(ctx, {\n  x,\n  y\n}, {\n  angle,\n  adjust\n}, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  ctx.save();\n  ctx.translate(x, y);\n  ctx.rotate(angle);\n  drawArrowHead(ctx, 0, -adjust, arrowOpts);\n  ctx.restore();\n}\nfunction drawCurve(ctx, element, cp, length) {\n  const {\n    x,\n    y,\n    x2,\n    y2,\n    options\n  } = element;\n  const {\n    startOpts,\n    endOpts,\n    startAdjust,\n    endAdjust\n  } = getArrowHeads(element);\n  const p1 = {\n    x,\n    y\n  };\n  const p2 = {\n    x: x2,\n    y: y2\n  };\n  const startAngle = angleInCurve(p1, cp, p2, 0);\n  const endAngle = angleInCurve(p1, cp, p2, 1) - PI;\n  const ps = pointInCurve(p1, cp, p2, startAdjust / length);\n  const pe = pointInCurve(p1, cp, p2, 1 - endAdjust / length);\n  const path = new Path2D();\n  ctx.beginPath();\n  path.moveTo(ps.x, ps.y);\n  path.quadraticCurveTo(cp.x, cp.y, pe.x, pe.y);\n  ctx.shadowColor = options.borderShadowColor;\n  ctx.stroke(path);\n  element.path = path;\n  element.ctx = ctx;\n  drawArrowHeadOnCurve(ctx, ps, {\n    angle: startAngle,\n    adjust: startAdjust\n  }, startOpts);\n  drawArrowHeadOnCurve(ctx, pe, {\n    angle: endAngle,\n    adjust: endAdjust\n  }, endOpts);\n}\nclass EllipseAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const rotation = this.options.rotation;\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return pointInEllipse({\n        x: mouseX,\n        y: mouseY\n      }, this.getProps(['width', 'height', 'centerX', 'centerY'], useFinalPosition), rotation, hitSize);\n    }\n    const {\n      x,\n      y,\n      x2,\n      y2\n    } = this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n    const limit = axis === 'y' ? {\n      start: y,\n      end: y2\n    } : {\n      start: x,\n      end: x2\n    };\n    const rotatedPoint = rotated({\n      x: mouseX,\n      y: mouseY\n    }, this.getCenterPoint(useFinalPosition), toRadians(-rotation));\n    return rotatedPoint[axis] >= limit.start - hitSize - EPSILON && rotatedPoint[axis] <= limit.end + hitSize + EPSILON;\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const {\n      width,\n      height,\n      centerX,\n      centerY,\n      options\n    } = this;\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), options.rotation);\n    setShadowStyle(ctx, this.options);\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    const stroke = setBorderStyle(ctx, options);\n    ctx.ellipse(centerX, centerY, height / 2, width / 2, PI / 2, 0, 2 * PI);\n    ctx.fill();\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n  get label() {\n    return this.elements && this.elements[0];\n  }\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n}\nEllipseAnnotation.id = 'ellipseAnnotation';\nEllipseAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  label: Object.assign({}, BoxAnnotation.defaults.label),\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\nEllipseAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\nEllipseAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\nfunction pointInEllipse(p, ellipse, rotation, hitSize) {\n  const {\n    width,\n    height,\n    centerX,\n    centerY\n  } = ellipse;\n  const xRadius = width / 2;\n  const yRadius = height / 2;\n  if (xRadius <= 0 || yRadius <= 0) {\n    return false;\n  }\n  // https://stackoverflow.com/questions/7946187/point-and-ellipse-rotated-position-test-algorithm\n  const angle = toRadians(rotation || 0);\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const a = Math.pow(cosAngle * (p.x - centerX) + sinAngle * (p.y - centerY), 2);\n  const b = Math.pow(sinAngle * (p.x - centerX) - cosAngle * (p.y - centerY), 2);\n  return a / Math.pow(xRadius + hitSize, 2) + b / Math.pow(yRadius + hitSize, 2) <= 1.0001;\n}\nclass PointAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {\n      x,\n      y,\n      x2,\n      y2,\n      width\n    } = this.getProps(['x', 'y', 'x2', 'y2', 'width'], useFinalPosition);\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return inPointRange({\n        x: mouseX,\n        y: mouseY\n      }, this.getCenterPoint(useFinalPosition), width / 2, hitSize);\n    }\n    const limit = axis === 'y' ? {\n      start: y,\n      end: y2,\n      value: mouseY\n    } : {\n      start: x,\n      end: x2,\n      value: mouseX\n    };\n    return inLimit(limit, hitSize);\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const options = this.options;\n    const borderWidth = options.borderWidth;\n    if (options.radius < 0.1) {\n      return;\n    }\n    ctx.save();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    drawPoint(ctx, this, this.centerX, this.centerY);\n    if (stroke && !isImageOrCanvas(options.pointStyle)) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n    options.borderWidth = borderWidth;\n  }\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    return properties;\n  }\n}\nPointAnnotation.id = 'pointAnnotation';\nPointAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  pointStyle: 'circle',\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\nPointAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\nclass PolygonAnnotation extends Element {\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    if (axis !== 'x' && axis !== 'y') {\n      return this.options.radius >= 0.1 && this.elements.length > 1 && pointIsInPolygon(this.elements, mouseX, mouseY, useFinalPosition);\n    }\n    const rotatedPoint = rotated({\n      x: mouseX,\n      y: mouseY\n    }, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    const axisPoints = this.elements.map(point => axis === 'y' ? point.bY : point.bX);\n    const start = Math.min(...axisPoints);\n    const end = Math.max(...axisPoints);\n    return rotatedPoint[axis] >= start && rotatedPoint[axis] <= end;\n  }\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n  draw(ctx) {\n    const {\n      elements,\n      options\n    } = this;\n    ctx.save();\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    let first = true;\n    for (const el of elements) {\n      if (first) {\n        ctx.moveTo(el.x, el.y);\n        first = false;\n      } else {\n        ctx.lineTo(el.x, el.y);\n      }\n    }\n    ctx.closePath();\n    ctx.fill();\n    // If no border, don't draw it\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    const {\n      sides,\n      rotation\n    } = options;\n    const elements = [];\n    const angle = 2 * PI / sides;\n    let rad = rotation * RAD_PER_DEG;\n    for (let i = 0; i < sides; i++, rad += angle) {\n      const elProps = buildPointElement(properties, options, rad);\n      elProps.initProperties = initAnimationProperties(chart, properties, options);\n      elements.push(elProps);\n    }\n    properties.elements = elements;\n    return properties;\n  }\n}\nPolygonAnnotation.id = 'polygonAnnotation';\nPolygonAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  point: {\n    radius: 0\n  },\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  sides: 3,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\nPolygonAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\nfunction buildPointElement({\n  centerX,\n  centerY\n}, {\n  radius,\n  borderWidth,\n  hitTolerance\n}, rad) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const sin = Math.sin(rad);\n  const cos = Math.cos(rad);\n  const point = {\n    x: centerX + sin * radius,\n    y: centerY - cos * radius\n  };\n  return {\n    type: 'point',\n    optionScope: 'point',\n    properties: {\n      x: point.x,\n      y: point.y,\n      centerX: point.x,\n      centerY: point.y,\n      bX: centerX + sin * (radius + hitSize),\n      bY: centerY - cos * (radius + hitSize)\n    }\n  };\n}\nfunction pointIsInPolygon(points, x, y, useFinalPosition) {\n  let isInside = false;\n  let A = points[points.length - 1].getProps(['bX', 'bY'], useFinalPosition);\n  for (const point of points) {\n    const B = point.getProps(['bX', 'bY'], useFinalPosition);\n    if (B.bY > y !== A.bY > y && x < (A.bX - B.bX) * (y - B.bY) / (A.bY - B.bY) + B.bX) {\n      isInside = !isInside;\n    }\n    A = B;\n  }\n  return isInside;\n}\nconst annotationTypes = {\n  box: BoxAnnotation,\n  doughnutLabel: DoughnutLabelAnnotation,\n  ellipse: EllipseAnnotation,\n  label: LabelAnnotation,\n  line: LineAnnotation,\n  point: PointAnnotation,\n  polygon: PolygonAnnotation\n};\n\n/**\n * Register fallback for annotation elements\n * For example lineAnnotation options would be looked through:\n * - the annotation object (options.plugins.annotation.annotations[id])\n * - element options (options.elements.lineAnnotation)\n * - element defaults (defaults.elements.lineAnnotation)\n * - annotation plugin defaults (defaults.plugins.annotation, this is what we are registering here)\n */\nObject.keys(annotationTypes).forEach(key => {\n  defaults.describe(`elements.${annotationTypes[key].id}`, {\n    _fallback: 'plugins.annotation.common'\n  });\n});\nconst directUpdater = {\n  update: Object.assign\n};\nconst hooks$1 = eventHooks.concat(elementHooks);\nconst resolve = (value, optDefs) => isObject(optDefs) ? resolveObj(value, optDefs) : value;\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").UpdateMode } UpdateMode\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\n/**\n * @param {string} prop\n * @returns {boolean}\n */\nconst isIndexable = prop => prop === 'color' || prop === 'font';\n\n/**\n * Resolve the annotation type, checking if is supported.\n * @param {string} [type=line] - annotation type\n * @returns {string} resolved annotation type\n */\nfunction resolveType(type = 'line') {\n  if (annotationTypes[type]) {\n    return type;\n  }\n  console.warn(`Unknown annotation type: '${type}', defaulting to 'line'`);\n  return 'line';\n}\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n * @param {UpdateMode} mode\n */\nfunction updateElements(chart, state, options, mode) {\n  const animations = resolveAnimations(chart, options.animations, mode);\n  const annotations = state.annotations;\n  const elements = resyncElements(state.elements, annotations);\n  for (let i = 0; i < annotations.length; i++) {\n    const annotationOptions = annotations[i];\n    const element = getOrCreateElement(elements, i, annotationOptions.type);\n    const resolver = annotationOptions.setContext(getContext(chart, element, elements, annotationOptions));\n    const properties = element.resolveElementProperties(chart, resolver);\n    properties.skip = toSkip(properties);\n    if ('elements' in properties) {\n      updateSubElements(element, properties.elements, resolver, animations);\n      // Remove the sub-element definitions from properties, so the actual elements\n      // are not overwritten by their definitions\n      delete properties.elements;\n    }\n    if (!defined(element.x)) {\n      // If the element is newly created, assing the properties directly - to\n      // make them readily awailable to any scriptable options. If we do not do this,\n      // the properties retruned by `resolveElementProperties` are available only\n      // after options resolution.\n      Object.assign(element, properties);\n    }\n    Object.assign(element, properties.initProperties);\n    properties.options = resolveAnnotationOptions(resolver);\n    animations.update(element, properties);\n  }\n}\nfunction toSkip(properties) {\n  return isNaN(properties.x) || isNaN(properties.y);\n}\nfunction resolveAnimations(chart, animOpts, mode) {\n  if (mode === 'reset' || mode === 'none' || mode === 'resize') {\n    return directUpdater;\n  }\n  return new Animations(chart, animOpts);\n}\nfunction updateSubElements(mainElement, elements, resolver, animations) {\n  const subElements = mainElement.elements || (mainElement.elements = []);\n  subElements.length = elements.length;\n  for (let i = 0; i < elements.length; i++) {\n    const definition = elements[i];\n    const properties = definition.properties;\n    const subElement = getOrCreateElement(subElements, i, definition.type, definition.initProperties);\n    const subResolver = resolver[definition.optionScope].override(definition);\n    properties.options = resolveAnnotationOptions(subResolver);\n    animations.update(subElement, properties);\n  }\n}\nfunction getOrCreateElement(elements, index, type, initProperties) {\n  const elementClass = annotationTypes[resolveType(type)];\n  let element = elements[index];\n  if (!element || !(element instanceof elementClass)) {\n    element = elements[index] = new elementClass();\n    Object.assign(element, initProperties);\n  }\n  return element;\n}\nfunction resolveAnnotationOptions(resolver) {\n  const elementClass = annotationTypes[resolveType(resolver.type)];\n  const result = {};\n  result.id = resolver.id;\n  result.type = resolver.type;\n  result.drawTime = resolver.drawTime;\n  Object.assign(result, resolveObj(resolver, elementClass.defaults), resolveObj(resolver, elementClass.defaultRoutes));\n  for (const hook of hooks$1) {\n    result[hook] = resolver[hook];\n  }\n  return result;\n}\nfunction resolveObj(resolver, defs) {\n  const result = {};\n  for (const prop of Object.keys(defs)) {\n    const optDefs = defs[prop];\n    const value = resolver[prop];\n    if (isIndexable(prop) && isArray(value)) {\n      result[prop] = value.map(item => resolve(item, optDefs));\n    } else {\n      result[prop] = resolve(value, optDefs);\n    }\n  }\n  return result;\n}\nfunction getContext(chart, element, elements, annotation) {\n  return element.$context || (element.$context = Object.assign(Object.create(chart.getContext()), {\n    element,\n    get elements() {\n      return elements.filter(el => el && el.options);\n    },\n    id: annotation.id,\n    type: 'annotation'\n  }));\n}\nfunction resyncElements(elements, annotations) {\n  const count = annotations.length;\n  const start = elements.length;\n  if (start < count) {\n    const add = count - start;\n    elements.splice(start, 0, ...new Array(add));\n  } else if (start > count) {\n    elements.splice(count, start - count);\n  }\n  return elements;\n}\nvar version = \"3.1.0\";\nconst chartStates = new Map();\nconst isNotDoughnutLabel = annotation => annotation.type !== 'doughnutLabel';\nconst hooks = eventHooks.concat(elementHooks);\nvar annotation = {\n  id: 'annotation',\n  version,\n  beforeRegister() {\n    requireVersion('chart.js', '4.0', Chart.version);\n  },\n  afterRegister() {\n    Chart.register(annotationTypes);\n  },\n  afterUnregister() {\n    Chart.unregister(annotationTypes);\n  },\n  beforeInit(chart) {\n    chartStates.set(chart, {\n      annotations: [],\n      elements: [],\n      visibleElements: [],\n      listeners: {},\n      listened: false,\n      moveListened: false,\n      hooks: {},\n      hooked: false,\n      hovered: []\n    });\n  },\n  beforeUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    const annotations = state.annotations = [];\n    let annotationOptions = options.annotations;\n    if (isObject(annotationOptions)) {\n      Object.keys(annotationOptions).forEach(key => {\n        const value = annotationOptions[key];\n        if (isObject(value)) {\n          value.id = key;\n          annotations.push(value);\n        }\n      });\n    } else if (isArray(annotationOptions)) {\n      annotations.push(...annotationOptions);\n    }\n    verifyScaleOptions(annotations.filter(isNotDoughnutLabel), chart.scales);\n  },\n  afterDataLimits(chart, args) {\n    const state = chartStates.get(chart);\n    adjustScaleRange(chart, args.scale, state.annotations.filter(isNotDoughnutLabel).filter(a => a.display && a.adjustScaleRange));\n  },\n  afterUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    updateListeners(chart, state, options);\n    updateElements(chart, state, options, args.mode);\n    state.visibleElements = state.elements.filter(el => !el.skip && el.options.display);\n    updateHooks(chart, state, options);\n  },\n  beforeDatasetsDraw(chart, _args, options) {\n    draw(chart, 'beforeDatasetsDraw', options.clip);\n  },\n  afterDatasetsDraw(chart, _args, options) {\n    draw(chart, 'afterDatasetsDraw', options.clip);\n  },\n  beforeDatasetDraw(chart, _args, options) {\n    draw(chart, _args.index, options.clip);\n  },\n  beforeDraw(chart, _args, options) {\n    draw(chart, 'beforeDraw', options.clip);\n  },\n  afterDraw(chart, _args, options) {\n    draw(chart, 'afterDraw', options.clip);\n  },\n  beforeEvent(chart, args, options) {\n    const state = chartStates.get(chart);\n    if (handleEvent(state, args.event, options)) {\n      args.changed = true;\n    }\n  },\n  afterDestroy(chart) {\n    chartStates.delete(chart);\n  },\n  getAnnotations(chart) {\n    const state = chartStates.get(chart);\n    return state ? state.elements : [];\n  },\n  // only for testing\n  _getAnnotationElementsAtEventForMode(visibleElements, event, options) {\n    return getElements(visibleElements, event, options);\n  },\n  defaults: {\n    animations: {\n      numbers: {\n        properties: ['x', 'y', 'x2', 'y2', 'width', 'height', 'centerX', 'centerY', 'pointX', 'pointY', 'radius'],\n        type: 'number'\n      },\n      colors: {\n        properties: ['backgroundColor', 'borderColor'],\n        type: 'color'\n      }\n    },\n    clip: true,\n    interaction: {\n      mode: undefined,\n      axis: undefined,\n      intersect: undefined\n    },\n    common: {\n      drawTime: 'afterDatasetsDraw',\n      init: false,\n      label: {}\n    }\n  },\n  descriptors: {\n    _indexable: false,\n    _scriptable: prop => !hooks.includes(prop) && prop !== 'init',\n    annotations: {\n      _allKeys: false,\n      _fallback: (prop, opts) => `elements.${annotationTypes[resolveType(opts.type)].id}`\n    },\n    interaction: {\n      _fallback: true\n    },\n    common: {\n      label: {\n        _indexable: isIndexable,\n        _fallback: true\n      },\n      _indexable: isIndexable\n    }\n  },\n  additionalOptionScopes: ['']\n};\nfunction draw(chart, caller, clip) {\n  const {\n    ctx,\n    chartArea\n  } = chart;\n  const state = chartStates.get(chart);\n  if (clip) {\n    clipArea(ctx, chartArea);\n  }\n  const drawableElements = getDrawableElements(state.visibleElements, caller).sort((a, b) => a.element.options.z - b.element.options.z);\n  for (const item of drawableElements) {\n    drawElement(ctx, chartArea, state, item);\n  }\n  if (clip) {\n    unclipArea(ctx);\n  }\n}\nfunction getDrawableElements(elements, caller) {\n  const drawableElements = [];\n  for (const el of elements) {\n    if (el.options.drawTime === caller) {\n      drawableElements.push({\n        element: el,\n        main: true\n      });\n    }\n    if (el.elements && el.elements.length) {\n      for (const sub of el.elements) {\n        if (sub.options.display && sub.options.drawTime === caller) {\n          drawableElements.push({\n            element: sub\n          });\n        }\n      }\n    }\n  }\n  return drawableElements;\n}\nfunction drawElement(ctx, chartArea, state, item) {\n  const el = item.element;\n  if (item.main) {\n    invokeHook(state, el, 'beforeDraw');\n    el.draw(ctx, chartArea);\n    invokeHook(state, el, 'afterDraw');\n  } else {\n    el.draw(ctx, chartArea);\n  }\n}\nexport { annotation as default };", "map": {"version": 3, "names": ["Element", "DoughnutController", "defaults", "Animations", "Chart", "distanceBetweenPoints", "toRadians", "isObject", "valueOrDefault", "defined", "isFunction", "callback", "isArray", "toFont", "addRoundedRectPath", "toTRBLCorners", "QUARTER_PI", "PI", "HALF_PI", "TWO_THIRDS_PI", "TAU", "isNumber", "RAD_PER_DEG", "toPadding", "isFinite", "getAngleFromPoint", "toDegrees", "clipArea", "unclipArea", "interaction", "modes", "point", "visibleElements", "event", "filterElements", "intersect", "nearest", "options", "getNearestItem", "x", "axis", "y", "getElements", "mode", "inRangeByAxis", "element", "inRange", "getPointByAxis", "center", "filter", "minDistance", "Number", "POSITIVE_INFINITY", "reduce", "nearestItems", "getCenterPoint", "evenPoint", "distance", "push", "sort", "a", "b", "_index", "slice", "rotated", "angle", "cos", "Math", "sin", "cx", "cy", "isOlderPart", "act", "req", "length", "EPSILON", "clamp", "from", "to", "min", "max", "inLimit", "limit", "hitSize", "value", "start", "end", "clampAll", "obj", "key", "Object", "keys", "inPointRange", "radius", "pow", "inBoxRange", "x2", "y2", "borderWidth", "hitTolerance", "inRangeX", "inRangeY", "inLabelRange", "rect", "rotation", "rotPoint", "getElementCenterPoint", "useFinalPosition", "centerX", "centerY", "getProps", "requireVersion", "pkg", "ver", "strict", "parts", "split", "i", "parseInt", "Error", "isPercentString", "s", "endsWith", "toPercent", "parseFloat", "toPositivePercent", "boxAppering", "width", "height", "defaultInitAnimation", "box", "properties", "doughnutLabel", "ellipse", "label", "line", "polygon", "getRelativePosition", "size", "position", "getSize", "positivePercent", "calculateTextAlignment", "textAlign", "measureLabelRectangle", "labelSize", "xAdjust", "yAdjust", "padding", "hasPadding", "positionObj", "toPosition", "calculateLabelPosition$1", "defaultValue", "shouldFit", "fitRatio", "autoFit", "toFonts", "optFont", "font", "fonts", "map", "f", "floor", "lineHeight", "isBoundToPoint", "xValue", "yValue", "adjust", "initAnimationProperties", "chart", "initAnim", "init", "applyDefault", "execCallback", "loadHooks", "hooks", "<PERSON><PERSON><PERSON><PERSON>", "activated", "for<PERSON>ach", "hook", "type", "result", "widthCache", "Map", "notRadius", "isNaN", "fontsKey", "prev", "item", "string", "isImageOrCanvas", "content", "toString", "translate", "ctx", "rotate", "setBorderStyle", "lineCap", "borderCapStyle", "setLineDash", "borderDash", "lineDashOffset", "borderDashOffset", "lineJoin", "borderJoinStyle", "lineWidth", "strokeStyle", "borderColor", "setShadowStyle", "shadowColor", "backgroundShadowColor", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "measureLabelSize", "strokeWidth", "textStrokeWidth", "lines", "mapKey", "join", "_measureText", "has", "set", "calculateLabelSize", "get", "drawBox", "save", "stroke", "fillStyle", "backgroundColor", "beginPath", "w", "h", "borderRadius", "closePath", "fill", "borderShadowColor", "restore", "drawLabel", "globalAlpha", "getOpacity", "opacity", "style", "drawImage", "labels", "optColor", "color", "colors", "textBaseline", "setTextStrokeStyle", "applyLabelDecoration", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "miterLimit", "textStrokeColor", "drawPoint", "pointStyle", "rad", "drawPointStyle", "xOffset", "yOffset", "cornerRadius", "arc", "moveTo", "lineTo", "SQRT1_2", "count", "text", "measureText", "lhs", "l", "lh", "strokeText", "c", "fillText", "elementValue", "positions", "drawCallout", "pointX", "pointY", "callout", "calloutPosition", "display", "resolveCalloutPosition", "isPointInRange", "separatorStart", "separatorEnd", "getCalloutSeparatorCoord", "sideStart", "sideEnd", "getCalloutSideCoord", "margin", "rotatedPoint", "getCalloutSeparatorAdjust", "side", "getCalloutSideAdjust", "includes", "resolveCalloutAutoPosition", "xPoints", "yPoints", "index", "limitedLineScale", "xScaleID", "startProp", "endProp", "yScaleID", "scaleValue", "scale", "fallback", "parse", "getPixelForValue", "retrieveScaleID", "scales", "scaleID", "char<PERSON>t", "axes", "values", "id", "getDimensionByScale", "reverse", "getChartPoint", "chartArea", "xScale", "yScale", "left", "top", "resolveBoxProperties", "right", "bottom", "xDim", "getChartDimensionByScale", "xMin", "xMax", "yDim", "yMin", "yMax", "resolvePointProperties", "adjustCenterX", "adjustCenterY", "getChartCircle", "resolveLineProperties", "area", "resolveFullLineProperties", "resolveLimitedLineProperties", "resolveBoxAndLabelProperties", "initProperties", "elements", "optionScope", "resolveLabelElementProperties$1", "NaN", "endValue", "isHorizontal", "scaleId", "dim", "calculateX", "calculatePosition", "calculateY", "boxOpts", "labelOpts", "padStart", "padEnd", "availableSize", "moveHooks", "eventHooks", "concat", "updateListeners", "state", "listened", "listeners", "moveListened", "annotations", "scope", "click", "handleEvent", "handleMoveEvents", "handleClickEvents", "previous", "hovered", "context", "changed", "dispatchMoveEvents", "checkElements", "indexOf", "dispatchEvent", "handler", "$context", "elementHooks", "updateHooks", "hooked", "invokeHook", "callback<PERSON><PERSON>", "adjustScaleRange", "range", "getScaleLimits", "changeScaleLimit", "handleTickRangeOptions", "verifyScaleOptions", "annotation", "verifyScaleIDs", "suggestedLimit", "scaleLimitDefined", "scaleOptions", "verifyProperties", "console", "warn", "prop", "scaleIDOption", "limits", "NEGATIVE_INFINITY", "updateLimits", "props", "raw", "BoxAnnotation", "mouseX", "mouseY", "draw", "resolveElementProperties", "undefined", "drawTime", "family", "weight", "z", "defaultRoutes", "descriptors", "_fallback", "DoughnutLabelAnnotation", "drawBackground", "_fitRatio", "meta", "getDatasetMeta", "controllerMeta", "getControllerMeta", "getFitRatio", "boxSize", "autoHide", "spacing", "getSortedVisibleDatasetMetas", "controller", "isControllerVisible", "data", "innerRadius", "circumference", "hidden", "getDataVisibility", "offsetX", "offsetY", "square", "space", "_radius", "_counterclockwise", "angles", "getAngles", "_centerX", "_centerY", "hypo", "sqrt", "yk2", "r2", "delta", "_startAngle", "_endAngle", "LabelAnnotation", "visible", "_visible", "getLabelSize", "hBorder<PERSON>idth", "pointInLine", "p1", "p2", "t", "interpolateX", "abs", "interpolateY", "sqr", "v", "rangeLimit", "coordInCurve", "cp", "pointInCurve", "coordAngleInCurve", "angleInCurve", "atan2", "LineAnnotation", "path", "mx", "currentDevicePixelRatio", "my", "isPointInStroke", "isOnLabel", "epsilon", "intersects", "inAxisRange", "curve", "drawCurve", "startOpts", "endOpts", "startAdjust", "endAdjust", "getArrowHeads", "drawArrowHead", "inside", "isLineInArea", "limitLineToArea", "getControlPoint", "labelProperties", "resolveLabelElementProperties", "arrowHeadsDefaults", "arrowHeads", "assign", "controlPoint", "limitPointToArea", "x1", "y1", "dx", "dy", "lenSq", "xx", "yy", "textSize", "calculateLabelPosition", "calculateAutoRotation", "sizes", "rotatedSize", "calculateT", "pt", "xCoordinateSizes", "yCoordinateSizes", "adjustLabelCoordinate", "spaceAround", "calculateTAdjust", "lineSize", "lineW", "lineH", "r", "coordinate", "labelSizes", "halfSize", "arrowStartOpts", "arrowEndOpts", "getLineAdjust", "arrowOpts", "offset", "arrowOffsetX", "drawArrowHeadOnCurve", "startAngle", "endAngle", "ps", "pe", "Path2D", "quadraticCurveTo", "EllipseAnnotation", "pointInEllipse", "p", "xRadius", "yRadius", "cosAngle", "sinAngle", "PointAnnotation", "PolygonAnnotation", "pointIsInPolygon", "axisPoints", "bY", "bX", "first", "el", "sides", "elProps", "buildPointElement", "points", "isInside", "A", "B", "annotationTypes", "describe", "directUpdater", "update", "hooks$1", "resolve", "optDefs", "resolveObj", "isIndexable", "resolveType", "updateElements", "animations", "resolveAnimations", "resyncElements", "annotationOptions", "getOrCreateElement", "resolver", "setContext", "getContext", "skip", "toSkip", "updateSubElements", "resolveAnnotationOptions", "animOpts", "mainElement", "subElements", "definition", "subElement", "subResolver", "override", "elementClass", "defs", "create", "add", "splice", "Array", "version", "chartStates", "isNotDoughnutLabel", "beforeRegister", "afterRegister", "register", "afterUnregister", "unregister", "beforeInit", "beforeUpdate", "args", "afterDataLimits", "afterUpdate", "beforeDatasetsDraw", "_args", "clip", "afterDatasetsDraw", "beforeDatasetDraw", "beforeDraw", "afterDraw", "beforeEvent", "after<PERSON><PERSON><PERSON>", "delete", "getAnnotations", "_getAnnotationElementsAtEventForMode", "numbers", "common", "_indexable", "_scriptable", "_allKeys", "opts", "additionalOptionScopes", "caller", "drawableElements", "getDrawableElements", "drawElement", "main", "sub", "default"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/chartjs-plugin-annotation/dist/chartjs-plugin-annotation.esm.js"], "sourcesContent": ["/*!\n* chartjs-plugin-annotation v3.1.0\n* https://www.chartjs.org/chartjs-plugin-annotation/index\n * (c) 2024 chartjs-plugin-annotation Contributors\n * Released under the MIT License\n */\nimport { Element, DoughnutController, defaults, Animations, Chart } from 'chart.js';\nimport { distanceBetweenPoints, toRadians, isObject, valueOrDefault, defined, isFunction, callback, isArray, toFont, addRoundedRectPath, toTRBLCorners, QUARTER_PI, PI, HALF_PI, TWO_THIRDS_PI, TAU, isNumber, RAD_PER_DEG, toPadding, isFinite, getAngleFromPoint, toDegrees, clipArea, unclipArea } from 'chart.js/helpers';\n\n/**\n * @typedef { import(\"chart.js\").ChartEvent } ChartEvent\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst interaction = {\n  modes: {\n    /**\n     * Point mode returns all elements that hit test based on the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    point(visibleElements, event) {\n      return filterElements(visibleElements, event, {intersect: true});\n    },\n\n    /**\n     * Nearest mode returns the element closest to the event position\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found (only 1 element)\n     */\n    nearest(visibleElements, event, options) {\n      return getNearestItem(visibleElements, event, options);\n    },\n    /**\n     * x mode returns the elements that hit-test at the current x coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    x(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'x'});\n    },\n\n    /**\n     * y mode returns the elements that hit-test at the current y coordinate\n     * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n     * @param {ChartEvent} event - the event we are find things at\n     * @param {Object} options - interaction options to use\n     * @return {AnnotationElement[]} - elements that are found\n     */\n    y(visibleElements, event, options) {\n      return filterElements(visibleElements, event, {intersect: options.intersect, axis: 'y'});\n    }\n  }\n};\n\n/**\n * Returns all elements that hit test based on the event position\n * @param {AnnotationElement[]} visibleElements - annotation elements which are visible\n * @param {ChartEvent} event - the event we are find things at\n * @param {Object} options - interaction options to use\n * @return {AnnotationElement[]} - elements that are found\n */\nfunction getElements(visibleElements, event, options) {\n  const mode = interaction.modes[options.mode] || interaction.modes.nearest;\n  return mode(visibleElements, event, options);\n}\n\nfunction inRangeByAxis(element, event, axis) {\n  if (axis !== 'x' && axis !== 'y') {\n    return element.inRange(event.x, event.y, 'x', true) || element.inRange(event.x, event.y, 'y', true);\n  }\n  return element.inRange(event.x, event.y, axis, true);\n}\n\nfunction getPointByAxis(event, center, axis) {\n  if (axis === 'x') {\n    return {x: event.x, y: center.y};\n  } else if (axis === 'y') {\n    return {x: center.x, y: event.y};\n  }\n  return center;\n}\n\nfunction filterElements(visibleElements, event, options) {\n  return visibleElements.filter((element) => options.intersect ? element.inRange(event.x, event.y) : inRangeByAxis(element, event, options.axis));\n}\n\nfunction getNearestItem(visibleElements, event, options) {\n  let minDistance = Number.POSITIVE_INFINITY;\n\n  return filterElements(visibleElements, event, options)\n    .reduce((nearestItems, element) => {\n      const center = element.getCenterPoint();\n      const evenPoint = getPointByAxis(event, center, options.axis);\n      const distance = distanceBetweenPoints(event, evenPoint);\n      if (distance < minDistance) {\n        nearestItems = [element];\n        minDistance = distance;\n      } else if (distance === minDistance) {\n        // Can have multiple items at the same distance in which case we sort by size\n        nearestItems.push(element);\n      }\n\n      return nearestItems;\n    }, [])\n    .sort((a, b) => a._index - b._index)\n    .slice(0, 1); // return only the top item;\n}\n\n/**\n * @typedef {import('chart.js').Point} Point\n */\n\n/**\n * Rotate a `point` relative to `center` point by `angle`\n * @param {Point} point - the point to rotate\n * @param {Point} center - center point for rotation\n * @param {number} angle - angle for rotation, in radians\n * @returns {Point} rotated point\n */\nfunction rotated(point, center, angle) {\n  const cos = Math.cos(angle);\n  const sin = Math.sin(angle);\n  const cx = center.x;\n  const cy = center.y;\n\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\n\nconst isOlderPart = (act, req) => req > act || (act.length > req.length && act.slice(0, req.length) === req);\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').InteractionAxis } InteractionAxis\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst EPSILON = 0.001;\nconst clamp = (x, from, to) => Math.min(to, Math.max(from, x));\n\n/**\n * @param {{value: number, start: number, end: number}} limit\n * @param {number} hitSize\n * @returns {boolean}\n */\nconst inLimit = (limit, hitSize) => limit.value >= limit.start - hitSize && limit.value <= limit.end + hitSize;\n\n/**\n * @param {Object} obj\n * @param {number} from\n * @param {number} to\n * @returns {Object}\n */\nfunction clampAll(obj, from, to) {\n  for (const key of Object.keys(obj)) {\n    obj[key] = clamp(obj[key], from, to);\n  }\n  return obj;\n}\n\n/**\n * @param {Point} point\n * @param {Point} center\n * @param {number} radius\n * @param {number} hitSize\n * @returns {boolean}\n */\nfunction inPointRange(point, center, radius, hitSize) {\n  if (!point || !center || radius <= 0) {\n    return false;\n  }\n  return (Math.pow(point.x - center.x, 2) + Math.pow(point.y - center.y, 2)) <= Math.pow(radius + hitSize, 2);\n}\n\n/**\n * @param {Point} point\n * @param {{x: number, y: number, x2: number, y2: number}} rect\n * @param {InteractionAxis} axis\n * @param {{borderWidth: number, hitTolerance: number}} hitsize\n * @returns {boolean}\n */\nfunction inBoxRange(point, {x, y, x2, y2}, axis, {borderWidth, hitTolerance}) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const inRangeX = point.x >= x - hitSize - EPSILON && point.x <= x2 + hitSize + EPSILON;\n  const inRangeY = point.y >= y - hitSize - EPSILON && point.y <= y2 + hitSize + EPSILON;\n  if (axis === 'x') {\n    return inRangeX;\n  } else if (axis === 'y') {\n    return inRangeY;\n  }\n  return inRangeX && inRangeY;\n}\n\n/**\n * @param {Point} point\n * @param {rect: {x: number, y: number, x2: number, y2: number}, center: {x: number, y: number}} element\n * @param {InteractionAxis} axis\n * @param {{rotation: number, borderWidth: number, hitTolerance: number}}\n * @returns {boolean}\n */\nfunction inLabelRange(point, {rect, center}, axis, {rotation, borderWidth, hitTolerance}) {\n  const rotPoint = rotated(point, center, toRadians(-rotation));\n  return inBoxRange(rotPoint, rect, axis, {borderWidth, hitTolerance});\n}\n\n/**\n * @param {AnnotationElement} element\n * @param {boolean} useFinalPosition\n * @returns {Point}\n */\nfunction getElementCenterPoint(element, useFinalPosition) {\n  const {centerX, centerY} = element.getProps(['centerX', 'centerY'], useFinalPosition);\n  return {x: centerX, y: centerY};\n}\n\n/**\n * @param {string} pkg\n * @param {string} min\n * @param {string} ver\n * @param {boolean} [strict=true]\n * @returns {boolean}\n */\nfunction requireVersion(pkg, min, ver, strict = true) {\n  const parts = ver.split('.');\n  let i = 0;\n  for (const req of min.split('.')) {\n    const act = parts[i++];\n    if (parseInt(req, 10) < parseInt(act, 10)) {\n      break;\n    }\n    if (isOlderPart(act, req)) {\n      if (strict) {\n        throw new Error(`${pkg} v${ver} is not supported. v${min} or newer is required.`);\n      } else {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nconst isPercentString = (s) => typeof s === 'string' && s.endsWith('%');\nconst toPercent = (s) => parseFloat(s) / 100;\nconst toPositivePercent = (s) => clamp(toPercent(s), 0, 1);\n\nconst boxAppering = (x, y) => ({x, y, x2: x, y2: y, width: 0, height: 0});\nconst defaultInitAnimation = {\n  box: (properties) => boxAppering(properties.centerX, properties.centerY),\n  doughnutLabel: (properties) => boxAppering(properties.centerX, properties.centerY),\n  ellipse: (properties) => ({centerX: properties.centerX, centerY: properties.centerX, radius: 0, width: 0, height: 0}),\n  label: (properties) => boxAppering(properties.centerX, properties.centerY),\n  line: (properties) => boxAppering(properties.x, properties.y),\n  point: (properties) => ({centerX: properties.centerX, centerY: properties.centerY, radius: 0, width: 0, height: 0}),\n  polygon: (properties) => boxAppering(properties.centerX, properties.centerY)\n};\n\n/**\n * @typedef { import('chart.js').FontSpec } FontSpec\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('chart.js').Padding } Padding\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n * @typedef { import('../../types/options').AnnotationPointCoordinates } AnnotationPointCoordinates\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/label').LabelPositionObject } LabelPositionObject\n */\n\n/**\n * @param {number} size\n * @param {number|string} position\n * @returns {number}\n */\nfunction getRelativePosition(size, position) {\n  if (position === 'start') {\n    return 0;\n  }\n  if (position === 'end') {\n    return size;\n  }\n  if (isPercentString(position)) {\n    return toPositivePercent(position) * size;\n  }\n  return size / 2;\n}\n\n/**\n * @param {number} size\n * @param {number|string} value\n * @param {boolean} [positivePercent=true]\n * @returns {number}\n */\nfunction getSize(size, value, positivePercent = true) {\n  if (typeof value === 'number') {\n    return value;\n  } else if (isPercentString(value)) {\n    return (positivePercent ? toPositivePercent(value) : toPercent(value)) * size;\n  }\n  return size;\n}\n\n/**\n * @param {{x: number, width: number}} size\n * @param {CoreLabelOptions} options\n * @returns {number}\n */\nfunction calculateTextAlignment(size, options) {\n  const {x, width} = size;\n  const textAlign = options.textAlign;\n  if (textAlign === 'center') {\n    return x + width / 2;\n  } else if (textAlign === 'end' || textAlign === 'right') {\n    return x + width;\n  }\n  return x;\n}\n\n/**\n * @param {Point} point\n * @param {{height: number, width: number}} labelSize\n * @param {{borderWidth: number, position: {LabelPositionObject|string}, xAdjust: number, yAdjust: number}} options\n * @param {Padding|undefined} padding\n * @returns {{x: number, y: number, x2: number, y2: number, height: number, width: number, centerX: number, centerY: number}}\n */\nfunction measureLabelRectangle(point, labelSize, {borderWidth, position, xAdjust, yAdjust}, padding) {\n  const hasPadding = isObject(padding);\n  const width = labelSize.width + (hasPadding ? padding.width : 0) + borderWidth;\n  const height = labelSize.height + (hasPadding ? padding.height : 0) + borderWidth;\n  const positionObj = toPosition(position);\n  const x = calculateLabelPosition$1(point.x, width, xAdjust, positionObj.x);\n  const y = calculateLabelPosition$1(point.y, height, yAdjust, positionObj.y);\n\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2\n  };\n}\n\n/**\n * @param {LabelPositionObject|string} value\n * @param {string|number} defaultValue\n * @returns {LabelPositionObject}\n */\nfunction toPosition(value, defaultValue = 'center') {\n  if (isObject(value)) {\n    return {\n      x: valueOrDefault(value.x, defaultValue),\n      y: valueOrDefault(value.y, defaultValue),\n    };\n  }\n  value = valueOrDefault(value, defaultValue);\n  return {\n    x: value,\n    y: value\n  };\n}\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {boolean}\n */\nconst shouldFit = (options, fitRatio) => options && options.autoFit && fitRatio < 1;\n\n/**\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n * @returns {FontSpec[]}\n */\nfunction toFonts(options, fitRatio) {\n  const optFont = options.font;\n  const fonts = isArray(optFont) ? optFont : [optFont];\n  if (shouldFit(options, fitRatio)) {\n    return fonts.map(function(f) {\n      const font = toFont(f);\n      font.size = Math.floor(f.size * fitRatio);\n      font.lineHeight = f.lineHeight;\n      return toFont(font);\n    });\n  }\n  return fonts.map(f => toFont(f));\n}\n\n/**\n * @param {AnnotationPointCoordinates} options\n * @returns {boolean}\n */\nfunction isBoundToPoint(options) {\n  return options && (defined(options.xValue) || defined(options.yValue));\n}\n\nfunction calculateLabelPosition$1(start, size, adjust = 0, position) {\n  return start - getRelativePosition(size, position) + adjust;\n}\n\n/**\n * @param {Chart} chart\n * @param {AnnotationBoxModel} properties\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationElement}\n */\nfunction initAnimationProperties(chart, properties, options) {\n  const initAnim = options.init;\n  if (!initAnim) {\n    return;\n  } else if (initAnim === true) {\n    return applyDefault(properties, options);\n  }\n  return execCallback(chart, properties, options);\n}\n\n/**\n * @param {Object} options\n * @param {Array} hooks\n * @param {Object} hooksContainer\n * @returns {boolean}\n */\nfunction loadHooks(options, hooks, hooksContainer) {\n  let activated = false;\n  hooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      activated = true;\n      hooksContainer[hook] = options[hook];\n    } else if (defined(hooksContainer[hook])) {\n      delete hooksContainer[hook];\n    }\n  });\n  return activated;\n}\n\nfunction applyDefault(properties, options) {\n  const type = options.type || 'line';\n  return defaultInitAnimation[type](properties);\n}\n\nfunction execCallback(chart, properties, options) {\n  const result = callback(options.init, [{chart, properties, options}]);\n  if (result === true) {\n    return applyDefault(properties, options);\n  } else if (isObject(result)) {\n    return result;\n  }\n}\n\nconst widthCache = new Map();\nconst notRadius = (radius) => isNaN(radius) || radius <= 0;\nconst fontsKey = (fonts) => fonts.reduce(function(prev, item) {\n  prev += item.string;\n  return prev;\n}, '');\n\n/**\n * @typedef { import('chart.js').Point } Point\n * @typedef { import('../../types/label').CoreLabelOptions } CoreLabelOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n */\n\n/**\n * Determine if content is an image or a canvas.\n * @param {*} content\n * @returns boolean|undefined\n * @todo move this function to chart.js helpers\n */\nfunction isImageOrCanvas(content) {\n  if (content && typeof content === 'object') {\n    const type = content.toString();\n    return (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]');\n  }\n}\n\n/**\n * Set the translation on the canvas if the rotation must be applied.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {Point} point - the point of translation\n * @param {number} rotation - rotation (in degrees) to apply\n */\nfunction translate(ctx, {x, y}, rotation) {\n  if (rotation) {\n    ctx.translate(x, y);\n    ctx.rotate(toRadians(rotation));\n    ctx.translate(-x, -y);\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n * @returns {boolean|undefined}\n */\nfunction setBorderStyle(ctx, options) {\n  if (options && options.borderWidth) {\n    ctx.lineCap = options.borderCapStyle || 'butt';\n    ctx.setLineDash(options.borderDash);\n    ctx.lineDashOffset = options.borderDashOffset;\n    ctx.lineJoin = options.borderJoinStyle || 'miter';\n    ctx.lineWidth = options.borderWidth;\n    ctx.strokeStyle = options.borderColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {Object} options\n */\nfunction setShadowStyle(ctx, options) {\n  ctx.shadowColor = options.backgroundShadowColor;\n  ctx.shadowBlur = options.shadowBlur;\n  ctx.shadowOffsetX = options.shadowOffsetX;\n  ctx.shadowOffsetY = options.shadowOffsetY;\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {CoreLabelOptions} options\n * @returns {{width: number, height: number}}\n */\nfunction measureLabelSize(ctx, options) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    const size = {\n      width: getSize(content.width, options.width),\n      height: getSize(content.height, options.height)\n    };\n    return size;\n  }\n  const fonts = toFonts(options);\n  const strokeWidth = options.textStrokeWidth;\n  const lines = isArray(content) ? content : [content];\n  const mapKey = lines.join() + fontsKey(fonts) + strokeWidth + (ctx._measureText ? '-spriting' : '');\n  if (!widthCache.has(mapKey)) {\n    widthCache.set(mapKey, calculateLabelSize(ctx, lines, fonts, strokeWidth));\n  }\n  return widthCache.get(mapKey);\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {Object} options\n */\nfunction drawBox(ctx, rect, options) {\n  const {x, y, width, height} = rect;\n  ctx.save();\n  setShadowStyle(ctx, options);\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  addRoundedRectPath(ctx, {\n    x, y, w: width, h: height,\n    radius: clampAll(toTRBLCorners(options.borderRadius), 0, Math.min(width, height) / 2)\n  });\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{x: number, y: number, width: number, height: number}} rect\n * @param {CoreLabelOptions} options\n * @param {number} fitRatio\n */\nfunction drawLabel(ctx, rect, options, fitRatio) {\n  const content = options.content;\n  if (isImageOrCanvas(content)) {\n    ctx.save();\n    ctx.globalAlpha = getOpacity(options.opacity, content.style.opacity);\n    ctx.drawImage(content, rect.x, rect.y, rect.width, rect.height);\n    ctx.restore();\n    return;\n  }\n  const labels = isArray(content) ? content : [content];\n  const fonts = toFonts(options, fitRatio);\n  const optColor = options.color;\n  const colors = isArray(optColor) ? optColor : [optColor];\n  const x = calculateTextAlignment(rect, options);\n  const y = rect.y + options.textStrokeWidth / 2;\n  ctx.save();\n  ctx.textBaseline = 'middle';\n  ctx.textAlign = options.textAlign;\n  if (setTextStrokeStyle(ctx, options)) {\n    applyLabelDecoration(ctx, {x, y}, labels, fonts);\n  }\n  applyLabelContent(ctx, {x, y}, labels, {fonts, colors});\n  ctx.restore();\n}\n\nfunction setTextStrokeStyle(ctx, options) {\n  if (options.textStrokeWidth > 0) {\n    // https://stackoverflow.com/questions/13627111/drawing-text-with-an-outer-stroke-with-html5s-canvas\n    ctx.lineJoin = 'round';\n    ctx.miterLimit = 2;\n    ctx.lineWidth = options.textStrokeWidth;\n    ctx.strokeStyle = options.textStrokeColor;\n    return true;\n  }\n}\n\n/**\n * @param {CanvasRenderingContext2D} ctx\n * @param {{radius: number, options: PointAnnotationOptions}} element\n * @param {number} x\n * @param {number} y\n */\nfunction drawPoint(ctx, element, x, y) {\n  const {radius, options} = element;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (isImageOrCanvas(style)) {\n    ctx.save();\n    ctx.translate(x, y);\n    ctx.rotate(rad);\n    ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n    ctx.restore();\n    return;\n  }\n  if (notRadius(radius)) {\n    return;\n  }\n  drawPointStyle(ctx, {x, y, radius, rotation, style, rad});\n}\n\nfunction drawPointStyle(ctx, {x, y, radius, rotation, style, rad}) {\n  let xOffset, yOffset, size, cornerRadius;\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n  default:\n    ctx.arc(x, y, radius, 0, TAU);\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      ctx.rect(x - size, y - size, 2 * size, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n    /* falls through */\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n\n  ctx.fill();\n}\n\nfunction calculateLabelSize(ctx, lines, fonts, strokeWidth) {\n  ctx.save();\n  const count = lines.length;\n  let width = 0;\n  let height = strokeWidth;\n  for (let i = 0; i < count; i++) {\n    const font = fonts[Math.min(i, fonts.length - 1)];\n    ctx.font = font.string;\n    const text = lines[i];\n    width = Math.max(width, ctx.measureText(text).width + strokeWidth);\n    height += font.lineHeight;\n  }\n  ctx.restore();\n  return {width, height};\n}\n\nfunction applyLabelDecoration(ctx, {x, y}, labels, fonts) {\n  ctx.beginPath();\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.font = f.string;\n    ctx.strokeText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n  });\n  ctx.stroke();\n}\n\nfunction applyLabelContent(ctx, {x, y}, labels, {fonts, colors}) {\n  let lhs = 0;\n  labels.forEach(function(l, i) {\n    const c = colors[Math.min(i, colors.length - 1)];\n    const f = fonts[Math.min(i, fonts.length - 1)];\n    const lh = f.lineHeight;\n    ctx.beginPath();\n    ctx.font = f.string;\n    ctx.fillStyle = c;\n    ctx.fillText(l, x, y + lh / 2 + lhs);\n    lhs += lh;\n    ctx.fill();\n  });\n}\n\nfunction getOpacity(value, elementValue) {\n  const opacity = isNumber(value) ? value : elementValue;\n  return isNumber(opacity) ? clamp(opacity, 0, 1) : 1;\n}\n\nconst positions = ['left', 'bottom', 'top', 'right'];\n\n/**\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\n/**\n * Drawa the callout component for labels.\n * @param {CanvasRenderingContext2D} ctx - chart canvas context\n * @param {AnnotationElement} element - the label element\n */\nfunction drawCallout(ctx, element) {\n  const {pointX, pointY, options} = element;\n  const callout = options.callout;\n  const calloutPosition = callout && callout.display && resolveCalloutPosition(element, callout);\n  if (!calloutPosition || isPointInRange(element, callout, calloutPosition)) {\n    return;\n  }\n\n  ctx.save();\n  ctx.beginPath();\n  const stroke = setBorderStyle(ctx, callout);\n  if (!stroke) {\n    return ctx.restore();\n  }\n  const {separatorStart, separatorEnd} = getCalloutSeparatorCoord(element, calloutPosition);\n  const {sideStart, sideEnd} = getCalloutSideCoord(element, calloutPosition, separatorStart);\n  if (callout.margin > 0 || options.borderWidth === 0) {\n    ctx.moveTo(separatorStart.x, separatorStart.y);\n    ctx.lineTo(separatorEnd.x, separatorEnd.y);\n  }\n  ctx.moveTo(sideStart.x, sideStart.y);\n  ctx.lineTo(sideEnd.x, sideEnd.y);\n  const rotatedPoint = rotated({x: pointX, y: pointY}, element.getCenterPoint(), toRadians(-element.rotation));\n  ctx.lineTo(rotatedPoint.x, rotatedPoint.y);\n  ctx.stroke();\n  ctx.restore();\n}\n\nfunction getCalloutSeparatorCoord(element, position) {\n  const {x, y, x2, y2} = element;\n  const adjust = getCalloutSeparatorAdjust(element, position);\n  let separatorStart, separatorEnd;\n  if (position === 'left' || position === 'right') {\n    separatorStart = {x: x + adjust, y};\n    separatorEnd = {x: separatorStart.x, y: y2};\n  } else {\n    //  position 'top' or 'bottom'\n    separatorStart = {x, y: y + adjust};\n    separatorEnd = {x: x2, y: separatorStart.y};\n  }\n  return {separatorStart, separatorEnd};\n}\n\nfunction getCalloutSeparatorAdjust(element, position) {\n  const {width, height, options} = element;\n  const adjust = options.callout.margin + options.borderWidth / 2;\n  if (position === 'right') {\n    return width + adjust;\n  } else if (position === 'bottom') {\n    return height + adjust;\n  }\n  return -adjust;\n}\n\nfunction getCalloutSideCoord(element, position, separatorStart) {\n  const {y, width, height, options} = element;\n  const start = options.callout.start;\n  const side = getCalloutSideAdjust(position, options.callout);\n  let sideStart, sideEnd;\n  if (position === 'left' || position === 'right') {\n    sideStart = {x: separatorStart.x, y: y + getSize(height, start)};\n    sideEnd = {x: sideStart.x + side, y: sideStart.y};\n  } else {\n    //  position 'top' or 'bottom'\n    sideStart = {x: separatorStart.x + getSize(width, start), y: separatorStart.y};\n    sideEnd = {x: sideStart.x, y: sideStart.y + side};\n  }\n  return {sideStart, sideEnd};\n}\n\nfunction getCalloutSideAdjust(position, options) {\n  const side = options.side;\n  if (position === 'left' || position === 'top') {\n    return -side;\n  }\n  return side;\n}\n\nfunction resolveCalloutPosition(element, options) {\n  const position = options.position;\n  if (positions.includes(position)) {\n    return position;\n  }\n  return resolveCalloutAutoPosition(element, options);\n}\n\nfunction resolveCalloutAutoPosition(element, options) {\n  const {x, y, x2, y2, width, height, pointX, pointY, centerX, centerY, rotation} = element;\n  const center = {x: centerX, y: centerY};\n  const start = options.start;\n  const xAdjust = getSize(width, start);\n  const yAdjust = getSize(height, start);\n  const xPoints = [x, x + xAdjust, x + xAdjust, x2];\n  const yPoints = [y + yAdjust, y2, y, y2];\n  const result = [];\n  for (let index = 0; index < 4; index++) {\n    const rotatedPoint = rotated({x: xPoints[index], y: yPoints[index]}, center, toRadians(rotation));\n    result.push({\n      position: positions[index],\n      distance: distanceBetweenPoints(rotatedPoint, {x: pointX, y: pointY})\n    });\n  }\n  return result.sort((a, b) => a.distance - b.distance)[0].position;\n}\n\nfunction isPointInRange(element, callout, position) {\n  const {pointX, pointY} = element;\n  const margin = callout.margin;\n  let x = pointX;\n  let y = pointY;\n  if (position === 'left') {\n    x += margin;\n  } else if (position === 'right') {\n    x -= margin;\n  } else if (position === 'top') {\n    y += margin;\n  } else if (position === 'bottom') {\n    y -= margin;\n  }\n  return element.inRange(x, y);\n}\n\nconst limitedLineScale = {\n  xScaleID: {min: 'xMin', max: 'xMax', start: 'left', end: 'right', startProp: 'x', endProp: 'x2'},\n  yScaleID: {min: 'yMin', max: 'yMax', start: 'bottom', end: 'top', startProp: 'y', endProp: 'y2'}\n};\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import(\"chart.js\").Point } Point\n * @typedef { import('../../types/element').AnnotationBoxModel } AnnotationBoxModel\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n * @typedef { import('../../types/options').LineAnnotationOptions } LineAnnotationOptions\n * @typedef { import('../../types/options').PointAnnotationOptions } PointAnnotationOptions\n * @typedef { import('../../types/options').PolygonAnnotationOptions } PolygonAnnotationOptions\n */\n\n/**\n * @param {Scale} scale\n * @param {number|string} value\n * @param {number} fallback\n * @returns {number}\n */\nfunction scaleValue(scale, value, fallback) {\n  value = typeof value === 'number' ? value : scale.parse(value);\n  return isFinite(value) ? scale.getPixelForValue(value) : fallback;\n}\n\n/**\n * Search the scale defined in chartjs by the axis related to the annotation options key.\n * @param {{ [key: string]: Scale }} scales\n * @param {CoreAnnotationOptions} options\n * @param {string} key\n * @returns {string}\n */\nfunction retrieveScaleID(scales, options, key) {\n  const scaleID = options[key];\n  if (scaleID || key === 'scaleID') {\n    return scaleID;\n  }\n  const axis = key.charAt(0);\n  const axes = Object.values(scales).filter((scale) => scale.axis && scale.axis === axis);\n  if (axes.length) {\n    return axes[0].id;\n  }\n  return axis;\n}\n\n/**\n * @param {Scale} scale\n * @param {{min: number, max: number, start: number, end: number}} options\n * @returns {{start: number, end: number}|undefined}\n */\nfunction getDimensionByScale(scale, options) {\n  if (scale) {\n    const reverse = scale.options.reverse;\n    const start = scaleValue(scale, options.min, reverse ? options.end : options.start);\n    const end = scaleValue(scale, options.max, reverse ? options.start : options.end);\n    return {\n      start,\n      end\n    };\n  }\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {Point}\n */\nfunction getChartPoint(chart, options) {\n  const {chartArea, scales} = chart;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n  let x = chartArea.width / 2;\n  let y = chartArea.height / 2;\n\n  if (xScale) {\n    x = scaleValue(xScale, options.xValue, xScale.left + xScale.width / 2);\n  }\n\n  if (yScale) {\n    y = scaleValue(yScale, options.yValue, yScale.top + yScale.height / 2);\n  }\n  return {x, y};\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxProperties(chart, options) {\n  const scales = chart.scales;\n  const xScale = scales[retrieveScaleID(scales, options, 'xScaleID')];\n  const yScale = scales[retrieveScaleID(scales, options, 'yScaleID')];\n\n  if (!xScale && !yScale) {\n    return {};\n  }\n\n  let {left: x, right: x2} = xScale || chart.chartArea;\n  let {top: y, bottom: y2} = yScale || chart.chartArea;\n  const xDim = getChartDimensionByScale(xScale, {min: options.xMin, max: options.xMax, start: x, end: x2});\n  x = xDim.start;\n  x2 = xDim.end;\n  const yDim = getChartDimensionByScale(yScale, {min: options.yMin, max: options.yMax, start: y2, end: y});\n  y = yDim.start;\n  y2 = yDim.end;\n\n  return {\n    x,\n    y,\n    x2,\n    y2,\n    width: x2 - x,\n    height: y2 - y,\n    centerX: x + (x2 - x) / 2,\n    centerY: y + (y2 - y) / 2\n  };\n}\n\n/**\n * @param {Chart} chart\n * @param {PointAnnotationOptions|PolygonAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolvePointProperties(chart, options) {\n  if (!isBoundToPoint(options)) {\n    const box = resolveBoxProperties(chart, options);\n    let radius = options.radius;\n    if (!radius || isNaN(radius)) {\n      radius = Math.min(box.width, box.height) / 2;\n      options.radius = radius;\n    }\n    const size = radius * 2;\n    const adjustCenterX = box.centerX + options.xAdjust;\n    const adjustCenterY = box.centerY + options.yAdjust;\n    return {\n      x: adjustCenterX - radius,\n      y: adjustCenterY - radius,\n      x2: adjustCenterX + radius,\n      y2: adjustCenterY + radius,\n      centerX: adjustCenterX,\n      centerY: adjustCenterY,\n      width: size,\n      height: size,\n      radius\n    };\n  }\n  return getChartCircle(chart, options);\n}\n/**\n * @param {Chart} chart\n * @param {LineAnnotationOptions} options\n * @returns {AnnotationBoxModel}\n */\nfunction resolveLineProperties(chart, options) {\n  const {scales, chartArea} = chart;\n  const scale = scales[options.scaleID];\n  const area = {x: chartArea.left, y: chartArea.top, x2: chartArea.right, y2: chartArea.bottom};\n\n  if (scale) {\n    resolveFullLineProperties(scale, area, options);\n  } else {\n    resolveLimitedLineProperties(scales, area, options);\n  }\n  return area;\n}\n\n/**\n * @param {Chart} chart\n * @param {CoreAnnotationOptions} options\n * @param {boolean} [centerBased=false]\n * @returns {AnnotationBoxModel}\n */\nfunction resolveBoxAndLabelProperties(chart, options) {\n  const properties = resolveBoxProperties(chart, options);\n  properties.initProperties = initAnimationProperties(chart, properties, options);\n  properties.elements = [{\n    type: 'label',\n    optionScope: 'label',\n    properties: resolveLabelElementProperties$1(chart, properties, options),\n    initProperties: properties.initProperties\n  }];\n  return properties;\n}\n\nfunction getChartCircle(chart, options) {\n  const point = getChartPoint(chart, options);\n  const size = options.radius * 2;\n  return {\n    x: point.x - options.radius + options.xAdjust,\n    y: point.y - options.radius + options.yAdjust,\n    x2: point.x + options.radius + options.xAdjust,\n    y2: point.y + options.radius + options.yAdjust,\n    centerX: point.x + options.xAdjust,\n    centerY: point.y + options.yAdjust,\n    radius: options.radius,\n    width: size,\n    height: size\n  };\n}\n\nfunction getChartDimensionByScale(scale, options) {\n  const result = getDimensionByScale(scale, options) || options;\n  return {\n    start: Math.min(result.start, result.end),\n    end: Math.max(result.start, result.end)\n  };\n}\n\nfunction resolveFullLineProperties(scale, area, options) {\n  const min = scaleValue(scale, options.value, NaN);\n  const max = scaleValue(scale, options.endValue, min);\n  if (scale.isHorizontal()) {\n    area.x = min;\n    area.x2 = max;\n  } else {\n    area.y = min;\n    area.y2 = max;\n  }\n}\n\nfunction resolveLimitedLineProperties(scales, area, options) {\n  for (const scaleId of Object.keys(limitedLineScale)) {\n    const scale = scales[retrieveScaleID(scales, options, scaleId)];\n    if (scale) {\n      const {min, max, start, end, startProp, endProp} = limitedLineScale[scaleId];\n      const dim = getDimensionByScale(scale, {min: options[min], max: options[max], start: scale[start], end: scale[end]});\n      area[startProp] = dim.start;\n      area[endProp] = dim.end;\n    }\n  }\n}\n\nfunction calculateX({properties, options}, labelSize, position, padding) {\n  const {x: start, x2: end, width: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.x,\n    padding: {start: padding.left, end: padding.right},\n    adjust: options.label.xAdjust,\n    size: labelSize.width\n  });\n}\n\nfunction calculateY({properties, options}, labelSize, position, padding) {\n  const {y: start, y2: end, height: size} = properties;\n  return calculatePosition({start, end, size, borderWidth: options.borderWidth}, {\n    position: position.y,\n    padding: {start: padding.top, end: padding.bottom},\n    adjust: options.label.yAdjust,\n    size: labelSize.height\n  });\n}\n\nfunction calculatePosition(boxOpts, labelOpts) {\n  const {start, end, borderWidth} = boxOpts;\n  const {position, padding: {start: padStart, end: padEnd}, adjust} = labelOpts;\n  const availableSize = end - borderWidth - start - padStart - padEnd - labelOpts.size;\n  return start + borderWidth / 2 + adjust + getRelativePosition(availableSize, position);\n}\n\nfunction resolveLabelElementProperties$1(chart, properties, options) {\n  const label = options.label;\n  label.backgroundColor = 'transparent';\n  label.callout.display = false;\n  const position = toPosition(label.position);\n  const padding = toPadding(label.padding);\n  const labelSize = measureLabelSize(chart.ctx, label);\n  const x = calculateX({properties, options}, labelSize, position, padding);\n  const y = calculateY({properties, options}, labelSize, position, padding);\n  const width = labelSize.width + padding.width;\n  const height = labelSize.height + padding.height;\n  return {\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n    width,\n    height,\n    centerX: x + width / 2,\n    centerY: y + height / 2,\n    rotation: label.rotation\n  };\n\n}\n\nconst moveHooks = ['enter', 'leave'];\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\nconst eventHooks = moveHooks.concat('click');\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateListeners(chart, state, options) {\n  state.listened = loadHooks(options, eventHooks, state.listeners);\n  state.moveListened = false;\n\n  moveHooks.forEach(hook => {\n    if (isFunction(options[hook])) {\n      state.moveListened = true;\n    }\n  });\n\n  if (!state.listened || !state.moveListened) {\n    state.annotations.forEach(scope => {\n      if (!state.listened && isFunction(scope.click)) {\n        state.listened = true;\n      }\n      if (!state.moveListened) {\n        moveHooks.forEach(hook => {\n          if (isFunction(scope[hook])) {\n            state.listened = true;\n            state.moveListened = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {ChartEvent} event\n * @param {AnnotationPluginOptions} options\n * @return {boolean|undefined}\n */\nfunction handleEvent(state, event, options) {\n  if (state.listened) {\n    switch (event.type) {\n    case 'mousemove':\n    case 'mouseout':\n      return handleMoveEvents(state, event, options);\n    case 'click':\n      return handleClickEvents(state, event, options);\n    }\n  }\n}\n\nfunction handleMoveEvents(state, event, options) {\n  if (!state.moveListened) {\n    return;\n  }\n\n  let elements;\n\n  if (event.type === 'mousemove') {\n    elements = getElements(state.visibleElements, event, options.interaction);\n  } else {\n    elements = [];\n  }\n\n  const previous = state.hovered;\n  state.hovered = elements;\n\n  const context = {state, event};\n  let changed = dispatchMoveEvents(context, 'leave', previous, elements);\n  return dispatchMoveEvents(context, 'enter', elements, previous) || changed;\n}\n\nfunction dispatchMoveEvents({state, event}, hook, elements, checkElements) {\n  let changed;\n  for (const element of elements) {\n    if (checkElements.indexOf(element) < 0) {\n      changed = dispatchEvent(element.options[hook] || state.listeners[hook], element, event) || changed;\n    }\n  }\n  return changed;\n}\n\nfunction handleClickEvents(state, event, options) {\n  const listeners = state.listeners;\n  const elements = getElements(state.visibleElements, event, options.interaction);\n  let changed;\n  for (const element of elements) {\n    changed = dispatchEvent(element.options.click || listeners.click, element, event) || changed;\n  }\n  return changed;\n}\n\nfunction dispatchEvent(handler, element, event) {\n  return callback(handler, [element.$context, event]) === true;\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n * @typedef { import('../../types/element').AnnotationElement } AnnotationElement\n */\n\nconst elementHooks = ['afterDraw', 'beforeDraw'];\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n */\nfunction updateHooks(chart, state, options) {\n  const visibleElements = state.visibleElements;\n  state.hooked = loadHooks(options, elementHooks, state.hooks);\n\n  if (!state.hooked) {\n    visibleElements.forEach(scope => {\n      if (!state.hooked) {\n        elementHooks.forEach(hook => {\n          if (isFunction(scope.options[hook])) {\n            state.hooked = true;\n          }\n        });\n      }\n    });\n  }\n}\n\n/**\n * @param {Object} state\n * @param {AnnotationElement} element\n * @param {string} hook\n */\nfunction invokeHook(state, element, hook) {\n  if (state.hooked) {\n    const callbackHook = element.options[hook] || state.hooks[hook];\n    return callback(callbackHook, [element.$context]);\n  }\n}\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").Scale } Scale\n * @typedef { import('../../types/options').CoreAnnotationOptions } CoreAnnotationOptions\n */\n\n/**\n * @param {Chart} chart\n * @param {Scale} scale\n * @param {CoreAnnotationOptions[]} annotations\n */\nfunction adjustScaleRange(chart, scale, annotations) {\n  const range = getScaleLimits(chart.scales, scale, annotations);\n  let changed = changeScaleLimit(scale, range, 'min', 'suggestedMin');\n  changed = changeScaleLimit(scale, range, 'max', 'suggestedMax') || changed;\n  if (changed && isFunction(scale.handleTickRangeOptions)) {\n    scale.handleTickRangeOptions();\n  }\n}\n\n/**\n * @param {CoreAnnotationOptions[]} annotations\n * @param {{ [key: string]: Scale }} scales\n */\nfunction verifyScaleOptions(annotations, scales) {\n  for (const annotation of annotations) {\n    verifyScaleIDs(annotation, scales);\n  }\n}\n\nfunction changeScaleLimit(scale, range, limit, suggestedLimit) {\n  if (isFinite(range[limit]) && !scaleLimitDefined(scale.options, limit, suggestedLimit)) {\n    const changed = scale[limit] !== range[limit];\n    scale[limit] = range[limit];\n    return changed;\n  }\n}\n\nfunction scaleLimitDefined(scaleOptions, limit, suggestedLimit) {\n  return defined(scaleOptions[limit]) || defined(scaleOptions[suggestedLimit]);\n}\n\nfunction verifyScaleIDs(annotation, scales) {\n  for (const key of ['scaleID', 'xScaleID', 'yScaleID']) {\n    const scaleID = retrieveScaleID(scales, annotation, key);\n    if (scaleID && !scales[scaleID] && verifyProperties(annotation, key)) {\n      console.warn(`No scale found with id '${scaleID}' for annotation '${annotation.id}'`);\n    }\n  }\n}\n\nfunction verifyProperties(annotation, key) {\n  if (key === 'scaleID') {\n    return true;\n  }\n  const axis = key.charAt(0);\n  for (const prop of ['Min', 'Max', 'Value']) {\n    if (defined(annotation[axis + prop])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction getScaleLimits(scales, scale, annotations) {\n  const axis = scale.axis;\n  const scaleID = scale.id;\n  const scaleIDOption = axis + 'ScaleID';\n  const limits = {\n    min: valueOrDefault(scale.min, Number.NEGATIVE_INFINITY),\n    max: valueOrDefault(scale.max, Number.POSITIVE_INFINITY)\n  };\n  for (const annotation of annotations) {\n    if (annotation.scaleID === scaleID) {\n      updateLimits(annotation, scale, ['value', 'endValue'], limits);\n    } else if (retrieveScaleID(scales, annotation, scaleIDOption) === scaleID) {\n      updateLimits(annotation, scale, [axis + 'Min', axis + 'Max', axis + 'Value'], limits);\n    }\n  }\n  return limits;\n}\n\nfunction updateLimits(annotation, scale, props, limits) {\n  for (const prop of props) {\n    const raw = annotation[prop];\n    if (defined(raw)) {\n      const value = scale.parse(raw);\n      limits.min = Math.min(limits.min, value);\n      limits.max = Math.max(limits.max, value);\n    }\n  }\n}\n\nclass BoxAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y} = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    return inBoxRange({x, y}, this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis, this.options);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.options.rotation);\n    drawBox(ctx, this, this.options);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n}\n\nBoxAnnotation.id = 'boxAnnotation';\n\nBoxAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'transparent',\n    borderWidth: 0,\n    callout: {\n      display: false\n    },\n    color: 'black',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: undefined,\n    textAlign: 'start',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nBoxAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nBoxAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nclass DoughnutLabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: 0, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    if (!options.display || !options.content) {\n      return;\n    }\n    drawBackground(ctx, this);\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawLabel(ctx, this, options, this._fitRatio);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const meta = getDatasetMeta(chart, options);\n    if (!meta) {\n      return {};\n    }\n    const {controllerMeta, point, radius} = getControllerMeta(chart, options, meta);\n    let labelSize = measureLabelSize(chart.ctx, options);\n    const _fitRatio = getFitRatio(labelSize, radius);\n    if (shouldFit(options, _fitRatio)) {\n      labelSize = {width: labelSize.width * _fitRatio, height: labelSize.height * _fitRatio};\n    }\n    const {position, xAdjust, yAdjust} = options;\n    const boxSize = measureLabelRectangle(point, labelSize, {borderWidth: 0, position, xAdjust, yAdjust});\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      ...boxSize,\n      ...controllerMeta,\n      rotation: options.rotation,\n      _fitRatio\n    };\n  }\n}\n\nDoughnutLabelAnnotation.id = 'doughnutLabelAnnotation';\n\nDoughnutLabelAnnotation.defaults = {\n  autoFit: true,\n  autoHide: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  spacing: 1,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  yAdjust: 0\n};\n\nDoughnutLabelAnnotation.defaultRoutes = {\n};\n\nfunction getDatasetMeta(chart, options) {\n  return chart.getSortedVisibleDatasetMetas().reduce(function(result, value) {\n    const controller = value.controller;\n    if (controller instanceof DoughnutController &&\n      isControllerVisible(chart, options, value.data) &&\n      (!result || controller.innerRadius < result.controller.innerRadius) &&\n      controller.options.circumference >= 90) {\n      return value;\n    }\n    return result;\n  }, undefined);\n}\n\nfunction isControllerVisible(chart, options, elements) {\n  if (!options.autoHide) {\n    return true;\n  }\n  for (let i = 0; i < elements.length; i++) {\n    if (!elements[i].hidden && chart.getDataVisibility(i)) {\n      return true;\n    }\n  }\n}\n\nfunction getControllerMeta({chartArea}, options, meta) {\n  const {left, top, right, bottom} = chartArea;\n  const {innerRadius, offsetX, offsetY} = meta.controller;\n  const x = (left + right) / 2 + offsetX;\n  const y = (top + bottom) / 2 + offsetY;\n  const square = {\n    left: Math.max(x - innerRadius, left),\n    right: Math.min(x + innerRadius, right),\n    top: Math.max(y - innerRadius, top),\n    bottom: Math.min(y + innerRadius, bottom)\n  };\n  const point = {\n    x: (square.left + square.right) / 2,\n    y: (square.top + square.bottom) / 2\n  };\n  const space = options.spacing + options.borderWidth / 2;\n  const _radius = innerRadius - space;\n  const _counterclockwise = point.y > y;\n  const side = _counterclockwise ? top + space : bottom - space;\n  const angles = getAngles(side, x, y, _radius);\n  const controllerMeta = {\n    _centerX: x,\n    _centerY: y,\n    _radius,\n    _counterclockwise,\n    ...angles\n  };\n  return {\n    controllerMeta,\n    point,\n    radius: Math.min(innerRadius, Math.min(square.right - square.left, square.bottom - square.top) / 2)\n  };\n}\n\nfunction getFitRatio({width, height}, radius) {\n  const hypo = Math.sqrt(Math.pow(width, 2) + Math.pow(height, 2));\n  return (radius * 2) / hypo;\n}\n\nfunction getAngles(y, centerX, centerY, radius) {\n  const yk2 = Math.pow(centerY - y, 2);\n  const r2 = Math.pow(radius, 2);\n  const b = centerX * -2;\n  const c = Math.pow(centerX, 2) + yk2 - r2;\n  const delta = Math.pow(b, 2) - (4 * c);\n  if (delta <= 0) {\n    return {\n      _startAngle: 0,\n      _endAngle: TAU\n    };\n  }\n  const start = (-b - Math.sqrt(delta)) / 2;\n  const end = (-b + Math.sqrt(delta)) / 2;\n  return {\n    _startAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: start, y}).angle,\n    _endAngle: getAngleFromPoint({x: centerX, y: centerY}, {x: end, y}).angle\n  };\n}\n\nfunction drawBackground(ctx, element) {\n  const {_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise, options} = element;\n  ctx.save();\n  const stroke = setBorderStyle(ctx, options);\n  ctx.fillStyle = options.backgroundColor;\n  ctx.beginPath();\n  ctx.arc(_centerX, _centerY, _radius, _startAngle, _endAngle, _counterclockwise);\n  ctx.closePath();\n  ctx.fill();\n  if (stroke) {\n    ctx.stroke();\n  }\n  ctx.restore();\n}\n\nclass LabelAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    return inLabelRange(\n      {x: mouseX, y: mouseY},\n      {rect: this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), center: this.getCenterPoint(useFinalPosition)},\n      axis,\n      {rotation: this.rotation, borderWidth: this.options.borderWidth, hitTolerance: this.options.hitTolerance}\n    );\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const visible = !defined(this._visible) || this._visible;\n    if (!options.display || !options.content || !visible) {\n      return;\n    }\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), this.rotation);\n    drawCallout(ctx, this);\n    drawBox(ctx, this, options);\n    drawLabel(ctx, getLabelSize(this), options);\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    let point;\n    if (!isBoundToPoint(options)) {\n      const {centerX, centerY} = resolveBoxProperties(chart, options);\n      point = {x: centerX, y: centerY};\n    } else {\n      point = getChartPoint(chart, options);\n    }\n    const padding = toPadding(options.padding);\n    const labelSize = measureLabelSize(chart.ctx, options);\n    const boxSize = measureLabelRectangle(point, labelSize, options, padding);\n    return {\n      initProperties: initAnimationProperties(chart, boxSize, options),\n      pointX: point.x,\n      pointY: point.y,\n      ...boxSize,\n      rotation: options.rotation\n    };\n  }\n}\n\nLabelAnnotation.id = 'labelAnnotation';\n\nLabelAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundColor: 'transparent',\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderRadius: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 0,\n  callout: {\n    borderCapStyle: 'butt',\n    borderColor: undefined,\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderWidth: 1,\n    display: false,\n    margin: 5,\n    position: 'auto',\n    side: 5,\n    start: '50%',\n  },\n  color: 'black',\n  content: null,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: undefined,\n    size: undefined,\n    style: undefined,\n    weight: undefined\n  },\n  height: undefined,\n  hitTolerance: 0,\n  init: undefined,\n  opacity: undefined,\n  padding: 6,\n  position: 'center',\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  textAlign: 'center',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  width: undefined,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nLabelAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction getLabelSize({x, y, width, height, options}) {\n  const hBorderWidth = options.borderWidth / 2;\n  const padding = toPadding(options.padding);\n  return {\n    x: x + padding.left + hBorderWidth,\n    y: y + padding.top + hBorderWidth,\n    width: width - padding.left - padding.right - options.borderWidth,\n    height: height - padding.top - padding.bottom - options.borderWidth\n  };\n}\n\nconst pointInLine = (p1, p2, t) => ({x: p1.x + t * (p2.x - p1.x), y: p1.y + t * (p2.y - p1.y)});\nconst interpolateX = (y, p1, p2) => pointInLine(p1, p2, Math.abs((y - p1.y) / (p2.y - p1.y))).x;\nconst interpolateY = (x, p1, p2) => pointInLine(p1, p2, Math.abs((x - p1.x) / (p2.x - p1.x))).y;\nconst sqr = v => v * v;\nconst rangeLimit = (mouseX, mouseY, {x, y, x2, y2}, axis) => axis === 'y' ? {start: Math.min(y, y2), end: Math.max(y, y2), value: mouseY} : {start: Math.min(x, x2), end: Math.max(x, x2), value: mouseX};\n// http://www.independent-software.com/determining-coordinates-on-a-html-canvas-bezier-curve.html\nconst coordInCurve = (start, cp, end, t) => (1 - t) * (1 - t) * start + 2 * (1 - t) * t * cp + t * t * end;\nconst pointInCurve = (start, cp, end, t) => ({x: coordInCurve(start.x, cp.x, end.x, t), y: coordInCurve(start.y, cp.y, end.y, t)});\nconst coordAngleInCurve = (start, cp, end, t) => 2 * (1 - t) * (cp - start) + 2 * t * (end - cp);\nconst angleInCurve = (start, cp, end, t) => -Math.atan2(coordAngleInCurve(start.x, cp.x, end.x, t), coordAngleInCurve(start.y, cp.y, end.y, t)) + 0.5 * PI;\n\nclass LineAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      const point = {mouseX, mouseY};\n      const {path, ctx} = this;\n      if (path) {\n        setBorderStyle(ctx, this.options);\n        ctx.lineWidth += this.options.hitTolerance;\n        const {chart} = this.$context;\n        const mx = mouseX * chart.currentDevicePixelRatio;\n        const my = mouseY * chart.currentDevicePixelRatio;\n        const result = ctx.isPointInStroke(path, mx, my) || isOnLabel(this, point, useFinalPosition);\n        ctx.restore();\n        return result;\n      }\n      const epsilon = sqr(hitSize);\n      return intersects(this, point, epsilon, useFinalPosition) || isOnLabel(this, point, useFinalPosition);\n    }\n    return inAxisRange(this, {mouseX, mouseY}, axis, {hitSize, useFinalPosition});\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {x, y, x2, y2, cp, options} = this;\n\n    ctx.save();\n    if (!setBorderStyle(ctx, options)) {\n      // no border width, then line is not drawn\n      return ctx.restore();\n    }\n    setShadowStyle(ctx, options);\n\n    const length = Math.sqrt(Math.pow(x2 - x, 2) + Math.pow(y2 - y, 2));\n    if (options.curve && cp) {\n      drawCurve(ctx, this, cp, length);\n      return ctx.restore();\n    }\n    const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(this);\n    const angle = Math.atan2(y2 - y, x2 - x);\n    ctx.translate(x, y);\n    ctx.rotate(angle);\n    ctx.beginPath();\n    ctx.moveTo(0 + startAdjust, 0);\n    ctx.lineTo(length - endAdjust, 0);\n    ctx.shadowColor = options.borderShadowColor;\n    ctx.stroke();\n    drawArrowHead(ctx, 0, startAdjust, startOpts);\n    drawArrowHead(ctx, length, -endAdjust, endOpts);\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    const area = resolveLineProperties(chart, options);\n    const {x, y, x2, y2} = area;\n    const inside = isLineInArea(area, chart.chartArea);\n    const properties = inside\n      ? limitLineToArea({x, y}, {x: x2, y: y2}, chart.chartArea)\n      : {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n    properties.centerX = (x2 + x) / 2;\n    properties.centerY = (y2 + y) / 2;\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    if (options.curve) {\n      const p1 = {x: properties.x, y: properties.y};\n      const p2 = {x: properties.x2, y: properties.y2};\n      properties.cp = getControlPoint(properties, options, distanceBetweenPoints(p1, p2));\n    }\n    const labelProperties = resolveLabelElementProperties(chart, properties, options.label);\n    // additonal prop to manage zoom/pan\n    labelProperties._visible = inside;\n\n    properties.elements = [{\n      type: 'label',\n      optionScope: 'label',\n      properties: labelProperties,\n      initProperties: properties.initProperties\n    }];\n    return properties;\n  }\n}\n\nLineAnnotation.id = 'lineAnnotation';\n\nconst arrowHeadsDefaults = {\n  backgroundColor: undefined,\n  backgroundShadowColor: undefined,\n  borderColor: undefined,\n  borderDash: undefined,\n  borderDashOffset: undefined,\n  borderShadowColor: undefined,\n  borderWidth: undefined,\n  display: undefined,\n  fill: undefined,\n  length: undefined,\n  shadowBlur: undefined,\n  shadowOffsetX: undefined,\n  shadowOffsetY: undefined,\n  width: undefined\n};\n\nLineAnnotation.defaults = {\n  adjustScaleRange: true,\n  arrowHeads: {\n    display: false,\n    end: Object.assign({}, arrowHeadsDefaults),\n    fill: false,\n    length: 12,\n    start: Object.assign({}, arrowHeadsDefaults),\n    width: 6\n  },\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 2,\n  curve: false,\n  controlPoint: {\n    y: '-50%'\n  },\n  display: true,\n  endValue: undefined,\n  init: undefined,\n  hitTolerance: 0,\n  label: {\n    backgroundColor: 'rgba(0,0,0,0.8)',\n    backgroundShadowColor: 'transparent',\n    borderCapStyle: 'butt',\n    borderColor: 'black',\n    borderDash: [],\n    borderDashOffset: 0,\n    borderJoinStyle: 'miter',\n    borderRadius: 6,\n    borderShadowColor: 'transparent',\n    borderWidth: 0,\n    callout: Object.assign({}, LabelAnnotation.defaults.callout),\n    color: '#fff',\n    content: null,\n    display: false,\n    drawTime: undefined,\n    font: {\n      family: undefined,\n      lineHeight: undefined,\n      size: undefined,\n      style: undefined,\n      weight: 'bold'\n    },\n    height: undefined,\n    hitTolerance: undefined,\n    opacity: undefined,\n    padding: 6,\n    position: 'center',\n    rotation: 0,\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    textAlign: 'center',\n    textStrokeColor: undefined,\n    textStrokeWidth: 0,\n    width: undefined,\n    xAdjust: 0,\n    yAdjust: 0,\n    z: undefined\n  },\n  scaleID: undefined,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  value: undefined,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nLineAnnotation.descriptors = {\n  arrowHeads: {\n    start: {\n      _fallback: true\n    },\n    end: {\n      _fallback: true\n    },\n    _fallback: true\n  }\n};\n\nLineAnnotation.defaultRoutes = {\n  borderColor: 'color'\n};\n\nfunction inAxisRange(element, {mouseX, mouseY}, axis, {hitSize, useFinalPosition}) {\n  const limit = rangeLimit(mouseX, mouseY, element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition), axis);\n  return inLimit(limit, hitSize) || isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis);\n}\n\nfunction isLineInArea({x, y, x2, y2}, {top, right, bottom, left}) {\n  return !(\n    (x < left && x2 < left) ||\n    (x > right && x2 > right) ||\n    (y < top && y2 < top) ||\n    (y > bottom && y2 > bottom)\n  );\n}\n\nfunction limitPointToArea({x, y}, p2, {top, right, bottom, left}) {\n  if (x < left) {\n    y = interpolateY(left, {x, y}, p2);\n    x = left;\n  }\n  if (x > right) {\n    y = interpolateY(right, {x, y}, p2);\n    x = right;\n  }\n  if (y < top) {\n    x = interpolateX(top, {x, y}, p2);\n    y = top;\n  }\n  if (y > bottom) {\n    x = interpolateX(bottom, {x, y}, p2);\n    y = bottom;\n  }\n  return {x, y};\n}\n\nfunction limitLineToArea(p1, p2, area) {\n  const {x, y} = limitPointToArea(p1, p2, area);\n  const {x: x2, y: y2} = limitPointToArea(p2, p1, area);\n  return {x, y, x2, y2, width: Math.abs(x2 - x), height: Math.abs(y2 - y)};\n}\n\nfunction intersects(element, {mouseX, mouseY}, epsilon = EPSILON, useFinalPosition) {\n  // Adapted from https://stackoverflow.com/a/6853926/25507\n  const {x: x1, y: y1, x2, y2} = element.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const lenSq = sqr(dx) + sqr(dy);\n  const t = lenSq === 0 ? -1 : ((mouseX - x1) * dx + (mouseY - y1) * dy) / lenSq;\n\n  let xx, yy;\n  if (t < 0) {\n    xx = x1;\n    yy = y1;\n  } else if (t > 1) {\n    xx = x2;\n    yy = y2;\n  } else {\n    xx = x1 + t * dx;\n    yy = y1 + t * dy;\n  }\n  return (sqr(mouseX - xx) + sqr(mouseY - yy)) <= epsilon;\n}\n\nfunction isOnLabel(element, {mouseX, mouseY}, useFinalPosition, axis) {\n  const label = element.label;\n  return label.options.display && label.inRange(mouseX, mouseY, axis, useFinalPosition);\n}\n\nfunction resolveLabelElementProperties(chart, properties, options) {\n  const borderWidth = options.borderWidth;\n  const padding = toPadding(options.padding);\n  const textSize = measureLabelSize(chart.ctx, options);\n  const width = textSize.width + padding.width + borderWidth;\n  const height = textSize.height + padding.height + borderWidth;\n  return calculateLabelPosition(properties, options, {width, height, padding}, chart.chartArea);\n}\n\nfunction calculateAutoRotation(properties) {\n  const {x, y, x2, y2} = properties;\n  const rotation = Math.atan2(y2 - y, x2 - x);\n  // Flip the rotation if it goes > PI/2 or < -PI/2, so label stays upright\n  return rotation > PI / 2 ? rotation - PI : rotation < PI / -2 ? rotation + PI : rotation;\n}\n\nfunction calculateLabelPosition(properties, label, sizes, chartArea) {\n  const {width, height, padding} = sizes;\n  const {xAdjust, yAdjust} = label;\n  const p1 = {x: properties.x, y: properties.y};\n  const p2 = {x: properties.x2, y: properties.y2};\n  const rotation = label.rotation === 'auto' ? calculateAutoRotation(properties) : toRadians(label.rotation);\n  const size = rotatedSize(width, height, rotation);\n  const t = calculateT(properties, label, {labelSize: size, padding}, chartArea);\n  const pt = properties.cp ? pointInCurve(p1, properties.cp, p2, t) : pointInLine(p1, p2, t);\n  const xCoordinateSizes = {size: size.w, min: chartArea.left, max: chartArea.right, padding: padding.left};\n  const yCoordinateSizes = {size: size.h, min: chartArea.top, max: chartArea.bottom, padding: padding.top};\n  const centerX = adjustLabelCoordinate(pt.x, xCoordinateSizes) + xAdjust;\n  const centerY = adjustLabelCoordinate(pt.y, yCoordinateSizes) + yAdjust;\n  return {\n    x: centerX - (width / 2),\n    y: centerY - (height / 2),\n    x2: centerX + (width / 2),\n    y2: centerY + (height / 2),\n    centerX,\n    centerY,\n    pointX: pt.x,\n    pointY: pt.y,\n    width,\n    height,\n    rotation: toDegrees(rotation)\n  };\n}\n\nfunction rotatedSize(width, height, rotation) {\n  const cos = Math.cos(rotation);\n  const sin = Math.sin(rotation);\n  return {\n    w: Math.abs(width * cos) + Math.abs(height * sin),\n    h: Math.abs(width * sin) + Math.abs(height * cos)\n  };\n}\n\nfunction calculateT(properties, label, sizes, chartArea) {\n  let t;\n  const space = spaceAround(properties, chartArea);\n  if (label.position === 'start') {\n    t = calculateTAdjust({w: properties.x2 - properties.x, h: properties.y2 - properties.y}, sizes, label, space);\n  } else if (label.position === 'end') {\n    t = 1 - calculateTAdjust({w: properties.x - properties.x2, h: properties.y - properties.y2}, sizes, label, space);\n  } else {\n    t = getRelativePosition(1, label.position);\n  }\n  return t;\n}\n\nfunction calculateTAdjust(lineSize, sizes, label, space) {\n  const {labelSize, padding} = sizes;\n  const lineW = lineSize.w * space.dx;\n  const lineH = lineSize.h * space.dy;\n  const x = (lineW > 0) && ((labelSize.w / 2 + padding.left - space.x) / lineW);\n  const y = (lineH > 0) && ((labelSize.h / 2 + padding.top - space.y) / lineH);\n  return clamp(Math.max(x, y), 0, 0.25);\n}\n\nfunction spaceAround(properties, chartArea) {\n  const {x, x2, y, y2} = properties;\n  const t = Math.min(y, y2) - chartArea.top;\n  const l = Math.min(x, x2) - chartArea.left;\n  const b = chartArea.bottom - Math.max(y, y2);\n  const r = chartArea.right - Math.max(x, x2);\n  return {\n    x: Math.min(l, r),\n    y: Math.min(t, b),\n    dx: l <= r ? 1 : -1,\n    dy: t <= b ? 1 : -1\n  };\n}\n\nfunction adjustLabelCoordinate(coordinate, labelSizes) {\n  const {size, min, max, padding} = labelSizes;\n  const halfSize = size / 2;\n  if (size > max - min) {\n    // if it does not fit, display as much as possible\n    return (max + min) / 2;\n  }\n  if (min >= (coordinate - padding - halfSize)) {\n    coordinate = min + padding + halfSize;\n  }\n  if (max <= (coordinate + padding + halfSize)) {\n    coordinate = max - padding - halfSize;\n  }\n  return coordinate;\n}\n\nfunction getArrowHeads(line) {\n  const options = line.options;\n  const arrowStartOpts = options.arrowHeads && options.arrowHeads.start;\n  const arrowEndOpts = options.arrowHeads && options.arrowHeads.end;\n  return {\n    startOpts: arrowStartOpts,\n    endOpts: arrowEndOpts,\n    startAdjust: getLineAdjust(line, arrowStartOpts),\n    endAdjust: getLineAdjust(line, arrowEndOpts)\n  };\n}\n\nfunction getLineAdjust(line, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return 0;\n  }\n  const {length, width} = arrowOpts;\n  const adjust = line.options.borderWidth / 2;\n  const p1 = {x: length, y: width + adjust};\n  const p2 = {x: 0, y: adjust};\n  return Math.abs(interpolateX(0, p1, p2));\n}\n\nfunction drawArrowHead(ctx, offset, adjust, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  const {length, width, fill, backgroundColor, borderColor} = arrowOpts;\n  const arrowOffsetX = Math.abs(offset - length) + adjust;\n  ctx.beginPath();\n  setShadowStyle(ctx, arrowOpts);\n  setBorderStyle(ctx, arrowOpts);\n  ctx.moveTo(arrowOffsetX, -width);\n  ctx.lineTo(offset + adjust, 0);\n  ctx.lineTo(arrowOffsetX, width);\n  if (fill === true) {\n    ctx.fillStyle = backgroundColor || borderColor;\n    ctx.closePath();\n    ctx.fill();\n    ctx.shadowColor = 'transparent';\n  } else {\n    ctx.shadowColor = arrowOpts.borderShadowColor;\n  }\n  ctx.stroke();\n}\n\nfunction getControlPoint(properties, options, distance) {\n  const {x, y, x2, y2, centerX, centerY} = properties;\n  const angle = Math.atan2(y2 - y, x2 - x);\n  const cp = toPosition(options.controlPoint, 0);\n  const point = {\n    x: centerX + getSize(distance, cp.x, false),\n    y: centerY + getSize(distance, cp.y, false)\n  };\n  return rotated(point, {x: centerX, y: centerY}, angle);\n}\n\nfunction drawArrowHeadOnCurve(ctx, {x, y}, {angle, adjust}, arrowOpts) {\n  if (!arrowOpts || !arrowOpts.display) {\n    return;\n  }\n  ctx.save();\n  ctx.translate(x, y);\n  ctx.rotate(angle);\n  drawArrowHead(ctx, 0, -adjust, arrowOpts);\n  ctx.restore();\n}\n\nfunction drawCurve(ctx, element, cp, length) {\n  const {x, y, x2, y2, options} = element;\n  const {startOpts, endOpts, startAdjust, endAdjust} = getArrowHeads(element);\n  const p1 = {x, y};\n  const p2 = {x: x2, y: y2};\n  const startAngle = angleInCurve(p1, cp, p2, 0);\n  const endAngle = angleInCurve(p1, cp, p2, 1) - PI;\n  const ps = pointInCurve(p1, cp, p2, startAdjust / length);\n  const pe = pointInCurve(p1, cp, p2, 1 - endAdjust / length);\n\n  const path = new Path2D();\n  ctx.beginPath();\n  path.moveTo(ps.x, ps.y);\n  path.quadraticCurveTo(cp.x, cp.y, pe.x, pe.y);\n  ctx.shadowColor = options.borderShadowColor;\n  ctx.stroke(path);\n  element.path = path;\n  element.ctx = ctx;\n  drawArrowHeadOnCurve(ctx, ps, {angle: startAngle, adjust: startAdjust}, startOpts);\n  drawArrowHeadOnCurve(ctx, pe, {angle: endAngle, adjust: endAdjust}, endOpts);\n}\n\nclass EllipseAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const rotation = this.options.rotation;\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return pointInEllipse({x: mouseX, y: mouseY}, this.getProps(['width', 'height', 'centerX', 'centerY'], useFinalPosition), rotation, hitSize);\n    }\n    const {x, y, x2, y2} = this.getProps(['x', 'y', 'x2', 'y2'], useFinalPosition);\n    const limit = axis === 'y' ? {start: y, end: y2} : {start: x, end: x2};\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-rotation));\n    return rotatedPoint[axis] >= limit.start - hitSize - EPSILON && rotatedPoint[axis] <= limit.end + hitSize + EPSILON;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {width, height, centerX, centerY, options} = this;\n    ctx.save();\n    translate(ctx, this.getCenterPoint(), options.rotation);\n    setShadowStyle(ctx, this.options);\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    const stroke = setBorderStyle(ctx, options);\n    ctx.ellipse(centerX, centerY, height / 2, width / 2, PI / 2, 0, 2 * PI);\n    ctx.fill();\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  get label() {\n    return this.elements && this.elements[0];\n  }\n\n  resolveElementProperties(chart, options) {\n    return resolveBoxAndLabelProperties(chart, options);\n  }\n\n}\n\nEllipseAnnotation.id = 'ellipseAnnotation';\n\nEllipseAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  label: Object.assign({}, BoxAnnotation.defaults.label),\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  z: 0\n};\n\nEllipseAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nEllipseAnnotation.descriptors = {\n  label: {\n    _fallback: true\n  }\n};\n\nfunction pointInEllipse(p, ellipse, rotation, hitSize) {\n  const {width, height, centerX, centerY} = ellipse;\n  const xRadius = width / 2;\n  const yRadius = height / 2;\n\n  if (xRadius <= 0 || yRadius <= 0) {\n    return false;\n  }\n  // https://stackoverflow.com/questions/7946187/point-and-ellipse-rotated-position-test-algorithm\n  const angle = toRadians(rotation || 0);\n  const cosAngle = Math.cos(angle);\n  const sinAngle = Math.sin(angle);\n  const a = Math.pow(cosAngle * (p.x - centerX) + sinAngle * (p.y - centerY), 2);\n  const b = Math.pow(sinAngle * (p.x - centerX) - cosAngle * (p.y - centerY), 2);\n  return (a / Math.pow(xRadius + hitSize, 2)) + (b / Math.pow(yRadius + hitSize, 2)) <= 1.0001;\n}\n\nclass PointAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    const {x, y, x2, y2, width} = this.getProps(['x', 'y', 'x2', 'y2', 'width'], useFinalPosition);\n    const hitSize = (this.options.borderWidth + this.options.hitTolerance) / 2;\n    if (axis !== 'x' && axis !== 'y') {\n      return inPointRange({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), width / 2, hitSize);\n    }\n    const limit = axis === 'y' ? {start: y, end: y2, value: mouseY} : {start: x, end: x2, value: mouseX};\n    return inLimit(limit, hitSize);\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const options = this.options;\n    const borderWidth = options.borderWidth;\n    if (options.radius < 0.1) {\n      return;\n    }\n    ctx.save();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    drawPoint(ctx, this, this.centerX, this.centerY);\n    if (stroke && !isImageOrCanvas(options.pointStyle)) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n    options.borderWidth = borderWidth;\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    properties.initProperties = initAnimationProperties(chart, properties, options);\n    return properties;\n  }\n}\n\nPointAnnotation.id = 'pointAnnotation';\n\nPointAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  pointStyle: 'circle',\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPointAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nclass PolygonAnnotation extends Element {\n\n  inRange(mouseX, mouseY, axis, useFinalPosition) {\n    if (axis !== 'x' && axis !== 'y') {\n      return this.options.radius >= 0.1 && this.elements.length > 1 && pointIsInPolygon(this.elements, mouseX, mouseY, useFinalPosition);\n    }\n    const rotatedPoint = rotated({x: mouseX, y: mouseY}, this.getCenterPoint(useFinalPosition), toRadians(-this.options.rotation));\n    const axisPoints = this.elements.map((point) => axis === 'y' ? point.bY : point.bX);\n    const start = Math.min(...axisPoints);\n    const end = Math.max(...axisPoints);\n    return rotatedPoint[axis] >= start && rotatedPoint[axis] <= end;\n  }\n\n  getCenterPoint(useFinalPosition) {\n    return getElementCenterPoint(this, useFinalPosition);\n  }\n\n  draw(ctx) {\n    const {elements, options} = this;\n    ctx.save();\n    ctx.beginPath();\n    ctx.fillStyle = options.backgroundColor;\n    setShadowStyle(ctx, options);\n    const stroke = setBorderStyle(ctx, options);\n    let first = true;\n    for (const el of elements) {\n      if (first) {\n        ctx.moveTo(el.x, el.y);\n        first = false;\n      } else {\n        ctx.lineTo(el.x, el.y);\n      }\n    }\n    ctx.closePath();\n    ctx.fill();\n    // If no border, don't draw it\n    if (stroke) {\n      ctx.shadowColor = options.borderShadowColor;\n      ctx.stroke();\n    }\n    ctx.restore();\n  }\n\n  resolveElementProperties(chart, options) {\n    const properties = resolvePointProperties(chart, options);\n    const {sides, rotation} = options;\n    const elements = [];\n    const angle = (2 * PI) / sides;\n    let rad = rotation * RAD_PER_DEG;\n    for (let i = 0; i < sides; i++, rad += angle) {\n      const elProps = buildPointElement(properties, options, rad);\n      elProps.initProperties = initAnimationProperties(chart, properties, options);\n      elements.push(elProps);\n    }\n    properties.elements = elements;\n    return properties;\n  }\n}\n\nPolygonAnnotation.id = 'polygonAnnotation';\n\nPolygonAnnotation.defaults = {\n  adjustScaleRange: true,\n  backgroundShadowColor: 'transparent',\n  borderCapStyle: 'butt',\n  borderDash: [],\n  borderDashOffset: 0,\n  borderJoinStyle: 'miter',\n  borderShadowColor: 'transparent',\n  borderWidth: 1,\n  display: true,\n  hitTolerance: 0,\n  init: undefined,\n  point: {\n    radius: 0\n  },\n  radius: 10,\n  rotation: 0,\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  sides: 3,\n  xAdjust: 0,\n  xMax: undefined,\n  xMin: undefined,\n  xScaleID: undefined,\n  xValue: undefined,\n  yAdjust: 0,\n  yMax: undefined,\n  yMin: undefined,\n  yScaleID: undefined,\n  yValue: undefined,\n  z: 0\n};\n\nPolygonAnnotation.defaultRoutes = {\n  borderColor: 'color',\n  backgroundColor: 'color'\n};\n\nfunction buildPointElement({centerX, centerY}, {radius, borderWidth, hitTolerance}, rad) {\n  const hitSize = (borderWidth + hitTolerance) / 2;\n  const sin = Math.sin(rad);\n  const cos = Math.cos(rad);\n  const point = {x: centerX + sin * radius, y: centerY - cos * radius};\n  return {\n    type: 'point',\n    optionScope: 'point',\n    properties: {\n      x: point.x,\n      y: point.y,\n      centerX: point.x,\n      centerY: point.y,\n      bX: centerX + sin * (radius + hitSize),\n      bY: centerY - cos * (radius + hitSize)\n    }\n  };\n}\n\nfunction pointIsInPolygon(points, x, y, useFinalPosition) {\n  let isInside = false;\n  let A = points[points.length - 1].getProps(['bX', 'bY'], useFinalPosition);\n  for (const point of points) {\n    const B = point.getProps(['bX', 'bY'], useFinalPosition);\n    if ((B.bY > y) !== (A.bY > y) && x < (A.bX - B.bX) * (y - B.bY) / (A.bY - B.bY) + B.bX) {\n      isInside = !isInside;\n    }\n    A = B;\n  }\n  return isInside;\n}\n\nconst annotationTypes = {\n  box: BoxAnnotation,\n  doughnutLabel: DoughnutLabelAnnotation,\n  ellipse: EllipseAnnotation,\n  label: LabelAnnotation,\n  line: LineAnnotation,\n  point: PointAnnotation,\n  polygon: PolygonAnnotation\n};\n\n/**\n * Register fallback for annotation elements\n * For example lineAnnotation options would be looked through:\n * - the annotation object (options.plugins.annotation.annotations[id])\n * - element options (options.elements.lineAnnotation)\n * - element defaults (defaults.elements.lineAnnotation)\n * - annotation plugin defaults (defaults.plugins.annotation, this is what we are registering here)\n */\nObject.keys(annotationTypes).forEach(key => {\n  defaults.describe(`elements.${annotationTypes[key].id}`, {\n    _fallback: 'plugins.annotation.common'\n  });\n});\n\nconst directUpdater = {\n  update: Object.assign\n};\n\nconst hooks$1 = eventHooks.concat(elementHooks);\nconst resolve = (value, optDefs) => isObject(optDefs) ? resolveObj(value, optDefs) : value;\n\n\n/**\n * @typedef { import(\"chart.js\").Chart } Chart\n * @typedef { import(\"chart.js\").UpdateMode } UpdateMode\n * @typedef { import('../../types/options').AnnotationPluginOptions } AnnotationPluginOptions\n */\n\n/**\n * @param {string} prop\n * @returns {boolean}\n */\nconst isIndexable = (prop) => prop === 'color' || prop === 'font';\n\n/**\n * Resolve the annotation type, checking if is supported.\n * @param {string} [type=line] - annotation type\n * @returns {string} resolved annotation type\n */\nfunction resolveType(type = 'line') {\n  if (annotationTypes[type]) {\n    return type;\n  }\n  console.warn(`Unknown annotation type: '${type}', defaulting to 'line'`);\n  return 'line';\n}\n\n/**\n * @param {Chart} chart\n * @param {Object} state\n * @param {AnnotationPluginOptions} options\n * @param {UpdateMode} mode\n */\nfunction updateElements(chart, state, options, mode) {\n  const animations = resolveAnimations(chart, options.animations, mode);\n\n  const annotations = state.annotations;\n  const elements = resyncElements(state.elements, annotations);\n\n  for (let i = 0; i < annotations.length; i++) {\n    const annotationOptions = annotations[i];\n    const element = getOrCreateElement(elements, i, annotationOptions.type);\n    const resolver = annotationOptions.setContext(getContext(chart, element, elements, annotationOptions));\n    const properties = element.resolveElementProperties(chart, resolver);\n\n    properties.skip = toSkip(properties);\n\n    if ('elements' in properties) {\n      updateSubElements(element, properties.elements, resolver, animations);\n      // Remove the sub-element definitions from properties, so the actual elements\n      // are not overwritten by their definitions\n      delete properties.elements;\n    }\n\n    if (!defined(element.x)) {\n      // If the element is newly created, assing the properties directly - to\n      // make them readily awailable to any scriptable options. If we do not do this,\n      // the properties retruned by `resolveElementProperties` are available only\n      // after options resolution.\n      Object.assign(element, properties);\n    }\n\n    Object.assign(element, properties.initProperties);\n    properties.options = resolveAnnotationOptions(resolver);\n\n    animations.update(element, properties);\n  }\n}\n\nfunction toSkip(properties) {\n  return isNaN(properties.x) || isNaN(properties.y);\n}\n\nfunction resolveAnimations(chart, animOpts, mode) {\n  if (mode === 'reset' || mode === 'none' || mode === 'resize') {\n    return directUpdater;\n  }\n  return new Animations(chart, animOpts);\n}\n\nfunction updateSubElements(mainElement, elements, resolver, animations) {\n  const subElements = mainElement.elements || (mainElement.elements = []);\n  subElements.length = elements.length;\n  for (let i = 0; i < elements.length; i++) {\n    const definition = elements[i];\n    const properties = definition.properties;\n    const subElement = getOrCreateElement(subElements, i, definition.type, definition.initProperties);\n    const subResolver = resolver[definition.optionScope].override(definition);\n    properties.options = resolveAnnotationOptions(subResolver);\n    animations.update(subElement, properties);\n  }\n}\n\nfunction getOrCreateElement(elements, index, type, initProperties) {\n  const elementClass = annotationTypes[resolveType(type)];\n  let element = elements[index];\n  if (!element || !(element instanceof elementClass)) {\n    element = elements[index] = new elementClass();\n    Object.assign(element, initProperties);\n  }\n  return element;\n}\n\nfunction resolveAnnotationOptions(resolver) {\n  const elementClass = annotationTypes[resolveType(resolver.type)];\n  const result = {};\n  result.id = resolver.id;\n  result.type = resolver.type;\n  result.drawTime = resolver.drawTime;\n  Object.assign(result,\n    resolveObj(resolver, elementClass.defaults),\n    resolveObj(resolver, elementClass.defaultRoutes));\n  for (const hook of hooks$1) {\n    result[hook] = resolver[hook];\n  }\n  return result;\n}\n\nfunction resolveObj(resolver, defs) {\n  const result = {};\n  for (const prop of Object.keys(defs)) {\n    const optDefs = defs[prop];\n    const value = resolver[prop];\n    if (isIndexable(prop) && isArray(value)) {\n      result[prop] = value.map((item) => resolve(item, optDefs));\n    } else {\n      result[prop] = resolve(value, optDefs);\n    }\n  }\n  return result;\n}\n\nfunction getContext(chart, element, elements, annotation) {\n  return element.$context || (element.$context = Object.assign(Object.create(chart.getContext()), {\n    element,\n    get elements() {\n      return elements.filter((el) => el && el.options);\n    },\n    id: annotation.id,\n    type: 'annotation'\n  }));\n}\n\nfunction resyncElements(elements, annotations) {\n  const count = annotations.length;\n  const start = elements.length;\n\n  if (start < count) {\n    const add = count - start;\n    elements.splice(start, 0, ...new Array(add));\n  } else if (start > count) {\n    elements.splice(count, start - count);\n  }\n  return elements;\n}\n\nvar version = \"3.1.0\";\n\nconst chartStates = new Map();\nconst isNotDoughnutLabel = annotation => annotation.type !== 'doughnutLabel';\nconst hooks = eventHooks.concat(elementHooks);\n\nvar annotation = {\n  id: 'annotation',\n\n  version,\n\n  beforeRegister() {\n    requireVersion('chart.js', '4.0', Chart.version);\n  },\n\n  afterRegister() {\n    Chart.register(annotationTypes);\n  },\n\n  afterUnregister() {\n    Chart.unregister(annotationTypes);\n  },\n\n  beforeInit(chart) {\n    chartStates.set(chart, {\n      annotations: [],\n      elements: [],\n      visibleElements: [],\n      listeners: {},\n      listened: false,\n      moveListened: false,\n      hooks: {},\n      hooked: false,\n      hovered: []\n    });\n  },\n\n  beforeUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    const annotations = state.annotations = [];\n\n    let annotationOptions = options.annotations;\n    if (isObject(annotationOptions)) {\n      Object.keys(annotationOptions).forEach(key => {\n        const value = annotationOptions[key];\n        if (isObject(value)) {\n          value.id = key;\n          annotations.push(value);\n        }\n      });\n    } else if (isArray(annotationOptions)) {\n      annotations.push(...annotationOptions);\n    }\n    verifyScaleOptions(annotations.filter(isNotDoughnutLabel), chart.scales);\n  },\n\n  afterDataLimits(chart, args) {\n    const state = chartStates.get(chart);\n    adjustScaleRange(chart, args.scale, state.annotations.filter(isNotDoughnutLabel).filter(a => a.display && a.adjustScaleRange));\n  },\n\n  afterUpdate(chart, args, options) {\n    const state = chartStates.get(chart);\n    updateListeners(chart, state, options);\n    updateElements(chart, state, options, args.mode);\n    state.visibleElements = state.elements.filter(el => !el.skip && el.options.display);\n    updateHooks(chart, state, options);\n  },\n\n  beforeDatasetsDraw(chart, _args, options) {\n    draw(chart, 'beforeDatasetsDraw', options.clip);\n  },\n\n  afterDatasetsDraw(chart, _args, options) {\n    draw(chart, 'afterDatasetsDraw', options.clip);\n  },\n\n  beforeDatasetDraw(chart, _args, options) {\n    draw(chart, _args.index, options.clip);\n  },\n\n  beforeDraw(chart, _args, options) {\n    draw(chart, 'beforeDraw', options.clip);\n  },\n\n  afterDraw(chart, _args, options) {\n    draw(chart, 'afterDraw', options.clip);\n  },\n\n  beforeEvent(chart, args, options) {\n    const state = chartStates.get(chart);\n    if (handleEvent(state, args.event, options)) {\n      args.changed = true;\n    }\n  },\n\n  afterDestroy(chart) {\n    chartStates.delete(chart);\n  },\n\n  getAnnotations(chart) {\n    const state = chartStates.get(chart);\n    return state ? state.elements : [];\n  },\n\n  // only for testing\n  _getAnnotationElementsAtEventForMode(visibleElements, event, options) {\n    return getElements(visibleElements, event, options);\n  },\n\n  defaults: {\n    animations: {\n      numbers: {\n        properties: ['x', 'y', 'x2', 'y2', 'width', 'height', 'centerX', 'centerY', 'pointX', 'pointY', 'radius'],\n        type: 'number'\n      },\n      colors: {\n        properties: ['backgroundColor', 'borderColor'],\n        type: 'color'\n      }\n    },\n    clip: true,\n    interaction: {\n      mode: undefined,\n      axis: undefined,\n      intersect: undefined\n    },\n    common: {\n      drawTime: 'afterDatasetsDraw',\n      init: false,\n      label: {\n      }\n    }\n  },\n\n  descriptors: {\n    _indexable: false,\n    _scriptable: (prop) => !hooks.includes(prop) && prop !== 'init',\n    annotations: {\n      _allKeys: false,\n      _fallback: (prop, opts) => `elements.${annotationTypes[resolveType(opts.type)].id}`\n    },\n    interaction: {\n      _fallback: true\n    },\n    common: {\n      label: {\n        _indexable: isIndexable,\n        _fallback: true\n      },\n      _indexable: isIndexable\n    }\n  },\n\n  additionalOptionScopes: ['']\n};\n\nfunction draw(chart, caller, clip) {\n  const {ctx, chartArea} = chart;\n  const state = chartStates.get(chart);\n\n  if (clip) {\n    clipArea(ctx, chartArea);\n  }\n\n  const drawableElements = getDrawableElements(state.visibleElements, caller).sort((a, b) => a.element.options.z - b.element.options.z);\n  for (const item of drawableElements) {\n    drawElement(ctx, chartArea, state, item);\n  }\n\n  if (clip) {\n    unclipArea(ctx);\n  }\n}\n\nfunction getDrawableElements(elements, caller) {\n  const drawableElements = [];\n  for (const el of elements) {\n    if (el.options.drawTime === caller) {\n      drawableElements.push({element: el, main: true});\n    }\n    if (el.elements && el.elements.length) {\n      for (const sub of el.elements) {\n        if (sub.options.display && sub.options.drawTime === caller) {\n          drawableElements.push({element: sub});\n        }\n      }\n    }\n  }\n  return drawableElements;\n}\n\nfunction drawElement(ctx, chartArea, state, item) {\n  const el = item.element;\n  if (item.main) {\n    invokeHook(state, el, 'beforeDraw');\n    el.draw(ctx, chartArea);\n    invokeHook(state, el, 'afterDraw');\n  } else {\n    el.draw(ctx, chartArea);\n  }\n}\n\nexport { annotation as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,QAAQ,UAAU;AACnF,SAASC,qBAAqB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,EAAE,EAAEC,OAAO,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,kBAAkB;;AAE7T;AACA;AACA;AACA;;AAEA,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE;IACL;AACJ;AACA;AACA;AACA;AACA;IACIC,KAAKA,CAACC,eAAe,EAAEC,KAAK,EAAE;MAC5B,OAAOC,cAAc,CAACF,eAAe,EAAEC,KAAK,EAAE;QAACE,SAAS,EAAE;MAAI,CAAC,CAAC;IAClE,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,OAAOA,CAACJ,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;MACvC,OAAOC,cAAc,CAACN,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAAC;IACxD,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACIE,CAACA,CAACP,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;MACjC,OAAOH,cAAc,CAACF,eAAe,EAAEC,KAAK,EAAE;QAACE,SAAS,EAAEE,OAAO,CAACF,SAAS;QAAEK,IAAI,EAAE;MAAG,CAAC,CAAC;IAC1F,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACIC,CAACA,CAACT,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;MACjC,OAAOH,cAAc,CAACF,eAAe,EAAEC,KAAK,EAAE;QAACE,SAAS,EAAEE,OAAO,CAACF,SAAS;QAAEK,IAAI,EAAE;MAAG,CAAC,CAAC;IAC1F;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACV,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;EACpD,MAAMM,IAAI,GAAGd,WAAW,CAACC,KAAK,CAACO,OAAO,CAACM,IAAI,CAAC,IAAId,WAAW,CAACC,KAAK,CAACM,OAAO;EACzE,OAAOO,IAAI,CAACX,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAAC;AAC9C;AAEA,SAASO,aAAaA,CAACC,OAAO,EAAEZ,KAAK,EAAEO,IAAI,EAAE;EAC3C,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;IAChC,OAAOK,OAAO,CAACC,OAAO,CAACb,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACQ,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,IAAII,OAAO,CAACC,OAAO,CAACb,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACQ,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;EACrG;EACA,OAAOI,OAAO,CAACC,OAAO,CAACb,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACQ,CAAC,EAAED,IAAI,EAAE,IAAI,CAAC;AACtD;AAEA,SAASO,cAAcA,CAACd,KAAK,EAAEe,MAAM,EAAER,IAAI,EAAE;EAC3C,IAAIA,IAAI,KAAK,GAAG,EAAE;IAChB,OAAO;MAACD,CAAC,EAAEN,KAAK,CAACM,CAAC;MAAEE,CAAC,EAAEO,MAAM,CAACP;IAAC,CAAC;EAClC,CAAC,MAAM,IAAID,IAAI,KAAK,GAAG,EAAE;IACvB,OAAO;MAACD,CAAC,EAAES,MAAM,CAACT,CAAC;MAAEE,CAAC,EAAER,KAAK,CAACQ;IAAC,CAAC;EAClC;EACA,OAAOO,MAAM;AACf;AAEA,SAASd,cAAcA,CAACF,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;EACvD,OAAOL,eAAe,CAACiB,MAAM,CAAEJ,OAAO,IAAKR,OAAO,CAACF,SAAS,GAAGU,OAAO,CAACC,OAAO,CAACb,KAAK,CAACM,CAAC,EAAEN,KAAK,CAACQ,CAAC,CAAC,GAAGG,aAAa,CAACC,OAAO,EAAEZ,KAAK,EAAEI,OAAO,CAACG,IAAI,CAAC,CAAC;AACjJ;AAEA,SAASF,cAAcA,CAACN,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;EACvD,IAAIa,WAAW,GAAGC,MAAM,CAACC,iBAAiB;EAE1C,OAAOlB,cAAc,CAACF,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAAC,CACnDgB,MAAM,CAAC,CAACC,YAAY,EAAET,OAAO,KAAK;IACjC,MAAMG,MAAM,GAAGH,OAAO,CAACU,cAAc,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAGT,cAAc,CAACd,KAAK,EAAEe,MAAM,EAAEX,OAAO,CAACG,IAAI,CAAC;IAC7D,MAAMiB,QAAQ,GAAGpD,qBAAqB,CAAC4B,KAAK,EAAEuB,SAAS,CAAC;IACxD,IAAIC,QAAQ,GAAGP,WAAW,EAAE;MAC1BI,YAAY,GAAG,CAACT,OAAO,CAAC;MACxBK,WAAW,GAAGO,QAAQ;IACxB,CAAC,MAAM,IAAIA,QAAQ,KAAKP,WAAW,EAAE;MACnC;MACAI,YAAY,CAACI,IAAI,CAACb,OAAO,CAAC;IAC5B;IAEA,OAAOS,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC,CACLK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,MAAM,GAAGD,CAAC,CAACC,MAAM,CAAC,CACnCC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAClB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACjC,KAAK,EAAEiB,MAAM,EAAEiB,KAAK,EAAE;EACrC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACD,KAAK,CAAC;EAC3B,MAAMG,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC;EAC3B,MAAMI,EAAE,GAAGrB,MAAM,CAACT,CAAC;EACnB,MAAM+B,EAAE,GAAGtB,MAAM,CAACP,CAAC;EAEnB,OAAO;IACLF,CAAC,EAAE8B,EAAE,GAAGH,GAAG,IAAInC,KAAK,CAACQ,CAAC,GAAG8B,EAAE,CAAC,GAAGD,GAAG,IAAIrC,KAAK,CAACU,CAAC,GAAG6B,EAAE,CAAC;IACnD7B,CAAC,EAAE6B,EAAE,GAAGF,GAAG,IAAIrC,KAAK,CAACQ,CAAC,GAAG8B,EAAE,CAAC,GAAGH,GAAG,IAAInC,KAAK,CAACU,CAAC,GAAG6B,EAAE;EACpD,CAAC;AACH;AAEA,MAAMC,WAAW,GAAGA,CAACC,GAAG,EAAEC,GAAG,KAAKA,GAAG,GAAGD,GAAG,IAAKA,GAAG,CAACE,MAAM,GAAGD,GAAG,CAACC,MAAM,IAAIF,GAAG,CAACT,KAAK,CAAC,CAAC,EAAEU,GAAG,CAACC,MAAM,CAAC,KAAKD,GAAI;;AAE5G;AACA;AACA;AACA;AACA;;AAEA,MAAME,OAAO,GAAG,KAAK;AACrB,MAAMC,KAAK,GAAGA,CAACrC,CAAC,EAAEsC,IAAI,EAAEC,EAAE,KAAKX,IAAI,CAACY,GAAG,CAACD,EAAE,EAAEX,IAAI,CAACa,GAAG,CAACH,IAAI,EAAEtC,CAAC,CAAC,CAAC;;AAE9D;AACA;AACA;AACA;AACA;AACA,MAAM0C,OAAO,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACG,KAAK,GAAGF,OAAO,IAAID,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACI,GAAG,GAAGH,OAAO;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACC,GAAG,EAAEX,IAAI,EAAEC,EAAE,EAAE;EAC/B,KAAK,MAAMW,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,EAAE;IAClCA,GAAG,CAACC,GAAG,CAAC,GAAGb,KAAK,CAACY,GAAG,CAACC,GAAG,CAAC,EAAEZ,IAAI,EAAEC,EAAE,CAAC;EACtC;EACA,OAAOU,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,YAAYA,CAAC7D,KAAK,EAAEiB,MAAM,EAAE6C,MAAM,EAAEV,OAAO,EAAE;EACpD,IAAI,CAACpD,KAAK,IAAI,CAACiB,MAAM,IAAI6C,MAAM,IAAI,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;EACA,OAAQ1B,IAAI,CAAC2B,GAAG,CAAC/D,KAAK,CAACQ,CAAC,GAAGS,MAAM,CAACT,CAAC,EAAE,CAAC,CAAC,GAAG4B,IAAI,CAAC2B,GAAG,CAAC/D,KAAK,CAACU,CAAC,GAAGO,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC,IAAK0B,IAAI,CAAC2B,GAAG,CAACD,MAAM,GAAGV,OAAO,EAAE,CAAC,CAAC;AAC7G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,UAAUA,CAAChE,KAAK,EAAE;EAACQ,CAAC;EAAEE,CAAC;EAAEuD,EAAE;EAAEC;AAAE,CAAC,EAAEzD,IAAI,EAAE;EAAC0D,WAAW;EAAEC;AAAY,CAAC,EAAE;EAC5E,MAAMhB,OAAO,GAAG,CAACe,WAAW,GAAGC,YAAY,IAAI,CAAC;EAChD,MAAMC,QAAQ,GAAGrE,KAAK,CAACQ,CAAC,IAAIA,CAAC,GAAG4C,OAAO,GAAGR,OAAO,IAAI5C,KAAK,CAACQ,CAAC,IAAIyD,EAAE,GAAGb,OAAO,GAAGR,OAAO;EACtF,MAAM0B,QAAQ,GAAGtE,KAAK,CAACU,CAAC,IAAIA,CAAC,GAAG0C,OAAO,GAAGR,OAAO,IAAI5C,KAAK,CAACU,CAAC,IAAIwD,EAAE,GAAGd,OAAO,GAAGR,OAAO;EACtF,IAAInC,IAAI,KAAK,GAAG,EAAE;IAChB,OAAO4D,QAAQ;EACjB,CAAC,MAAM,IAAI5D,IAAI,KAAK,GAAG,EAAE;IACvB,OAAO6D,QAAQ;EACjB;EACA,OAAOD,QAAQ,IAAIC,QAAQ;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACvE,KAAK,EAAE;EAACwE,IAAI;EAAEvD;AAAM,CAAC,EAAER,IAAI,EAAE;EAACgE,QAAQ;EAAEN,WAAW;EAAEC;AAAY,CAAC,EAAE;EACxF,MAAMM,QAAQ,GAAGzC,OAAO,CAACjC,KAAK,EAAEiB,MAAM,EAAE1C,SAAS,CAAC,CAACkG,QAAQ,CAAC,CAAC;EAC7D,OAAOT,UAAU,CAACU,QAAQ,EAAEF,IAAI,EAAE/D,IAAI,EAAE;IAAC0D,WAAW;IAAEC;EAAY,CAAC,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASO,qBAAqBA,CAAC7D,OAAO,EAAE8D,gBAAgB,EAAE;EACxD,MAAM;IAACC,OAAO;IAAEC;EAAO,CAAC,GAAGhE,OAAO,CAACiE,QAAQ,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAEH,gBAAgB,CAAC;EACrF,OAAO;IAACpE,CAAC,EAAEqE,OAAO;IAAEnE,CAAC,EAAEoE;EAAO,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACC,GAAG,EAAEjC,GAAG,EAAEkC,GAAG,EAAEC,MAAM,GAAG,IAAI,EAAE;EACpD,MAAMC,KAAK,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAC5B,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,MAAM5C,GAAG,IAAIM,GAAG,CAACqC,KAAK,CAAC,GAAG,CAAC,EAAE;IAChC,MAAM5C,GAAG,GAAG2C,KAAK,CAACE,CAAC,EAAE,CAAC;IACtB,IAAIC,QAAQ,CAAC7C,GAAG,EAAE,EAAE,CAAC,GAAG6C,QAAQ,CAAC9C,GAAG,EAAE,EAAE,CAAC,EAAE;MACzC;IACF;IACA,IAAID,WAAW,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;MACzB,IAAIyC,MAAM,EAAE;QACV,MAAM,IAAIK,KAAK,CAAC,GAAGP,GAAG,KAAKC,GAAG,uBAAuBlC,GAAG,wBAAwB,CAAC;MACnF,CAAC,MAAM;QACL,OAAO,KAAK;MACd;IACF;EACF;EACA,OAAO,IAAI;AACb;AAEA,MAAMyC,eAAe,GAAIC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,QAAQ,CAAC,GAAG,CAAC;AACvE,MAAMC,SAAS,GAAIF,CAAC,IAAKG,UAAU,CAACH,CAAC,CAAC,GAAG,GAAG;AAC5C,MAAMI,iBAAiB,GAAIJ,CAAC,IAAK7C,KAAK,CAAC+C,SAAS,CAACF,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAE1D,MAAMK,WAAW,GAAGA,CAACvF,CAAC,EAAEE,CAAC,MAAM;EAACF,CAAC;EAAEE,CAAC;EAAEuD,EAAE,EAAEzD,CAAC;EAAE0D,EAAE,EAAExD,CAAC;EAAEsF,KAAK,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAC,CAAC,CAAC;AACzE,MAAMC,oBAAoB,GAAG;EAC3BC,GAAG,EAAGC,UAAU,IAAKL,WAAW,CAACK,UAAU,CAACvB,OAAO,EAAEuB,UAAU,CAACtB,OAAO,CAAC;EACxEuB,aAAa,EAAGD,UAAU,IAAKL,WAAW,CAACK,UAAU,CAACvB,OAAO,EAAEuB,UAAU,CAACtB,OAAO,CAAC;EAClFwB,OAAO,EAAGF,UAAU,KAAM;IAACvB,OAAO,EAAEuB,UAAU,CAACvB,OAAO;IAAEC,OAAO,EAAEsB,UAAU,CAACvB,OAAO;IAAEf,MAAM,EAAE,CAAC;IAAEkC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAC,CAAC;EACrHM,KAAK,EAAGH,UAAU,IAAKL,WAAW,CAACK,UAAU,CAACvB,OAAO,EAAEuB,UAAU,CAACtB,OAAO,CAAC;EAC1E0B,IAAI,EAAGJ,UAAU,IAAKL,WAAW,CAACK,UAAU,CAAC5F,CAAC,EAAE4F,UAAU,CAAC1F,CAAC,CAAC;EAC7DV,KAAK,EAAGoG,UAAU,KAAM;IAACvB,OAAO,EAAEuB,UAAU,CAACvB,OAAO;IAAEC,OAAO,EAAEsB,UAAU,CAACtB,OAAO;IAAEhB,MAAM,EAAE,CAAC;IAAEkC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAC,CAAC;EACnHQ,OAAO,EAAGL,UAAU,IAAKL,WAAW,CAACK,UAAU,CAACvB,OAAO,EAAEuB,UAAU,CAACtB,OAAO;AAC7E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS4B,mBAAmBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC3C,IAAIA,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO,CAAC;EACV;EACA,IAAIA,QAAQ,KAAK,KAAK,EAAE;IACtB,OAAOD,IAAI;EACb;EACA,IAAIlB,eAAe,CAACmB,QAAQ,CAAC,EAAE;IAC7B,OAAOd,iBAAiB,CAACc,QAAQ,CAAC,GAAGD,IAAI;EAC3C;EACA,OAAOA,IAAI,GAAG,CAAC;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACF,IAAI,EAAEtD,KAAK,EAAEyD,eAAe,GAAG,IAAI,EAAE;EACpD,IAAI,OAAOzD,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd,CAAC,MAAM,IAAIoC,eAAe,CAACpC,KAAK,CAAC,EAAE;IACjC,OAAO,CAACyD,eAAe,GAAGhB,iBAAiB,CAACzC,KAAK,CAAC,GAAGuC,SAAS,CAACvC,KAAK,CAAC,IAAIsD,IAAI;EAC/E;EACA,OAAOA,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASI,sBAAsBA,CAACJ,IAAI,EAAErG,OAAO,EAAE;EAC7C,MAAM;IAACE,CAAC;IAAEwF;EAAK,CAAC,GAAGW,IAAI;EACvB,MAAMK,SAAS,GAAG1G,OAAO,CAAC0G,SAAS;EACnC,IAAIA,SAAS,KAAK,QAAQ,EAAE;IAC1B,OAAOxG,CAAC,GAAGwF,KAAK,GAAG,CAAC;EACtB,CAAC,MAAM,IAAIgB,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,OAAO,EAAE;IACvD,OAAOxG,CAAC,GAAGwF,KAAK;EAClB;EACA,OAAOxF,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyG,qBAAqBA,CAACjH,KAAK,EAAEkH,SAAS,EAAE;EAAC/C,WAAW;EAAEyC,QAAQ;EAAEO,OAAO;EAAEC;AAAO,CAAC,EAAEC,OAAO,EAAE;EACnG,MAAMC,UAAU,GAAG9I,QAAQ,CAAC6I,OAAO,CAAC;EACpC,MAAMrB,KAAK,GAAGkB,SAAS,CAAClB,KAAK,IAAIsB,UAAU,GAAGD,OAAO,CAACrB,KAAK,GAAG,CAAC,CAAC,GAAG7B,WAAW;EAC9E,MAAM8B,MAAM,GAAGiB,SAAS,CAACjB,MAAM,IAAIqB,UAAU,GAAGD,OAAO,CAACpB,MAAM,GAAG,CAAC,CAAC,GAAG9B,WAAW;EACjF,MAAMoD,WAAW,GAAGC,UAAU,CAACZ,QAAQ,CAAC;EACxC,MAAMpG,CAAC,GAAGiH,wBAAwB,CAACzH,KAAK,CAACQ,CAAC,EAAEwF,KAAK,EAAEmB,OAAO,EAAEI,WAAW,CAAC/G,CAAC,CAAC;EAC1E,MAAME,CAAC,GAAG+G,wBAAwB,CAACzH,KAAK,CAACU,CAAC,EAAEuF,MAAM,EAAEmB,OAAO,EAAEG,WAAW,CAAC7G,CAAC,CAAC;EAE3E,OAAO;IACLF,CAAC;IACDE,CAAC;IACDuD,EAAE,EAAEzD,CAAC,GAAGwF,KAAK;IACb9B,EAAE,EAAExD,CAAC,GAAGuF,MAAM;IACdD,KAAK;IACLC,MAAM;IACNpB,OAAO,EAAErE,CAAC,GAAGwF,KAAK,GAAG,CAAC;IACtBlB,OAAO,EAAEpE,CAAC,GAAGuF,MAAM,GAAG;EACxB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASuB,UAAUA,CAACnE,KAAK,EAAEqE,YAAY,GAAG,QAAQ,EAAE;EAClD,IAAIlJ,QAAQ,CAAC6E,KAAK,CAAC,EAAE;IACnB,OAAO;MACL7C,CAAC,EAAE/B,cAAc,CAAC4E,KAAK,CAAC7C,CAAC,EAAEkH,YAAY,CAAC;MACxChH,CAAC,EAAEjC,cAAc,CAAC4E,KAAK,CAAC3C,CAAC,EAAEgH,YAAY;IACzC,CAAC;EACH;EACArE,KAAK,GAAG5E,cAAc,CAAC4E,KAAK,EAAEqE,YAAY,CAAC;EAC3C,OAAO;IACLlH,CAAC,EAAE6C,KAAK;IACR3C,CAAC,EAAE2C;EACL,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMsE,SAAS,GAAGA,CAACrH,OAAO,EAAEsH,QAAQ,KAAKtH,OAAO,IAAIA,OAAO,CAACuH,OAAO,IAAID,QAAQ,GAAG,CAAC;;AAEnF;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACxH,OAAO,EAAEsH,QAAQ,EAAE;EAClC,MAAMG,OAAO,GAAGzH,OAAO,CAAC0H,IAAI;EAC5B,MAAMC,KAAK,GAAGpJ,OAAO,CAACkJ,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACpD,IAAIJ,SAAS,CAACrH,OAAO,EAAEsH,QAAQ,CAAC,EAAE;IAChC,OAAOK,KAAK,CAACC,GAAG,CAAC,UAASC,CAAC,EAAE;MAC3B,MAAMH,IAAI,GAAGlJ,MAAM,CAACqJ,CAAC,CAAC;MACtBH,IAAI,CAACrB,IAAI,GAAGvE,IAAI,CAACgG,KAAK,CAACD,CAAC,CAACxB,IAAI,GAAGiB,QAAQ,CAAC;MACzCI,IAAI,CAACK,UAAU,GAAGF,CAAC,CAACE,UAAU;MAC9B,OAAOvJ,MAAM,CAACkJ,IAAI,CAAC;IACrB,CAAC,CAAC;EACJ;EACA,OAAOC,KAAK,CAACC,GAAG,CAACC,CAAC,IAAIrJ,MAAM,CAACqJ,CAAC,CAAC,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA,SAASG,cAAcA,CAAChI,OAAO,EAAE;EAC/B,OAAOA,OAAO,KAAK5B,OAAO,CAAC4B,OAAO,CAACiI,MAAM,CAAC,IAAI7J,OAAO,CAAC4B,OAAO,CAACkI,MAAM,CAAC,CAAC;AACxE;AAEA,SAASf,wBAAwBA,CAACnE,KAAK,EAAEqD,IAAI,EAAE8B,MAAM,GAAG,CAAC,EAAE7B,QAAQ,EAAE;EACnE,OAAOtD,KAAK,GAAGoD,mBAAmB,CAACC,IAAI,EAAEC,QAAQ,CAAC,GAAG6B,MAAM;AAC7D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACC,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,EAAE;EAC3D,MAAMsI,QAAQ,GAAGtI,OAAO,CAACuI,IAAI;EAC7B,IAAI,CAACD,QAAQ,EAAE;IACb;EACF,CAAC,MAAM,IAAIA,QAAQ,KAAK,IAAI,EAAE;IAC5B,OAAOE,YAAY,CAAC1C,UAAU,EAAE9F,OAAO,CAAC;EAC1C;EACA,OAAOyI,YAAY,CAACJ,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0I,SAASA,CAAC1I,OAAO,EAAE2I,KAAK,EAAEC,cAAc,EAAE;EACjD,IAAIC,SAAS,GAAG,KAAK;EACrBF,KAAK,CAACG,OAAO,CAACC,IAAI,IAAI;IACpB,IAAI1K,UAAU,CAAC2B,OAAO,CAAC+I,IAAI,CAAC,CAAC,EAAE;MAC7BF,SAAS,GAAG,IAAI;MAChBD,cAAc,CAACG,IAAI,CAAC,GAAG/I,OAAO,CAAC+I,IAAI,CAAC;IACtC,CAAC,MAAM,IAAI3K,OAAO,CAACwK,cAAc,CAACG,IAAI,CAAC,CAAC,EAAE;MACxC,OAAOH,cAAc,CAACG,IAAI,CAAC;IAC7B;EACF,CAAC,CAAC;EACF,OAAOF,SAAS;AAClB;AAEA,SAASL,YAAYA,CAAC1C,UAAU,EAAE9F,OAAO,EAAE;EACzC,MAAMgJ,IAAI,GAAGhJ,OAAO,CAACgJ,IAAI,IAAI,MAAM;EACnC,OAAOpD,oBAAoB,CAACoD,IAAI,CAAC,CAAClD,UAAU,CAAC;AAC/C;AAEA,SAAS2C,YAAYA,CAACJ,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,EAAE;EAChD,MAAMiJ,MAAM,GAAG3K,QAAQ,CAAC0B,OAAO,CAACuI,IAAI,EAAE,CAAC;IAACF,KAAK;IAAEvC,UAAU;IAAE9F;EAAO,CAAC,CAAC,CAAC;EACrE,IAAIiJ,MAAM,KAAK,IAAI,EAAE;IACnB,OAAOT,YAAY,CAAC1C,UAAU,EAAE9F,OAAO,CAAC;EAC1C,CAAC,MAAM,IAAI9B,QAAQ,CAAC+K,MAAM,CAAC,EAAE;IAC3B,OAAOA,MAAM;EACf;AACF;AAEA,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC5B,MAAMC,SAAS,GAAI5F,MAAM,IAAK6F,KAAK,CAAC7F,MAAM,CAAC,IAAIA,MAAM,IAAI,CAAC;AAC1D,MAAM8F,QAAQ,GAAI3B,KAAK,IAAKA,KAAK,CAAC3G,MAAM,CAAC,UAASuI,IAAI,EAAEC,IAAI,EAAE;EAC5DD,IAAI,IAAIC,IAAI,CAACC,MAAM;EACnB,OAAOF,IAAI;AACb,CAAC,EAAE,EAAE,CAAC;;AAEN;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACC,OAAO,EAAE;EAChC,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC1C,MAAMX,IAAI,GAAGW,OAAO,CAACC,QAAQ,CAAC,CAAC;IAC/B,OAAQZ,IAAI,KAAK,2BAA2B,IAAIA,IAAI,KAAK,4BAA4B;EACvF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,SAASA,CAACC,GAAG,EAAE;EAAC5J,CAAC;EAAEE;AAAC,CAAC,EAAE+D,QAAQ,EAAE;EACxC,IAAIA,QAAQ,EAAE;IACZ2F,GAAG,CAACD,SAAS,CAAC3J,CAAC,EAAEE,CAAC,CAAC;IACnB0J,GAAG,CAACC,MAAM,CAAC9L,SAAS,CAACkG,QAAQ,CAAC,CAAC;IAC/B2F,GAAG,CAACD,SAAS,CAAC,CAAC3J,CAAC,EAAE,CAACE,CAAC,CAAC;EACvB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS4J,cAAcA,CAACF,GAAG,EAAE9J,OAAO,EAAE;EACpC,IAAIA,OAAO,IAAIA,OAAO,CAAC6D,WAAW,EAAE;IAClCiG,GAAG,CAACG,OAAO,GAAGjK,OAAO,CAACkK,cAAc,IAAI,MAAM;IAC9CJ,GAAG,CAACK,WAAW,CAACnK,OAAO,CAACoK,UAAU,CAAC;IACnCN,GAAG,CAACO,cAAc,GAAGrK,OAAO,CAACsK,gBAAgB;IAC7CR,GAAG,CAACS,QAAQ,GAAGvK,OAAO,CAACwK,eAAe,IAAI,OAAO;IACjDV,GAAG,CAACW,SAAS,GAAGzK,OAAO,CAAC6D,WAAW;IACnCiG,GAAG,CAACY,WAAW,GAAG1K,OAAO,CAAC2K,WAAW;IACrC,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACd,GAAG,EAAE9J,OAAO,EAAE;EACpC8J,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAAC8K,qBAAqB;EAC/ChB,GAAG,CAACiB,UAAU,GAAG/K,OAAO,CAAC+K,UAAU;EACnCjB,GAAG,CAACkB,aAAa,GAAGhL,OAAO,CAACgL,aAAa;EACzClB,GAAG,CAACmB,aAAa,GAAGjL,OAAO,CAACiL,aAAa;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACpB,GAAG,EAAE9J,OAAO,EAAE;EACtC,MAAM2J,OAAO,GAAG3J,OAAO,CAAC2J,OAAO;EAC/B,IAAID,eAAe,CAACC,OAAO,CAAC,EAAE;IAC5B,MAAMtD,IAAI,GAAG;MACXX,KAAK,EAAEa,OAAO,CAACoD,OAAO,CAACjE,KAAK,EAAE1F,OAAO,CAAC0F,KAAK,CAAC;MAC5CC,MAAM,EAAEY,OAAO,CAACoD,OAAO,CAAChE,MAAM,EAAE3F,OAAO,CAAC2F,MAAM;IAChD,CAAC;IACD,OAAOU,IAAI;EACb;EACA,MAAMsB,KAAK,GAAGH,OAAO,CAACxH,OAAO,CAAC;EAC9B,MAAMmL,WAAW,GAAGnL,OAAO,CAACoL,eAAe;EAC3C,MAAMC,KAAK,GAAG9M,OAAO,CAACoL,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACpD,MAAM2B,MAAM,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,GAAGjC,QAAQ,CAAC3B,KAAK,CAAC,GAAGwD,WAAW,IAAIrB,GAAG,CAAC0B,YAAY,GAAG,WAAW,GAAG,EAAE,CAAC;EACnG,IAAI,CAACtC,UAAU,CAACuC,GAAG,CAACH,MAAM,CAAC,EAAE;IAC3BpC,UAAU,CAACwC,GAAG,CAACJ,MAAM,EAAEK,kBAAkB,CAAC7B,GAAG,EAAEuB,KAAK,EAAE1D,KAAK,EAAEwD,WAAW,CAAC,CAAC;EAC5E;EACA,OAAOjC,UAAU,CAAC0C,GAAG,CAACN,MAAM,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASO,OAAOA,CAAC/B,GAAG,EAAE5F,IAAI,EAAElE,OAAO,EAAE;EACnC,MAAM;IAACE,CAAC;IAAEE,CAAC;IAAEsF,KAAK;IAAEC;EAAM,CAAC,GAAGzB,IAAI;EAClC4F,GAAG,CAACgC,IAAI,CAAC,CAAC;EACVlB,cAAc,CAACd,GAAG,EAAE9J,OAAO,CAAC;EAC5B,MAAM+L,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC;EAC3C8J,GAAG,CAACkC,SAAS,GAAGhM,OAAO,CAACiM,eAAe;EACvCnC,GAAG,CAACoC,SAAS,CAAC,CAAC;EACfzN,kBAAkB,CAACqL,GAAG,EAAE;IACtB5J,CAAC;IAAEE,CAAC;IAAE+L,CAAC,EAAEzG,KAAK;IAAE0G,CAAC,EAAEzG,MAAM;IACzBnC,MAAM,EAAEN,QAAQ,CAACxE,aAAa,CAACsB,OAAO,CAACqM,YAAY,CAAC,EAAE,CAAC,EAAEvK,IAAI,CAACY,GAAG,CAACgD,KAAK,EAAEC,MAAM,CAAC,GAAG,CAAC;EACtF,CAAC,CAAC;EACFmE,GAAG,CAACwC,SAAS,CAAC,CAAC;EACfxC,GAAG,CAACyC,IAAI,CAAC,CAAC;EACV,IAAIR,MAAM,EAAE;IACVjC,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;IAC3C1C,GAAG,CAACiC,MAAM,CAAC,CAAC;EACd;EACAjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC5C,GAAG,EAAE5F,IAAI,EAAElE,OAAO,EAAEsH,QAAQ,EAAE;EAC/C,MAAMqC,OAAO,GAAG3J,OAAO,CAAC2J,OAAO;EAC/B,IAAID,eAAe,CAACC,OAAO,CAAC,EAAE;IAC5BG,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVhC,GAAG,CAAC6C,WAAW,GAAGC,UAAU,CAAC5M,OAAO,CAAC6M,OAAO,EAAElD,OAAO,CAACmD,KAAK,CAACD,OAAO,CAAC;IACpE/C,GAAG,CAACiD,SAAS,CAACpD,OAAO,EAAEzF,IAAI,CAAChE,CAAC,EAAEgE,IAAI,CAAC9D,CAAC,EAAE8D,IAAI,CAACwB,KAAK,EAAExB,IAAI,CAACyB,MAAM,CAAC;IAC/DmE,GAAG,CAAC2C,OAAO,CAAC,CAAC;IACb;EACF;EACA,MAAMO,MAAM,GAAGzO,OAAO,CAACoL,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACrD,MAAMhC,KAAK,GAAGH,OAAO,CAACxH,OAAO,EAAEsH,QAAQ,CAAC;EACxC,MAAM2F,QAAQ,GAAGjN,OAAO,CAACkN,KAAK;EAC9B,MAAMC,MAAM,GAAG5O,OAAO,CAAC0O,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACxD,MAAM/M,CAAC,GAAGuG,sBAAsB,CAACvC,IAAI,EAAElE,OAAO,CAAC;EAC/C,MAAMI,CAAC,GAAG8D,IAAI,CAAC9D,CAAC,GAAGJ,OAAO,CAACoL,eAAe,GAAG,CAAC;EAC9CtB,GAAG,CAACgC,IAAI,CAAC,CAAC;EACVhC,GAAG,CAACsD,YAAY,GAAG,QAAQ;EAC3BtD,GAAG,CAACpD,SAAS,GAAG1G,OAAO,CAAC0G,SAAS;EACjC,IAAI2G,kBAAkB,CAACvD,GAAG,EAAE9J,OAAO,CAAC,EAAE;IACpCsN,oBAAoB,CAACxD,GAAG,EAAE;MAAC5J,CAAC;MAAEE;IAAC,CAAC,EAAE4M,MAAM,EAAErF,KAAK,CAAC;EAClD;EACA4F,iBAAiB,CAACzD,GAAG,EAAE;IAAC5J,CAAC;IAAEE;EAAC,CAAC,EAAE4M,MAAM,EAAE;IAACrF,KAAK;IAAEwF;EAAM,CAAC,CAAC;EACvDrD,GAAG,CAAC2C,OAAO,CAAC,CAAC;AACf;AAEA,SAASY,kBAAkBA,CAACvD,GAAG,EAAE9J,OAAO,EAAE;EACxC,IAAIA,OAAO,CAACoL,eAAe,GAAG,CAAC,EAAE;IAC/B;IACAtB,GAAG,CAACS,QAAQ,GAAG,OAAO;IACtBT,GAAG,CAAC0D,UAAU,GAAG,CAAC;IAClB1D,GAAG,CAACW,SAAS,GAAGzK,OAAO,CAACoL,eAAe;IACvCtB,GAAG,CAACY,WAAW,GAAG1K,OAAO,CAACyN,eAAe;IACzC,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAC5D,GAAG,EAAEtJ,OAAO,EAAEN,CAAC,EAAEE,CAAC,EAAE;EACrC,MAAM;IAACoD,MAAM;IAAExD;EAAO,CAAC,GAAGQ,OAAO;EACjC,MAAMsM,KAAK,GAAG9M,OAAO,CAAC2N,UAAU;EAChC,MAAMxJ,QAAQ,GAAGnE,OAAO,CAACmE,QAAQ;EACjC,IAAIyJ,GAAG,GAAG,CAACzJ,QAAQ,IAAI,CAAC,IAAIlF,WAAW;EAEvC,IAAIyK,eAAe,CAACoD,KAAK,CAAC,EAAE;IAC1BhD,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVhC,GAAG,CAACD,SAAS,CAAC3J,CAAC,EAAEE,CAAC,CAAC;IACnB0J,GAAG,CAACC,MAAM,CAAC6D,GAAG,CAAC;IACf9D,GAAG,CAACiD,SAAS,CAACD,KAAK,EAAE,CAACA,KAAK,CAACpH,KAAK,GAAG,CAAC,EAAE,CAACoH,KAAK,CAACnH,MAAM,GAAG,CAAC,EAAEmH,KAAK,CAACpH,KAAK,EAAEoH,KAAK,CAACnH,MAAM,CAAC;IACpFmE,GAAG,CAAC2C,OAAO,CAAC,CAAC;IACb;EACF;EACA,IAAIrD,SAAS,CAAC5F,MAAM,CAAC,EAAE;IACrB;EACF;EACAqK,cAAc,CAAC/D,GAAG,EAAE;IAAC5J,CAAC;IAAEE,CAAC;IAAEoD,MAAM;IAAEW,QAAQ;IAAE2I,KAAK;IAAEc;EAAG,CAAC,CAAC;AAC3D;AAEA,SAASC,cAAcA,CAAC/D,GAAG,EAAE;EAAC5J,CAAC;EAAEE,CAAC;EAAEoD,MAAM;EAAEW,QAAQ;EAAE2I,KAAK;EAAEc;AAAG,CAAC,EAAE;EACjE,IAAIE,OAAO,EAAEC,OAAO,EAAE1H,IAAI,EAAE2H,YAAY;EACxClE,GAAG,CAACoC,SAAS,CAAC,CAAC;EAEf,QAAQY,KAAK;IACb;IACA;MACEhD,GAAG,CAACmE,GAAG,CAAC/N,CAAC,EAAEE,CAAC,EAAEoD,MAAM,EAAE,CAAC,EAAEzE,GAAG,CAAC;MAC7B+K,GAAG,CAACwC,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACbxC,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM,EAAEpD,CAAC,GAAG0B,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM,CAAC;MAClEoK,GAAG,IAAI9O,aAAa;MACpBgL,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM,EAAEpD,CAAC,GAAG0B,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM,CAAC;MAClEoK,GAAG,IAAI9O,aAAa;MACpBgL,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4B,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM,EAAEpD,CAAC,GAAG0B,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM,CAAC;MAClEsG,GAAG,CAACwC,SAAS,CAAC,CAAC;MACf;IACF,KAAK,aAAa;MAChB;MACA;MACA;MACA;MACA;MACA;MACA;MACA0B,YAAY,GAAGxK,MAAM,GAAG,KAAK;MAC7B6C,IAAI,GAAG7C,MAAM,GAAGwK,YAAY;MAC5BF,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,GAAGjP,UAAU,CAAC,GAAG0H,IAAI;MAC3C0H,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,GAAGjP,UAAU,CAAC,GAAG0H,IAAI;MAC3CyD,GAAG,CAACmE,GAAG,CAAC/N,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,EAAEC,YAAY,EAAEJ,GAAG,GAAGhP,EAAE,EAAEgP,GAAG,GAAG/O,OAAO,CAAC;MACxEiL,GAAG,CAACmE,GAAG,CAAC/N,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,EAAEE,YAAY,EAAEJ,GAAG,GAAG/O,OAAO,EAAE+O,GAAG,CAAC;MACnE9D,GAAG,CAACmE,GAAG,CAAC/N,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,EAAEC,YAAY,EAAEJ,GAAG,EAAEA,GAAG,GAAG/O,OAAO,CAAC;MACnEiL,GAAG,CAACmE,GAAG,CAAC/N,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,EAAEE,YAAY,EAAEJ,GAAG,GAAG/O,OAAO,EAAE+O,GAAG,GAAGhP,EAAE,CAAC;MACxEkL,GAAG,CAACwC,SAAS,CAAC,CAAC;MACf;IACF,KAAK,MAAM;MACT,IAAI,CAACnI,QAAQ,EAAE;QACbkC,IAAI,GAAGvE,IAAI,CAACsM,OAAO,GAAG5K,MAAM;QAC5BsG,GAAG,CAAC5F,IAAI,CAAChE,CAAC,GAAGmG,IAAI,EAAEjG,CAAC,GAAGiG,IAAI,EAAE,CAAC,GAAGA,IAAI,EAAE,CAAC,GAAGA,IAAI,CAAC;QAChD;MACF;MACAuH,GAAG,IAAIjP,UAAU;IACjB;IACF,KAAK,SAAS;MACZmP,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM;MAChCuK,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM;MAChCsG,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpChE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpChE,GAAG,CAACwC,SAAS,CAAC,CAAC;MACf;IACF,KAAK,UAAU;MACbsB,GAAG,IAAIjP,UAAU;IACjB;IACF,KAAK,OAAO;MACVmP,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM;MAChCuK,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM;MAChCsG,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpChE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM;MAChCuK,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM;MAChCsG,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpChE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpCF,GAAG,IAAIjP,UAAU;MACjBmP,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM;MAChCuK,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM;MAChCsG,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpChE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG6N,OAAO,EAAE3N,CAAC,GAAG0N,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTA,OAAO,GAAGhM,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM;MAChCuK,OAAO,GAAGjM,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM;MAChCsG,GAAG,CAACoE,MAAM,CAAChO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpCjE,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4N,OAAO,EAAE1N,CAAC,GAAG2N,OAAO,CAAC;MACpC;IACF,KAAK,MAAM;MACTjE,GAAG,CAACoE,MAAM,CAAChO,CAAC,EAAEE,CAAC,CAAC;MAChB0J,GAAG,CAACqE,MAAM,CAACjO,CAAC,GAAG4B,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC,GAAGpK,MAAM,EAAEpD,CAAC,GAAG0B,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC,GAAGpK,MAAM,CAAC;MAClE;EACF;EAEAsG,GAAG,CAACyC,IAAI,CAAC,CAAC;AACZ;AAEA,SAASZ,kBAAkBA,CAAC7B,GAAG,EAAEuB,KAAK,EAAE1D,KAAK,EAAEwD,WAAW,EAAE;EAC1DrB,GAAG,CAACgC,IAAI,CAAC,CAAC;EACV,MAAMuC,KAAK,GAAGhD,KAAK,CAAChJ,MAAM;EAC1B,IAAIqD,KAAK,GAAG,CAAC;EACb,IAAIC,MAAM,GAAGwF,WAAW;EACxB,KAAK,IAAInG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,KAAK,EAAErJ,CAAC,EAAE,EAAE;IAC9B,MAAM0C,IAAI,GAAGC,KAAK,CAAC7F,IAAI,CAACY,GAAG,CAACsC,CAAC,EAAE2C,KAAK,CAACtF,MAAM,GAAG,CAAC,CAAC,CAAC;IACjDyH,GAAG,CAACpC,IAAI,GAAGA,IAAI,CAAC+B,MAAM;IACtB,MAAM6E,IAAI,GAAGjD,KAAK,CAACrG,CAAC,CAAC;IACrBU,KAAK,GAAG5D,IAAI,CAACa,GAAG,CAAC+C,KAAK,EAAEoE,GAAG,CAACyE,WAAW,CAACD,IAAI,CAAC,CAAC5I,KAAK,GAAGyF,WAAW,CAAC;IAClExF,MAAM,IAAI+B,IAAI,CAACK,UAAU;EAC3B;EACA+B,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACb,OAAO;IAAC/G,KAAK;IAAEC;EAAM,CAAC;AACxB;AAEA,SAAS2H,oBAAoBA,CAACxD,GAAG,EAAE;EAAC5J,CAAC;EAAEE;AAAC,CAAC,EAAE4M,MAAM,EAAErF,KAAK,EAAE;EACxDmC,GAAG,CAACoC,SAAS,CAAC,CAAC;EACf,IAAIsC,GAAG,GAAG,CAAC;EACXxB,MAAM,CAAClE,OAAO,CAAC,UAAS2F,CAAC,EAAEzJ,CAAC,EAAE;IAC5B,MAAM6C,CAAC,GAAGF,KAAK,CAAC7F,IAAI,CAACY,GAAG,CAACsC,CAAC,EAAE2C,KAAK,CAACtF,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAMqM,EAAE,GAAG7G,CAAC,CAACE,UAAU;IACvB+B,GAAG,CAACpC,IAAI,GAAGG,CAAC,CAAC4B,MAAM;IACnBK,GAAG,CAAC6E,UAAU,CAACF,CAAC,EAAEvO,CAAC,EAAEE,CAAC,GAAGsO,EAAE,GAAG,CAAC,GAAGF,GAAG,CAAC;IACtCA,GAAG,IAAIE,EAAE;EACX,CAAC,CAAC;EACF5E,GAAG,CAACiC,MAAM,CAAC,CAAC;AACd;AAEA,SAASwB,iBAAiBA,CAACzD,GAAG,EAAE;EAAC5J,CAAC;EAAEE;AAAC,CAAC,EAAE4M,MAAM,EAAE;EAACrF,KAAK;EAAEwF;AAAM,CAAC,EAAE;EAC/D,IAAIqB,GAAG,GAAG,CAAC;EACXxB,MAAM,CAAClE,OAAO,CAAC,UAAS2F,CAAC,EAAEzJ,CAAC,EAAE;IAC5B,MAAM4J,CAAC,GAAGzB,MAAM,CAACrL,IAAI,CAACY,GAAG,CAACsC,CAAC,EAAEmI,MAAM,CAAC9K,MAAM,GAAG,CAAC,CAAC,CAAC;IAChD,MAAMwF,CAAC,GAAGF,KAAK,CAAC7F,IAAI,CAACY,GAAG,CAACsC,CAAC,EAAE2C,KAAK,CAACtF,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAMqM,EAAE,GAAG7G,CAAC,CAACE,UAAU;IACvB+B,GAAG,CAACoC,SAAS,CAAC,CAAC;IACfpC,GAAG,CAACpC,IAAI,GAAGG,CAAC,CAAC4B,MAAM;IACnBK,GAAG,CAACkC,SAAS,GAAG4C,CAAC;IACjB9E,GAAG,CAAC+E,QAAQ,CAACJ,CAAC,EAAEvO,CAAC,EAAEE,CAAC,GAAGsO,EAAE,GAAG,CAAC,GAAGF,GAAG,CAAC;IACpCA,GAAG,IAAIE,EAAE;IACT5E,GAAG,CAACyC,IAAI,CAAC,CAAC;EACZ,CAAC,CAAC;AACJ;AAEA,SAASK,UAAUA,CAAC7J,KAAK,EAAE+L,YAAY,EAAE;EACvC,MAAMjC,OAAO,GAAG7N,QAAQ,CAAC+D,KAAK,CAAC,GAAGA,KAAK,GAAG+L,YAAY;EACtD,OAAO9P,QAAQ,CAAC6N,OAAO,CAAC,GAAGtK,KAAK,CAACsK,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;AACrD;AAEA,MAAMkC,SAAS,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAAClF,GAAG,EAAEtJ,OAAO,EAAE;EACjC,MAAM;IAACyO,MAAM;IAAEC,MAAM;IAAElP;EAAO,CAAC,GAAGQ,OAAO;EACzC,MAAM2O,OAAO,GAAGnP,OAAO,CAACmP,OAAO;EAC/B,MAAMC,eAAe,GAAGD,OAAO,IAAIA,OAAO,CAACE,OAAO,IAAIC,sBAAsB,CAAC9O,OAAO,EAAE2O,OAAO,CAAC;EAC9F,IAAI,CAACC,eAAe,IAAIG,cAAc,CAAC/O,OAAO,EAAE2O,OAAO,EAAEC,eAAe,CAAC,EAAE;IACzE;EACF;EAEAtF,GAAG,CAACgC,IAAI,CAAC,CAAC;EACVhC,GAAG,CAACoC,SAAS,CAAC,CAAC;EACf,MAAMH,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAEqF,OAAO,CAAC;EAC3C,IAAI,CAACpD,MAAM,EAAE;IACX,OAAOjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACtB;EACA,MAAM;IAAC+C,cAAc;IAAEC;EAAY,CAAC,GAAGC,wBAAwB,CAAClP,OAAO,EAAE4O,eAAe,CAAC;EACzF,MAAM;IAACO,SAAS;IAAEC;EAAO,CAAC,GAAGC,mBAAmB,CAACrP,OAAO,EAAE4O,eAAe,EAAEI,cAAc,CAAC;EAC1F,IAAIL,OAAO,CAACW,MAAM,GAAG,CAAC,IAAI9P,OAAO,CAAC6D,WAAW,KAAK,CAAC,EAAE;IACnDiG,GAAG,CAACoE,MAAM,CAACsB,cAAc,CAACtP,CAAC,EAAEsP,cAAc,CAACpP,CAAC,CAAC;IAC9C0J,GAAG,CAACqE,MAAM,CAACsB,YAAY,CAACvP,CAAC,EAAEuP,YAAY,CAACrP,CAAC,CAAC;EAC5C;EACA0J,GAAG,CAACoE,MAAM,CAACyB,SAAS,CAACzP,CAAC,EAAEyP,SAAS,CAACvP,CAAC,CAAC;EACpC0J,GAAG,CAACqE,MAAM,CAACyB,OAAO,CAAC1P,CAAC,EAAE0P,OAAO,CAACxP,CAAC,CAAC;EAChC,MAAM2P,YAAY,GAAGpO,OAAO,CAAC;IAACzB,CAAC,EAAE+O,MAAM;IAAE7O,CAAC,EAAE8O;EAAM,CAAC,EAAE1O,OAAO,CAACU,cAAc,CAAC,CAAC,EAAEjD,SAAS,CAAC,CAACuC,OAAO,CAAC2D,QAAQ,CAAC,CAAC;EAC5G2F,GAAG,CAACqE,MAAM,CAAC4B,YAAY,CAAC7P,CAAC,EAAE6P,YAAY,CAAC3P,CAAC,CAAC;EAC1C0J,GAAG,CAACiC,MAAM,CAAC,CAAC;EACZjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;AACf;AAEA,SAASiD,wBAAwBA,CAAClP,OAAO,EAAE8F,QAAQ,EAAE;EACnD,MAAM;IAACpG,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC;EAAE,CAAC,GAAGpD,OAAO;EAC9B,MAAM2H,MAAM,GAAG6H,yBAAyB,CAACxP,OAAO,EAAE8F,QAAQ,CAAC;EAC3D,IAAIkJ,cAAc,EAAEC,YAAY;EAChC,IAAInJ,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE;IAC/CkJ,cAAc,GAAG;MAACtP,CAAC,EAAEA,CAAC,GAAGiI,MAAM;MAAE/H;IAAC,CAAC;IACnCqP,YAAY,GAAG;MAACvP,CAAC,EAAEsP,cAAc,CAACtP,CAAC;MAAEE,CAAC,EAAEwD;IAAE,CAAC;EAC7C,CAAC,MAAM;IACL;IACA4L,cAAc,GAAG;MAACtP,CAAC;MAAEE,CAAC,EAAEA,CAAC,GAAG+H;IAAM,CAAC;IACnCsH,YAAY,GAAG;MAACvP,CAAC,EAAEyD,EAAE;MAAEvD,CAAC,EAAEoP,cAAc,CAACpP;IAAC,CAAC;EAC7C;EACA,OAAO;IAACoP,cAAc;IAAEC;EAAY,CAAC;AACvC;AAEA,SAASO,yBAAyBA,CAACxP,OAAO,EAAE8F,QAAQ,EAAE;EACpD,MAAM;IAACZ,KAAK;IAAEC,MAAM;IAAE3F;EAAO,CAAC,GAAGQ,OAAO;EACxC,MAAM2H,MAAM,GAAGnI,OAAO,CAACmP,OAAO,CAACW,MAAM,GAAG9P,OAAO,CAAC6D,WAAW,GAAG,CAAC;EAC/D,IAAIyC,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAOZ,KAAK,GAAGyC,MAAM;EACvB,CAAC,MAAM,IAAI7B,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOX,MAAM,GAAGwC,MAAM;EACxB;EACA,OAAO,CAACA,MAAM;AAChB;AAEA,SAAS0H,mBAAmBA,CAACrP,OAAO,EAAE8F,QAAQ,EAAEkJ,cAAc,EAAE;EAC9D,MAAM;IAACpP,CAAC;IAAEsF,KAAK;IAAEC,MAAM;IAAE3F;EAAO,CAAC,GAAGQ,OAAO;EAC3C,MAAMwC,KAAK,GAAGhD,OAAO,CAACmP,OAAO,CAACnM,KAAK;EACnC,MAAMiN,IAAI,GAAGC,oBAAoB,CAAC5J,QAAQ,EAAEtG,OAAO,CAACmP,OAAO,CAAC;EAC5D,IAAIQ,SAAS,EAAEC,OAAO;EACtB,IAAItJ,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE;IAC/CqJ,SAAS,GAAG;MAACzP,CAAC,EAAEsP,cAAc,CAACtP,CAAC;MAAEE,CAAC,EAAEA,CAAC,GAAGmG,OAAO,CAACZ,MAAM,EAAE3C,KAAK;IAAC,CAAC;IAChE4M,OAAO,GAAG;MAAC1P,CAAC,EAAEyP,SAAS,CAACzP,CAAC,GAAG+P,IAAI;MAAE7P,CAAC,EAAEuP,SAAS,CAACvP;IAAC,CAAC;EACnD,CAAC,MAAM;IACL;IACAuP,SAAS,GAAG;MAACzP,CAAC,EAAEsP,cAAc,CAACtP,CAAC,GAAGqG,OAAO,CAACb,KAAK,EAAE1C,KAAK,CAAC;MAAE5C,CAAC,EAAEoP,cAAc,CAACpP;IAAC,CAAC;IAC9EwP,OAAO,GAAG;MAAC1P,CAAC,EAAEyP,SAAS,CAACzP,CAAC;MAAEE,CAAC,EAAEuP,SAAS,CAACvP,CAAC,GAAG6P;IAAI,CAAC;EACnD;EACA,OAAO;IAACN,SAAS;IAAEC;EAAO,CAAC;AAC7B;AAEA,SAASM,oBAAoBA,CAAC5J,QAAQ,EAAEtG,OAAO,EAAE;EAC/C,MAAMiQ,IAAI,GAAGjQ,OAAO,CAACiQ,IAAI;EACzB,IAAI3J,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,KAAK,EAAE;IAC7C,OAAO,CAAC2J,IAAI;EACd;EACA,OAAOA,IAAI;AACb;AAEA,SAASX,sBAAsBA,CAAC9O,OAAO,EAAER,OAAO,EAAE;EAChD,MAAMsG,QAAQ,GAAGtG,OAAO,CAACsG,QAAQ;EACjC,IAAIyI,SAAS,CAACoB,QAAQ,CAAC7J,QAAQ,CAAC,EAAE;IAChC,OAAOA,QAAQ;EACjB;EACA,OAAO8J,0BAA0B,CAAC5P,OAAO,EAAER,OAAO,CAAC;AACrD;AAEA,SAASoQ,0BAA0BA,CAAC5P,OAAO,EAAER,OAAO,EAAE;EACpD,MAAM;IAACE,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC,EAAE;IAAE8B,KAAK;IAAEC,MAAM;IAAEsJ,MAAM;IAAEC,MAAM;IAAE3K,OAAO;IAAEC,OAAO;IAAEL;EAAQ,CAAC,GAAG3D,OAAO;EACzF,MAAMG,MAAM,GAAG;IAACT,CAAC,EAAEqE,OAAO;IAAEnE,CAAC,EAAEoE;EAAO,CAAC;EACvC,MAAMxB,KAAK,GAAGhD,OAAO,CAACgD,KAAK;EAC3B,MAAM6D,OAAO,GAAGN,OAAO,CAACb,KAAK,EAAE1C,KAAK,CAAC;EACrC,MAAM8D,OAAO,GAAGP,OAAO,CAACZ,MAAM,EAAE3C,KAAK,CAAC;EACtC,MAAMqN,OAAO,GAAG,CAACnQ,CAAC,EAAEA,CAAC,GAAG2G,OAAO,EAAE3G,CAAC,GAAG2G,OAAO,EAAElD,EAAE,CAAC;EACjD,MAAM2M,OAAO,GAAG,CAAClQ,CAAC,GAAG0G,OAAO,EAAElD,EAAE,EAAExD,CAAC,EAAEwD,EAAE,CAAC;EACxC,MAAMqF,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIsH,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAE,EAAE;IACtC,MAAMR,YAAY,GAAGpO,OAAO,CAAC;MAACzB,CAAC,EAAEmQ,OAAO,CAACE,KAAK,CAAC;MAAEnQ,CAAC,EAAEkQ,OAAO,CAACC,KAAK;IAAC,CAAC,EAAE5P,MAAM,EAAE1C,SAAS,CAACkG,QAAQ,CAAC,CAAC;IACjG8E,MAAM,CAAC5H,IAAI,CAAC;MACViF,QAAQ,EAAEyI,SAAS,CAACwB,KAAK,CAAC;MAC1BnP,QAAQ,EAAEpD,qBAAqB,CAAC+R,YAAY,EAAE;QAAC7P,CAAC,EAAE+O,MAAM;QAAE7O,CAAC,EAAE8O;MAAM,CAAC;IACtE,CAAC,CAAC;EACJ;EACA,OAAOjG,MAAM,CAAC3H,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACH,QAAQ,GAAGI,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAACkF,QAAQ;AACnE;AAEA,SAASiJ,cAAcA,CAAC/O,OAAO,EAAE2O,OAAO,EAAE7I,QAAQ,EAAE;EAClD,MAAM;IAAC2I,MAAM;IAAEC;EAAM,CAAC,GAAG1O,OAAO;EAChC,MAAMsP,MAAM,GAAGX,OAAO,CAACW,MAAM;EAC7B,IAAI5P,CAAC,GAAG+O,MAAM;EACd,IAAI7O,CAAC,GAAG8O,MAAM;EACd,IAAI5I,QAAQ,KAAK,MAAM,EAAE;IACvBpG,CAAC,IAAI4P,MAAM;EACb,CAAC,MAAM,IAAIxJ,QAAQ,KAAK,OAAO,EAAE;IAC/BpG,CAAC,IAAI4P,MAAM;EACb,CAAC,MAAM,IAAIxJ,QAAQ,KAAK,KAAK,EAAE;IAC7BlG,CAAC,IAAI0P,MAAM;EACb,CAAC,MAAM,IAAIxJ,QAAQ,KAAK,QAAQ,EAAE;IAChClG,CAAC,IAAI0P,MAAM;EACb;EACA,OAAOtP,OAAO,CAACC,OAAO,CAACP,CAAC,EAAEE,CAAC,CAAC;AAC9B;AAEA,MAAMoQ,gBAAgB,GAAG;EACvBC,QAAQ,EAAE;IAAC/N,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE,MAAM;IAAEK,KAAK,EAAE,MAAM;IAAEC,GAAG,EAAE,OAAO;IAAEyN,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAI,CAAC;EAChGC,QAAQ,EAAE;IAAClO,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE,MAAM;IAAEK,KAAK,EAAE,QAAQ;IAAEC,GAAG,EAAE,KAAK;IAAEyN,SAAS,EAAE,GAAG;IAAEC,OAAO,EAAE;EAAI;AACjG,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,KAAK,EAAE/N,KAAK,EAAEgO,QAAQ,EAAE;EAC1ChO,KAAK,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG+N,KAAK,CAACE,KAAK,CAACjO,KAAK,CAAC;EAC9D,OAAO5D,QAAQ,CAAC4D,KAAK,CAAC,GAAG+N,KAAK,CAACG,gBAAgB,CAAClO,KAAK,CAAC,GAAGgO,QAAQ;AACnE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACC,MAAM,EAAEnR,OAAO,EAAEoD,GAAG,EAAE;EAC7C,MAAMgO,OAAO,GAAGpR,OAAO,CAACoD,GAAG,CAAC;EAC5B,IAAIgO,OAAO,IAAIhO,GAAG,KAAK,SAAS,EAAE;IAChC,OAAOgO,OAAO;EAChB;EACA,MAAMjR,IAAI,GAAGiD,GAAG,CAACiO,MAAM,CAAC,CAAC,CAAC;EAC1B,MAAMC,IAAI,GAAGjO,MAAM,CAACkO,MAAM,CAACJ,MAAM,CAAC,CAACvQ,MAAM,CAAEkQ,KAAK,IAAKA,KAAK,CAAC3Q,IAAI,IAAI2Q,KAAK,CAAC3Q,IAAI,KAAKA,IAAI,CAAC;EACvF,IAAImR,IAAI,CAACjP,MAAM,EAAE;IACf,OAAOiP,IAAI,CAAC,CAAC,CAAC,CAACE,EAAE;EACnB;EACA,OAAOrR,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsR,mBAAmBA,CAACX,KAAK,EAAE9Q,OAAO,EAAE;EAC3C,IAAI8Q,KAAK,EAAE;IACT,MAAMY,OAAO,GAAGZ,KAAK,CAAC9Q,OAAO,CAAC0R,OAAO;IACrC,MAAM1O,KAAK,GAAG6N,UAAU,CAACC,KAAK,EAAE9Q,OAAO,CAAC0C,GAAG,EAAEgP,OAAO,GAAG1R,OAAO,CAACiD,GAAG,GAAGjD,OAAO,CAACgD,KAAK,CAAC;IACnF,MAAMC,GAAG,GAAG4N,UAAU,CAACC,KAAK,EAAE9Q,OAAO,CAAC2C,GAAG,EAAE+O,OAAO,GAAG1R,OAAO,CAACgD,KAAK,GAAGhD,OAAO,CAACiD,GAAG,CAAC;IACjF,OAAO;MACLD,KAAK;MACLC;IACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS0O,aAAaA,CAACtJ,KAAK,EAAErI,OAAO,EAAE;EACrC,MAAM;IAAC4R,SAAS;IAAET;EAAM,CAAC,GAAG9I,KAAK;EACjC,MAAMwJ,MAAM,GAAGV,MAAM,CAACD,eAAe,CAACC,MAAM,EAAEnR,OAAO,EAAE,UAAU,CAAC,CAAC;EACnE,MAAM8R,MAAM,GAAGX,MAAM,CAACD,eAAe,CAACC,MAAM,EAAEnR,OAAO,EAAE,UAAU,CAAC,CAAC;EACnE,IAAIE,CAAC,GAAG0R,SAAS,CAAClM,KAAK,GAAG,CAAC;EAC3B,IAAItF,CAAC,GAAGwR,SAAS,CAACjM,MAAM,GAAG,CAAC;EAE5B,IAAIkM,MAAM,EAAE;IACV3R,CAAC,GAAG2Q,UAAU,CAACgB,MAAM,EAAE7R,OAAO,CAACiI,MAAM,EAAE4J,MAAM,CAACE,IAAI,GAAGF,MAAM,CAACnM,KAAK,GAAG,CAAC,CAAC;EACxE;EAEA,IAAIoM,MAAM,EAAE;IACV1R,CAAC,GAAGyQ,UAAU,CAACiB,MAAM,EAAE9R,OAAO,CAACkI,MAAM,EAAE4J,MAAM,CAACE,GAAG,GAAGF,MAAM,CAACnM,MAAM,GAAG,CAAC,CAAC;EACxE;EACA,OAAO;IAACzF,CAAC;IAAEE;EAAC,CAAC;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS6R,oBAAoBA,CAAC5J,KAAK,EAAErI,OAAO,EAAE;EAC5C,MAAMmR,MAAM,GAAG9I,KAAK,CAAC8I,MAAM;EAC3B,MAAMU,MAAM,GAAGV,MAAM,CAACD,eAAe,CAACC,MAAM,EAAEnR,OAAO,EAAE,UAAU,CAAC,CAAC;EACnE,MAAM8R,MAAM,GAAGX,MAAM,CAACD,eAAe,CAACC,MAAM,EAAEnR,OAAO,EAAE,UAAU,CAAC,CAAC;EAEnE,IAAI,CAAC6R,MAAM,IAAI,CAACC,MAAM,EAAE;IACtB,OAAO,CAAC,CAAC;EACX;EAEA,IAAI;IAACC,IAAI,EAAE7R,CAAC;IAAEgS,KAAK,EAAEvO;EAAE,CAAC,GAAGkO,MAAM,IAAIxJ,KAAK,CAACuJ,SAAS;EACpD,IAAI;IAACI,GAAG,EAAE5R,CAAC;IAAE+R,MAAM,EAAEvO;EAAE,CAAC,GAAGkO,MAAM,IAAIzJ,KAAK,CAACuJ,SAAS;EACpD,MAAMQ,IAAI,GAAGC,wBAAwB,CAACR,MAAM,EAAE;IAACnP,GAAG,EAAE1C,OAAO,CAACsS,IAAI;IAAE3P,GAAG,EAAE3C,OAAO,CAACuS,IAAI;IAAEvP,KAAK,EAAE9C,CAAC;IAAE+C,GAAG,EAAEU;EAAE,CAAC,CAAC;EACxGzD,CAAC,GAAGkS,IAAI,CAACpP,KAAK;EACdW,EAAE,GAAGyO,IAAI,CAACnP,GAAG;EACb,MAAMuP,IAAI,GAAGH,wBAAwB,CAACP,MAAM,EAAE;IAACpP,GAAG,EAAE1C,OAAO,CAACyS,IAAI;IAAE9P,GAAG,EAAE3C,OAAO,CAAC0S,IAAI;IAAE1P,KAAK,EAAEY,EAAE;IAAEX,GAAG,EAAE7C;EAAC,CAAC,CAAC;EACxGA,CAAC,GAAGoS,IAAI,CAACxP,KAAK;EACdY,EAAE,GAAG4O,IAAI,CAACvP,GAAG;EAEb,OAAO;IACL/C,CAAC;IACDE,CAAC;IACDuD,EAAE;IACFC,EAAE;IACF8B,KAAK,EAAE/B,EAAE,GAAGzD,CAAC;IACbyF,MAAM,EAAE/B,EAAE,GAAGxD,CAAC;IACdmE,OAAO,EAAErE,CAAC,GAAG,CAACyD,EAAE,GAAGzD,CAAC,IAAI,CAAC;IACzBsE,OAAO,EAAEpE,CAAC,GAAG,CAACwD,EAAE,GAAGxD,CAAC,IAAI;EAC1B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASuS,sBAAsBA,CAACtK,KAAK,EAAErI,OAAO,EAAE;EAC9C,IAAI,CAACgI,cAAc,CAAChI,OAAO,CAAC,EAAE;IAC5B,MAAM6F,GAAG,GAAGoM,oBAAoB,CAAC5J,KAAK,EAAErI,OAAO,CAAC;IAChD,IAAIwD,MAAM,GAAGxD,OAAO,CAACwD,MAAM;IAC3B,IAAI,CAACA,MAAM,IAAI6F,KAAK,CAAC7F,MAAM,CAAC,EAAE;MAC5BA,MAAM,GAAG1B,IAAI,CAACY,GAAG,CAACmD,GAAG,CAACH,KAAK,EAAEG,GAAG,CAACF,MAAM,CAAC,GAAG,CAAC;MAC5C3F,OAAO,CAACwD,MAAM,GAAGA,MAAM;IACzB;IACA,MAAM6C,IAAI,GAAG7C,MAAM,GAAG,CAAC;IACvB,MAAMoP,aAAa,GAAG/M,GAAG,CAACtB,OAAO,GAAGvE,OAAO,CAAC6G,OAAO;IACnD,MAAMgM,aAAa,GAAGhN,GAAG,CAACrB,OAAO,GAAGxE,OAAO,CAAC8G,OAAO;IACnD,OAAO;MACL5G,CAAC,EAAE0S,aAAa,GAAGpP,MAAM;MACzBpD,CAAC,EAAEyS,aAAa,GAAGrP,MAAM;MACzBG,EAAE,EAAEiP,aAAa,GAAGpP,MAAM;MAC1BI,EAAE,EAAEiP,aAAa,GAAGrP,MAAM;MAC1Be,OAAO,EAAEqO,aAAa;MACtBpO,OAAO,EAAEqO,aAAa;MACtBnN,KAAK,EAAEW,IAAI;MACXV,MAAM,EAAEU,IAAI;MACZ7C;IACF,CAAC;EACH;EACA,OAAOsP,cAAc,CAACzK,KAAK,EAAErI,OAAO,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+S,qBAAqBA,CAAC1K,KAAK,EAAErI,OAAO,EAAE;EAC7C,MAAM;IAACmR,MAAM;IAAES;EAAS,CAAC,GAAGvJ,KAAK;EACjC,MAAMyI,KAAK,GAAGK,MAAM,CAACnR,OAAO,CAACoR,OAAO,CAAC;EACrC,MAAM4B,IAAI,GAAG;IAAC9S,CAAC,EAAE0R,SAAS,CAACG,IAAI;IAAE3R,CAAC,EAAEwR,SAAS,CAACI,GAAG;IAAErO,EAAE,EAAEiO,SAAS,CAACM,KAAK;IAAEtO,EAAE,EAAEgO,SAAS,CAACO;EAAM,CAAC;EAE7F,IAAIrB,KAAK,EAAE;IACTmC,yBAAyB,CAACnC,KAAK,EAAEkC,IAAI,EAAEhT,OAAO,CAAC;EACjD,CAAC,MAAM;IACLkT,4BAA4B,CAAC/B,MAAM,EAAE6B,IAAI,EAAEhT,OAAO,CAAC;EACrD;EACA,OAAOgT,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,4BAA4BA,CAAC9K,KAAK,EAAErI,OAAO,EAAE;EACpD,MAAM8F,UAAU,GAAGmM,oBAAoB,CAAC5J,KAAK,EAAErI,OAAO,CAAC;EACvD8F,UAAU,CAACsN,cAAc,GAAGhL,uBAAuB,CAACC,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;EAC/E8F,UAAU,CAACuN,QAAQ,GAAG,CAAC;IACrBrK,IAAI,EAAE,OAAO;IACbsK,WAAW,EAAE,OAAO;IACpBxN,UAAU,EAAEyN,+BAA+B,CAAClL,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;IACvEoT,cAAc,EAAEtN,UAAU,CAACsN;EAC7B,CAAC,CAAC;EACF,OAAOtN,UAAU;AACnB;AAEA,SAASgN,cAAcA,CAACzK,KAAK,EAAErI,OAAO,EAAE;EACtC,MAAMN,KAAK,GAAGiS,aAAa,CAACtJ,KAAK,EAAErI,OAAO,CAAC;EAC3C,MAAMqG,IAAI,GAAGrG,OAAO,CAACwD,MAAM,GAAG,CAAC;EAC/B,OAAO;IACLtD,CAAC,EAAER,KAAK,CAACQ,CAAC,GAAGF,OAAO,CAACwD,MAAM,GAAGxD,OAAO,CAAC6G,OAAO;IAC7CzG,CAAC,EAAEV,KAAK,CAACU,CAAC,GAAGJ,OAAO,CAACwD,MAAM,GAAGxD,OAAO,CAAC8G,OAAO;IAC7CnD,EAAE,EAAEjE,KAAK,CAACQ,CAAC,GAAGF,OAAO,CAACwD,MAAM,GAAGxD,OAAO,CAAC6G,OAAO;IAC9CjD,EAAE,EAAElE,KAAK,CAACU,CAAC,GAAGJ,OAAO,CAACwD,MAAM,GAAGxD,OAAO,CAAC8G,OAAO;IAC9CvC,OAAO,EAAE7E,KAAK,CAACQ,CAAC,GAAGF,OAAO,CAAC6G,OAAO;IAClCrC,OAAO,EAAE9E,KAAK,CAACU,CAAC,GAAGJ,OAAO,CAAC8G,OAAO;IAClCtD,MAAM,EAAExD,OAAO,CAACwD,MAAM;IACtBkC,KAAK,EAAEW,IAAI;IACXV,MAAM,EAAEU;EACV,CAAC;AACH;AAEA,SAASgM,wBAAwBA,CAACvB,KAAK,EAAE9Q,OAAO,EAAE;EAChD,MAAMiJ,MAAM,GAAGwI,mBAAmB,CAACX,KAAK,EAAE9Q,OAAO,CAAC,IAAIA,OAAO;EAC7D,OAAO;IACLgD,KAAK,EAAElB,IAAI,CAACY,GAAG,CAACuG,MAAM,CAACjG,KAAK,EAAEiG,MAAM,CAAChG,GAAG,CAAC;IACzCA,GAAG,EAAEnB,IAAI,CAACa,GAAG,CAACsG,MAAM,CAACjG,KAAK,EAAEiG,MAAM,CAAChG,GAAG;EACxC,CAAC;AACH;AAEA,SAASgQ,yBAAyBA,CAACnC,KAAK,EAAEkC,IAAI,EAAEhT,OAAO,EAAE;EACvD,MAAM0C,GAAG,GAAGmO,UAAU,CAACC,KAAK,EAAE9Q,OAAO,CAAC+C,KAAK,EAAEyQ,GAAG,CAAC;EACjD,MAAM7Q,GAAG,GAAGkO,UAAU,CAACC,KAAK,EAAE9Q,OAAO,CAACyT,QAAQ,EAAE/Q,GAAG,CAAC;EACpD,IAAIoO,KAAK,CAAC4C,YAAY,CAAC,CAAC,EAAE;IACxBV,IAAI,CAAC9S,CAAC,GAAGwC,GAAG;IACZsQ,IAAI,CAACrP,EAAE,GAAGhB,GAAG;EACf,CAAC,MAAM;IACLqQ,IAAI,CAAC5S,CAAC,GAAGsC,GAAG;IACZsQ,IAAI,CAACpP,EAAE,GAAGjB,GAAG;EACf;AACF;AAEA,SAASuQ,4BAA4BA,CAAC/B,MAAM,EAAE6B,IAAI,EAAEhT,OAAO,EAAE;EAC3D,KAAK,MAAM2T,OAAO,IAAItQ,MAAM,CAACC,IAAI,CAACkN,gBAAgB,CAAC,EAAE;IACnD,MAAMM,KAAK,GAAGK,MAAM,CAACD,eAAe,CAACC,MAAM,EAAEnR,OAAO,EAAE2T,OAAO,CAAC,CAAC;IAC/D,IAAI7C,KAAK,EAAE;MACT,MAAM;QAACpO,GAAG;QAAEC,GAAG;QAAEK,KAAK;QAAEC,GAAG;QAAEyN,SAAS;QAAEC;MAAO,CAAC,GAAGH,gBAAgB,CAACmD,OAAO,CAAC;MAC5E,MAAMC,GAAG,GAAGnC,mBAAmB,CAACX,KAAK,EAAE;QAACpO,GAAG,EAAE1C,OAAO,CAAC0C,GAAG,CAAC;QAAEC,GAAG,EAAE3C,OAAO,CAAC2C,GAAG,CAAC;QAAEK,KAAK,EAAE8N,KAAK,CAAC9N,KAAK,CAAC;QAAEC,GAAG,EAAE6N,KAAK,CAAC7N,GAAG;MAAC,CAAC,CAAC;MACpH+P,IAAI,CAACtC,SAAS,CAAC,GAAGkD,GAAG,CAAC5Q,KAAK;MAC3BgQ,IAAI,CAACrC,OAAO,CAAC,GAAGiD,GAAG,CAAC3Q,GAAG;IACzB;EACF;AACF;AAEA,SAAS4Q,UAAUA,CAAC;EAAC/N,UAAU;EAAE9F;AAAO,CAAC,EAAE4G,SAAS,EAAEN,QAAQ,EAAES,OAAO,EAAE;EACvE,MAAM;IAAC7G,CAAC,EAAE8C,KAAK;IAAEW,EAAE,EAAEV,GAAG;IAAEyC,KAAK,EAAEW;EAAI,CAAC,GAAGP,UAAU;EACnD,OAAOgO,iBAAiB,CAAC;IAAC9Q,KAAK;IAAEC,GAAG;IAAEoD,IAAI;IAAExC,WAAW,EAAE7D,OAAO,CAAC6D;EAAW,CAAC,EAAE;IAC7EyC,QAAQ,EAAEA,QAAQ,CAACpG,CAAC;IACpB6G,OAAO,EAAE;MAAC/D,KAAK,EAAE+D,OAAO,CAACgL,IAAI;MAAE9O,GAAG,EAAE8D,OAAO,CAACmL;IAAK,CAAC;IAClD/J,MAAM,EAAEnI,OAAO,CAACiG,KAAK,CAACY,OAAO;IAC7BR,IAAI,EAAEO,SAAS,CAAClB;EAClB,CAAC,CAAC;AACJ;AAEA,SAASqO,UAAUA,CAAC;EAACjO,UAAU;EAAE9F;AAAO,CAAC,EAAE4G,SAAS,EAAEN,QAAQ,EAAES,OAAO,EAAE;EACvE,MAAM;IAAC3G,CAAC,EAAE4C,KAAK;IAAEY,EAAE,EAAEX,GAAG;IAAE0C,MAAM,EAAEU;EAAI,CAAC,GAAGP,UAAU;EACpD,OAAOgO,iBAAiB,CAAC;IAAC9Q,KAAK;IAAEC,GAAG;IAAEoD,IAAI;IAAExC,WAAW,EAAE7D,OAAO,CAAC6D;EAAW,CAAC,EAAE;IAC7EyC,QAAQ,EAAEA,QAAQ,CAAClG,CAAC;IACpB2G,OAAO,EAAE;MAAC/D,KAAK,EAAE+D,OAAO,CAACiL,GAAG;MAAE/O,GAAG,EAAE8D,OAAO,CAACoL;IAAM,CAAC;IAClDhK,MAAM,EAAEnI,OAAO,CAACiG,KAAK,CAACa,OAAO;IAC7BT,IAAI,EAAEO,SAAS,CAACjB;EAClB,CAAC,CAAC;AACJ;AAEA,SAASmO,iBAAiBA,CAACE,OAAO,EAAEC,SAAS,EAAE;EAC7C,MAAM;IAACjR,KAAK;IAAEC,GAAG;IAAEY;EAAW,CAAC,GAAGmQ,OAAO;EACzC,MAAM;IAAC1N,QAAQ;IAAES,OAAO,EAAE;MAAC/D,KAAK,EAAEkR,QAAQ;MAAEjR,GAAG,EAAEkR;IAAM,CAAC;IAAEhM;EAAM,CAAC,GAAG8L,SAAS;EAC7E,MAAMG,aAAa,GAAGnR,GAAG,GAAGY,WAAW,GAAGb,KAAK,GAAGkR,QAAQ,GAAGC,MAAM,GAAGF,SAAS,CAAC5N,IAAI;EACpF,OAAOrD,KAAK,GAAGa,WAAW,GAAG,CAAC,GAAGsE,MAAM,GAAG/B,mBAAmB,CAACgO,aAAa,EAAE9N,QAAQ,CAAC;AACxF;AAEA,SAASiN,+BAA+BA,CAAClL,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,EAAE;EACnE,MAAMiG,KAAK,GAAGjG,OAAO,CAACiG,KAAK;EAC3BA,KAAK,CAACgG,eAAe,GAAG,aAAa;EACrChG,KAAK,CAACkJ,OAAO,CAACE,OAAO,GAAG,KAAK;EAC7B,MAAM/I,QAAQ,GAAGY,UAAU,CAACjB,KAAK,CAACK,QAAQ,CAAC;EAC3C,MAAMS,OAAO,GAAG7H,SAAS,CAAC+G,KAAK,CAACc,OAAO,CAAC;EACxC,MAAMH,SAAS,GAAGsE,gBAAgB,CAAC7C,KAAK,CAACyB,GAAG,EAAE7D,KAAK,CAAC;EACpD,MAAM/F,CAAC,GAAG2T,UAAU,CAAC;IAAC/N,UAAU;IAAE9F;EAAO,CAAC,EAAE4G,SAAS,EAAEN,QAAQ,EAAES,OAAO,CAAC;EACzE,MAAM3G,CAAC,GAAG2T,UAAU,CAAC;IAACjO,UAAU;IAAE9F;EAAO,CAAC,EAAE4G,SAAS,EAAEN,QAAQ,EAAES,OAAO,CAAC;EACzE,MAAMrB,KAAK,GAAGkB,SAAS,CAAClB,KAAK,GAAGqB,OAAO,CAACrB,KAAK;EAC7C,MAAMC,MAAM,GAAGiB,SAAS,CAACjB,MAAM,GAAGoB,OAAO,CAACpB,MAAM;EAChD,OAAO;IACLzF,CAAC;IACDE,CAAC;IACDuD,EAAE,EAAEzD,CAAC,GAAGwF,KAAK;IACb9B,EAAE,EAAExD,CAAC,GAAGuF,MAAM;IACdD,KAAK;IACLC,MAAM;IACNpB,OAAO,EAAErE,CAAC,GAAGwF,KAAK,GAAG,CAAC;IACtBlB,OAAO,EAAEpE,CAAC,GAAGuF,MAAM,GAAG,CAAC;IACvBxB,QAAQ,EAAE8B,KAAK,CAAC9B;EAClB,CAAC;AAEH;AAEA,MAAMkQ,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;;AAEpC;AACA;AACA;AACA;;AAEA,MAAMC,UAAU,GAAGD,SAAS,CAACE,MAAM,CAAC,OAAO,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACnM,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,EAAE;EAC9CyU,KAAK,CAACC,QAAQ,GAAGhM,SAAS,CAAC1I,OAAO,EAAEsU,UAAU,EAAEG,KAAK,CAACE,SAAS,CAAC;EAChEF,KAAK,CAACG,YAAY,GAAG,KAAK;EAE1BP,SAAS,CAACvL,OAAO,CAACC,IAAI,IAAI;IACxB,IAAI1K,UAAU,CAAC2B,OAAO,CAAC+I,IAAI,CAAC,CAAC,EAAE;MAC7B0L,KAAK,CAACG,YAAY,GAAG,IAAI;IAC3B;EACF,CAAC,CAAC;EAEF,IAAI,CAACH,KAAK,CAACC,QAAQ,IAAI,CAACD,KAAK,CAACG,YAAY,EAAE;IAC1CH,KAAK,CAACI,WAAW,CAAC/L,OAAO,CAACgM,KAAK,IAAI;MACjC,IAAI,CAACL,KAAK,CAACC,QAAQ,IAAIrW,UAAU,CAACyW,KAAK,CAACC,KAAK,CAAC,EAAE;QAC9CN,KAAK,CAACC,QAAQ,GAAG,IAAI;MACvB;MACA,IAAI,CAACD,KAAK,CAACG,YAAY,EAAE;QACvBP,SAAS,CAACvL,OAAO,CAACC,IAAI,IAAI;UACxB,IAAI1K,UAAU,CAACyW,KAAK,CAAC/L,IAAI,CAAC,CAAC,EAAE;YAC3B0L,KAAK,CAACC,QAAQ,GAAG,IAAI;YACrBD,KAAK,CAACG,YAAY,GAAG,IAAI;UAC3B;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,WAAWA,CAACP,KAAK,EAAE7U,KAAK,EAAEI,OAAO,EAAE;EAC1C,IAAIyU,KAAK,CAACC,QAAQ,EAAE;IAClB,QAAQ9U,KAAK,CAACoJ,IAAI;MAClB,KAAK,WAAW;MAChB,KAAK,UAAU;QACb,OAAOiM,gBAAgB,CAACR,KAAK,EAAE7U,KAAK,EAAEI,OAAO,CAAC;MAChD,KAAK,OAAO;QACV,OAAOkV,iBAAiB,CAACT,KAAK,EAAE7U,KAAK,EAAEI,OAAO,CAAC;IACjD;EACF;AACF;AAEA,SAASiV,gBAAgBA,CAACR,KAAK,EAAE7U,KAAK,EAAEI,OAAO,EAAE;EAC/C,IAAI,CAACyU,KAAK,CAACG,YAAY,EAAE;IACvB;EACF;EAEA,IAAIvB,QAAQ;EAEZ,IAAIzT,KAAK,CAACoJ,IAAI,KAAK,WAAW,EAAE;IAC9BqK,QAAQ,GAAGhT,WAAW,CAACoU,KAAK,CAAC9U,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAACR,WAAW,CAAC;EAC3E,CAAC,MAAM;IACL6T,QAAQ,GAAG,EAAE;EACf;EAEA,MAAM8B,QAAQ,GAAGV,KAAK,CAACW,OAAO;EAC9BX,KAAK,CAACW,OAAO,GAAG/B,QAAQ;EAExB,MAAMgC,OAAO,GAAG;IAACZ,KAAK;IAAE7U;EAAK,CAAC;EAC9B,IAAI0V,OAAO,GAAGC,kBAAkB,CAACF,OAAO,EAAE,OAAO,EAAEF,QAAQ,EAAE9B,QAAQ,CAAC;EACtE,OAAOkC,kBAAkB,CAACF,OAAO,EAAE,OAAO,EAAEhC,QAAQ,EAAE8B,QAAQ,CAAC,IAAIG,OAAO;AAC5E;AAEA,SAASC,kBAAkBA,CAAC;EAACd,KAAK;EAAE7U;AAAK,CAAC,EAAEmJ,IAAI,EAAEsK,QAAQ,EAAEmC,aAAa,EAAE;EACzE,IAAIF,OAAO;EACX,KAAK,MAAM9U,OAAO,IAAI6S,QAAQ,EAAE;IAC9B,IAAImC,aAAa,CAACC,OAAO,CAACjV,OAAO,CAAC,GAAG,CAAC,EAAE;MACtC8U,OAAO,GAAGI,aAAa,CAAClV,OAAO,CAACR,OAAO,CAAC+I,IAAI,CAAC,IAAI0L,KAAK,CAACE,SAAS,CAAC5L,IAAI,CAAC,EAAEvI,OAAO,EAAEZ,KAAK,CAAC,IAAI0V,OAAO;IACpG;EACF;EACA,OAAOA,OAAO;AAChB;AAEA,SAASJ,iBAAiBA,CAACT,KAAK,EAAE7U,KAAK,EAAEI,OAAO,EAAE;EAChD,MAAM2U,SAAS,GAAGF,KAAK,CAACE,SAAS;EACjC,MAAMtB,QAAQ,GAAGhT,WAAW,CAACoU,KAAK,CAAC9U,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAACR,WAAW,CAAC;EAC/E,IAAI8V,OAAO;EACX,KAAK,MAAM9U,OAAO,IAAI6S,QAAQ,EAAE;IAC9BiC,OAAO,GAAGI,aAAa,CAAClV,OAAO,CAACR,OAAO,CAAC+U,KAAK,IAAIJ,SAAS,CAACI,KAAK,EAAEvU,OAAO,EAAEZ,KAAK,CAAC,IAAI0V,OAAO;EAC9F;EACA,OAAOA,OAAO;AAChB;AAEA,SAASI,aAAaA,CAACC,OAAO,EAAEnV,OAAO,EAAEZ,KAAK,EAAE;EAC9C,OAAOtB,QAAQ,CAACqX,OAAO,EAAE,CAACnV,OAAO,CAACoV,QAAQ,EAAEhW,KAAK,CAAC,CAAC,KAAK,IAAI;AAC9D;;AAEA;AACA;AACA;AACA;AACA;;AAEA,MAAMiW,YAAY,GAAG,CAAC,WAAW,EAAE,YAAY,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACzN,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,EAAE;EAC1C,MAAML,eAAe,GAAG8U,KAAK,CAAC9U,eAAe;EAC7C8U,KAAK,CAACsB,MAAM,GAAGrN,SAAS,CAAC1I,OAAO,EAAE6V,YAAY,EAAEpB,KAAK,CAAC9L,KAAK,CAAC;EAE5D,IAAI,CAAC8L,KAAK,CAACsB,MAAM,EAAE;IACjBpW,eAAe,CAACmJ,OAAO,CAACgM,KAAK,IAAI;MAC/B,IAAI,CAACL,KAAK,CAACsB,MAAM,EAAE;QACjBF,YAAY,CAAC/M,OAAO,CAACC,IAAI,IAAI;UAC3B,IAAI1K,UAAU,CAACyW,KAAK,CAAC9U,OAAO,CAAC+I,IAAI,CAAC,CAAC,EAAE;YACnC0L,KAAK,CAACsB,MAAM,GAAG,IAAI;UACrB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACvB,KAAK,EAAEjU,OAAO,EAAEuI,IAAI,EAAE;EACxC,IAAI0L,KAAK,CAACsB,MAAM,EAAE;IAChB,MAAME,YAAY,GAAGzV,OAAO,CAACR,OAAO,CAAC+I,IAAI,CAAC,IAAI0L,KAAK,CAAC9L,KAAK,CAACI,IAAI,CAAC;IAC/D,OAAOzK,QAAQ,CAAC2X,YAAY,EAAE,CAACzV,OAAO,CAACoV,QAAQ,CAAC,CAAC;EACnD;AACF;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAAC7N,KAAK,EAAEyI,KAAK,EAAE+D,WAAW,EAAE;EACnD,MAAMsB,KAAK,GAAGC,cAAc,CAAC/N,KAAK,CAAC8I,MAAM,EAAEL,KAAK,EAAE+D,WAAW,CAAC;EAC9D,IAAIS,OAAO,GAAGe,gBAAgB,CAACvF,KAAK,EAAEqF,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC;EACnEb,OAAO,GAAGe,gBAAgB,CAACvF,KAAK,EAAEqF,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC,IAAIb,OAAO;EAC1E,IAAIA,OAAO,IAAIjX,UAAU,CAACyS,KAAK,CAACwF,sBAAsB,CAAC,EAAE;IACvDxF,KAAK,CAACwF,sBAAsB,CAAC,CAAC;EAChC;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAC1B,WAAW,EAAE1D,MAAM,EAAE;EAC/C,KAAK,MAAMqF,UAAU,IAAI3B,WAAW,EAAE;IACpC4B,cAAc,CAACD,UAAU,EAAErF,MAAM,CAAC;EACpC;AACF;AAEA,SAASkF,gBAAgBA,CAACvF,KAAK,EAAEqF,KAAK,EAAEtT,KAAK,EAAE6T,cAAc,EAAE;EAC7D,IAAIvX,QAAQ,CAACgX,KAAK,CAACtT,KAAK,CAAC,CAAC,IAAI,CAAC8T,iBAAiB,CAAC7F,KAAK,CAAC9Q,OAAO,EAAE6C,KAAK,EAAE6T,cAAc,CAAC,EAAE;IACtF,MAAMpB,OAAO,GAAGxE,KAAK,CAACjO,KAAK,CAAC,KAAKsT,KAAK,CAACtT,KAAK,CAAC;IAC7CiO,KAAK,CAACjO,KAAK,CAAC,GAAGsT,KAAK,CAACtT,KAAK,CAAC;IAC3B,OAAOyS,OAAO;EAChB;AACF;AAEA,SAASqB,iBAAiBA,CAACC,YAAY,EAAE/T,KAAK,EAAE6T,cAAc,EAAE;EAC9D,OAAOtY,OAAO,CAACwY,YAAY,CAAC/T,KAAK,CAAC,CAAC,IAAIzE,OAAO,CAACwY,YAAY,CAACF,cAAc,CAAC,CAAC;AAC9E;AAEA,SAASD,cAAcA,CAACD,UAAU,EAAErF,MAAM,EAAE;EAC1C,KAAK,MAAM/N,GAAG,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE;IACrD,MAAMgO,OAAO,GAAGF,eAAe,CAACC,MAAM,EAAEqF,UAAU,EAAEpT,GAAG,CAAC;IACxD,IAAIgO,OAAO,IAAI,CAACD,MAAM,CAACC,OAAO,CAAC,IAAIyF,gBAAgB,CAACL,UAAU,EAAEpT,GAAG,CAAC,EAAE;MACpE0T,OAAO,CAACC,IAAI,CAAC,2BAA2B3F,OAAO,qBAAqBoF,UAAU,CAAChF,EAAE,GAAG,CAAC;IACvF;EACF;AACF;AAEA,SAASqF,gBAAgBA,CAACL,UAAU,EAAEpT,GAAG,EAAE;EACzC,IAAIA,GAAG,KAAK,SAAS,EAAE;IACrB,OAAO,IAAI;EACb;EACA,MAAMjD,IAAI,GAAGiD,GAAG,CAACiO,MAAM,CAAC,CAAC,CAAC;EAC1B,KAAK,MAAM2F,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE;IAC1C,IAAI5Y,OAAO,CAACoY,UAAU,CAACrW,IAAI,GAAG6W,IAAI,CAAC,CAAC,EAAE;MACpC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEA,SAASZ,cAAcA,CAACjF,MAAM,EAAEL,KAAK,EAAE+D,WAAW,EAAE;EAClD,MAAM1U,IAAI,GAAG2Q,KAAK,CAAC3Q,IAAI;EACvB,MAAMiR,OAAO,GAAGN,KAAK,CAACU,EAAE;EACxB,MAAMyF,aAAa,GAAG9W,IAAI,GAAG,SAAS;EACtC,MAAM+W,MAAM,GAAG;IACbxU,GAAG,EAAEvE,cAAc,CAAC2S,KAAK,CAACpO,GAAG,EAAE5B,MAAM,CAACqW,iBAAiB,CAAC;IACxDxU,GAAG,EAAExE,cAAc,CAAC2S,KAAK,CAACnO,GAAG,EAAE7B,MAAM,CAACC,iBAAiB;EACzD,CAAC;EACD,KAAK,MAAMyV,UAAU,IAAI3B,WAAW,EAAE;IACpC,IAAI2B,UAAU,CAACpF,OAAO,KAAKA,OAAO,EAAE;MAClCgG,YAAY,CAACZ,UAAU,EAAE1F,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,EAAEoG,MAAM,CAAC;IAChE,CAAC,MAAM,IAAIhG,eAAe,CAACC,MAAM,EAAEqF,UAAU,EAAES,aAAa,CAAC,KAAK7F,OAAO,EAAE;MACzEgG,YAAY,CAACZ,UAAU,EAAE1F,KAAK,EAAE,CAAC3Q,IAAI,GAAG,KAAK,EAAEA,IAAI,GAAG,KAAK,EAAEA,IAAI,GAAG,OAAO,CAAC,EAAE+W,MAAM,CAAC;IACvF;EACF;EACA,OAAOA,MAAM;AACf;AAEA,SAASE,YAAYA,CAACZ,UAAU,EAAE1F,KAAK,EAAEuG,KAAK,EAAEH,MAAM,EAAE;EACtD,KAAK,MAAMF,IAAI,IAAIK,KAAK,EAAE;IACxB,MAAMC,GAAG,GAAGd,UAAU,CAACQ,IAAI,CAAC;IAC5B,IAAI5Y,OAAO,CAACkZ,GAAG,CAAC,EAAE;MAChB,MAAMvU,KAAK,GAAG+N,KAAK,CAACE,KAAK,CAACsG,GAAG,CAAC;MAC9BJ,MAAM,CAACxU,GAAG,GAAGZ,IAAI,CAACY,GAAG,CAACwU,MAAM,CAACxU,GAAG,EAAEK,KAAK,CAAC;MACxCmU,MAAM,CAACvU,GAAG,GAAGb,IAAI,CAACa,GAAG,CAACuU,MAAM,CAACvU,GAAG,EAAEI,KAAK,CAAC;IAC1C;EACF;AACF;AAEA,MAAMwU,aAAa,SAAS5Z,OAAO,CAAC;EAElC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,MAAM;MAACpE,CAAC;MAAEE;IAAC,CAAC,GAAGuB,OAAO,CAAC;MAACzB,CAAC,EAAEsX,MAAM;MAAEpX,CAAC,EAAEqX;IAAM,CAAC,EAAE,IAAI,CAACvW,cAAc,CAACoD,gBAAgB,CAAC,EAAErG,SAAS,CAAC,CAAC,IAAI,CAAC+B,OAAO,CAACmE,QAAQ,CAAC,CAAC;IACxH,OAAOT,UAAU,CAAC;MAACxD,CAAC;MAAEE;IAAC,CAAC,EAAE,IAAI,CAACqE,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC,EAAEnE,IAAI,EAAE,IAAI,CAACH,OAAO,CAAC;EACxG;EAEAkB,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACRA,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVjC,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC5I,cAAc,CAAC,CAAC,EAAE,IAAI,CAAClB,OAAO,CAACmE,QAAQ,CAAC;IAC5D0H,OAAO,CAAC/B,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC9J,OAAO,CAAC;IAChC8J,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEA,IAAIxG,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoN,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;EAC1C;EAEAsE,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,OAAOmT,4BAA4B,CAAC9K,KAAK,EAAErI,OAAO,CAAC;EACrD;AACF;AAEAuX,aAAa,CAAC/F,EAAE,GAAG,eAAe;AAElC+F,aAAa,CAAC1Z,QAAQ,GAAG;EACvBqY,gBAAgB,EAAE,IAAI;EACtBpL,qBAAqB,EAAE,aAAa;EACpCZ,cAAc,EAAE,MAAM;EACtBE,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBE,eAAe,EAAE,OAAO;EACxB6B,YAAY,EAAE,CAAC;EACfG,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdwL,OAAO,EAAE,IAAI;EACb9G,IAAI,EAAEqP,SAAS;EACf9T,YAAY,EAAE,CAAC;EACfmC,KAAK,EAAE;IACLgG,eAAe,EAAE,aAAa;IAC9BpI,WAAW,EAAE,CAAC;IACdsL,OAAO,EAAE;MACPE,OAAO,EAAE;IACX,CAAC;IACDnC,KAAK,EAAE,OAAO;IACdvD,OAAO,EAAE,IAAI;IACb0F,OAAO,EAAE,KAAK;IACdwI,QAAQ,EAAED,SAAS;IACnBlQ,IAAI,EAAE;MACJoQ,MAAM,EAAEF,SAAS;MACjB7P,UAAU,EAAE6P,SAAS;MACrBvR,IAAI,EAAEuR,SAAS;MACf9K,KAAK,EAAE8K,SAAS;MAChBG,MAAM,EAAE;IACV,CAAC;IACDpS,MAAM,EAAEiS,SAAS;IACjB9T,YAAY,EAAE8T,SAAS;IACvB/K,OAAO,EAAE+K,SAAS;IAClB7Q,OAAO,EAAE,CAAC;IACVT,QAAQ,EAAE,QAAQ;IAClBnC,QAAQ,EAAEyT,SAAS;IACnBlR,SAAS,EAAE,OAAO;IAClB+G,eAAe,EAAEmK,SAAS;IAC1BxM,eAAe,EAAE,CAAC;IAClB1F,KAAK,EAAEkS,SAAS;IAChB/Q,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVkR,CAAC,EAAEJ;EACL,CAAC;EACDzT,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBsH,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnBlF,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnBI,CAAC,EAAE;AACL,CAAC;AAEDT,aAAa,CAACU,aAAa,GAAG;EAC5BtN,WAAW,EAAE,OAAO;EACpBsB,eAAe,EAAE;AACnB,CAAC;AAEDsL,aAAa,CAACW,WAAW,GAAG;EAC1BjS,KAAK,EAAE;IACLkS,SAAS,EAAE;EACb;AACF,CAAC;AAED,MAAMC,uBAAuB,SAASza,OAAO,CAAC;EAE5C8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,OAAOL,YAAY,CACjB;MAAC/D,CAAC,EAAEsX,MAAM;MAAEpX,CAAC,EAAEqX;IAAM,CAAC,EACtB;MAACvT,IAAI,EAAE,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;MAAE3D,MAAM,EAAE,IAAI,CAACO,cAAc,CAACoD,gBAAgB;IAAC,CAAC,EAC9GnE,IAAI,EACJ;MAACgE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEN,WAAW,EAAE,CAAC;MAAEC,YAAY,EAAE,IAAI,CAAC9D,OAAO,CAAC8D;IAAY,CACnF,CAAC;EACH;EAEA5C,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM9J,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,CAACqP,OAAO,IAAI,CAACrP,OAAO,CAAC2J,OAAO,EAAE;MACxC;IACF;IACA0O,cAAc,CAACvO,GAAG,EAAE,IAAI,CAAC;IACzBA,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVjC,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC5I,cAAc,CAAC,CAAC,EAAE,IAAI,CAACiD,QAAQ,CAAC;IACpDuI,SAAS,CAAC5C,GAAG,EAAE,IAAI,EAAE9J,OAAO,EAAE,IAAI,CAACsY,SAAS,CAAC;IAC7CxO,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEAkL,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,MAAMuY,IAAI,GAAGC,cAAc,CAACnQ,KAAK,EAAErI,OAAO,CAAC;IAC3C,IAAI,CAACuY,IAAI,EAAE;MACT,OAAO,CAAC,CAAC;IACX;IACA,MAAM;MAACE,cAAc;MAAE/Y,KAAK;MAAE8D;IAAM,CAAC,GAAGkV,iBAAiB,CAACrQ,KAAK,EAAErI,OAAO,EAAEuY,IAAI,CAAC;IAC/E,IAAI3R,SAAS,GAAGsE,gBAAgB,CAAC7C,KAAK,CAACyB,GAAG,EAAE9J,OAAO,CAAC;IACpD,MAAMsY,SAAS,GAAGK,WAAW,CAAC/R,SAAS,EAAEpD,MAAM,CAAC;IAChD,IAAI6D,SAAS,CAACrH,OAAO,EAAEsY,SAAS,CAAC,EAAE;MACjC1R,SAAS,GAAG;QAAClB,KAAK,EAAEkB,SAAS,CAAClB,KAAK,GAAG4S,SAAS;QAAE3S,MAAM,EAAEiB,SAAS,CAACjB,MAAM,GAAG2S;MAAS,CAAC;IACxF;IACA,MAAM;MAAChS,QAAQ;MAAEO,OAAO;MAAEC;IAAO,CAAC,GAAG9G,OAAO;IAC5C,MAAM4Y,OAAO,GAAGjS,qBAAqB,CAACjH,KAAK,EAAEkH,SAAS,EAAE;MAAC/C,WAAW,EAAE,CAAC;MAAEyC,QAAQ;MAAEO,OAAO;MAAEC;IAAO,CAAC,CAAC;IACrG,OAAO;MACLsM,cAAc,EAAEhL,uBAAuB,CAACC,KAAK,EAAEuQ,OAAO,EAAE5Y,OAAO,CAAC;MAChE,GAAG4Y,OAAO;MACV,GAAGH,cAAc;MACjBtU,QAAQ,EAAEnE,OAAO,CAACmE,QAAQ;MAC1BmU;IACF,CAAC;EACH;AACF;AAEAF,uBAAuB,CAAC5G,EAAE,GAAG,yBAAyB;AAEtD4G,uBAAuB,CAACva,QAAQ,GAAG;EACjC0J,OAAO,EAAE,IAAI;EACbsR,QAAQ,EAAE,IAAI;EACd5M,eAAe,EAAE,aAAa;EAC9BnB,qBAAqB,EAAE,aAAa;EACpCH,WAAW,EAAE,aAAa;EAC1BP,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBE,eAAe,EAAE,OAAO;EACxBgC,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdqJ,KAAK,EAAE,OAAO;EACdvD,OAAO,EAAE,IAAI;EACb0F,OAAO,EAAE,IAAI;EACb3H,IAAI,EAAE;IACJoQ,MAAM,EAAEF,SAAS;IACjB7P,UAAU,EAAE6P,SAAS;IACrBvR,IAAI,EAAEuR,SAAS;IACf9K,KAAK,EAAE8K,SAAS;IAChBG,MAAM,EAAEH;EACV,CAAC;EACDjS,MAAM,EAAEiS,SAAS;EACjB9T,YAAY,EAAE,CAAC;EACfyE,IAAI,EAAEqP,SAAS;EACf/K,OAAO,EAAE+K,SAAS;EAClBtR,QAAQ,EAAE,QAAQ;EAClBnC,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChB6N,OAAO,EAAE,CAAC;EACVpS,SAAS,EAAE,QAAQ;EACnB+G,eAAe,EAAEmK,SAAS;EAC1BxM,eAAe,EAAE,CAAC;EAClB1F,KAAK,EAAEkS,SAAS;EAChB/Q,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AAEDsR,uBAAuB,CAACH,aAAa,GAAG,CACxC,CAAC;AAED,SAASO,cAAcA,CAACnQ,KAAK,EAAErI,OAAO,EAAE;EACtC,OAAOqI,KAAK,CAAC0Q,4BAA4B,CAAC,CAAC,CAAC/X,MAAM,CAAC,UAASiI,MAAM,EAAElG,KAAK,EAAE;IACzE,MAAMiW,UAAU,GAAGjW,KAAK,CAACiW,UAAU;IACnC,IAAIA,UAAU,YAAYpb,kBAAkB,IAC1Cqb,mBAAmB,CAAC5Q,KAAK,EAAErI,OAAO,EAAE+C,KAAK,CAACmW,IAAI,CAAC,KAC9C,CAACjQ,MAAM,IAAI+P,UAAU,CAACG,WAAW,GAAGlQ,MAAM,CAAC+P,UAAU,CAACG,WAAW,CAAC,IACnEH,UAAU,CAAChZ,OAAO,CAACoZ,aAAa,IAAI,EAAE,EAAE;MACxC,OAAOrW,KAAK;IACd;IACA,OAAOkG,MAAM;EACf,CAAC,EAAE2O,SAAS,CAAC;AACf;AAEA,SAASqB,mBAAmBA,CAAC5Q,KAAK,EAAErI,OAAO,EAAEqT,QAAQ,EAAE;EACrD,IAAI,CAACrT,OAAO,CAAC6Y,QAAQ,EAAE;IACrB,OAAO,IAAI;EACb;EACA,KAAK,IAAI7T,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,QAAQ,CAAChR,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACxC,IAAI,CAACqO,QAAQ,CAACrO,CAAC,CAAC,CAACqU,MAAM,IAAIhR,KAAK,CAACiR,iBAAiB,CAACtU,CAAC,CAAC,EAAE;MACrD,OAAO,IAAI;IACb;EACF;AACF;AAEA,SAAS0T,iBAAiBA,CAAC;EAAC9G;AAAS,CAAC,EAAE5R,OAAO,EAAEuY,IAAI,EAAE;EACrD,MAAM;IAACxG,IAAI;IAAEC,GAAG;IAAEE,KAAK;IAAEC;EAAM,CAAC,GAAGP,SAAS;EAC5C,MAAM;IAACuH,WAAW;IAAEI,OAAO;IAAEC;EAAO,CAAC,GAAGjB,IAAI,CAACS,UAAU;EACvD,MAAM9Y,CAAC,GAAG,CAAC6R,IAAI,GAAGG,KAAK,IAAI,CAAC,GAAGqH,OAAO;EACtC,MAAMnZ,CAAC,GAAG,CAAC4R,GAAG,GAAGG,MAAM,IAAI,CAAC,GAAGqH,OAAO;EACtC,MAAMC,MAAM,GAAG;IACb1H,IAAI,EAAEjQ,IAAI,CAACa,GAAG,CAACzC,CAAC,GAAGiZ,WAAW,EAAEpH,IAAI,CAAC;IACrCG,KAAK,EAAEpQ,IAAI,CAACY,GAAG,CAACxC,CAAC,GAAGiZ,WAAW,EAAEjH,KAAK,CAAC;IACvCF,GAAG,EAAElQ,IAAI,CAACa,GAAG,CAACvC,CAAC,GAAG+Y,WAAW,EAAEnH,GAAG,CAAC;IACnCG,MAAM,EAAErQ,IAAI,CAACY,GAAG,CAACtC,CAAC,GAAG+Y,WAAW,EAAEhH,MAAM;EAC1C,CAAC;EACD,MAAMzS,KAAK,GAAG;IACZQ,CAAC,EAAE,CAACuZ,MAAM,CAAC1H,IAAI,GAAG0H,MAAM,CAACvH,KAAK,IAAI,CAAC;IACnC9R,CAAC,EAAE,CAACqZ,MAAM,CAACzH,GAAG,GAAGyH,MAAM,CAACtH,MAAM,IAAI;EACpC,CAAC;EACD,MAAMuH,KAAK,GAAG1Z,OAAO,CAAC8Y,OAAO,GAAG9Y,OAAO,CAAC6D,WAAW,GAAG,CAAC;EACvD,MAAM8V,OAAO,GAAGR,WAAW,GAAGO,KAAK;EACnC,MAAME,iBAAiB,GAAGla,KAAK,CAACU,CAAC,GAAGA,CAAC;EACrC,MAAM6P,IAAI,GAAG2J,iBAAiB,GAAG5H,GAAG,GAAG0H,KAAK,GAAGvH,MAAM,GAAGuH,KAAK;EAC7D,MAAMG,MAAM,GAAGC,SAAS,CAAC7J,IAAI,EAAE/P,CAAC,EAAEE,CAAC,EAAEuZ,OAAO,CAAC;EAC7C,MAAMlB,cAAc,GAAG;IACrBsB,QAAQ,EAAE7Z,CAAC;IACX8Z,QAAQ,EAAE5Z,CAAC;IACXuZ,OAAO;IACPC,iBAAiB;IACjB,GAAGC;EACL,CAAC;EACD,OAAO;IACLpB,cAAc;IACd/Y,KAAK;IACL8D,MAAM,EAAE1B,IAAI,CAACY,GAAG,CAACyW,WAAW,EAAErX,IAAI,CAACY,GAAG,CAAC+W,MAAM,CAACvH,KAAK,GAAGuH,MAAM,CAAC1H,IAAI,EAAE0H,MAAM,CAACtH,MAAM,GAAGsH,MAAM,CAACzH,GAAG,CAAC,GAAG,CAAC;EACpG,CAAC;AACH;AAEA,SAAS2G,WAAWA,CAAC;EAACjT,KAAK;EAAEC;AAAM,CAAC,EAAEnC,MAAM,EAAE;EAC5C,MAAMyW,IAAI,GAAGnY,IAAI,CAACoY,IAAI,CAACpY,IAAI,CAAC2B,GAAG,CAACiC,KAAK,EAAE,CAAC,CAAC,GAAG5D,IAAI,CAAC2B,GAAG,CAACkC,MAAM,EAAE,CAAC,CAAC,CAAC;EAChE,OAAQnC,MAAM,GAAG,CAAC,GAAIyW,IAAI;AAC5B;AAEA,SAASH,SAASA,CAAC1Z,CAAC,EAAEmE,OAAO,EAAEC,OAAO,EAAEhB,MAAM,EAAE;EAC9C,MAAM2W,GAAG,GAAGrY,IAAI,CAAC2B,GAAG,CAACe,OAAO,GAAGpE,CAAC,EAAE,CAAC,CAAC;EACpC,MAAMga,EAAE,GAAGtY,IAAI,CAAC2B,GAAG,CAACD,MAAM,EAAE,CAAC,CAAC;EAC9B,MAAMhC,CAAC,GAAG+C,OAAO,GAAG,CAAC,CAAC;EACtB,MAAMqK,CAAC,GAAG9M,IAAI,CAAC2B,GAAG,CAACc,OAAO,EAAE,CAAC,CAAC,GAAG4V,GAAG,GAAGC,EAAE;EACzC,MAAMC,KAAK,GAAGvY,IAAI,CAAC2B,GAAG,CAACjC,CAAC,EAAE,CAAC,CAAC,GAAI,CAAC,GAAGoN,CAAE;EACtC,IAAIyL,KAAK,IAAI,CAAC,EAAE;IACd,OAAO;MACLC,WAAW,EAAE,CAAC;MACdC,SAAS,EAAExb;IACb,CAAC;EACH;EACA,MAAMiE,KAAK,GAAG,CAAC,CAACxB,CAAC,GAAGM,IAAI,CAACoY,IAAI,CAACG,KAAK,CAAC,IAAI,CAAC;EACzC,MAAMpX,GAAG,GAAG,CAAC,CAACzB,CAAC,GAAGM,IAAI,CAACoY,IAAI,CAACG,KAAK,CAAC,IAAI,CAAC;EACvC,OAAO;IACLC,WAAW,EAAElb,iBAAiB,CAAC;MAACc,CAAC,EAAEqE,OAAO;MAAEnE,CAAC,EAAEoE;IAAO,CAAC,EAAE;MAACtE,CAAC,EAAE8C,KAAK;MAAE5C;IAAC,CAAC,CAAC,CAACwB,KAAK;IAC7E2Y,SAAS,EAAEnb,iBAAiB,CAAC;MAACc,CAAC,EAAEqE,OAAO;MAAEnE,CAAC,EAAEoE;IAAO,CAAC,EAAE;MAACtE,CAAC,EAAE+C,GAAG;MAAE7C;IAAC,CAAC,CAAC,CAACwB;EACtE,CAAC;AACH;AAEA,SAASyW,cAAcA,CAACvO,GAAG,EAAEtJ,OAAO,EAAE;EACpC,MAAM;IAACuZ,QAAQ;IAAEC,QAAQ;IAAEL,OAAO;IAAEW,WAAW;IAAEC,SAAS;IAAEX,iBAAiB;IAAE5Z;EAAO,CAAC,GAAGQ,OAAO;EACjGsJ,GAAG,CAACgC,IAAI,CAAC,CAAC;EACV,MAAMC,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC;EAC3C8J,GAAG,CAACkC,SAAS,GAAGhM,OAAO,CAACiM,eAAe;EACvCnC,GAAG,CAACoC,SAAS,CAAC,CAAC;EACfpC,GAAG,CAACmE,GAAG,CAAC8L,QAAQ,EAAEC,QAAQ,EAAEL,OAAO,EAAEW,WAAW,EAAEC,SAAS,EAAEX,iBAAiB,CAAC;EAC/E9P,GAAG,CAACwC,SAAS,CAAC,CAAC;EACfxC,GAAG,CAACyC,IAAI,CAAC,CAAC;EACV,IAAIR,MAAM,EAAE;IACVjC,GAAG,CAACiC,MAAM,CAAC,CAAC;EACd;EACAjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;AACf;AAEA,MAAM+N,eAAe,SAAS7c,OAAO,CAAC;EAEpC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,OAAOL,YAAY,CACjB;MAAC/D,CAAC,EAAEsX,MAAM;MAAEpX,CAAC,EAAEqX;IAAM,CAAC,EACtB;MAACvT,IAAI,EAAE,IAAI,CAACO,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;MAAE3D,MAAM,EAAE,IAAI,CAACO,cAAc,CAACoD,gBAAgB;IAAC,CAAC,EAC9GnE,IAAI,EACJ;MAACgE,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEN,WAAW,EAAE,IAAI,CAAC7D,OAAO,CAAC6D,WAAW;MAAEC,YAAY,EAAE,IAAI,CAAC9D,OAAO,CAAC8D;IAAY,CAC1G,CAAC;EACH;EAEA5C,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM9J,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMya,OAAO,GAAG,CAACrc,OAAO,CAAC,IAAI,CAACsc,QAAQ,CAAC,IAAI,IAAI,CAACA,QAAQ;IACxD,IAAI,CAAC1a,OAAO,CAACqP,OAAO,IAAI,CAACrP,OAAO,CAAC2J,OAAO,IAAI,CAAC8Q,OAAO,EAAE;MACpD;IACF;IACA3Q,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVjC,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC5I,cAAc,CAAC,CAAC,EAAE,IAAI,CAACiD,QAAQ,CAAC;IACpD6K,WAAW,CAAClF,GAAG,EAAE,IAAI,CAAC;IACtB+B,OAAO,CAAC/B,GAAG,EAAE,IAAI,EAAE9J,OAAO,CAAC;IAC3B0M,SAAS,CAAC5C,GAAG,EAAE6Q,YAAY,CAAC,IAAI,CAAC,EAAE3a,OAAO,CAAC;IAC3C8J,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEAkL,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,IAAIN,KAAK;IACT,IAAI,CAACsI,cAAc,CAAChI,OAAO,CAAC,EAAE;MAC5B,MAAM;QAACuE,OAAO;QAAEC;MAAO,CAAC,GAAGyN,oBAAoB,CAAC5J,KAAK,EAAErI,OAAO,CAAC;MAC/DN,KAAK,GAAG;QAACQ,CAAC,EAAEqE,OAAO;QAAEnE,CAAC,EAAEoE;MAAO,CAAC;IAClC,CAAC,MAAM;MACL9E,KAAK,GAAGiS,aAAa,CAACtJ,KAAK,EAAErI,OAAO,CAAC;IACvC;IACA,MAAM+G,OAAO,GAAG7H,SAAS,CAACc,OAAO,CAAC+G,OAAO,CAAC;IAC1C,MAAMH,SAAS,GAAGsE,gBAAgB,CAAC7C,KAAK,CAACyB,GAAG,EAAE9J,OAAO,CAAC;IACtD,MAAM4Y,OAAO,GAAGjS,qBAAqB,CAACjH,KAAK,EAAEkH,SAAS,EAAE5G,OAAO,EAAE+G,OAAO,CAAC;IACzE,OAAO;MACLqM,cAAc,EAAEhL,uBAAuB,CAACC,KAAK,EAAEuQ,OAAO,EAAE5Y,OAAO,CAAC;MAChEiP,MAAM,EAAEvP,KAAK,CAACQ,CAAC;MACfgP,MAAM,EAAExP,KAAK,CAACU,CAAC;MACf,GAAGwY,OAAO;MACVzU,QAAQ,EAAEnE,OAAO,CAACmE;IACpB,CAAC;EACH;AACF;AAEAqW,eAAe,CAAChJ,EAAE,GAAG,iBAAiB;AAEtCgJ,eAAe,CAAC3c,QAAQ,GAAG;EACzBqY,gBAAgB,EAAE,IAAI;EACtBjK,eAAe,EAAE,aAAa;EAC9BnB,qBAAqB,EAAE,aAAa;EACpCZ,cAAc,EAAE,MAAM;EACtBE,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBE,eAAe,EAAE,OAAO;EACxB6B,YAAY,EAAE,CAAC;EACfG,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdsL,OAAO,EAAE;IACPjF,cAAc,EAAE,MAAM;IACtBS,WAAW,EAAEiN,SAAS;IACtBxN,UAAU,EAAE,EAAE;IACdE,gBAAgB,EAAE,CAAC;IACnBE,eAAe,EAAE,OAAO;IACxB3G,WAAW,EAAE,CAAC;IACdwL,OAAO,EAAE,KAAK;IACdS,MAAM,EAAE,CAAC;IACTxJ,QAAQ,EAAE,MAAM;IAChB2J,IAAI,EAAE,CAAC;IACPjN,KAAK,EAAE;EACT,CAAC;EACDkK,KAAK,EAAE,OAAO;EACdvD,OAAO,EAAE,IAAI;EACb0F,OAAO,EAAE,IAAI;EACb3H,IAAI,EAAE;IACJoQ,MAAM,EAAEF,SAAS;IACjB7P,UAAU,EAAE6P,SAAS;IACrBvR,IAAI,EAAEuR,SAAS;IACf9K,KAAK,EAAE8K,SAAS;IAChBG,MAAM,EAAEH;EACV,CAAC;EACDjS,MAAM,EAAEiS,SAAS;EACjB9T,YAAY,EAAE,CAAC;EACfyE,IAAI,EAAEqP,SAAS;EACf/K,OAAO,EAAE+K,SAAS;EAClB7Q,OAAO,EAAE,CAAC;EACVT,QAAQ,EAAE,QAAQ;EAClBnC,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBvE,SAAS,EAAE,QAAQ;EACnB+G,eAAe,EAAEmK,SAAS;EAC1BxM,eAAe,EAAE,CAAC;EAClB1F,KAAK,EAAEkS,SAAS;EAChB/Q,OAAO,EAAE,CAAC;EACV0L,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnB3P,MAAM,EAAE2P,SAAS;EACjB9Q,OAAO,EAAE,CAAC;EACV4L,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnB1P,MAAM,EAAE0P,SAAS;EACjBI,CAAC,EAAE;AACL,CAAC;AAEDwC,eAAe,CAACvC,aAAa,GAAG;EAC9BtN,WAAW,EAAE;AACf,CAAC;AAED,SAASgQ,YAAYA,CAAC;EAACza,CAAC;EAAEE,CAAC;EAAEsF,KAAK;EAAEC,MAAM;EAAE3F;AAAO,CAAC,EAAE;EACpD,MAAM4a,YAAY,GAAG5a,OAAO,CAAC6D,WAAW,GAAG,CAAC;EAC5C,MAAMkD,OAAO,GAAG7H,SAAS,CAACc,OAAO,CAAC+G,OAAO,CAAC;EAC1C,OAAO;IACL7G,CAAC,EAAEA,CAAC,GAAG6G,OAAO,CAACgL,IAAI,GAAG6I,YAAY;IAClCxa,CAAC,EAAEA,CAAC,GAAG2G,OAAO,CAACiL,GAAG,GAAG4I,YAAY;IACjClV,KAAK,EAAEA,KAAK,GAAGqB,OAAO,CAACgL,IAAI,GAAGhL,OAAO,CAACmL,KAAK,GAAGlS,OAAO,CAAC6D,WAAW;IACjE8B,MAAM,EAAEA,MAAM,GAAGoB,OAAO,CAACiL,GAAG,GAAGjL,OAAO,CAACoL,MAAM,GAAGnS,OAAO,CAAC6D;EAC1D,CAAC;AACH;AAEA,MAAMgX,WAAW,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,MAAM;EAAC9a,CAAC,EAAE4a,EAAE,CAAC5a,CAAC,GAAG8a,CAAC,IAAID,EAAE,CAAC7a,CAAC,GAAG4a,EAAE,CAAC5a,CAAC,CAAC;EAAEE,CAAC,EAAE0a,EAAE,CAAC1a,CAAC,GAAG4a,CAAC,IAAID,EAAE,CAAC3a,CAAC,GAAG0a,EAAE,CAAC1a,CAAC;AAAC,CAAC,CAAC;AAC/F,MAAM6a,YAAY,GAAGA,CAAC7a,CAAC,EAAE0a,EAAE,EAAEC,EAAE,KAAKF,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEjZ,IAAI,CAACoZ,GAAG,CAAC,CAAC9a,CAAC,GAAG0a,EAAE,CAAC1a,CAAC,KAAK2a,EAAE,CAAC3a,CAAC,GAAG0a,EAAE,CAAC1a,CAAC,CAAC,CAAC,CAAC,CAACF,CAAC;AAC/F,MAAMib,YAAY,GAAGA,CAACjb,CAAC,EAAE4a,EAAE,EAAEC,EAAE,KAAKF,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEjZ,IAAI,CAACoZ,GAAG,CAAC,CAAChb,CAAC,GAAG4a,EAAE,CAAC5a,CAAC,KAAK6a,EAAE,CAAC7a,CAAC,GAAG4a,EAAE,CAAC5a,CAAC,CAAC,CAAC,CAAC,CAACE,CAAC;AAC/F,MAAMgb,GAAG,GAAGC,CAAC,IAAIA,CAAC,GAAGA,CAAC;AACtB,MAAMC,UAAU,GAAGA,CAAC9D,MAAM,EAAEC,MAAM,EAAE;EAACvX,CAAC;EAAEE,CAAC;EAAEuD,EAAE;EAAEC;AAAE,CAAC,EAAEzD,IAAI,KAAKA,IAAI,KAAK,GAAG,GAAG;EAAC6C,KAAK,EAAElB,IAAI,CAACY,GAAG,CAACtC,CAAC,EAAEwD,EAAE,CAAC;EAAEX,GAAG,EAAEnB,IAAI,CAACa,GAAG,CAACvC,CAAC,EAAEwD,EAAE,CAAC;EAAEb,KAAK,EAAE0U;AAAM,CAAC,GAAG;EAACzU,KAAK,EAAElB,IAAI,CAACY,GAAG,CAACxC,CAAC,EAAEyD,EAAE,CAAC;EAAEV,GAAG,EAAEnB,IAAI,CAACa,GAAG,CAACzC,CAAC,EAAEyD,EAAE,CAAC;EAAEZ,KAAK,EAAEyU;AAAM,CAAC;AACzM;AACA,MAAM+D,YAAY,GAAGA,CAACvY,KAAK,EAAEwY,EAAE,EAAEvY,GAAG,EAAE+X,CAAC,KAAK,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC,GAAGhY,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGgY,CAAC,CAAC,GAAGA,CAAC,GAAGQ,EAAE,GAAGR,CAAC,GAAGA,CAAC,GAAG/X,GAAG;AAC1G,MAAMwY,YAAY,GAAGA,CAACzY,KAAK,EAAEwY,EAAE,EAAEvY,GAAG,EAAE+X,CAAC,MAAM;EAAC9a,CAAC,EAAEqb,YAAY,CAACvY,KAAK,CAAC9C,CAAC,EAAEsb,EAAE,CAACtb,CAAC,EAAE+C,GAAG,CAAC/C,CAAC,EAAE8a,CAAC,CAAC;EAAE5a,CAAC,EAAEmb,YAAY,CAACvY,KAAK,CAAC5C,CAAC,EAAEob,EAAE,CAACpb,CAAC,EAAE6C,GAAG,CAAC7C,CAAC,EAAE4a,CAAC;AAAC,CAAC,CAAC;AAClI,MAAMU,iBAAiB,GAAGA,CAAC1Y,KAAK,EAAEwY,EAAE,EAAEvY,GAAG,EAAE+X,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,IAAIQ,EAAE,GAAGxY,KAAK,CAAC,GAAG,CAAC,GAAGgY,CAAC,IAAI/X,GAAG,GAAGuY,EAAE,CAAC;AAChG,MAAMG,YAAY,GAAGA,CAAC3Y,KAAK,EAAEwY,EAAE,EAAEvY,GAAG,EAAE+X,CAAC,KAAK,CAAClZ,IAAI,CAAC8Z,KAAK,CAACF,iBAAiB,CAAC1Y,KAAK,CAAC9C,CAAC,EAAEsb,EAAE,CAACtb,CAAC,EAAE+C,GAAG,CAAC/C,CAAC,EAAE8a,CAAC,CAAC,EAAEU,iBAAiB,CAAC1Y,KAAK,CAAC5C,CAAC,EAAEob,EAAE,CAACpb,CAAC,EAAE6C,GAAG,CAAC7C,CAAC,EAAE4a,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGpc,EAAE;AAE1J,MAAMid,cAAc,SAASle,OAAO,CAAC;EAEnC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,MAAMxB,OAAO,GAAG,CAAC,IAAI,CAAC9C,OAAO,CAAC6D,WAAW,GAAG,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,IAAI,CAAC;IAC1E,IAAI3D,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChC,MAAMT,KAAK,GAAG;QAAC8X,MAAM;QAAEC;MAAM,CAAC;MAC9B,MAAM;QAACqE,IAAI;QAAEhS;MAAG,CAAC,GAAG,IAAI;MACxB,IAAIgS,IAAI,EAAE;QACR9R,cAAc,CAACF,GAAG,EAAE,IAAI,CAAC9J,OAAO,CAAC;QACjC8J,GAAG,CAACW,SAAS,IAAI,IAAI,CAACzK,OAAO,CAAC8D,YAAY;QAC1C,MAAM;UAACuE;QAAK,CAAC,GAAG,IAAI,CAACuN,QAAQ;QAC7B,MAAMmG,EAAE,GAAGvE,MAAM,GAAGnP,KAAK,CAAC2T,uBAAuB;QACjD,MAAMC,EAAE,GAAGxE,MAAM,GAAGpP,KAAK,CAAC2T,uBAAuB;QACjD,MAAM/S,MAAM,GAAGa,GAAG,CAACoS,eAAe,CAACJ,IAAI,EAAEC,EAAE,EAAEE,EAAE,CAAC,IAAIE,SAAS,CAAC,IAAI,EAAEzc,KAAK,EAAE4E,gBAAgB,CAAC;QAC5FwF,GAAG,CAAC2C,OAAO,CAAC,CAAC;QACb,OAAOxD,MAAM;MACf;MACA,MAAMmT,OAAO,GAAGhB,GAAG,CAACtY,OAAO,CAAC;MAC5B,OAAOuZ,UAAU,CAAC,IAAI,EAAE3c,KAAK,EAAE0c,OAAO,EAAE9X,gBAAgB,CAAC,IAAI6X,SAAS,CAAC,IAAI,EAAEzc,KAAK,EAAE4E,gBAAgB,CAAC;IACvG;IACA,OAAOgY,WAAW,CAAC,IAAI,EAAE;MAAC9E,MAAM;MAAEC;IAAM,CAAC,EAAEtX,IAAI,EAAE;MAAC2C,OAAO;MAAEwB;IAAgB,CAAC,CAAC;EAC/E;EAEApD,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM;MAAC5J,CAAC;MAAEE,CAAC;MAAEuD,EAAE;MAAEC,EAAE;MAAE4X,EAAE;MAAExb;IAAO,CAAC,GAAG,IAAI;IAExC8J,GAAG,CAACgC,IAAI,CAAC,CAAC;IACV,IAAI,CAAC9B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC,EAAE;MACjC;MACA,OAAO8J,GAAG,CAAC2C,OAAO,CAAC,CAAC;IACtB;IACA7B,cAAc,CAACd,GAAG,EAAE9J,OAAO,CAAC;IAE5B,MAAMqC,MAAM,GAAGP,IAAI,CAACoY,IAAI,CAACpY,IAAI,CAAC2B,GAAG,CAACE,EAAE,GAAGzD,CAAC,EAAE,CAAC,CAAC,GAAG4B,IAAI,CAAC2B,GAAG,CAACG,EAAE,GAAGxD,CAAC,EAAE,CAAC,CAAC,CAAC;IACnE,IAAIJ,OAAO,CAACuc,KAAK,IAAIf,EAAE,EAAE;MACvBgB,SAAS,CAAC1S,GAAG,EAAE,IAAI,EAAE0R,EAAE,EAAEnZ,MAAM,CAAC;MAChC,OAAOyH,GAAG,CAAC2C,OAAO,CAAC,CAAC;IACtB;IACA,MAAM;MAACgQ,SAAS;MAAEC,OAAO;MAAEC,WAAW;MAAEC;IAAS,CAAC,GAAGC,aAAa,CAAC,IAAI,CAAC;IACxE,MAAMjb,KAAK,GAAGE,IAAI,CAAC8Z,KAAK,CAAChY,EAAE,GAAGxD,CAAC,EAAEuD,EAAE,GAAGzD,CAAC,CAAC;IACxC4J,GAAG,CAACD,SAAS,CAAC3J,CAAC,EAAEE,CAAC,CAAC;IACnB0J,GAAG,CAACC,MAAM,CAACnI,KAAK,CAAC;IACjBkI,GAAG,CAACoC,SAAS,CAAC,CAAC;IACfpC,GAAG,CAACoE,MAAM,CAAC,CAAC,GAAGyO,WAAW,EAAE,CAAC,CAAC;IAC9B7S,GAAG,CAACqE,MAAM,CAAC9L,MAAM,GAAGua,SAAS,EAAE,CAAC,CAAC;IACjC9S,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;IAC3C1C,GAAG,CAACiC,MAAM,CAAC,CAAC;IACZ+Q,aAAa,CAAChT,GAAG,EAAE,CAAC,EAAE6S,WAAW,EAAEF,SAAS,CAAC;IAC7CK,aAAa,CAAChT,GAAG,EAAEzH,MAAM,EAAE,CAACua,SAAS,EAAEF,OAAO,CAAC;IAC/C5S,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEA,IAAIxG,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoN,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;EAC1C;EAEAsE,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,MAAMgT,IAAI,GAAGD,qBAAqB,CAAC1K,KAAK,EAAErI,OAAO,CAAC;IAClD,MAAM;MAACE,CAAC;MAAEE,CAAC;MAAEuD,EAAE;MAAEC;IAAE,CAAC,GAAGoP,IAAI;IAC3B,MAAM+J,MAAM,GAAGC,YAAY,CAAChK,IAAI,EAAE3K,KAAK,CAACuJ,SAAS,CAAC;IAClD,MAAM9L,UAAU,GAAGiX,MAAM,GACrBE,eAAe,CAAC;MAAC/c,CAAC;MAAEE;IAAC,CAAC,EAAE;MAACF,CAAC,EAAEyD,EAAE;MAAEvD,CAAC,EAAEwD;IAAE,CAAC,EAAEyE,KAAK,CAACuJ,SAAS,CAAC,GACxD;MAAC1R,CAAC;MAAEE,CAAC;MAAEuD,EAAE;MAAEC,EAAE;MAAE8B,KAAK,EAAE5D,IAAI,CAACoZ,GAAG,CAACvX,EAAE,GAAGzD,CAAC,CAAC;MAAEyF,MAAM,EAAE7D,IAAI,CAACoZ,GAAG,CAACtX,EAAE,GAAGxD,CAAC;IAAC,CAAC;IACrE0F,UAAU,CAACvB,OAAO,GAAG,CAACZ,EAAE,GAAGzD,CAAC,IAAI,CAAC;IACjC4F,UAAU,CAACtB,OAAO,GAAG,CAACZ,EAAE,GAAGxD,CAAC,IAAI,CAAC;IACjC0F,UAAU,CAACsN,cAAc,GAAGhL,uBAAuB,CAACC,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;IAC/E,IAAIA,OAAO,CAACuc,KAAK,EAAE;MACjB,MAAMzB,EAAE,GAAG;QAAC5a,CAAC,EAAE4F,UAAU,CAAC5F,CAAC;QAAEE,CAAC,EAAE0F,UAAU,CAAC1F;MAAC,CAAC;MAC7C,MAAM2a,EAAE,GAAG;QAAC7a,CAAC,EAAE4F,UAAU,CAACnC,EAAE;QAAEvD,CAAC,EAAE0F,UAAU,CAAClC;MAAE,CAAC;MAC/CkC,UAAU,CAAC0V,EAAE,GAAG0B,eAAe,CAACpX,UAAU,EAAE9F,OAAO,EAAEhC,qBAAqB,CAAC8c,EAAE,EAAEC,EAAE,CAAC,CAAC;IACrF;IACA,MAAMoC,eAAe,GAAGC,6BAA6B,CAAC/U,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAACiG,KAAK,CAAC;IACvF;IACAkX,eAAe,CAACzC,QAAQ,GAAGqC,MAAM;IAEjCjX,UAAU,CAACuN,QAAQ,GAAG,CAAC;MACrBrK,IAAI,EAAE,OAAO;MACbsK,WAAW,EAAE,OAAO;MACpBxN,UAAU,EAAEqX,eAAe;MAC3B/J,cAAc,EAAEtN,UAAU,CAACsN;IAC7B,CAAC,CAAC;IACF,OAAOtN,UAAU;EACnB;AACF;AAEA+V,cAAc,CAACrK,EAAE,GAAG,gBAAgB;AAEpC,MAAM6L,kBAAkB,GAAG;EACzBpR,eAAe,EAAE2L,SAAS;EAC1B9M,qBAAqB,EAAE8M,SAAS;EAChCjN,WAAW,EAAEiN,SAAS;EACtBxN,UAAU,EAAEwN,SAAS;EACrBtN,gBAAgB,EAAEsN,SAAS;EAC3BpL,iBAAiB,EAAEoL,SAAS;EAC5B/T,WAAW,EAAE+T,SAAS;EACtBvI,OAAO,EAAEuI,SAAS;EAClBrL,IAAI,EAAEqL,SAAS;EACfvV,MAAM,EAAEuV,SAAS;EACjB7M,UAAU,EAAE6M,SAAS;EACrB5M,aAAa,EAAE4M,SAAS;EACxB3M,aAAa,EAAE2M,SAAS;EACxBlS,KAAK,EAAEkS;AACT,CAAC;AAEDiE,cAAc,CAAChe,QAAQ,GAAG;EACxBqY,gBAAgB,EAAE,IAAI;EACtBoH,UAAU,EAAE;IACVjO,OAAO,EAAE,KAAK;IACdpM,GAAG,EAAEI,MAAM,CAACka,MAAM,CAAC,CAAC,CAAC,EAAEF,kBAAkB,CAAC;IAC1C9Q,IAAI,EAAE,KAAK;IACXlK,MAAM,EAAE,EAAE;IACVW,KAAK,EAAEK,MAAM,CAACka,MAAM,CAAC,CAAC,CAAC,EAAEF,kBAAkB,CAAC;IAC5C3X,KAAK,EAAE;EACT,CAAC;EACD0E,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBkC,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACd0Y,KAAK,EAAE,KAAK;EACZiB,YAAY,EAAE;IACZpd,CAAC,EAAE;EACL,CAAC;EACDiP,OAAO,EAAE,IAAI;EACboE,QAAQ,EAAEmE,SAAS;EACnBrP,IAAI,EAAEqP,SAAS;EACf9T,YAAY,EAAE,CAAC;EACfmC,KAAK,EAAE;IACLgG,eAAe,EAAE,iBAAiB;IAClCnB,qBAAqB,EAAE,aAAa;IACpCZ,cAAc,EAAE,MAAM;IACtBS,WAAW,EAAE,OAAO;IACpBP,UAAU,EAAE,EAAE;IACdE,gBAAgB,EAAE,CAAC;IACnBE,eAAe,EAAE,OAAO;IACxB6B,YAAY,EAAE,CAAC;IACfG,iBAAiB,EAAE,aAAa;IAChC3I,WAAW,EAAE,CAAC;IACdsL,OAAO,EAAE9L,MAAM,CAACka,MAAM,CAAC,CAAC,CAAC,EAAE/C,eAAe,CAAC3c,QAAQ,CAACsR,OAAO,CAAC;IAC5DjC,KAAK,EAAE,MAAM;IACbvD,OAAO,EAAE,IAAI;IACb0F,OAAO,EAAE,KAAK;IACdwI,QAAQ,EAAED,SAAS;IACnBlQ,IAAI,EAAE;MACJoQ,MAAM,EAAEF,SAAS;MACjB7P,UAAU,EAAE6P,SAAS;MACrBvR,IAAI,EAAEuR,SAAS;MACf9K,KAAK,EAAE8K,SAAS;MAChBG,MAAM,EAAE;IACV,CAAC;IACDpS,MAAM,EAAEiS,SAAS;IACjB9T,YAAY,EAAE8T,SAAS;IACvB/K,OAAO,EAAE+K,SAAS;IAClB7Q,OAAO,EAAE,CAAC;IACVT,QAAQ,EAAE,QAAQ;IAClBnC,QAAQ,EAAE,CAAC;IACX4G,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC;IAChBvE,SAAS,EAAE,QAAQ;IACnB+G,eAAe,EAAEmK,SAAS;IAC1BxM,eAAe,EAAE,CAAC;IAClB1F,KAAK,EAAEkS,SAAS;IAChB/Q,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVkR,CAAC,EAAEJ;EACL,CAAC;EACDxG,OAAO,EAAEwG,SAAS;EAClB7M,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBlI,KAAK,EAAE6U,SAAS;EAChBrF,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnBlF,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnBI,CAAC,EAAE;AACL,CAAC;AAED6D,cAAc,CAAC3D,WAAW,GAAG;EAC3BoF,UAAU,EAAE;IACVta,KAAK,EAAE;MACLmV,SAAS,EAAE;IACb,CAAC;IACDlV,GAAG,EAAE;MACHkV,SAAS,EAAE;IACb,CAAC;IACDA,SAAS,EAAE;EACb;AACF,CAAC;AAED0D,cAAc,CAAC5D,aAAa,GAAG;EAC7BtN,WAAW,EAAE;AACf,CAAC;AAED,SAAS2R,WAAWA,CAAC9b,OAAO,EAAE;EAACgX,MAAM;EAAEC;AAAM,CAAC,EAAEtX,IAAI,EAAE;EAAC2C,OAAO;EAAEwB;AAAgB,CAAC,EAAE;EACjF,MAAMzB,KAAK,GAAGyY,UAAU,CAAC9D,MAAM,EAAEC,MAAM,EAAEjX,OAAO,CAACiE,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC,EAAEnE,IAAI,CAAC;EAC1G,OAAOyC,OAAO,CAACC,KAAK,EAAEC,OAAO,CAAC,IAAIqZ,SAAS,CAAC3b,OAAO,EAAE;IAACgX,MAAM;IAAEC;EAAM,CAAC,EAAEnT,gBAAgB,EAAEnE,IAAI,CAAC;AAChG;AAEA,SAAS6c,YAAYA,CAAC;EAAC9c,CAAC;EAAEE,CAAC;EAAEuD,EAAE;EAAEC;AAAE,CAAC,EAAE;EAACoO,GAAG;EAAEE,KAAK;EAAEC,MAAM;EAAEJ;AAAI,CAAC,EAAE;EAChE,OAAO,EACJ7R,CAAC,GAAG6R,IAAI,IAAIpO,EAAE,GAAGoO,IAAI,IACrB7R,CAAC,GAAGgS,KAAK,IAAIvO,EAAE,GAAGuO,KAAM,IACxB9R,CAAC,GAAG4R,GAAG,IAAIpO,EAAE,GAAGoO,GAAI,IACpB5R,CAAC,GAAG+R,MAAM,IAAIvO,EAAE,GAAGuO,MAAO,CAC5B;AACH;AAEA,SAASsL,gBAAgBA,CAAC;EAACvd,CAAC;EAAEE;AAAC,CAAC,EAAE2a,EAAE,EAAE;EAAC/I,GAAG;EAAEE,KAAK;EAAEC,MAAM;EAAEJ;AAAI,CAAC,EAAE;EAChE,IAAI7R,CAAC,GAAG6R,IAAI,EAAE;IACZ3R,CAAC,GAAG+a,YAAY,CAACpJ,IAAI,EAAE;MAAC7R,CAAC;MAAEE;IAAC,CAAC,EAAE2a,EAAE,CAAC;IAClC7a,CAAC,GAAG6R,IAAI;EACV;EACA,IAAI7R,CAAC,GAAGgS,KAAK,EAAE;IACb9R,CAAC,GAAG+a,YAAY,CAACjJ,KAAK,EAAE;MAAChS,CAAC;MAAEE;IAAC,CAAC,EAAE2a,EAAE,CAAC;IACnC7a,CAAC,GAAGgS,KAAK;EACX;EACA,IAAI9R,CAAC,GAAG4R,GAAG,EAAE;IACX9R,CAAC,GAAG+a,YAAY,CAACjJ,GAAG,EAAE;MAAC9R,CAAC;MAAEE;IAAC,CAAC,EAAE2a,EAAE,CAAC;IACjC3a,CAAC,GAAG4R,GAAG;EACT;EACA,IAAI5R,CAAC,GAAG+R,MAAM,EAAE;IACdjS,CAAC,GAAG+a,YAAY,CAAC9I,MAAM,EAAE;MAACjS,CAAC;MAAEE;IAAC,CAAC,EAAE2a,EAAE,CAAC;IACpC3a,CAAC,GAAG+R,MAAM;EACZ;EACA,OAAO;IAACjS,CAAC;IAAEE;EAAC,CAAC;AACf;AAEA,SAAS6c,eAAeA,CAACnC,EAAE,EAAEC,EAAE,EAAE/H,IAAI,EAAE;EACrC,MAAM;IAAC9S,CAAC;IAAEE;EAAC,CAAC,GAAGqd,gBAAgB,CAAC3C,EAAE,EAAEC,EAAE,EAAE/H,IAAI,CAAC;EAC7C,MAAM;IAAC9S,CAAC,EAAEyD,EAAE;IAAEvD,CAAC,EAAEwD;EAAE,CAAC,GAAG6Z,gBAAgB,CAAC1C,EAAE,EAAED,EAAE,EAAE9H,IAAI,CAAC;EACrD,OAAO;IAAC9S,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC,EAAE;IAAE8B,KAAK,EAAE5D,IAAI,CAACoZ,GAAG,CAACvX,EAAE,GAAGzD,CAAC,CAAC;IAAEyF,MAAM,EAAE7D,IAAI,CAACoZ,GAAG,CAACtX,EAAE,GAAGxD,CAAC;EAAC,CAAC;AAC1E;AAEA,SAASic,UAAUA,CAAC7b,OAAO,EAAE;EAACgX,MAAM;EAAEC;AAAM,CAAC,EAAE2E,OAAO,GAAG9Z,OAAO,EAAEgC,gBAAgB,EAAE;EAClF;EACA,MAAM;IAACpE,CAAC,EAAEwd,EAAE;IAAEtd,CAAC,EAAEud,EAAE;IAAEha,EAAE;IAAEC;EAAE,CAAC,GAAGpD,OAAO,CAACiE,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;EACzF,MAAMsZ,EAAE,GAAGja,EAAE,GAAG+Z,EAAE;EAClB,MAAMG,EAAE,GAAGja,EAAE,GAAG+Z,EAAE;EAClB,MAAMG,KAAK,GAAG1C,GAAG,CAACwC,EAAE,CAAC,GAAGxC,GAAG,CAACyC,EAAE,CAAC;EAC/B,MAAM7C,CAAC,GAAG8C,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAACtG,MAAM,GAAGkG,EAAE,IAAIE,EAAE,GAAG,CAACnG,MAAM,GAAGkG,EAAE,IAAIE,EAAE,IAAIC,KAAK;EAE9E,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIhD,CAAC,GAAG,CAAC,EAAE;IACT+C,EAAE,GAAGL,EAAE;IACPM,EAAE,GAAGL,EAAE;EACT,CAAC,MAAM,IAAI3C,CAAC,GAAG,CAAC,EAAE;IAChB+C,EAAE,GAAGpa,EAAE;IACPqa,EAAE,GAAGpa,EAAE;EACT,CAAC,MAAM;IACLma,EAAE,GAAGL,EAAE,GAAG1C,CAAC,GAAG4C,EAAE;IAChBI,EAAE,GAAGL,EAAE,GAAG3C,CAAC,GAAG6C,EAAE;EAClB;EACA,OAAQzC,GAAG,CAAC5D,MAAM,GAAGuG,EAAE,CAAC,GAAG3C,GAAG,CAAC3D,MAAM,GAAGuG,EAAE,CAAC,IAAK5B,OAAO;AACzD;AAEA,SAASD,SAASA,CAAC3b,OAAO,EAAE;EAACgX,MAAM;EAAEC;AAAM,CAAC,EAAEnT,gBAAgB,EAAEnE,IAAI,EAAE;EACpE,MAAM8F,KAAK,GAAGzF,OAAO,CAACyF,KAAK;EAC3B,OAAOA,KAAK,CAACjG,OAAO,CAACqP,OAAO,IAAIpJ,KAAK,CAACxF,OAAO,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,CAAC;AACvF;AAEA,SAAS8Y,6BAA6BA,CAAC/U,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,EAAE;EACjE,MAAM6D,WAAW,GAAG7D,OAAO,CAAC6D,WAAW;EACvC,MAAMkD,OAAO,GAAG7H,SAAS,CAACc,OAAO,CAAC+G,OAAO,CAAC;EAC1C,MAAMkX,QAAQ,GAAG/S,gBAAgB,CAAC7C,KAAK,CAACyB,GAAG,EAAE9J,OAAO,CAAC;EACrD,MAAM0F,KAAK,GAAGuY,QAAQ,CAACvY,KAAK,GAAGqB,OAAO,CAACrB,KAAK,GAAG7B,WAAW;EAC1D,MAAM8B,MAAM,GAAGsY,QAAQ,CAACtY,MAAM,GAAGoB,OAAO,CAACpB,MAAM,GAAG9B,WAAW;EAC7D,OAAOqa,sBAAsB,CAACpY,UAAU,EAAE9F,OAAO,EAAE;IAAC0F,KAAK;IAAEC,MAAM;IAAEoB;EAAO,CAAC,EAAEsB,KAAK,CAACuJ,SAAS,CAAC;AAC/F;AAEA,SAASuM,qBAAqBA,CAACrY,UAAU,EAAE;EACzC,MAAM;IAAC5F,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC;EAAE,CAAC,GAAGkC,UAAU;EACjC,MAAM3B,QAAQ,GAAGrC,IAAI,CAAC8Z,KAAK,CAAChY,EAAE,GAAGxD,CAAC,EAAEuD,EAAE,GAAGzD,CAAC,CAAC;EAC3C;EACA,OAAOiE,QAAQ,GAAGvF,EAAE,GAAG,CAAC,GAAGuF,QAAQ,GAAGvF,EAAE,GAAGuF,QAAQ,GAAGvF,EAAE,GAAG,CAAC,CAAC,GAAGuF,QAAQ,GAAGvF,EAAE,GAAGuF,QAAQ;AAC1F;AAEA,SAAS+Z,sBAAsBA,CAACpY,UAAU,EAAEG,KAAK,EAAEmY,KAAK,EAAExM,SAAS,EAAE;EACnE,MAAM;IAAClM,KAAK;IAAEC,MAAM;IAAEoB;EAAO,CAAC,GAAGqX,KAAK;EACtC,MAAM;IAACvX,OAAO;IAAEC;EAAO,CAAC,GAAGb,KAAK;EAChC,MAAM6U,EAAE,GAAG;IAAC5a,CAAC,EAAE4F,UAAU,CAAC5F,CAAC;IAAEE,CAAC,EAAE0F,UAAU,CAAC1F;EAAC,CAAC;EAC7C,MAAM2a,EAAE,GAAG;IAAC7a,CAAC,EAAE4F,UAAU,CAACnC,EAAE;IAAEvD,CAAC,EAAE0F,UAAU,CAAClC;EAAE,CAAC;EAC/C,MAAMO,QAAQ,GAAG8B,KAAK,CAAC9B,QAAQ,KAAK,MAAM,GAAGga,qBAAqB,CAACrY,UAAU,CAAC,GAAG7H,SAAS,CAACgI,KAAK,CAAC9B,QAAQ,CAAC;EAC1G,MAAMkC,IAAI,GAAGgY,WAAW,CAAC3Y,KAAK,EAAEC,MAAM,EAAExB,QAAQ,CAAC;EACjD,MAAM6W,CAAC,GAAGsD,UAAU,CAACxY,UAAU,EAAEG,KAAK,EAAE;IAACW,SAAS,EAAEP,IAAI;IAAEU;EAAO,CAAC,EAAE6K,SAAS,CAAC;EAC9E,MAAM2M,EAAE,GAAGzY,UAAU,CAAC0V,EAAE,GAAGC,YAAY,CAACX,EAAE,EAAEhV,UAAU,CAAC0V,EAAE,EAAET,EAAE,EAAEC,CAAC,CAAC,GAAGH,WAAW,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,CAAC;EAC1F,MAAMwD,gBAAgB,GAAG;IAACnY,IAAI,EAAEA,IAAI,CAAC8F,CAAC;IAAEzJ,GAAG,EAAEkP,SAAS,CAACG,IAAI;IAAEpP,GAAG,EAAEiP,SAAS,CAACM,KAAK;IAAEnL,OAAO,EAAEA,OAAO,CAACgL;EAAI,CAAC;EACzG,MAAM0M,gBAAgB,GAAG;IAACpY,IAAI,EAAEA,IAAI,CAAC+F,CAAC;IAAE1J,GAAG,EAAEkP,SAAS,CAACI,GAAG;IAAErP,GAAG,EAAEiP,SAAS,CAACO,MAAM;IAAEpL,OAAO,EAAEA,OAAO,CAACiL;EAAG,CAAC;EACxG,MAAMzN,OAAO,GAAGma,qBAAqB,CAACH,EAAE,CAACre,CAAC,EAAEse,gBAAgB,CAAC,GAAG3X,OAAO;EACvE,MAAMrC,OAAO,GAAGka,qBAAqB,CAACH,EAAE,CAACne,CAAC,EAAEqe,gBAAgB,CAAC,GAAG3X,OAAO;EACvE,OAAO;IACL5G,CAAC,EAAEqE,OAAO,GAAImB,KAAK,GAAG,CAAE;IACxBtF,CAAC,EAAEoE,OAAO,GAAImB,MAAM,GAAG,CAAE;IACzBhC,EAAE,EAAEY,OAAO,GAAImB,KAAK,GAAG,CAAE;IACzB9B,EAAE,EAAEY,OAAO,GAAImB,MAAM,GAAG,CAAE;IAC1BpB,OAAO;IACPC,OAAO;IACPyK,MAAM,EAAEsP,EAAE,CAACre,CAAC;IACZgP,MAAM,EAAEqP,EAAE,CAACne,CAAC;IACZsF,KAAK;IACLC,MAAM;IACNxB,QAAQ,EAAE9E,SAAS,CAAC8E,QAAQ;EAC9B,CAAC;AACH;AAEA,SAASka,WAAWA,CAAC3Y,KAAK,EAAEC,MAAM,EAAExB,QAAQ,EAAE;EAC5C,MAAMtC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACsC,QAAQ,CAAC;EAC9B,MAAMpC,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACoC,QAAQ,CAAC;EAC9B,OAAO;IACLgI,CAAC,EAAErK,IAAI,CAACoZ,GAAG,CAACxV,KAAK,GAAG7D,GAAG,CAAC,GAAGC,IAAI,CAACoZ,GAAG,CAACvV,MAAM,GAAG5D,GAAG,CAAC;IACjDqK,CAAC,EAAEtK,IAAI,CAACoZ,GAAG,CAACxV,KAAK,GAAG3D,GAAG,CAAC,GAAGD,IAAI,CAACoZ,GAAG,CAACvV,MAAM,GAAG9D,GAAG;EAClD,CAAC;AACH;AAEA,SAASyc,UAAUA,CAACxY,UAAU,EAAEG,KAAK,EAAEmY,KAAK,EAAExM,SAAS,EAAE;EACvD,IAAIoJ,CAAC;EACL,MAAMtB,KAAK,GAAGiF,WAAW,CAAC7Y,UAAU,EAAE8L,SAAS,CAAC;EAChD,IAAI3L,KAAK,CAACK,QAAQ,KAAK,OAAO,EAAE;IAC9B0U,CAAC,GAAG4D,gBAAgB,CAAC;MAACzS,CAAC,EAAErG,UAAU,CAACnC,EAAE,GAAGmC,UAAU,CAAC5F,CAAC;MAAEkM,CAAC,EAAEtG,UAAU,CAAClC,EAAE,GAAGkC,UAAU,CAAC1F;IAAC,CAAC,EAAEge,KAAK,EAAEnY,KAAK,EAAEyT,KAAK,CAAC;EAC/G,CAAC,MAAM,IAAIzT,KAAK,CAACK,QAAQ,KAAK,KAAK,EAAE;IACnC0U,CAAC,GAAG,CAAC,GAAG4D,gBAAgB,CAAC;MAACzS,CAAC,EAAErG,UAAU,CAAC5F,CAAC,GAAG4F,UAAU,CAACnC,EAAE;MAAEyI,CAAC,EAAEtG,UAAU,CAAC1F,CAAC,GAAG0F,UAAU,CAAClC;IAAE,CAAC,EAAEwa,KAAK,EAAEnY,KAAK,EAAEyT,KAAK,CAAC;EACnH,CAAC,MAAM;IACLsB,CAAC,GAAG5U,mBAAmB,CAAC,CAAC,EAAEH,KAAK,CAACK,QAAQ,CAAC;EAC5C;EACA,OAAO0U,CAAC;AACV;AAEA,SAAS4D,gBAAgBA,CAACC,QAAQ,EAAET,KAAK,EAAEnY,KAAK,EAAEyT,KAAK,EAAE;EACvD,MAAM;IAAC9S,SAAS;IAAEG;EAAO,CAAC,GAAGqX,KAAK;EAClC,MAAMU,KAAK,GAAGD,QAAQ,CAAC1S,CAAC,GAAGuN,KAAK,CAACkE,EAAE;EACnC,MAAMmB,KAAK,GAAGF,QAAQ,CAACzS,CAAC,GAAGsN,KAAK,CAACmE,EAAE;EACnC,MAAM3d,CAAC,GAAI4e,KAAK,GAAG,CAAC,IAAM,CAAClY,SAAS,CAACuF,CAAC,GAAG,CAAC,GAAGpF,OAAO,CAACgL,IAAI,GAAG2H,KAAK,CAACxZ,CAAC,IAAI4e,KAAM;EAC7E,MAAM1e,CAAC,GAAI2e,KAAK,GAAG,CAAC,IAAM,CAACnY,SAAS,CAACwF,CAAC,GAAG,CAAC,GAAGrF,OAAO,CAACiL,GAAG,GAAG0H,KAAK,CAACtZ,CAAC,IAAI2e,KAAM;EAC5E,OAAOxc,KAAK,CAACT,IAAI,CAACa,GAAG,CAACzC,CAAC,EAAEE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;AACvC;AAEA,SAASue,WAAWA,CAAC7Y,UAAU,EAAE8L,SAAS,EAAE;EAC1C,MAAM;IAAC1R,CAAC;IAAEyD,EAAE;IAAEvD,CAAC;IAAEwD;EAAE,CAAC,GAAGkC,UAAU;EACjC,MAAMkV,CAAC,GAAGlZ,IAAI,CAACY,GAAG,CAACtC,CAAC,EAAEwD,EAAE,CAAC,GAAGgO,SAAS,CAACI,GAAG;EACzC,MAAMvD,CAAC,GAAG3M,IAAI,CAACY,GAAG,CAACxC,CAAC,EAAEyD,EAAE,CAAC,GAAGiO,SAAS,CAACG,IAAI;EAC1C,MAAMvQ,CAAC,GAAGoQ,SAAS,CAACO,MAAM,GAAGrQ,IAAI,CAACa,GAAG,CAACvC,CAAC,EAAEwD,EAAE,CAAC;EAC5C,MAAMob,CAAC,GAAGpN,SAAS,CAACM,KAAK,GAAGpQ,IAAI,CAACa,GAAG,CAACzC,CAAC,EAAEyD,EAAE,CAAC;EAC3C,OAAO;IACLzD,CAAC,EAAE4B,IAAI,CAACY,GAAG,CAAC+L,CAAC,EAAEuQ,CAAC,CAAC;IACjB5e,CAAC,EAAE0B,IAAI,CAACY,GAAG,CAACsY,CAAC,EAAExZ,CAAC,CAAC;IACjBoc,EAAE,EAAEnP,CAAC,IAAIuQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnBnB,EAAE,EAAE7C,CAAC,IAAIxZ,CAAC,GAAG,CAAC,GAAG,CAAC;EACpB,CAAC;AACH;AAEA,SAASkd,qBAAqBA,CAACO,UAAU,EAAEC,UAAU,EAAE;EACrD,MAAM;IAAC7Y,IAAI;IAAE3D,GAAG;IAAEC,GAAG;IAAEoE;EAAO,CAAC,GAAGmY,UAAU;EAC5C,MAAMC,QAAQ,GAAG9Y,IAAI,GAAG,CAAC;EACzB,IAAIA,IAAI,GAAG1D,GAAG,GAAGD,GAAG,EAAE;IACpB;IACA,OAAO,CAACC,GAAG,GAAGD,GAAG,IAAI,CAAC;EACxB;EACA,IAAIA,GAAG,IAAKuc,UAAU,GAAGlY,OAAO,GAAGoY,QAAS,EAAE;IAC5CF,UAAU,GAAGvc,GAAG,GAAGqE,OAAO,GAAGoY,QAAQ;EACvC;EACA,IAAIxc,GAAG,IAAKsc,UAAU,GAAGlY,OAAO,GAAGoY,QAAS,EAAE;IAC5CF,UAAU,GAAGtc,GAAG,GAAGoE,OAAO,GAAGoY,QAAQ;EACvC;EACA,OAAOF,UAAU;AACnB;AAEA,SAASpC,aAAaA,CAAC3W,IAAI,EAAE;EAC3B,MAAMlG,OAAO,GAAGkG,IAAI,CAAClG,OAAO;EAC5B,MAAMof,cAAc,GAAGpf,OAAO,CAACsd,UAAU,IAAItd,OAAO,CAACsd,UAAU,CAACta,KAAK;EACrE,MAAMqc,YAAY,GAAGrf,OAAO,CAACsd,UAAU,IAAItd,OAAO,CAACsd,UAAU,CAACra,GAAG;EACjE,OAAO;IACLwZ,SAAS,EAAE2C,cAAc;IACzB1C,OAAO,EAAE2C,YAAY;IACrB1C,WAAW,EAAE2C,aAAa,CAACpZ,IAAI,EAAEkZ,cAAc,CAAC;IAChDxC,SAAS,EAAE0C,aAAa,CAACpZ,IAAI,EAAEmZ,YAAY;EAC7C,CAAC;AACH;AAEA,SAASC,aAAaA,CAACpZ,IAAI,EAAEqZ,SAAS,EAAE;EACtC,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAAClQ,OAAO,EAAE;IACpC,OAAO,CAAC;EACV;EACA,MAAM;IAAChN,MAAM;IAAEqD;EAAK,CAAC,GAAG6Z,SAAS;EACjC,MAAMpX,MAAM,GAAGjC,IAAI,CAAClG,OAAO,CAAC6D,WAAW,GAAG,CAAC;EAC3C,MAAMiX,EAAE,GAAG;IAAC5a,CAAC,EAAEmC,MAAM;IAAEjC,CAAC,EAAEsF,KAAK,GAAGyC;EAAM,CAAC;EACzC,MAAM4S,EAAE,GAAG;IAAC7a,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE+H;EAAM,CAAC;EAC5B,OAAOrG,IAAI,CAACoZ,GAAG,CAACD,YAAY,CAAC,CAAC,EAAEH,EAAE,EAAEC,EAAE,CAAC,CAAC;AAC1C;AAEA,SAAS+B,aAAaA,CAAChT,GAAG,EAAE0V,MAAM,EAAErX,MAAM,EAAEoX,SAAS,EAAE;EACrD,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAAClQ,OAAO,EAAE;IACpC;EACF;EACA,MAAM;IAAChN,MAAM;IAAEqD,KAAK;IAAE6G,IAAI;IAAEN,eAAe;IAAEtB;EAAW,CAAC,GAAG4U,SAAS;EACrE,MAAME,YAAY,GAAG3d,IAAI,CAACoZ,GAAG,CAACsE,MAAM,GAAGnd,MAAM,CAAC,GAAG8F,MAAM;EACvD2B,GAAG,CAACoC,SAAS,CAAC,CAAC;EACftB,cAAc,CAACd,GAAG,EAAEyV,SAAS,CAAC;EAC9BvV,cAAc,CAACF,GAAG,EAAEyV,SAAS,CAAC;EAC9BzV,GAAG,CAACoE,MAAM,CAACuR,YAAY,EAAE,CAAC/Z,KAAK,CAAC;EAChCoE,GAAG,CAACqE,MAAM,CAACqR,MAAM,GAAGrX,MAAM,EAAE,CAAC,CAAC;EAC9B2B,GAAG,CAACqE,MAAM,CAACsR,YAAY,EAAE/Z,KAAK,CAAC;EAC/B,IAAI6G,IAAI,KAAK,IAAI,EAAE;IACjBzC,GAAG,CAACkC,SAAS,GAAGC,eAAe,IAAItB,WAAW;IAC9Cb,GAAG,CAACwC,SAAS,CAAC,CAAC;IACfxC,GAAG,CAACyC,IAAI,CAAC,CAAC;IACVzC,GAAG,CAACe,WAAW,GAAG,aAAa;EACjC,CAAC,MAAM;IACLf,GAAG,CAACe,WAAW,GAAG0U,SAAS,CAAC/S,iBAAiB;EAC/C;EACA1C,GAAG,CAACiC,MAAM,CAAC,CAAC;AACd;AAEA,SAASmR,eAAeA,CAACpX,UAAU,EAAE9F,OAAO,EAAEoB,QAAQ,EAAE;EACtD,MAAM;IAAClB,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC,EAAE;IAAEW,OAAO;IAAEC;EAAO,CAAC,GAAGsB,UAAU;EACnD,MAAMlE,KAAK,GAAGE,IAAI,CAAC8Z,KAAK,CAAChY,EAAE,GAAGxD,CAAC,EAAEuD,EAAE,GAAGzD,CAAC,CAAC;EACxC,MAAMsb,EAAE,GAAGtU,UAAU,CAAClH,OAAO,CAACwd,YAAY,EAAE,CAAC,CAAC;EAC9C,MAAM9d,KAAK,GAAG;IACZQ,CAAC,EAAEqE,OAAO,GAAGgC,OAAO,CAACnF,QAAQ,EAAEoa,EAAE,CAACtb,CAAC,EAAE,KAAK,CAAC;IAC3CE,CAAC,EAAEoE,OAAO,GAAG+B,OAAO,CAACnF,QAAQ,EAAEoa,EAAE,CAACpb,CAAC,EAAE,KAAK;EAC5C,CAAC;EACD,OAAOuB,OAAO,CAACjC,KAAK,EAAE;IAACQ,CAAC,EAAEqE,OAAO;IAAEnE,CAAC,EAAEoE;EAAO,CAAC,EAAE5C,KAAK,CAAC;AACxD;AAEA,SAAS8d,oBAAoBA,CAAC5V,GAAG,EAAE;EAAC5J,CAAC;EAAEE;AAAC,CAAC,EAAE;EAACwB,KAAK;EAAEuG;AAAM,CAAC,EAAEoX,SAAS,EAAE;EACrE,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAAClQ,OAAO,EAAE;IACpC;EACF;EACAvF,GAAG,CAACgC,IAAI,CAAC,CAAC;EACVhC,GAAG,CAACD,SAAS,CAAC3J,CAAC,EAAEE,CAAC,CAAC;EACnB0J,GAAG,CAACC,MAAM,CAACnI,KAAK,CAAC;EACjBkb,aAAa,CAAChT,GAAG,EAAE,CAAC,EAAE,CAAC3B,MAAM,EAAEoX,SAAS,CAAC;EACzCzV,GAAG,CAAC2C,OAAO,CAAC,CAAC;AACf;AAEA,SAAS+P,SAASA,CAAC1S,GAAG,EAAEtJ,OAAO,EAAEgb,EAAE,EAAEnZ,MAAM,EAAE;EAC3C,MAAM;IAACnC,CAAC;IAAEE,CAAC;IAAEuD,EAAE;IAAEC,EAAE;IAAE5D;EAAO,CAAC,GAAGQ,OAAO;EACvC,MAAM;IAACic,SAAS;IAAEC,OAAO;IAAEC,WAAW;IAAEC;EAAS,CAAC,GAAGC,aAAa,CAACrc,OAAO,CAAC;EAC3E,MAAMsa,EAAE,GAAG;IAAC5a,CAAC;IAAEE;EAAC,CAAC;EACjB,MAAM2a,EAAE,GAAG;IAAC7a,CAAC,EAAEyD,EAAE;IAAEvD,CAAC,EAAEwD;EAAE,CAAC;EACzB,MAAM+b,UAAU,GAAGhE,YAAY,CAACb,EAAE,EAAEU,EAAE,EAAET,EAAE,EAAE,CAAC,CAAC;EAC9C,MAAM6E,QAAQ,GAAGjE,YAAY,CAACb,EAAE,EAAEU,EAAE,EAAET,EAAE,EAAE,CAAC,CAAC,GAAGnc,EAAE;EACjD,MAAMihB,EAAE,GAAGpE,YAAY,CAACX,EAAE,EAAEU,EAAE,EAAET,EAAE,EAAE4B,WAAW,GAAGta,MAAM,CAAC;EACzD,MAAMyd,EAAE,GAAGrE,YAAY,CAACX,EAAE,EAAEU,EAAE,EAAET,EAAE,EAAE,CAAC,GAAG6B,SAAS,GAAGva,MAAM,CAAC;EAE3D,MAAMyZ,IAAI,GAAG,IAAIiE,MAAM,CAAC,CAAC;EACzBjW,GAAG,CAACoC,SAAS,CAAC,CAAC;EACf4P,IAAI,CAAC5N,MAAM,CAAC2R,EAAE,CAAC3f,CAAC,EAAE2f,EAAE,CAACzf,CAAC,CAAC;EACvB0b,IAAI,CAACkE,gBAAgB,CAACxE,EAAE,CAACtb,CAAC,EAAEsb,EAAE,CAACpb,CAAC,EAAE0f,EAAE,CAAC5f,CAAC,EAAE4f,EAAE,CAAC1f,CAAC,CAAC;EAC7C0J,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;EAC3C1C,GAAG,CAACiC,MAAM,CAAC+P,IAAI,CAAC;EAChBtb,OAAO,CAACsb,IAAI,GAAGA,IAAI;EACnBtb,OAAO,CAACsJ,GAAG,GAAGA,GAAG;EACjB4V,oBAAoB,CAAC5V,GAAG,EAAE+V,EAAE,EAAE;IAACje,KAAK,EAAE+d,UAAU;IAAExX,MAAM,EAAEwU;EAAW,CAAC,EAAEF,SAAS,CAAC;EAClFiD,oBAAoB,CAAC5V,GAAG,EAAEgW,EAAE,EAAE;IAACle,KAAK,EAAEge,QAAQ;IAAEzX,MAAM,EAAEyU;EAAS,CAAC,EAAEF,OAAO,CAAC;AAC9E;AAEA,MAAMuD,iBAAiB,SAAStiB,OAAO,CAAC;EAEtC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,MAAMH,QAAQ,GAAG,IAAI,CAACnE,OAAO,CAACmE,QAAQ;IACtC,MAAMrB,OAAO,GAAG,CAAC,IAAI,CAAC9C,OAAO,CAAC6D,WAAW,GAAG,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,IAAI,CAAC;IAC1E,IAAI3D,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChC,OAAO+f,cAAc,CAAC;QAAChgB,CAAC,EAAEsX,MAAM;QAAEpX,CAAC,EAAEqX;MAAM,CAAC,EAAE,IAAI,CAAChT,QAAQ,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,EAAEH,gBAAgB,CAAC,EAAEH,QAAQ,EAAErB,OAAO,CAAC;IAC9I;IACA,MAAM;MAAC5C,CAAC;MAAEE,CAAC;MAAEuD,EAAE;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACa,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;IAC9E,MAAMzB,KAAK,GAAG1C,IAAI,KAAK,GAAG,GAAG;MAAC6C,KAAK,EAAE5C,CAAC;MAAE6C,GAAG,EAAEW;IAAE,CAAC,GAAG;MAACZ,KAAK,EAAE9C,CAAC;MAAE+C,GAAG,EAAEU;IAAE,CAAC;IACtE,MAAMoM,YAAY,GAAGpO,OAAO,CAAC;MAACzB,CAAC,EAAEsX,MAAM;MAAEpX,CAAC,EAAEqX;IAAM,CAAC,EAAE,IAAI,CAACvW,cAAc,CAACoD,gBAAgB,CAAC,EAAErG,SAAS,CAAC,CAACkG,QAAQ,CAAC,CAAC;IACjH,OAAO4L,YAAY,CAAC5P,IAAI,CAAC,IAAI0C,KAAK,CAACG,KAAK,GAAGF,OAAO,GAAGR,OAAO,IAAIyN,YAAY,CAAC5P,IAAI,CAAC,IAAI0C,KAAK,CAACI,GAAG,GAAGH,OAAO,GAAGR,OAAO;EACrH;EAEApB,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM;MAACpE,KAAK;MAAEC,MAAM;MAAEpB,OAAO;MAAEC,OAAO;MAAExE;IAAO,CAAC,GAAG,IAAI;IACvD8J,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVjC,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC5I,cAAc,CAAC,CAAC,EAAElB,OAAO,CAACmE,QAAQ,CAAC;IACvDyG,cAAc,CAACd,GAAG,EAAE,IAAI,CAAC9J,OAAO,CAAC;IACjC8J,GAAG,CAACoC,SAAS,CAAC,CAAC;IACfpC,GAAG,CAACkC,SAAS,GAAGhM,OAAO,CAACiM,eAAe;IACvC,MAAMF,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC;IAC3C8J,GAAG,CAAC9D,OAAO,CAACzB,OAAO,EAAEC,OAAO,EAAEmB,MAAM,GAAG,CAAC,EAAED,KAAK,GAAG,CAAC,EAAE9G,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGA,EAAE,CAAC;IACvEkL,GAAG,CAACyC,IAAI,CAAC,CAAC;IACV,IAAIR,MAAM,EAAE;MACVjC,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;MAC3C1C,GAAG,CAACiC,MAAM,CAAC,CAAC;IACd;IACAjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEA,IAAIxG,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoN,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC;EAC1C;EAEAsE,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,OAAOmT,4BAA4B,CAAC9K,KAAK,EAAErI,OAAO,CAAC;EACrD;AAEF;AAEAigB,iBAAiB,CAACzO,EAAE,GAAG,mBAAmB;AAE1CyO,iBAAiB,CAACpiB,QAAQ,GAAG;EAC3BqY,gBAAgB,EAAE,IAAI;EACtBpL,qBAAqB,EAAE,aAAa;EACpCV,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBkC,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdwL,OAAO,EAAE,IAAI;EACbvL,YAAY,EAAE,CAAC;EACfyE,IAAI,EAAEqP,SAAS;EACf3R,KAAK,EAAE5C,MAAM,CAACka,MAAM,CAAC,CAAC,CAAC,EAAEhG,aAAa,CAAC1Z,QAAQ,CAACoI,KAAK,CAAC;EACtD9B,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBsH,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnBlF,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnBI,CAAC,EAAE;AACL,CAAC;AAEDiI,iBAAiB,CAAChI,aAAa,GAAG;EAChCtN,WAAW,EAAE,OAAO;EACpBsB,eAAe,EAAE;AACnB,CAAC;AAEDgU,iBAAiB,CAAC/H,WAAW,GAAG;EAC9BjS,KAAK,EAAE;IACLkS,SAAS,EAAE;EACb;AACF,CAAC;AAED,SAAS+H,cAAcA,CAACC,CAAC,EAAEna,OAAO,EAAE7B,QAAQ,EAAErB,OAAO,EAAE;EACrD,MAAM;IAAC4C,KAAK;IAAEC,MAAM;IAAEpB,OAAO;IAAEC;EAAO,CAAC,GAAGwB,OAAO;EACjD,MAAMoa,OAAO,GAAG1a,KAAK,GAAG,CAAC;EACzB,MAAM2a,OAAO,GAAG1a,MAAM,GAAG,CAAC;EAE1B,IAAIya,OAAO,IAAI,CAAC,IAAIC,OAAO,IAAI,CAAC,EAAE;IAChC,OAAO,KAAK;EACd;EACA;EACA,MAAMze,KAAK,GAAG3D,SAAS,CAACkG,QAAQ,IAAI,CAAC,CAAC;EACtC,MAAMmc,QAAQ,GAAGxe,IAAI,CAACD,GAAG,CAACD,KAAK,CAAC;EAChC,MAAM2e,QAAQ,GAAGze,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC;EAChC,MAAML,CAAC,GAAGO,IAAI,CAAC2B,GAAG,CAAC6c,QAAQ,IAAIH,CAAC,CAACjgB,CAAC,GAAGqE,OAAO,CAAC,GAAGgc,QAAQ,IAAIJ,CAAC,CAAC/f,CAAC,GAAGoE,OAAO,CAAC,EAAE,CAAC,CAAC;EAC9E,MAAMhD,CAAC,GAAGM,IAAI,CAAC2B,GAAG,CAAC8c,QAAQ,IAAIJ,CAAC,CAACjgB,CAAC,GAAGqE,OAAO,CAAC,GAAG+b,QAAQ,IAAIH,CAAC,CAAC/f,CAAC,GAAGoE,OAAO,CAAC,EAAE,CAAC,CAAC;EAC9E,OAAQjD,CAAC,GAAGO,IAAI,CAAC2B,GAAG,CAAC2c,OAAO,GAAGtd,OAAO,EAAE,CAAC,CAAC,GAAKtB,CAAC,GAAGM,IAAI,CAAC2B,GAAG,CAAC4c,OAAO,GAAGvd,OAAO,EAAE,CAAC,CAAE,IAAI,MAAM;AAC9F;AAEA,MAAM0d,eAAe,SAAS7iB,OAAO,CAAC;EAEpC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,MAAM;MAACpE,CAAC;MAAEE,CAAC;MAAEuD,EAAE;MAAEC,EAAE;MAAE8B;IAAK,CAAC,GAAG,IAAI,CAACjB,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,EAAEH,gBAAgB,CAAC;IAC9F,MAAMxB,OAAO,GAAG,CAAC,IAAI,CAAC9C,OAAO,CAAC6D,WAAW,GAAG,IAAI,CAAC7D,OAAO,CAAC8D,YAAY,IAAI,CAAC;IAC1E,IAAI3D,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChC,OAAOoD,YAAY,CAAC;QAACrD,CAAC,EAAEsX,MAAM;QAAEpX,CAAC,EAAEqX;MAAM,CAAC,EAAE,IAAI,CAACvW,cAAc,CAACoD,gBAAgB,CAAC,EAAEoB,KAAK,GAAG,CAAC,EAAE5C,OAAO,CAAC;IACxG;IACA,MAAMD,KAAK,GAAG1C,IAAI,KAAK,GAAG,GAAG;MAAC6C,KAAK,EAAE5C,CAAC;MAAE6C,GAAG,EAAEW,EAAE;MAAEb,KAAK,EAAE0U;IAAM,CAAC,GAAG;MAACzU,KAAK,EAAE9C,CAAC;MAAE+C,GAAG,EAAEU,EAAE;MAAEZ,KAAK,EAAEyU;IAAM,CAAC;IACpG,OAAO5U,OAAO,CAACC,KAAK,EAAEC,OAAO,CAAC;EAChC;EAEA5B,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM9J,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAM6D,WAAW,GAAG7D,OAAO,CAAC6D,WAAW;IACvC,IAAI7D,OAAO,CAACwD,MAAM,GAAG,GAAG,EAAE;MACxB;IACF;IACAsG,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVhC,GAAG,CAACkC,SAAS,GAAGhM,OAAO,CAACiM,eAAe;IACvCrB,cAAc,CAACd,GAAG,EAAE9J,OAAO,CAAC;IAC5B,MAAM+L,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC;IAC3C0N,SAAS,CAAC5D,GAAG,EAAE,IAAI,EAAE,IAAI,CAACvF,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC;IAChD,IAAIuH,MAAM,IAAI,CAACrC,eAAe,CAAC1J,OAAO,CAAC2N,UAAU,CAAC,EAAE;MAClD7D,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;MAC3C1C,GAAG,CAACiC,MAAM,CAAC,CAAC;IACd;IACAjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;IACbzM,OAAO,CAAC6D,WAAW,GAAGA,WAAW;EACnC;EAEA8T,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,MAAM8F,UAAU,GAAG6M,sBAAsB,CAACtK,KAAK,EAAErI,OAAO,CAAC;IACzD8F,UAAU,CAACsN,cAAc,GAAGhL,uBAAuB,CAACC,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;IAC/E,OAAO8F,UAAU;EACnB;AACF;AAEA0a,eAAe,CAAChP,EAAE,GAAG,iBAAiB;AAEtCgP,eAAe,CAAC3iB,QAAQ,GAAG;EACzBqY,gBAAgB,EAAE,IAAI;EACtBpL,qBAAqB,EAAE,aAAa;EACpCV,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBkC,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdwL,OAAO,EAAE,IAAI;EACbvL,YAAY,EAAE,CAAC;EACfyE,IAAI,EAAEqP,SAAS;EACfjK,UAAU,EAAE,QAAQ;EACpBnK,MAAM,EAAE,EAAE;EACVW,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBpE,OAAO,EAAE,CAAC;EACV0L,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnB3P,MAAM,EAAE2P,SAAS;EACjB9Q,OAAO,EAAE,CAAC;EACV4L,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnB1P,MAAM,EAAE0P,SAAS;EACjBI,CAAC,EAAE;AACL,CAAC;AAEDwI,eAAe,CAACvI,aAAa,GAAG;EAC9BtN,WAAW,EAAE,OAAO;EACpBsB,eAAe,EAAE;AACnB,CAAC;AAED,MAAMwU,iBAAiB,SAAS9iB,OAAO,CAAC;EAEtC8C,OAAOA,CAAC+W,MAAM,EAAEC,MAAM,EAAEtX,IAAI,EAAEmE,gBAAgB,EAAE;IAC9C,IAAInE,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChC,OAAO,IAAI,CAACH,OAAO,CAACwD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC6P,QAAQ,CAAChR,MAAM,GAAG,CAAC,IAAIqe,gBAAgB,CAAC,IAAI,CAACrN,QAAQ,EAAEmE,MAAM,EAAEC,MAAM,EAAEnT,gBAAgB,CAAC;IACpI;IACA,MAAMyL,YAAY,GAAGpO,OAAO,CAAC;MAACzB,CAAC,EAAEsX,MAAM;MAAEpX,CAAC,EAAEqX;IAAM,CAAC,EAAE,IAAI,CAACvW,cAAc,CAACoD,gBAAgB,CAAC,EAAErG,SAAS,CAAC,CAAC,IAAI,CAAC+B,OAAO,CAACmE,QAAQ,CAAC,CAAC;IAC9H,MAAMwc,UAAU,GAAG,IAAI,CAACtN,QAAQ,CAACzL,GAAG,CAAElI,KAAK,IAAKS,IAAI,KAAK,GAAG,GAAGT,KAAK,CAACkhB,EAAE,GAAGlhB,KAAK,CAACmhB,EAAE,CAAC;IACnF,MAAM7d,KAAK,GAAGlB,IAAI,CAACY,GAAG,CAAC,GAAGie,UAAU,CAAC;IACrC,MAAM1d,GAAG,GAAGnB,IAAI,CAACa,GAAG,CAAC,GAAGge,UAAU,CAAC;IACnC,OAAO5Q,YAAY,CAAC5P,IAAI,CAAC,IAAI6C,KAAK,IAAI+M,YAAY,CAAC5P,IAAI,CAAC,IAAI8C,GAAG;EACjE;EAEA/B,cAAcA,CAACoD,gBAAgB,EAAE;IAC/B,OAAOD,qBAAqB,CAAC,IAAI,EAAEC,gBAAgB,CAAC;EACtD;EAEAoT,IAAIA,CAAC5N,GAAG,EAAE;IACR,MAAM;MAACuJ,QAAQ;MAAErT;IAAO,CAAC,GAAG,IAAI;IAChC8J,GAAG,CAACgC,IAAI,CAAC,CAAC;IACVhC,GAAG,CAACoC,SAAS,CAAC,CAAC;IACfpC,GAAG,CAACkC,SAAS,GAAGhM,OAAO,CAACiM,eAAe;IACvCrB,cAAc,CAACd,GAAG,EAAE9J,OAAO,CAAC;IAC5B,MAAM+L,MAAM,GAAG/B,cAAc,CAACF,GAAG,EAAE9J,OAAO,CAAC;IAC3C,IAAI8gB,KAAK,GAAG,IAAI;IAChB,KAAK,MAAMC,EAAE,IAAI1N,QAAQ,EAAE;MACzB,IAAIyN,KAAK,EAAE;QACThX,GAAG,CAACoE,MAAM,CAAC6S,EAAE,CAAC7gB,CAAC,EAAE6gB,EAAE,CAAC3gB,CAAC,CAAC;QACtB0gB,KAAK,GAAG,KAAK;MACf,CAAC,MAAM;QACLhX,GAAG,CAACqE,MAAM,CAAC4S,EAAE,CAAC7gB,CAAC,EAAE6gB,EAAE,CAAC3gB,CAAC,CAAC;MACxB;IACF;IACA0J,GAAG,CAACwC,SAAS,CAAC,CAAC;IACfxC,GAAG,CAACyC,IAAI,CAAC,CAAC;IACV;IACA,IAAIR,MAAM,EAAE;MACVjC,GAAG,CAACe,WAAW,GAAG7K,OAAO,CAACwM,iBAAiB;MAC3C1C,GAAG,CAACiC,MAAM,CAAC,CAAC;IACd;IACAjC,GAAG,CAAC2C,OAAO,CAAC,CAAC;EACf;EAEAkL,wBAAwBA,CAACtP,KAAK,EAAErI,OAAO,EAAE;IACvC,MAAM8F,UAAU,GAAG6M,sBAAsB,CAACtK,KAAK,EAAErI,OAAO,CAAC;IACzD,MAAM;MAACghB,KAAK;MAAE7c;IAAQ,CAAC,GAAGnE,OAAO;IACjC,MAAMqT,QAAQ,GAAG,EAAE;IACnB,MAAMzR,KAAK,GAAI,CAAC,GAAGhD,EAAE,GAAIoiB,KAAK;IAC9B,IAAIpT,GAAG,GAAGzJ,QAAQ,GAAGlF,WAAW;IAChC,KAAK,IAAI+F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgc,KAAK,EAAEhc,CAAC,EAAE,EAAE4I,GAAG,IAAIhM,KAAK,EAAE;MAC5C,MAAMqf,OAAO,GAAGC,iBAAiB,CAACpb,UAAU,EAAE9F,OAAO,EAAE4N,GAAG,CAAC;MAC3DqT,OAAO,CAAC7N,cAAc,GAAGhL,uBAAuB,CAACC,KAAK,EAAEvC,UAAU,EAAE9F,OAAO,CAAC;MAC5EqT,QAAQ,CAAChS,IAAI,CAAC4f,OAAO,CAAC;IACxB;IACAnb,UAAU,CAACuN,QAAQ,GAAGA,QAAQ;IAC9B,OAAOvN,UAAU;EACnB;AACF;AAEA2a,iBAAiB,CAACjP,EAAE,GAAG,mBAAmB;AAE1CiP,iBAAiB,CAAC5iB,QAAQ,GAAG;EAC3BqY,gBAAgB,EAAE,IAAI;EACtBpL,qBAAqB,EAAE,aAAa;EACpCZ,cAAc,EAAE,MAAM;EACtBE,UAAU,EAAE,EAAE;EACdE,gBAAgB,EAAE,CAAC;EACnBE,eAAe,EAAE,OAAO;EACxBgC,iBAAiB,EAAE,aAAa;EAChC3I,WAAW,EAAE,CAAC;EACdwL,OAAO,EAAE,IAAI;EACbvL,YAAY,EAAE,CAAC;EACfyE,IAAI,EAAEqP,SAAS;EACflY,KAAK,EAAE;IACL8D,MAAM,EAAE;EACV,CAAC;EACDA,MAAM,EAAE,EAAE;EACVW,QAAQ,EAAE,CAAC;EACX4G,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChB+V,KAAK,EAAE,CAAC;EACRna,OAAO,EAAE,CAAC;EACV0L,IAAI,EAAEqF,SAAS;EACftF,IAAI,EAAEsF,SAAS;EACfnH,QAAQ,EAAEmH,SAAS;EACnB3P,MAAM,EAAE2P,SAAS;EACjB9Q,OAAO,EAAE,CAAC;EACV4L,IAAI,EAAEkF,SAAS;EACfnF,IAAI,EAAEmF,SAAS;EACfhH,QAAQ,EAAEgH,SAAS;EACnB1P,MAAM,EAAE0P,SAAS;EACjBI,CAAC,EAAE;AACL,CAAC;AAEDyI,iBAAiB,CAACxI,aAAa,GAAG;EAChCtN,WAAW,EAAE,OAAO;EACpBsB,eAAe,EAAE;AACnB,CAAC;AAED,SAASiV,iBAAiBA,CAAC;EAAC3c,OAAO;EAAEC;AAAO,CAAC,EAAE;EAAChB,MAAM;EAAEK,WAAW;EAAEC;AAAY,CAAC,EAAE8J,GAAG,EAAE;EACvF,MAAM9K,OAAO,GAAG,CAACe,WAAW,GAAGC,YAAY,IAAI,CAAC;EAChD,MAAM/B,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC6L,GAAG,CAAC;EACzB,MAAM/L,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC+L,GAAG,CAAC;EACzB,MAAMlO,KAAK,GAAG;IAACQ,CAAC,EAAEqE,OAAO,GAAGxC,GAAG,GAAGyB,MAAM;IAAEpD,CAAC,EAAEoE,OAAO,GAAG3C,GAAG,GAAG2B;EAAM,CAAC;EACpE,OAAO;IACLwF,IAAI,EAAE,OAAO;IACbsK,WAAW,EAAE,OAAO;IACpBxN,UAAU,EAAE;MACV5F,CAAC,EAAER,KAAK,CAACQ,CAAC;MACVE,CAAC,EAAEV,KAAK,CAACU,CAAC;MACVmE,OAAO,EAAE7E,KAAK,CAACQ,CAAC;MAChBsE,OAAO,EAAE9E,KAAK,CAACU,CAAC;MAChBygB,EAAE,EAAEtc,OAAO,GAAGxC,GAAG,IAAIyB,MAAM,GAAGV,OAAO,CAAC;MACtC8d,EAAE,EAAEpc,OAAO,GAAG3C,GAAG,IAAI2B,MAAM,GAAGV,OAAO;IACvC;EACF,CAAC;AACH;AAEA,SAAS4d,gBAAgBA,CAACS,MAAM,EAAEjhB,CAAC,EAAEE,CAAC,EAAEkE,gBAAgB,EAAE;EACxD,IAAI8c,QAAQ,GAAG,KAAK;EACpB,IAAIC,CAAC,GAAGF,MAAM,CAACA,MAAM,CAAC9e,MAAM,GAAG,CAAC,CAAC,CAACoC,QAAQ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;EAC1E,KAAK,MAAM5E,KAAK,IAAIyhB,MAAM,EAAE;IAC1B,MAAMG,CAAC,GAAG5hB,KAAK,CAAC+E,QAAQ,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAEH,gBAAgB,CAAC;IACxD,IAAKgd,CAAC,CAACV,EAAE,GAAGxgB,CAAC,KAAOihB,CAAC,CAACT,EAAE,GAAGxgB,CAAE,IAAIF,CAAC,GAAG,CAACmhB,CAAC,CAACR,EAAE,GAAGS,CAAC,CAACT,EAAE,KAAKzgB,CAAC,GAAGkhB,CAAC,CAACV,EAAE,CAAC,IAAIS,CAAC,CAACT,EAAE,GAAGU,CAAC,CAACV,EAAE,CAAC,GAAGU,CAAC,CAACT,EAAE,EAAE;MACtFO,QAAQ,GAAG,CAACA,QAAQ;IACtB;IACAC,CAAC,GAAGC,CAAC;EACP;EACA,OAAOF,QAAQ;AACjB;AAEA,MAAMG,eAAe,GAAG;EACtB1b,GAAG,EAAE0R,aAAa;EAClBxR,aAAa,EAAEqS,uBAAuB;EACtCpS,OAAO,EAAEia,iBAAiB;EAC1Bha,KAAK,EAAEuU,eAAe;EACtBtU,IAAI,EAAE2V,cAAc;EACpBnc,KAAK,EAAE8gB,eAAe;EACtBra,OAAO,EAAEsa;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACApd,MAAM,CAACC,IAAI,CAACie,eAAe,CAAC,CAACzY,OAAO,CAAC1F,GAAG,IAAI;EAC1CvF,QAAQ,CAAC2jB,QAAQ,CAAC,YAAYD,eAAe,CAACne,GAAG,CAAC,CAACoO,EAAE,EAAE,EAAE;IACvD2G,SAAS,EAAE;EACb,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAMsJ,aAAa,GAAG;EACpBC,MAAM,EAAEre,MAAM,CAACka;AACjB,CAAC;AAED,MAAMoE,OAAO,GAAGrN,UAAU,CAACC,MAAM,CAACsB,YAAY,CAAC;AAC/C,MAAM+L,OAAO,GAAGA,CAAC7e,KAAK,EAAE8e,OAAO,KAAK3jB,QAAQ,CAAC2jB,OAAO,CAAC,GAAGC,UAAU,CAAC/e,KAAK,EAAE8e,OAAO,CAAC,GAAG9e,KAAK;;AAG1F;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAMgf,WAAW,GAAI/K,IAAI,IAAKA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,MAAM;;AAEjE;AACA;AACA;AACA;AACA;AACA,SAASgL,WAAWA,CAAChZ,IAAI,GAAG,MAAM,EAAE;EAClC,IAAIuY,eAAe,CAACvY,IAAI,CAAC,EAAE;IACzB,OAAOA,IAAI;EACb;EACA8N,OAAO,CAACC,IAAI,CAAC,6BAA6B/N,IAAI,yBAAyB,CAAC;EACxE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiZ,cAAcA,CAAC5Z,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,EAAEM,IAAI,EAAE;EACnD,MAAM4hB,UAAU,GAAGC,iBAAiB,CAAC9Z,KAAK,EAAErI,OAAO,CAACkiB,UAAU,EAAE5hB,IAAI,CAAC;EAErE,MAAMuU,WAAW,GAAGJ,KAAK,CAACI,WAAW;EACrC,MAAMxB,QAAQ,GAAG+O,cAAc,CAAC3N,KAAK,CAACpB,QAAQ,EAAEwB,WAAW,CAAC;EAE5D,KAAK,IAAI7P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6P,WAAW,CAACxS,MAAM,EAAE2C,CAAC,EAAE,EAAE;IAC3C,MAAMqd,iBAAiB,GAAGxN,WAAW,CAAC7P,CAAC,CAAC;IACxC,MAAMxE,OAAO,GAAG8hB,kBAAkB,CAACjP,QAAQ,EAAErO,CAAC,EAAEqd,iBAAiB,CAACrZ,IAAI,CAAC;IACvE,MAAMuZ,QAAQ,GAAGF,iBAAiB,CAACG,UAAU,CAACC,UAAU,CAACpa,KAAK,EAAE7H,OAAO,EAAE6S,QAAQ,EAAEgP,iBAAiB,CAAC,CAAC;IACtG,MAAMvc,UAAU,GAAGtF,OAAO,CAACmX,wBAAwB,CAACtP,KAAK,EAAEka,QAAQ,CAAC;IAEpEzc,UAAU,CAAC4c,IAAI,GAAGC,MAAM,CAAC7c,UAAU,CAAC;IAEpC,IAAI,UAAU,IAAIA,UAAU,EAAE;MAC5B8c,iBAAiB,CAACpiB,OAAO,EAAEsF,UAAU,CAACuN,QAAQ,EAAEkP,QAAQ,EAAEL,UAAU,CAAC;MACrE;MACA;MACA,OAAOpc,UAAU,CAACuN,QAAQ;IAC5B;IAEA,IAAI,CAACjV,OAAO,CAACoC,OAAO,CAACN,CAAC,CAAC,EAAE;MACvB;MACA;MACA;MACA;MACAmD,MAAM,CAACka,MAAM,CAAC/c,OAAO,EAAEsF,UAAU,CAAC;IACpC;IAEAzC,MAAM,CAACka,MAAM,CAAC/c,OAAO,EAAEsF,UAAU,CAACsN,cAAc,CAAC;IACjDtN,UAAU,CAAC9F,OAAO,GAAG6iB,wBAAwB,CAACN,QAAQ,CAAC;IAEvDL,UAAU,CAACR,MAAM,CAAClhB,OAAO,EAAEsF,UAAU,CAAC;EACxC;AACF;AAEA,SAAS6c,MAAMA,CAAC7c,UAAU,EAAE;EAC1B,OAAOuD,KAAK,CAACvD,UAAU,CAAC5F,CAAC,CAAC,IAAImJ,KAAK,CAACvD,UAAU,CAAC1F,CAAC,CAAC;AACnD;AAEA,SAAS+hB,iBAAiBA,CAAC9Z,KAAK,EAAEya,QAAQ,EAAExiB,IAAI,EAAE;EAChD,IAAIA,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,QAAQ,EAAE;IAC5D,OAAOmhB,aAAa;EACtB;EACA,OAAO,IAAI3jB,UAAU,CAACuK,KAAK,EAAEya,QAAQ,CAAC;AACxC;AAEA,SAASF,iBAAiBA,CAACG,WAAW,EAAE1P,QAAQ,EAAEkP,QAAQ,EAAEL,UAAU,EAAE;EACtE,MAAMc,WAAW,GAAGD,WAAW,CAAC1P,QAAQ,KAAK0P,WAAW,CAAC1P,QAAQ,GAAG,EAAE,CAAC;EACvE2P,WAAW,CAAC3gB,MAAM,GAAGgR,QAAQ,CAAChR,MAAM;EACpC,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,QAAQ,CAAChR,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACxC,MAAMie,UAAU,GAAG5P,QAAQ,CAACrO,CAAC,CAAC;IAC9B,MAAMc,UAAU,GAAGmd,UAAU,CAACnd,UAAU;IACxC,MAAMod,UAAU,GAAGZ,kBAAkB,CAACU,WAAW,EAAEhe,CAAC,EAAEie,UAAU,CAACja,IAAI,EAAEia,UAAU,CAAC7P,cAAc,CAAC;IACjG,MAAM+P,WAAW,GAAGZ,QAAQ,CAACU,UAAU,CAAC3P,WAAW,CAAC,CAAC8P,QAAQ,CAACH,UAAU,CAAC;IACzEnd,UAAU,CAAC9F,OAAO,GAAG6iB,wBAAwB,CAACM,WAAW,CAAC;IAC1DjB,UAAU,CAACR,MAAM,CAACwB,UAAU,EAAEpd,UAAU,CAAC;EAC3C;AACF;AAEA,SAASwc,kBAAkBA,CAACjP,QAAQ,EAAE9C,KAAK,EAAEvH,IAAI,EAAEoK,cAAc,EAAE;EACjE,MAAMiQ,YAAY,GAAG9B,eAAe,CAACS,WAAW,CAAChZ,IAAI,CAAC,CAAC;EACvD,IAAIxI,OAAO,GAAG6S,QAAQ,CAAC9C,KAAK,CAAC;EAC7B,IAAI,CAAC/P,OAAO,IAAI,EAAEA,OAAO,YAAY6iB,YAAY,CAAC,EAAE;IAClD7iB,OAAO,GAAG6S,QAAQ,CAAC9C,KAAK,CAAC,GAAG,IAAI8S,YAAY,CAAC,CAAC;IAC9ChgB,MAAM,CAACka,MAAM,CAAC/c,OAAO,EAAE4S,cAAc,CAAC;EACxC;EACA,OAAO5S,OAAO;AAChB;AAEA,SAASqiB,wBAAwBA,CAACN,QAAQ,EAAE;EAC1C,MAAMc,YAAY,GAAG9B,eAAe,CAACS,WAAW,CAACO,QAAQ,CAACvZ,IAAI,CAAC,CAAC;EAChE,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBA,MAAM,CAACuI,EAAE,GAAG+Q,QAAQ,CAAC/Q,EAAE;EACvBvI,MAAM,CAACD,IAAI,GAAGuZ,QAAQ,CAACvZ,IAAI;EAC3BC,MAAM,CAAC4O,QAAQ,GAAG0K,QAAQ,CAAC1K,QAAQ;EACnCxU,MAAM,CAACka,MAAM,CAACtU,MAAM,EAClB6Y,UAAU,CAACS,QAAQ,EAAEc,YAAY,CAACxlB,QAAQ,CAAC,EAC3CikB,UAAU,CAACS,QAAQ,EAAEc,YAAY,CAACpL,aAAa,CAAC,CAAC;EACnD,KAAK,MAAMlP,IAAI,IAAI4Y,OAAO,EAAE;IAC1B1Y,MAAM,CAACF,IAAI,CAAC,GAAGwZ,QAAQ,CAACxZ,IAAI,CAAC;EAC/B;EACA,OAAOE,MAAM;AACf;AAEA,SAAS6Y,UAAUA,CAACS,QAAQ,EAAEe,IAAI,EAAE;EAClC,MAAMra,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAM+N,IAAI,IAAI3T,MAAM,CAACC,IAAI,CAACggB,IAAI,CAAC,EAAE;IACpC,MAAMzB,OAAO,GAAGyB,IAAI,CAACtM,IAAI,CAAC;IAC1B,MAAMjU,KAAK,GAAGwf,QAAQ,CAACvL,IAAI,CAAC;IAC5B,IAAI+K,WAAW,CAAC/K,IAAI,CAAC,IAAIzY,OAAO,CAACwE,KAAK,CAAC,EAAE;MACvCkG,MAAM,CAAC+N,IAAI,CAAC,GAAGjU,KAAK,CAAC6E,GAAG,CAAE4B,IAAI,IAAKoY,OAAO,CAACpY,IAAI,EAAEqY,OAAO,CAAC,CAAC;IAC5D,CAAC,MAAM;MACL5Y,MAAM,CAAC+N,IAAI,CAAC,GAAG4K,OAAO,CAAC7e,KAAK,EAAE8e,OAAO,CAAC;IACxC;EACF;EACA,OAAO5Y,MAAM;AACf;AAEA,SAASwZ,UAAUA,CAACpa,KAAK,EAAE7H,OAAO,EAAE6S,QAAQ,EAAEmD,UAAU,EAAE;EACxD,OAAOhW,OAAO,CAACoV,QAAQ,KAAKpV,OAAO,CAACoV,QAAQ,GAAGvS,MAAM,CAACka,MAAM,CAACla,MAAM,CAACkgB,MAAM,CAAClb,KAAK,CAACoa,UAAU,CAAC,CAAC,CAAC,EAAE;IAC9FjiB,OAAO;IACP,IAAI6S,QAAQA,CAAA,EAAG;MACb,OAAOA,QAAQ,CAACzS,MAAM,CAAEmgB,EAAE,IAAKA,EAAE,IAAIA,EAAE,CAAC/gB,OAAO,CAAC;IAClD,CAAC;IACDwR,EAAE,EAAEgF,UAAU,CAAChF,EAAE;IACjBxI,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;AACL;AAEA,SAASoZ,cAAcA,CAAC/O,QAAQ,EAAEwB,WAAW,EAAE;EAC7C,MAAMxG,KAAK,GAAGwG,WAAW,CAACxS,MAAM;EAChC,MAAMW,KAAK,GAAGqQ,QAAQ,CAAChR,MAAM;EAE7B,IAAIW,KAAK,GAAGqL,KAAK,EAAE;IACjB,MAAMmV,GAAG,GAAGnV,KAAK,GAAGrL,KAAK;IACzBqQ,QAAQ,CAACoQ,MAAM,CAACzgB,KAAK,EAAE,CAAC,EAAE,GAAG,IAAI0gB,KAAK,CAACF,GAAG,CAAC,CAAC;EAC9C,CAAC,MAAM,IAAIxgB,KAAK,GAAGqL,KAAK,EAAE;IACxBgF,QAAQ,CAACoQ,MAAM,CAACpV,KAAK,EAAErL,KAAK,GAAGqL,KAAK,CAAC;EACvC;EACA,OAAOgF,QAAQ;AACjB;AAEA,IAAIsQ,OAAO,GAAG,OAAO;AAErB,MAAMC,WAAW,GAAG,IAAIza,GAAG,CAAC,CAAC;AAC7B,MAAM0a,kBAAkB,GAAGrN,UAAU,IAAIA,UAAU,CAACxN,IAAI,KAAK,eAAe;AAC5E,MAAML,KAAK,GAAG2L,UAAU,CAACC,MAAM,CAACsB,YAAY,CAAC;AAE7C,IAAIW,UAAU,GAAG;EACfhF,EAAE,EAAE,YAAY;EAEhBmS,OAAO;EAEPG,cAAcA,CAAA,EAAG;IACfpf,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE3G,KAAK,CAAC4lB,OAAO,CAAC;EAClD,CAAC;EAEDI,aAAaA,CAAA,EAAG;IACdhmB,KAAK,CAACimB,QAAQ,CAACzC,eAAe,CAAC;EACjC,CAAC;EAED0C,eAAeA,CAAA,EAAG;IAChBlmB,KAAK,CAACmmB,UAAU,CAAC3C,eAAe,CAAC;EACnC,CAAC;EAED4C,UAAUA,CAAC9b,KAAK,EAAE;IAChBub,WAAW,CAAClY,GAAG,CAACrD,KAAK,EAAE;MACrBwM,WAAW,EAAE,EAAE;MACfxB,QAAQ,EAAE,EAAE;MACZ1T,eAAe,EAAE,EAAE;MACnBgV,SAAS,EAAE,CAAC,CAAC;MACbD,QAAQ,EAAE,KAAK;MACfE,YAAY,EAAE,KAAK;MACnBjM,KAAK,EAAE,CAAC,CAAC;MACToN,MAAM,EAAE,KAAK;MACbX,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EAEDgP,YAAYA,CAAC/b,KAAK,EAAEgc,IAAI,EAAErkB,OAAO,EAAE;IACjC,MAAMyU,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;IACpC,MAAMwM,WAAW,GAAGJ,KAAK,CAACI,WAAW,GAAG,EAAE;IAE1C,IAAIwN,iBAAiB,GAAGriB,OAAO,CAAC6U,WAAW;IAC3C,IAAI3W,QAAQ,CAACmkB,iBAAiB,CAAC,EAAE;MAC/Bhf,MAAM,CAACC,IAAI,CAAC+e,iBAAiB,CAAC,CAACvZ,OAAO,CAAC1F,GAAG,IAAI;QAC5C,MAAML,KAAK,GAAGsf,iBAAiB,CAACjf,GAAG,CAAC;QACpC,IAAIlF,QAAQ,CAAC6E,KAAK,CAAC,EAAE;UACnBA,KAAK,CAACyO,EAAE,GAAGpO,GAAG;UACdyR,WAAW,CAACxT,IAAI,CAAC0B,KAAK,CAAC;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIxE,OAAO,CAAC8jB,iBAAiB,CAAC,EAAE;MACrCxN,WAAW,CAACxT,IAAI,CAAC,GAAGghB,iBAAiB,CAAC;IACxC;IACA9L,kBAAkB,CAAC1B,WAAW,CAACjU,MAAM,CAACijB,kBAAkB,CAAC,EAAExb,KAAK,CAAC8I,MAAM,CAAC;EAC1E,CAAC;EAEDmT,eAAeA,CAACjc,KAAK,EAAEgc,IAAI,EAAE;IAC3B,MAAM5P,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;IACpC6N,gBAAgB,CAAC7N,KAAK,EAAEgc,IAAI,CAACvT,KAAK,EAAE2D,KAAK,CAACI,WAAW,CAACjU,MAAM,CAACijB,kBAAkB,CAAC,CAACjjB,MAAM,CAACW,CAAC,IAAIA,CAAC,CAAC8N,OAAO,IAAI9N,CAAC,CAAC2U,gBAAgB,CAAC,CAAC;EAChI,CAAC;EAEDqO,WAAWA,CAAClc,KAAK,EAAEgc,IAAI,EAAErkB,OAAO,EAAE;IAChC,MAAMyU,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;IACpCmM,eAAe,CAACnM,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,CAAC;IACtCiiB,cAAc,CAAC5Z,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,EAAEqkB,IAAI,CAAC/jB,IAAI,CAAC;IAChDmU,KAAK,CAAC9U,eAAe,GAAG8U,KAAK,CAACpB,QAAQ,CAACzS,MAAM,CAACmgB,EAAE,IAAI,CAACA,EAAE,CAAC2B,IAAI,IAAI3B,EAAE,CAAC/gB,OAAO,CAACqP,OAAO,CAAC;IACnFyG,WAAW,CAACzN,KAAK,EAAEoM,KAAK,EAAEzU,OAAO,CAAC;EACpC,CAAC;EAEDwkB,kBAAkBA,CAACnc,KAAK,EAAEoc,KAAK,EAAEzkB,OAAO,EAAE;IACxC0X,IAAI,CAACrP,KAAK,EAAE,oBAAoB,EAAErI,OAAO,CAAC0kB,IAAI,CAAC;EACjD,CAAC;EAEDC,iBAAiBA,CAACtc,KAAK,EAAEoc,KAAK,EAAEzkB,OAAO,EAAE;IACvC0X,IAAI,CAACrP,KAAK,EAAE,mBAAmB,EAAErI,OAAO,CAAC0kB,IAAI,CAAC;EAChD,CAAC;EAEDE,iBAAiBA,CAACvc,KAAK,EAAEoc,KAAK,EAAEzkB,OAAO,EAAE;IACvC0X,IAAI,CAACrP,KAAK,EAAEoc,KAAK,CAAClU,KAAK,EAAEvQ,OAAO,CAAC0kB,IAAI,CAAC;EACxC,CAAC;EAEDG,UAAUA,CAACxc,KAAK,EAAEoc,KAAK,EAAEzkB,OAAO,EAAE;IAChC0X,IAAI,CAACrP,KAAK,EAAE,YAAY,EAAErI,OAAO,CAAC0kB,IAAI,CAAC;EACzC,CAAC;EAEDI,SAASA,CAACzc,KAAK,EAAEoc,KAAK,EAAEzkB,OAAO,EAAE;IAC/B0X,IAAI,CAACrP,KAAK,EAAE,WAAW,EAAErI,OAAO,CAAC0kB,IAAI,CAAC;EACxC,CAAC;EAEDK,WAAWA,CAAC1c,KAAK,EAAEgc,IAAI,EAAErkB,OAAO,EAAE;IAChC,MAAMyU,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;IACpC,IAAI2M,WAAW,CAACP,KAAK,EAAE4P,IAAI,CAACzkB,KAAK,EAAEI,OAAO,CAAC,EAAE;MAC3CqkB,IAAI,CAAC/O,OAAO,GAAG,IAAI;IACrB;EACF,CAAC;EAED0P,YAAYA,CAAC3c,KAAK,EAAE;IAClBub,WAAW,CAACqB,MAAM,CAAC5c,KAAK,CAAC;EAC3B,CAAC;EAED6c,cAAcA,CAAC7c,KAAK,EAAE;IACpB,MAAMoM,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;IACpC,OAAOoM,KAAK,GAAGA,KAAK,CAACpB,QAAQ,GAAG,EAAE;EACpC,CAAC;EAED;EACA8R,oCAAoCA,CAACxlB,eAAe,EAAEC,KAAK,EAAEI,OAAO,EAAE;IACpE,OAAOK,WAAW,CAACV,eAAe,EAAEC,KAAK,EAAEI,OAAO,CAAC;EACrD,CAAC;EAEDnC,QAAQ,EAAE;IACRqkB,UAAU,EAAE;MACVkD,OAAO,EAAE;QACPtf,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;QACzGkD,IAAI,EAAE;MACR,CAAC;MACDmE,MAAM,EAAE;QACNrH,UAAU,EAAE,CAAC,iBAAiB,EAAE,aAAa,CAAC;QAC9CkD,IAAI,EAAE;MACR;IACF,CAAC;IACD0b,IAAI,EAAE,IAAI;IACVllB,WAAW,EAAE;MACXc,IAAI,EAAEsX,SAAS;MACfzX,IAAI,EAAEyX,SAAS;MACf9X,SAAS,EAAE8X;IACb,CAAC;IACDyN,MAAM,EAAE;MACNxN,QAAQ,EAAE,mBAAmB;MAC7BtP,IAAI,EAAE,KAAK;MACXtC,KAAK,EAAE,CACP;IACF;EACF,CAAC;EAEDiS,WAAW,EAAE;IACXoN,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAGvO,IAAI,IAAK,CAACrO,KAAK,CAACwH,QAAQ,CAAC6G,IAAI,CAAC,IAAIA,IAAI,KAAK,MAAM;IAC/DnC,WAAW,EAAE;MACX2Q,QAAQ,EAAE,KAAK;MACfrN,SAAS,EAAEA,CAACnB,IAAI,EAAEyO,IAAI,KAAK,YAAYlE,eAAe,CAACS,WAAW,CAACyD,IAAI,CAACzc,IAAI,CAAC,CAAC,CAACwI,EAAE;IACnF,CAAC;IACDhS,WAAW,EAAE;MACX2Y,SAAS,EAAE;IACb,CAAC;IACDkN,MAAM,EAAE;MACNpf,KAAK,EAAE;QACLqf,UAAU,EAAEvD,WAAW;QACvB5J,SAAS,EAAE;MACb,CAAC;MACDmN,UAAU,EAAEvD;IACd;EACF,CAAC;EAED2D,sBAAsB,EAAE,CAAC,EAAE;AAC7B,CAAC;AAED,SAAShO,IAAIA,CAACrP,KAAK,EAAEsd,MAAM,EAAEjB,IAAI,EAAE;EACjC,MAAM;IAAC5a,GAAG;IAAE8H;EAAS,CAAC,GAAGvJ,KAAK;EAC9B,MAAMoM,KAAK,GAAGmP,WAAW,CAAChY,GAAG,CAACvD,KAAK,CAAC;EAEpC,IAAIqc,IAAI,EAAE;IACRplB,QAAQ,CAACwK,GAAG,EAAE8H,SAAS,CAAC;EAC1B;EAEA,MAAMgU,gBAAgB,GAAGC,mBAAmB,CAACpR,KAAK,CAAC9U,eAAe,EAAEgmB,MAAM,CAAC,CAACrkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,OAAO,CAACR,OAAO,CAACgY,CAAC,GAAGxW,CAAC,CAAChB,OAAO,CAACR,OAAO,CAACgY,CAAC,CAAC;EACrI,KAAK,MAAMxO,IAAI,IAAIoc,gBAAgB,EAAE;IACnCE,WAAW,CAAChc,GAAG,EAAE8H,SAAS,EAAE6C,KAAK,EAAEjL,IAAI,CAAC;EAC1C;EAEA,IAAIkb,IAAI,EAAE;IACRnlB,UAAU,CAACuK,GAAG,CAAC;EACjB;AACF;AAEA,SAAS+b,mBAAmBA,CAACxS,QAAQ,EAAEsS,MAAM,EAAE;EAC7C,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,KAAK,MAAM7E,EAAE,IAAI1N,QAAQ,EAAE;IACzB,IAAI0N,EAAE,CAAC/gB,OAAO,CAAC6X,QAAQ,KAAK8N,MAAM,EAAE;MAClCC,gBAAgB,CAACvkB,IAAI,CAAC;QAACb,OAAO,EAAEugB,EAAE;QAAEgF,IAAI,EAAE;MAAI,CAAC,CAAC;IAClD;IACA,IAAIhF,EAAE,CAAC1N,QAAQ,IAAI0N,EAAE,CAAC1N,QAAQ,CAAChR,MAAM,EAAE;MACrC,KAAK,MAAM2jB,GAAG,IAAIjF,EAAE,CAAC1N,QAAQ,EAAE;QAC7B,IAAI2S,GAAG,CAAChmB,OAAO,CAACqP,OAAO,IAAI2W,GAAG,CAAChmB,OAAO,CAAC6X,QAAQ,KAAK8N,MAAM,EAAE;UAC1DC,gBAAgB,CAACvkB,IAAI,CAAC;YAACb,OAAO,EAAEwlB;UAAG,CAAC,CAAC;QACvC;MACF;IACF;EACF;EACA,OAAOJ,gBAAgB;AACzB;AAEA,SAASE,WAAWA,CAAChc,GAAG,EAAE8H,SAAS,EAAE6C,KAAK,EAAEjL,IAAI,EAAE;EAChD,MAAMuX,EAAE,GAAGvX,IAAI,CAAChJ,OAAO;EACvB,IAAIgJ,IAAI,CAACuc,IAAI,EAAE;IACb/P,UAAU,CAACvB,KAAK,EAAEsM,EAAE,EAAE,YAAY,CAAC;IACnCA,EAAE,CAACrJ,IAAI,CAAC5N,GAAG,EAAE8H,SAAS,CAAC;IACvBoE,UAAU,CAACvB,KAAK,EAAEsM,EAAE,EAAE,WAAW,CAAC;EACpC,CAAC,MAAM;IACLA,EAAE,CAACrJ,IAAI,CAAC5N,GAAG,EAAE8H,SAAS,CAAC;EACzB;AACF;AAEA,SAAS4E,UAAU,IAAIyP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}