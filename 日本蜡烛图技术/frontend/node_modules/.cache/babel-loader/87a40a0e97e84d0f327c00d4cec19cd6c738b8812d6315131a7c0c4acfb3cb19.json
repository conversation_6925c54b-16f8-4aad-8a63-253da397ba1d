{"ast": null, "code": "import { integerBetween, isLeapYear, timeObject, daysInYear, daysInMonth, weeksInWeekYear, isInteger, isUndefined } from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\"unit out of range\", `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`);\n}\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n  const js = d.getUTCDay();\n  return js === 0 ? 7 : js;\n}\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex(i => i < ordinal),\n    day = ordinal - table[month0];\n  return {\n    month: month0 + 1,\n    day\n  };\n}\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return (isoWeekday - startOfWeek + 7) % 7 + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const {\n      year,\n      month,\n      day\n    } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n  return {\n    weekYear,\n    weekNumber,\n    weekday,\n    ...timeObject(gregObj)\n  };\n}\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const {\n      weekYear,\n      weekNumber,\n      weekday\n    } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n  const {\n    month,\n    day\n  } = uncomputeOrdinal(year, ordinal);\n  return {\n    year,\n    month,\n    day,\n    ...timeObject(weekData)\n  };\n}\nexport function gregorianToOrdinal(gregData) {\n  const {\n    year,\n    month,\n    day\n  } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return {\n    year,\n    ordinal,\n    ...timeObject(gregData)\n  };\n}\nexport function ordinalToGregorian(ordinalData) {\n  const {\n    year,\n    ordinal\n  } = ordinalData;\n  const {\n    month,\n    day\n  } = uncomputeOrdinal(year, ordinal);\n  return {\n    year,\n    month,\n    day,\n    ...timeObject(ordinalData)\n  };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData = !isUndefined(obj.localWeekday) || !isUndefined(obj.localWeekNumber) || !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData = !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\"Cannot mix locale-based week fields with ISO-based week fields\");\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek()\n    };\n  } else {\n    return {\n      minDaysInFirstWeek: 4,\n      startOfWeek: 1\n    };\n  }\n}\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(obj.weekNumber, 1, weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\nexport function hasInvalidTimeData(obj) {\n  const {\n    hour,\n    minute,\n    second,\n    millisecond\n  } = obj;\n  const validHour = integerBetween(hour, 0, 23) || hour === 24 && minute === 0 && second === 0 && millisecond === 0,\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}", "map": {"version": 3, "names": ["integerBetween", "isLeapYear", "timeObject", "daysInYear", "daysInMonth", "weeksInWeekYear", "isInteger", "isUndefined", "Invalid", "ConflictingSpecificationError", "nonLeapLadder", "<PERSON><PERSON><PERSON><PERSON>", "unitOutOfRange", "unit", "value", "dayOfWeek", "year", "month", "day", "d", "Date", "UTC", "setUTCFullYear", "getUTCFullYear", "js", "getUTCDay", "computeOrdinal", "uncomputeOrdinal", "ordinal", "table", "month0", "findIndex", "i", "isoWeekdayToLocal", "isoWeekday", "startOfWeek", "gregorianToWeek", "greg<PERSON><PERSON><PERSON>", "minDaysInFirstWeek", "weekday", "weekNumber", "Math", "floor", "weekYear", "weekT<PERSON><PERSON><PERSON><PERSON><PERSON>", "weekData", "weekdayOfJan4", "yearInDays", "gregorianToOrdinal", "gregData", "ordinalToGregorian", "ordinalData", "usesLocalWeekValues", "obj", "loc", "hasLocaleWeekData", "localWeekday", "localWeekNumber", "localWeekYear", "hasIsoWeekData", "getMinDaysInFirstWeek", "getStartOfWeek", "hasInvalidWeekData", "validYear", "validWeek", "validWeekday", "hasInvalidOrdinalData", "validOrdinal", "hasInvalidGregorianData", "valid<PERSON><PERSON><PERSON>", "validDay", "hasInvalidTimeData", "hour", "minute", "second", "millisecond", "validHour", "validMinute", "validSecond", "validMillisecond"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/impl/conversions.js"], "sourcesContent": ["import {\n  integerBetween,\n  isLeapYear,\n  timeObject,\n  daysInYear,\n  daysInMonth,\n  weeksInWeekYear,\n  isInteger,\n  isUndefined,\n} from \"./util.js\";\nimport Invalid from \"./invalid.js\";\nimport { ConflictingSpecificationError } from \"../errors.js\";\n\nconst nonLeapLadder = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334],\n  leapLadder = [0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335];\n\nfunction unitOutOfRange(unit, value) {\n  return new Invalid(\n    \"unit out of range\",\n    `you specified ${value} (of type ${typeof value}) as a ${unit}, which is invalid`\n  );\n}\n\nexport function dayOfWeek(year, month, day) {\n  const d = new Date(Date.UTC(year, month - 1, day));\n\n  if (year < 100 && year >= 0) {\n    d.setUTCFullYear(d.getUTCFullYear() - 1900);\n  }\n\n  const js = d.getUTCDay();\n\n  return js === 0 ? 7 : js;\n}\n\nfunction computeOrdinal(year, month, day) {\n  return day + (isLeapYear(year) ? leapLadder : nonLeapLadder)[month - 1];\n}\n\nfunction uncomputeOrdinal(year, ordinal) {\n  const table = isLeapYear(year) ? leapLadder : nonLeapLadder,\n    month0 = table.findIndex((i) => i < ordinal),\n    day = ordinal - table[month0];\n  return { month: month0 + 1, day };\n}\n\nexport function isoWeekdayToLocal(isoWeekday, startOfWeek) {\n  return ((isoWeekday - startOfWeek + 7) % 7) + 1;\n}\n\n/**\n * @private\n */\n\nexport function gregorianToWeek(gregObj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { year, month, day } = gregObj,\n    ordinal = computeOrdinal(year, month, day),\n    weekday = isoWeekdayToLocal(dayOfWeek(year, month, day), startOfWeek);\n\n  let weekNumber = Math.floor((ordinal - weekday + 14 - minDaysInFirstWeek) / 7),\n    weekYear;\n\n  if (weekNumber < 1) {\n    weekYear = year - 1;\n    weekNumber = weeksInWeekYear(weekYear, minDaysInFirstWeek, startOfWeek);\n  } else if (weekNumber > weeksInWeekYear(year, minDaysInFirstWeek, startOfWeek)) {\n    weekYear = year + 1;\n    weekNumber = 1;\n  } else {\n    weekYear = year;\n  }\n\n  return { weekYear, weekNumber, weekday, ...timeObject(gregObj) };\n}\n\nexport function weekToGregorian(weekData, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const { weekYear, weekNumber, weekday } = weekData,\n    weekdayOfJan4 = isoWeekdayToLocal(dayOfWeek(weekYear, 1, minDaysInFirstWeek), startOfWeek),\n    yearInDays = daysInYear(weekYear);\n\n  let ordinal = weekNumber * 7 + weekday - weekdayOfJan4 - 7 + minDaysInFirstWeek,\n    year;\n\n  if (ordinal < 1) {\n    year = weekYear - 1;\n    ordinal += daysInYear(year);\n  } else if (ordinal > yearInDays) {\n    year = weekYear + 1;\n    ordinal -= daysInYear(weekYear);\n  } else {\n    year = weekYear;\n  }\n\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(weekData) };\n}\n\nexport function gregorianToOrdinal(gregData) {\n  const { year, month, day } = gregData;\n  const ordinal = computeOrdinal(year, month, day);\n  return { year, ordinal, ...timeObject(gregData) };\n}\n\nexport function ordinalToGregorian(ordinalData) {\n  const { year, ordinal } = ordinalData;\n  const { month, day } = uncomputeOrdinal(year, ordinal);\n  return { year, month, day, ...timeObject(ordinalData) };\n}\n\n/**\n * Check if local week units like localWeekday are used in obj.\n * If so, validates that they are not mixed with ISO week units and then copies them to the normal week unit properties.\n * Modifies obj in-place!\n * @param obj the object values\n */\nexport function usesLocalWeekValues(obj, loc) {\n  const hasLocaleWeekData =\n    !isUndefined(obj.localWeekday) ||\n    !isUndefined(obj.localWeekNumber) ||\n    !isUndefined(obj.localWeekYear);\n  if (hasLocaleWeekData) {\n    const hasIsoWeekData =\n      !isUndefined(obj.weekday) || !isUndefined(obj.weekNumber) || !isUndefined(obj.weekYear);\n\n    if (hasIsoWeekData) {\n      throw new ConflictingSpecificationError(\n        \"Cannot mix locale-based week fields with ISO-based week fields\"\n      );\n    }\n    if (!isUndefined(obj.localWeekday)) obj.weekday = obj.localWeekday;\n    if (!isUndefined(obj.localWeekNumber)) obj.weekNumber = obj.localWeekNumber;\n    if (!isUndefined(obj.localWeekYear)) obj.weekYear = obj.localWeekYear;\n    delete obj.localWeekday;\n    delete obj.localWeekNumber;\n    delete obj.localWeekYear;\n    return {\n      minDaysInFirstWeek: loc.getMinDaysInFirstWeek(),\n      startOfWeek: loc.getStartOfWeek(),\n    };\n  } else {\n    return { minDaysInFirstWeek: 4, startOfWeek: 1 };\n  }\n}\n\nexport function hasInvalidWeekData(obj, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const validYear = isInteger(obj.weekYear),\n    validWeek = integerBetween(\n      obj.weekNumber,\n      1,\n      weeksInWeekYear(obj.weekYear, minDaysInFirstWeek, startOfWeek)\n    ),\n    validWeekday = integerBetween(obj.weekday, 1, 7);\n\n  if (!validYear) {\n    return unitOutOfRange(\"weekYear\", obj.weekYear);\n  } else if (!validWeek) {\n    return unitOutOfRange(\"week\", obj.weekNumber);\n  } else if (!validWeekday) {\n    return unitOutOfRange(\"weekday\", obj.weekday);\n  } else return false;\n}\n\nexport function hasInvalidOrdinalData(obj) {\n  const validYear = isInteger(obj.year),\n    validOrdinal = integerBetween(obj.ordinal, 1, daysInYear(obj.year));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validOrdinal) {\n    return unitOutOfRange(\"ordinal\", obj.ordinal);\n  } else return false;\n}\n\nexport function hasInvalidGregorianData(obj) {\n  const validYear = isInteger(obj.year),\n    validMonth = integerBetween(obj.month, 1, 12),\n    validDay = integerBetween(obj.day, 1, daysInMonth(obj.year, obj.month));\n\n  if (!validYear) {\n    return unitOutOfRange(\"year\", obj.year);\n  } else if (!validMonth) {\n    return unitOutOfRange(\"month\", obj.month);\n  } else if (!validDay) {\n    return unitOutOfRange(\"day\", obj.day);\n  } else return false;\n}\n\nexport function hasInvalidTimeData(obj) {\n  const { hour, minute, second, millisecond } = obj;\n  const validHour =\n      integerBetween(hour, 0, 23) ||\n      (hour === 24 && minute === 0 && second === 0 && millisecond === 0),\n    validMinute = integerBetween(minute, 0, 59),\n    validSecond = integerBetween(second, 0, 59),\n    validMillisecond = integerBetween(millisecond, 0, 999);\n\n  if (!validHour) {\n    return unitOutOfRange(\"hour\", hour);\n  } else if (!validMinute) {\n    return unitOutOfRange(\"minute\", minute);\n  } else if (!validSecond) {\n    return unitOutOfRange(\"second\", second);\n  } else if (!validMillisecond) {\n    return unitOutOfRange(\"millisecond\", millisecond);\n  } else return false;\n}\n"], "mappings": "AAAA,SACEA,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,eAAe,EACfC,SAAS,EACTC,WAAW,QACN,WAAW;AAClB,OAAOC,OAAO,MAAM,cAAc;AAClC,SAASC,6BAA6B,QAAQ,cAAc;AAE5D,MAAMC,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3EC,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAEtE,SAASC,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACnC,OAAO,IAAIN,OAAO,CAChB,mBAAmB,EACnB,iBAAiBM,KAAK,aAAa,OAAOA,KAAK,UAAUD,IAAI,oBAC/D,CAAC;AACH;AAEA,OAAO,SAASE,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC1C,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAACL,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,CAAC,CAAC;EAElD,IAAIF,IAAI,GAAG,GAAG,IAAIA,IAAI,IAAI,CAAC,EAAE;IAC3BG,CAAC,CAACG,cAAc,CAACH,CAAC,CAACI,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;EAC7C;EAEA,MAAMC,EAAE,GAAGL,CAAC,CAACM,SAAS,CAAC,CAAC;EAExB,OAAOD,EAAE,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;AAC1B;AAEA,SAASE,cAAcA,CAACV,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACxC,OAAOA,GAAG,GAAG,CAACjB,UAAU,CAACe,IAAI,CAAC,GAAGL,UAAU,GAAGD,aAAa,EAAEO,KAAK,GAAG,CAAC,CAAC;AACzE;AAEA,SAASU,gBAAgBA,CAACX,IAAI,EAAEY,OAAO,EAAE;EACvC,MAAMC,KAAK,GAAG5B,UAAU,CAACe,IAAI,CAAC,GAAGL,UAAU,GAAGD,aAAa;IACzDoB,MAAM,GAAGD,KAAK,CAACE,SAAS,CAAEC,CAAC,IAAKA,CAAC,GAAGJ,OAAO,CAAC;IAC5CV,GAAG,GAAGU,OAAO,GAAGC,KAAK,CAACC,MAAM,CAAC;EAC/B,OAAO;IAAEb,KAAK,EAAEa,MAAM,GAAG,CAAC;IAAEZ;EAAI,CAAC;AACnC;AAEA,OAAO,SAASe,iBAAiBA,CAACC,UAAU,EAAEC,WAAW,EAAE;EACzD,OAAQ,CAACD,UAAU,GAAGC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAI,CAAC;AACjD;;AAEA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAEC,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EAChF,MAAM;MAAEnB,IAAI;MAAEC,KAAK;MAAEC;IAAI,CAAC,GAAGmB,OAAO;IAClCT,OAAO,GAAGF,cAAc,CAACV,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;IAC1CqB,OAAO,GAAGN,iBAAiB,CAAClB,SAAS,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC,EAAEiB,WAAW,CAAC;EAEvE,IAAIK,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACd,OAAO,GAAGW,OAAO,GAAG,EAAE,GAAGD,kBAAkB,IAAI,CAAC,CAAC;IAC5EK,QAAQ;EAEV,IAAIH,UAAU,GAAG,CAAC,EAAE;IAClBG,QAAQ,GAAG3B,IAAI,GAAG,CAAC;IACnBwB,UAAU,GAAGnC,eAAe,CAACsC,QAAQ,EAAEL,kBAAkB,EAAEH,WAAW,CAAC;EACzE,CAAC,MAAM,IAAIK,UAAU,GAAGnC,eAAe,CAACW,IAAI,EAAEsB,kBAAkB,EAAEH,WAAW,CAAC,EAAE;IAC9EQ,QAAQ,GAAG3B,IAAI,GAAG,CAAC;IACnBwB,UAAU,GAAG,CAAC;EAChB,CAAC,MAAM;IACLG,QAAQ,GAAG3B,IAAI;EACjB;EAEA,OAAO;IAAE2B,QAAQ;IAAEH,UAAU;IAAED,OAAO;IAAE,GAAGrC,UAAU,CAACmC,OAAO;EAAE,CAAC;AAClE;AAEA,OAAO,SAASO,eAAeA,CAACC,QAAQ,EAAEP,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EACjF,MAAM;MAAEQ,QAAQ;MAAEH,UAAU;MAAED;IAAQ,CAAC,GAAGM,QAAQ;IAChDC,aAAa,GAAGb,iBAAiB,CAAClB,SAAS,CAAC4B,QAAQ,EAAE,CAAC,EAAEL,kBAAkB,CAAC,EAAEH,WAAW,CAAC;IAC1FY,UAAU,GAAG5C,UAAU,CAACwC,QAAQ,CAAC;EAEnC,IAAIf,OAAO,GAAGY,UAAU,GAAG,CAAC,GAAGD,OAAO,GAAGO,aAAa,GAAG,CAAC,GAAGR,kBAAkB;IAC7EtB,IAAI;EAEN,IAAIY,OAAO,GAAG,CAAC,EAAE;IACfZ,IAAI,GAAG2B,QAAQ,GAAG,CAAC;IACnBf,OAAO,IAAIzB,UAAU,CAACa,IAAI,CAAC;EAC7B,CAAC,MAAM,IAAIY,OAAO,GAAGmB,UAAU,EAAE;IAC/B/B,IAAI,GAAG2B,QAAQ,GAAG,CAAC;IACnBf,OAAO,IAAIzB,UAAU,CAACwC,QAAQ,CAAC;EACjC,CAAC,MAAM;IACL3B,IAAI,GAAG2B,QAAQ;EACjB;EAEA,MAAM;IAAE1B,KAAK;IAAEC;EAAI,CAAC,GAAGS,gBAAgB,CAACX,IAAI,EAAEY,OAAO,CAAC;EACtD,OAAO;IAAEZ,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAE,GAAGhB,UAAU,CAAC2C,QAAQ;EAAE,CAAC;AACtD;AAEA,OAAO,SAASG,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,MAAM;IAAEjC,IAAI;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAG+B,QAAQ;EACrC,MAAMrB,OAAO,GAAGF,cAAc,CAACV,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EAChD,OAAO;IAAEF,IAAI;IAAEY,OAAO;IAAE,GAAG1B,UAAU,CAAC+C,QAAQ;EAAE,CAAC;AACnD;AAEA,OAAO,SAASC,kBAAkBA,CAACC,WAAW,EAAE;EAC9C,MAAM;IAAEnC,IAAI;IAAEY;EAAQ,CAAC,GAAGuB,WAAW;EACrC,MAAM;IAAElC,KAAK;IAAEC;EAAI,CAAC,GAAGS,gBAAgB,CAACX,IAAI,EAAEY,OAAO,CAAC;EACtD,OAAO;IAAEZ,IAAI;IAAEC,KAAK;IAAEC,GAAG;IAAE,GAAGhB,UAAU,CAACiD,WAAW;EAAE,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAC5C,MAAMC,iBAAiB,GACrB,CAAChD,WAAW,CAAC8C,GAAG,CAACG,YAAY,CAAC,IAC9B,CAACjD,WAAW,CAAC8C,GAAG,CAACI,eAAe,CAAC,IACjC,CAAClD,WAAW,CAAC8C,GAAG,CAACK,aAAa,CAAC;EACjC,IAAIH,iBAAiB,EAAE;IACrB,MAAMI,cAAc,GAClB,CAACpD,WAAW,CAAC8C,GAAG,CAACd,OAAO,CAAC,IAAI,CAAChC,WAAW,CAAC8C,GAAG,CAACb,UAAU,CAAC,IAAI,CAACjC,WAAW,CAAC8C,GAAG,CAACV,QAAQ,CAAC;IAEzF,IAAIgB,cAAc,EAAE;MAClB,MAAM,IAAIlD,6BAA6B,CACrC,gEACF,CAAC;IACH;IACA,IAAI,CAACF,WAAW,CAAC8C,GAAG,CAACG,YAAY,CAAC,EAAEH,GAAG,CAACd,OAAO,GAAGc,GAAG,CAACG,YAAY;IAClE,IAAI,CAACjD,WAAW,CAAC8C,GAAG,CAACI,eAAe,CAAC,EAAEJ,GAAG,CAACb,UAAU,GAAGa,GAAG,CAACI,eAAe;IAC3E,IAAI,CAAClD,WAAW,CAAC8C,GAAG,CAACK,aAAa,CAAC,EAAEL,GAAG,CAACV,QAAQ,GAAGU,GAAG,CAACK,aAAa;IACrE,OAAOL,GAAG,CAACG,YAAY;IACvB,OAAOH,GAAG,CAACI,eAAe;IAC1B,OAAOJ,GAAG,CAACK,aAAa;IACxB,OAAO;MACLpB,kBAAkB,EAAEgB,GAAG,CAACM,qBAAqB,CAAC,CAAC;MAC/CzB,WAAW,EAAEmB,GAAG,CAACO,cAAc,CAAC;IAClC,CAAC;EACH,CAAC,MAAM;IACL,OAAO;MAAEvB,kBAAkB,EAAE,CAAC;MAAEH,WAAW,EAAE;IAAE,CAAC;EAClD;AACF;AAEA,OAAO,SAAS2B,kBAAkBA,CAACT,GAAG,EAAEf,kBAAkB,GAAG,CAAC,EAAEH,WAAW,GAAG,CAAC,EAAE;EAC/E,MAAM4B,SAAS,GAAGzD,SAAS,CAAC+C,GAAG,CAACV,QAAQ,CAAC;IACvCqB,SAAS,GAAGhE,cAAc,CACxBqD,GAAG,CAACb,UAAU,EACd,CAAC,EACDnC,eAAe,CAACgD,GAAG,CAACV,QAAQ,EAAEL,kBAAkB,EAAEH,WAAW,CAC/D,CAAC;IACD8B,YAAY,GAAGjE,cAAc,CAACqD,GAAG,CAACd,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;EAElD,IAAI,CAACwB,SAAS,EAAE;IACd,OAAOnD,cAAc,CAAC,UAAU,EAAEyC,GAAG,CAACV,QAAQ,CAAC;EACjD,CAAC,MAAM,IAAI,CAACqB,SAAS,EAAE;IACrB,OAAOpD,cAAc,CAAC,MAAM,EAAEyC,GAAG,CAACb,UAAU,CAAC;EAC/C,CAAC,MAAM,IAAI,CAACyB,YAAY,EAAE;IACxB,OAAOrD,cAAc,CAAC,SAAS,EAAEyC,GAAG,CAACd,OAAO,CAAC;EAC/C,CAAC,MAAM,OAAO,KAAK;AACrB;AAEA,OAAO,SAAS2B,qBAAqBA,CAACb,GAAG,EAAE;EACzC,MAAMU,SAAS,GAAGzD,SAAS,CAAC+C,GAAG,CAACrC,IAAI,CAAC;IACnCmD,YAAY,GAAGnE,cAAc,CAACqD,GAAG,CAACzB,OAAO,EAAE,CAAC,EAAEzB,UAAU,CAACkD,GAAG,CAACrC,IAAI,CAAC,CAAC;EAErE,IAAI,CAAC+C,SAAS,EAAE;IACd,OAAOnD,cAAc,CAAC,MAAM,EAAEyC,GAAG,CAACrC,IAAI,CAAC;EACzC,CAAC,MAAM,IAAI,CAACmD,YAAY,EAAE;IACxB,OAAOvD,cAAc,CAAC,SAAS,EAAEyC,GAAG,CAACzB,OAAO,CAAC;EAC/C,CAAC,MAAM,OAAO,KAAK;AACrB;AAEA,OAAO,SAASwC,uBAAuBA,CAACf,GAAG,EAAE;EAC3C,MAAMU,SAAS,GAAGzD,SAAS,CAAC+C,GAAG,CAACrC,IAAI,CAAC;IACnCqD,UAAU,GAAGrE,cAAc,CAACqD,GAAG,CAACpC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7CqD,QAAQ,GAAGtE,cAAc,CAACqD,GAAG,CAACnC,GAAG,EAAE,CAAC,EAAEd,WAAW,CAACiD,GAAG,CAACrC,IAAI,EAAEqC,GAAG,CAACpC,KAAK,CAAC,CAAC;EAEzE,IAAI,CAAC8C,SAAS,EAAE;IACd,OAAOnD,cAAc,CAAC,MAAM,EAAEyC,GAAG,CAACrC,IAAI,CAAC;EACzC,CAAC,MAAM,IAAI,CAACqD,UAAU,EAAE;IACtB,OAAOzD,cAAc,CAAC,OAAO,EAAEyC,GAAG,CAACpC,KAAK,CAAC;EAC3C,CAAC,MAAM,IAAI,CAACqD,QAAQ,EAAE;IACpB,OAAO1D,cAAc,CAAC,KAAK,EAAEyC,GAAG,CAACnC,GAAG,CAAC;EACvC,CAAC,MAAM,OAAO,KAAK;AACrB;AAEA,OAAO,SAASqD,kBAAkBA,CAAClB,GAAG,EAAE;EACtC,MAAM;IAAEmB,IAAI;IAAEC,MAAM;IAAEC,MAAM;IAAEC;EAAY,CAAC,GAAGtB,GAAG;EACjD,MAAMuB,SAAS,GACX5E,cAAc,CAACwE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAC1BA,IAAI,KAAK,EAAE,IAAIC,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,IAAIC,WAAW,KAAK,CAAE;IACpEE,WAAW,GAAG7E,cAAc,CAACyE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3CK,WAAW,GAAG9E,cAAc,CAAC0E,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3CK,gBAAgB,GAAG/E,cAAc,CAAC2E,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC;EAExD,IAAI,CAACC,SAAS,EAAE;IACd,OAAOhE,cAAc,CAAC,MAAM,EAAE4D,IAAI,CAAC;EACrC,CAAC,MAAM,IAAI,CAACK,WAAW,EAAE;IACvB,OAAOjE,cAAc,CAAC,QAAQ,EAAE6D,MAAM,CAAC;EACzC,CAAC,MAAM,IAAI,CAACK,WAAW,EAAE;IACvB,OAAOlE,cAAc,CAAC,QAAQ,EAAE8D,MAAM,CAAC;EACzC,CAAC,MAAM,IAAI,CAACK,gBAAgB,EAAE;IAC5B,OAAOnE,cAAc,CAAC,aAAa,EAAE+D,WAAW,CAAC;EACnD,CAAC,MAAM,OAAO,KAAK;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}