{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/\\u65E5\\u672C\\u8721\\u70DB\\u56FE\\u6280\\u672F/frontend/src/components/CandlestickChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement);\nconst {\n  Option\n} = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\nconst CandlestickChart = ({\n  data,\n  patterns = []\n}) => {\n  _s();\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    const filteredPatterns = selectedPattern === 'all' ? patterns : patterns.filter(p => p.pattern_name === selectedPattern);\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n\n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n      if (!startData || !endData) return null;\n      const color = getPatternColor(pattern.signal);\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = signal => {\n    switch (signal) {\n      case 'bullish':\n        return '#52c41a';\n      case 'bearish':\n        return '#ff4d4f';\n      case 'neutral':\n        return '#faad14';\n      default:\n        return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({\n              x: d.x,\n              y: d.c\n            })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [{\n          label: '日本蜡烛图',\n          data: candleData,\n          // 经典黑白风格配置\n          color: {\n            up: '#000000',\n            // 阳线边框：黑色\n            down: '#000000',\n            // 阴线边框：黑色\n            unchanged: '#000000'\n          },\n          backgroundColor: {\n            up: '#FFFFFF',\n            // 阳线填充：白色\n            down: '#000000',\n            // 阴线填充：黑色\n            unchanged: '#FFFFFF'\n          },\n          borderColor: {\n            up: '#000000',\n            down: '#000000',\n            unchanged: '#000000'\n          },\n          borderWidth: 1.5,\n          // 蜡烛图样式\n          candlestick: {\n            bodyWidth: 0.8,\n            wickWidth: 1\n          }\n        }]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd'\n              }\n            },\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              },\n              callback: function (value) {\n                return value.toFixed(2);\n              }\n            }\n          }\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: '#333333',\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: context => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ? new Date(candle.timestamp).toLocaleDateString('zh-CN') : `第 ${dataIndex + 1} 根K线`;\n              },\n              label: context => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [`开盘: ${candle.o.toFixed(2)}`, `最高: ${candle.h.toFixed(2)}`, `最低: ${candle.l.toFixed(2)}`, `收盘: ${candle.c.toFixed(2)}`, `成交量: ${candle.v.toLocaleString()}`];\n              }\n            }\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    chartInstance.current = new Chart(ctx, config);\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n  if (!data || data.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u6682\\u65E0\\u6570\\u636E\\uFF0C\\u8BF7\\u5148\\u4E0A\\u4F20\\u8721\\u70DB\\u56FE\\u6570\\u636E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 16,\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u56FE\\u8868\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: chartType,\n            onChange: setChartType,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"candlestick\",\n              children: \"\\u8721\\u70DB\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"line\",\n              children: \"\\u6298\\u7EBF\\u56FE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u663E\\u793A\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showPatterns,\n            onChange: setShowPatterns,\n            checkedChildren: \"\\u5F00\",\n            unCheckedChildren: \"\\u5173\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), showPatterns && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u5F62\\u6001\\u8FC7\\u6EE4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedPattern,\n            onChange: setSelectedPattern,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u5168\\u90E8\\u5F62\\u6001\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), getUniquePatterns().map(pattern => /*#__PURE__*/_jsxDEV(Option, {\n              value: pattern,\n              children: pattern\n            }, pattern, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u6570\\u636E\\u70B9\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"blue\",\n            children: data.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this), patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginRight: 8\n            },\n            children: \"\\u8BC6\\u522B\\u5F62\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"green\",\n            children: patterns.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '500px',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: chartRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), showPatterns && patterns && patterns.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginRight: 16\n          },\n          children: \"\\u5F62\\u6001\\u56FE\\u4F8B:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this), getUniquePatterns().map(patternName => {\n          const pattern = patterns.find(p => p.pattern_name === patternName);\n          if (!pattern) return null;\n          const color = getPatternColor(pattern.signal);\n          return /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: pattern.description,\n            children: /*#__PURE__*/_jsxDEV(Tag, {\n              color: pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange',\n              children: patternName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this)\n          }, patternName, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 17\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 321,\n    columnNumber: 5\n  }, this);\n};\n_s(CandlestickChart, \"e/7/0Cw5CRvQSoAkveSHJuxV6s4=\");\n_c = CandlestickChart;\nexport default CandlestickChart;\nvar _c;\n$RefreshReg$(_c, \"CandlestickChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "Select", "Switch", "<PERSON><PERSON><PERSON>", "Tag", "<PERSON><PERSON>", "Chart", "registerables", "CandlestickController", "CandlestickElement", "OhlcController", "OhlcElement", "jsxDEV", "_jsxDEV", "register", "Option", "PATTERN_NAMES", "CandlestickChart", "data", "patterns", "_s", "chartRef", "chartInstance", "showPatterns", "setShowPatterns", "selectedPatt<PERSON>", "setSelectedPattern", "chartType", "setChartType", "classicStyle", "setClassicStyle", "processCandleData", "length", "map", "candle", "index", "x", "timestamp", "Date", "getTime", "o", "parseFloat", "open", "h", "high", "l", "low", "c", "close", "v", "volume", "processPatternAnnotations", "filteredPatterns", "filter", "p", "pattern_name", "pattern", "startIndex", "start_index", "endIndex", "end_index", "startData", "endData", "color", "getPatternColor", "signal", "type", "xMin", "xMax", "yMin", "Math", "min", "yMax", "max", "backgroundColor", "borderColor", "borderWidth", "label", "content", "confidence", "toFixed", "enabled", "position", "Boolean", "createChartConfig", "candleData", "annotations", "datasets", "d", "y", "fill", "tension", "options", "responsive", "maintainAspectRatio", "scales", "time", "unit", "beginAtZero", "plugins", "legend", "display", "annotation", "up", "down", "unchanged", "candlestick", "bodyWidth", "wick<PERSON>idth", "interaction", "intersect", "mode", "displayFormats", "day", "grid", "lineWidth", "ticks", "font", "size", "callback", "value", "labels", "weight", "tooltip", "titleColor", "bodyColor", "callbacks", "title", "context", "dataIndex", "toLocaleDateString", "toLocaleString", "current", "destroy", "ctx", "getContext", "config", "getUniquePatterns", "uniqueNames", "Set", "sort", "children", "style", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "alignItems", "gap", "flexWrap", "marginRight", "onChange", "width", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "height", "ref", "marginTop", "fontWeight", "patternName", "find", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';\nimport { Chart, registerables } from 'chart.js';\nimport { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';\nimport 'chartjs-adapter-luxon';\n\n// 注册Chart.js组件和金融图表插件\nChart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement);\n\nconst { Option } = Select;\n\n// 形态名称中英文对照\nconst PATTERN_NAMES = {\n  'hammer': '锤子线',\n  'hanging_man': '上吊线',\n  'doji': '十字线',\n  'long_legged_doji': '长腿十字线',\n  'gravestone_doji': '墓碑十字线',\n  'dragonfly_doji': '蜻蜓十字线',\n  'spinning_top': '纺锤线',\n  'marubozu': '光头光脚线',\n  'engulfing_bullish': '看涨吞没',\n  'engulfing_bearish': '看跌吞没',\n  'dark_cloud_cover': '乌云盖顶',\n  'piercing_pattern': '刺透形态',\n  'harami_bullish': '看涨孕线',\n  'harami_bearish': '看跌孕线',\n  'harami_cross': '十字孕线',\n  'tweezers_top': '平头顶部',\n  'tweezers_bottom': '平头底部',\n  'morning_star': '启明星',\n  'evening_star': '黄昏星',\n  'morning_doji_star': '十字启明星',\n  'evening_doji_star': '十字黄昏星',\n  'three_white_soldiers': '前进白色三兵',\n  'three_black_crows': '三只乌鸦',\n  'rising_three_methods': '上升三法',\n  'falling_three_methods': '下降三法',\n  'shooting_star': '流星',\n  'inverted_hammer': '倒锤子'\n};\n\nconst CandlestickChart = ({ data, patterns = [] }) => {\n  const chartRef = useRef(null);\n  const chartInstance = useRef(null);\n  const [showPatterns, setShowPatterns] = useState(true);\n  const [selectedPattern, setSelectedPattern] = useState('all');\n  const [chartType, setChartType] = useState('candlestick');\n  const [classicStyle, setClassicStyle] = useState(true);\n\n  // 处理蜡烛图数据\n  const processCandleData = () => {\n    if (!data || data.length === 0) return [];\n\n    return data.map((candle, index) => ({\n      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,\n      o: parseFloat(candle.open),\n      h: parseFloat(candle.high),\n      l: parseFloat(candle.low),\n      c: parseFloat(candle.close),\n      v: parseFloat(candle.volume || 1000),\n      timestamp: candle.timestamp\n    }));\n  };\n\n  // 处理形态标注\n  const processPatternAnnotations = () => {\n    if (!showPatterns || !patterns || patterns.length === 0) return [];\n    \n    const filteredPatterns = selectedPattern === 'all' \n      ? patterns \n      : patterns.filter(p => p.pattern_name === selectedPattern);\n\n    return filteredPatterns.map((pattern, index) => {\n      const startIndex = pattern.start_index || 0;\n      const endIndex = pattern.end_index || startIndex;\n      \n      // 获取对应的数据点\n      const startData = data[startIndex];\n      const endData = data[endIndex];\n\n      if (!startData || !endData) return null;\n\n      const color = getPatternColor(pattern.signal);\n\n      return {\n        type: 'box',\n        xMin: startIndex,\n        xMax: endIndex,\n        yMin: Math.min(startData.low, endData.low) * 0.999,\n        yMax: Math.max(startData.high, endData.high) * 1.001,\n        backgroundColor: color + '20',\n        borderColor: color,\n        borderWidth: 2,\n        label: {\n          content: `${pattern.pattern_name} (${(pattern.confidence * 100).toFixed(1)}%)`,\n          enabled: true,\n          position: 'top'\n        }\n      };\n    }).filter(Boolean);\n  };\n\n  // 获取形态颜色\n  const getPatternColor = (signal) => {\n    switch (signal) {\n      case 'bullish': return '#52c41a';\n      case 'bearish': return '#ff4d4f';\n      case 'neutral': return '#faad14';\n      default: return '#1890ff';\n    }\n  };\n\n  // 创建图表配置\n  const createChartConfig = () => {\n    const candleData = processCandleData();\n    const annotations = processPatternAnnotations();\n\n    if (chartType === 'line') {\n      return {\n        type: 'line',\n        data: {\n          datasets: [{\n            label: '收盘价',\n            data: candleData.map(d => ({ x: d.x, y: d.c })),\n            borderColor: '#1890ff',\n            backgroundColor: '#1890ff20',\n            fill: false,\n            tension: 0.1\n          }]\n        },\n        options: {\n          responsive: true,\n          maintainAspectRatio: false,\n          scales: {\n            x: {\n              type: 'time',\n              time: {\n                unit: 'day'\n              }\n            },\n            y: {\n              beginAtZero: false\n            }\n          },\n          plugins: {\n            legend: {\n              display: true\n            },\n            annotation: {\n              annotations: annotations\n            }\n          }\n        }\n      };\n    }\n\n    // 真正的蜡烛图配置\n    return {\n      type: 'candlestick',\n      data: {\n        datasets: [\n          {\n            label: '日本蜡烛图',\n            data: candleData,\n            // 经典黑白风格配置\n            color: {\n              up: '#000000',    // 阳线边框：黑色\n              down: '#000000',  // 阴线边框：黑色\n              unchanged: '#000000'\n            },\n            backgroundColor: {\n              up: '#FFFFFF',    // 阳线填充：白色\n              down: '#000000',  // 阴线填充：黑色\n              unchanged: '#FFFFFF'\n            },\n            borderColor: {\n              up: '#000000',\n              down: '#000000',\n              unchanged: '#000000'\n            },\n            borderWidth: 1.5,\n            // 蜡烛图样式\n            candlestick: {\n              bodyWidth: 0.8,\n              wickWidth: 1\n            }\n          }\n        ]\n      },\n      options: {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        scales: {\n          x: {\n            type: 'time',\n            time: {\n              unit: 'day',\n              displayFormats: {\n                day: 'MM/dd'\n              }\n            },\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              }\n            }\n          },\n          y: {\n            beginAtZero: false,\n            grid: {\n              display: true,\n              color: '#E0E0E0',\n              lineWidth: 0.5\n            },\n            ticks: {\n              color: '#666666',\n              font: {\n                size: 11\n              },\n              callback: function(value) {\n                return value.toFixed(2);\n              }\n            }\n          }\n        },\n        plugins: {\n          legend: {\n            display: true,\n            position: 'top',\n            labels: {\n              color: '#333333',\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            }\n          },\n          tooltip: {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#FFFFFF',\n            bodyColor: '#FFFFFF',\n            borderColor: '#333333',\n            borderWidth: 1,\n            callbacks: {\n              title: (context) => {\n                const dataIndex = context[0].dataIndex;\n                const candle = candleData[dataIndex];\n                return candle.timestamp ?\n                  new Date(candle.timestamp).toLocaleDateString('zh-CN') :\n                  `第 ${dataIndex + 1} 根K线`;\n              },\n              label: (context) => {\n                const dataIndex = context.dataIndex;\n                const candle = candleData[dataIndex];\n                return [\n                  `开盘: ${candle.o.toFixed(2)}`,\n                  `最高: ${candle.h.toFixed(2)}`,\n                  `最低: ${candle.l.toFixed(2)}`,\n                  `收盘: ${candle.c.toFixed(2)}`,\n                  `成交量: ${candle.v.toLocaleString()}`\n                ];\n              }\n            }\n          }\n        }\n      }\n    };\n  };\n\n  // 初始化图表\n  useEffect(() => {\n    if (!chartRef.current || !data || data.length === 0) return;\n\n    // 销毁现有图表\n    if (chartInstance.current) {\n      chartInstance.current.destroy();\n    }\n\n    // 创建新图表\n    const ctx = chartRef.current.getContext('2d');\n    const config = createChartConfig();\n    \n    chartInstance.current = new Chart(ctx, config);\n\n    return () => {\n      if (chartInstance.current) {\n        chartInstance.current.destroy();\n      }\n    };\n  }, [data, patterns, showPatterns, selectedPattern, chartType]);\n\n  // 获取唯一的形态名称\n  const getUniquePatterns = () => {\n    if (!patterns || patterns.length === 0) return [];\n    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];\n    return uniqueNames.sort();\n  };\n\n  if (!data || data.length === 0) {\n    return (\n      <Card>\n        <div style={{ textAlign: 'center', padding: '50px' }}>\n          <p>暂无数据，请先上传蜡烛图数据</p>\n        </div>\n      </Card>\n    );\n  }\n\n  return (\n    <div>\n      {/* 控制面板 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>\n          <div>\n            <span style={{ marginRight: 8 }}>图表类型:</span>\n            <Select \n              value={chartType} \n              onChange={setChartType}\n              style={{ width: 120 }}\n            >\n              <Option value=\"candlestick\">蜡烛图</Option>\n              <Option value=\"line\">折线图</Option>\n            </Select>\n          </div>\n          \n          <div>\n            <span style={{ marginRight: 8 }}>显示形态:</span>\n            <Switch \n              checked={showPatterns} \n              onChange={setShowPatterns}\n              checkedChildren=\"开\"\n              unCheckedChildren=\"关\"\n            />\n          </div>\n          \n          {showPatterns && (\n            <div>\n              <span style={{ marginRight: 8 }}>形态过滤:</span>\n              <Select \n                value={selectedPattern} \n                onChange={setSelectedPattern}\n                style={{ width: 150 }}\n              >\n                <Option value=\"all\">全部形态</Option>\n                {getUniquePatterns().map(pattern => (\n                  <Option key={pattern} value={pattern}>{pattern}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n          \n          <div>\n            <span style={{ marginRight: 8 }}>数据点数:</span>\n            <Tag color=\"blue\">{data.length}</Tag>\n          </div>\n          \n          {patterns && patterns.length > 0 && (\n            <div>\n              <span style={{ marginRight: 8 }}>识别形态:</span>\n              <Tag color=\"green\">{patterns.length}</Tag>\n            </div>\n          )}\n        </div>\n      </Card>\n\n      {/* 图表区域 */}\n      <Card>\n        <div style={{ height: '500px', position: 'relative' }}>\n          <canvas ref={chartRef} />\n        </div>\n      </Card>\n\n      {/* 形态图例 */}\n      {showPatterns && patterns && patterns.length > 0 && (\n        <Card size=\"small\" style={{ marginTop: 16 }}>\n          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>\n            <span style={{ fontWeight: 'bold', marginRight: 16 }}>形态图例:</span>\n            {getUniquePatterns().map(patternName => {\n              const pattern = patterns.find(p => p.pattern_name === patternName);\n              if (!pattern) return null;\n              \n              const color = getPatternColor(pattern.signal);\n              return (\n                <Tooltip key={patternName} title={pattern.description}>\n                  <Tag \n                    color={pattern.signal === 'bullish' ? 'green' : pattern.signal === 'bearish' ? 'red' : 'orange'}\n                  >\n                    {patternName}\n                  </Tag>\n                </Tooltip>\n              );\n            })}\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default CandlestickChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,QAAQ,MAAM;AACjE,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AAChH,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAP,KAAK,CAACQ,QAAQ,CAAC,GAAGP,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,WAAW,CAAC;AAExG,MAAM;EAAEI;AAAO,CAAC,GAAGd,MAAM;;AAEzB;AACA,MAAMe,aAAa,GAAG;EACpB,QAAQ,EAAE,KAAK;EACf,aAAa,EAAE,KAAK;EACpB,MAAM,EAAE,KAAK;EACb,kBAAkB,EAAE,OAAO;EAC3B,iBAAiB,EAAE,OAAO;EAC1B,gBAAgB,EAAE,OAAO;EACzB,cAAc,EAAE,KAAK;EACrB,UAAU,EAAE,OAAO;EACnB,mBAAmB,EAAE,MAAM;EAC3B,mBAAmB,EAAE,MAAM;EAC3B,kBAAkB,EAAE,MAAM;EAC1B,kBAAkB,EAAE,MAAM;EAC1B,gBAAgB,EAAE,MAAM;EACxB,gBAAgB,EAAE,MAAM;EACxB,cAAc,EAAE,MAAM;EACtB,cAAc,EAAE,MAAM;EACtB,iBAAiB,EAAE,MAAM;EACzB,cAAc,EAAE,KAAK;EACrB,cAAc,EAAE,KAAK;EACrB,mBAAmB,EAAE,OAAO;EAC5B,mBAAmB,EAAE,OAAO;EAC5B,sBAAsB,EAAE,QAAQ;EAChC,mBAAmB,EAAE,MAAM;EAC3B,sBAAsB,EAAE,MAAM;EAC9B,uBAAuB,EAAE,MAAM;EAC/B,eAAe,EAAE,IAAI;EACrB,iBAAiB,EAAE;AACrB,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAMC,QAAQ,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMwB,aAAa,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMgC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACb,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOd,IAAI,CAACe,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;MAClCC,CAAC,EAAEF,MAAM,CAACG,SAAS,GAAG,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGJ,KAAK;MAClEK,CAAC,EAAEC,UAAU,CAACP,MAAM,CAACQ,IAAI,CAAC;MAC1BC,CAAC,EAAEF,UAAU,CAACP,MAAM,CAACU,IAAI,CAAC;MAC1BC,CAAC,EAAEJ,UAAU,CAACP,MAAM,CAACY,GAAG,CAAC;MACzBC,CAAC,EAAEN,UAAU,CAACP,MAAM,CAACc,KAAK,CAAC;MAC3BC,CAAC,EAAER,UAAU,CAACP,MAAM,CAACgB,MAAM,IAAI,IAAI,CAAC;MACpCb,SAAS,EAAEH,MAAM,CAACG;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMc,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC5B,YAAY,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAElE,MAAMoB,gBAAgB,GAAG3B,eAAe,KAAK,KAAK,GAC9CN,QAAQ,GACRA,QAAQ,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK9B,eAAe,CAAC;IAE5D,OAAO2B,gBAAgB,CAACnB,GAAG,CAAC,CAACuB,OAAO,EAAErB,KAAK,KAAK;MAC9C,MAAMsB,UAAU,GAAGD,OAAO,CAACE,WAAW,IAAI,CAAC;MAC3C,MAAMC,QAAQ,GAAGH,OAAO,CAACI,SAAS,IAAIH,UAAU;;MAEhD;MACA,MAAMI,SAAS,GAAG3C,IAAI,CAACuC,UAAU,CAAC;MAClC,MAAMK,OAAO,GAAG5C,IAAI,CAACyC,QAAQ,CAAC;MAE9B,IAAI,CAACE,SAAS,IAAI,CAACC,OAAO,EAAE,OAAO,IAAI;MAEvC,MAAMC,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;MAE7C,OAAO;QACLC,IAAI,EAAE,KAAK;QACXC,IAAI,EAAEV,UAAU;QAChBW,IAAI,EAAET,QAAQ;QACdU,IAAI,EAAEC,IAAI,CAACC,GAAG,CAACV,SAAS,CAACf,GAAG,EAAEgB,OAAO,CAAChB,GAAG,CAAC,GAAG,KAAK;QAClD0B,IAAI,EAAEF,IAAI,CAACG,GAAG,CAACZ,SAAS,CAACjB,IAAI,EAAEkB,OAAO,CAAClB,IAAI,CAAC,GAAG,KAAK;QACpD8B,eAAe,EAAEX,KAAK,GAAG,IAAI;QAC7BY,WAAW,EAAEZ,KAAK;QAClBa,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLC,OAAO,EAAE,GAAGtB,OAAO,CAACD,YAAY,KAAK,CAACC,OAAO,CAACuB,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI;UAC9EC,OAAO,EAAE,IAAI;UACbC,QAAQ,EAAE;QACZ;MACF,CAAC;IACH,CAAC,CAAC,CAAC7B,MAAM,CAAC8B,OAAO,CAAC;EACpB,CAAC;;EAED;EACA,MAAMnB,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,UAAU,GAAGtD,iBAAiB,CAAC,CAAC;IACtC,MAAMuD,WAAW,GAAGnC,yBAAyB,CAAC,CAAC;IAE/C,IAAIxB,SAAS,KAAK,MAAM,EAAE;MACxB,OAAO;QACLuC,IAAI,EAAE,MAAM;QACZhD,IAAI,EAAE;UACJqE,QAAQ,EAAE,CAAC;YACTV,KAAK,EAAE,KAAK;YACZ3D,IAAI,EAAEmE,UAAU,CAACpD,GAAG,CAACuD,CAAC,KAAK;cAAEpD,CAAC,EAAEoD,CAAC,CAACpD,CAAC;cAAEqD,CAAC,EAAED,CAAC,CAACzC;YAAE,CAAC,CAAC,CAAC;YAC/C4B,WAAW,EAAE,SAAS;YACtBD,eAAe,EAAE,WAAW;YAC5BgB,IAAI,EAAE,KAAK;YACXC,OAAO,EAAE;UACX,CAAC;QACH,CAAC;QACDC,OAAO,EAAE;UACPC,UAAU,EAAE,IAAI;UAChBC,mBAAmB,EAAE,KAAK;UAC1BC,MAAM,EAAE;YACN3D,CAAC,EAAE;cACD8B,IAAI,EAAE,MAAM;cACZ8B,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF,CAAC;YACDR,CAAC,EAAE;cACDS,WAAW,EAAE;YACf;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;YACX,CAAC;YACDC,UAAU,EAAE;cACVhB,WAAW,EAAEA;YACf;UACF;QACF;MACF,CAAC;IACH;;IAEA;IACA,OAAO;MACLpB,IAAI,EAAE,aAAa;MACnBhD,IAAI,EAAE;QACJqE,QAAQ,EAAE,CACR;UACEV,KAAK,EAAE,OAAO;UACd3D,IAAI,EAAEmE,UAAU;UAChB;UACAtB,KAAK,EAAE;YACLwC,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC;UACD/B,eAAe,EAAE;YACf6B,EAAE,EAAE,SAAS;YAAK;YAClBC,IAAI,EAAE,SAAS;YAAG;YAClBC,SAAS,EAAE;UACb,CAAC;UACD9B,WAAW,EAAE;YACX4B,EAAE,EAAE,SAAS;YACbC,IAAI,EAAE,SAAS;YACfC,SAAS,EAAE;UACb,CAAC;UACD7B,WAAW,EAAE,GAAG;UAChB;UACA8B,WAAW,EAAE;YACXC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE;UACb;QACF,CAAC;MAEL,CAAC;MACDhB,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBC,mBAAmB,EAAE,KAAK;QAC1Be,WAAW,EAAE;UACXC,SAAS,EAAE,KAAK;UAChBC,IAAI,EAAE;QACR,CAAC;QACDhB,MAAM,EAAE;UACN3D,CAAC,EAAE;YACD8B,IAAI,EAAE,MAAM;YACZ8B,IAAI,EAAE;cACJC,IAAI,EAAE,KAAK;cACXe,cAAc,EAAE;gBACdC,GAAG,EAAE;cACP;YACF,CAAC;YACDC,IAAI,EAAE;cACJb,OAAO,EAAE,IAAI;cACbtC,KAAK,EAAE,SAAS;cAChBoD,SAAS,EAAE;YACb,CAAC;YACDC,KAAK,EAAE;cACLrD,KAAK,EAAE,SAAS;cAChBsD,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR;YACF;UACF,CAAC;UACD7B,CAAC,EAAE;YACDS,WAAW,EAAE,KAAK;YAClBgB,IAAI,EAAE;cACJb,OAAO,EAAE,IAAI;cACbtC,KAAK,EAAE,SAAS;cAChBoD,SAAS,EAAE;YACb,CAAC;YACDC,KAAK,EAAE;cACLrD,KAAK,EAAE,SAAS;cAChBsD,IAAI,EAAE;gBACJC,IAAI,EAAE;cACR,CAAC;cACDC,QAAQ,EAAE,SAAAA,CAASC,KAAK,EAAE;gBACxB,OAAOA,KAAK,CAACxC,OAAO,CAAC,CAAC,CAAC;cACzB;YACF;UACF;QACF,CAAC;QACDmB,OAAO,EAAE;UACPC,MAAM,EAAE;YACNC,OAAO,EAAE,IAAI;YACbnB,QAAQ,EAAE,KAAK;YACfuC,MAAM,EAAE;cACN1D,KAAK,EAAE,SAAS;cAChBsD,IAAI,EAAE;gBACJC,IAAI,EAAE,EAAE;gBACRI,MAAM,EAAE;cACV;YACF;UACF,CAAC;UACDC,OAAO,EAAE;YACPjD,eAAe,EAAE,oBAAoB;YACrCkD,UAAU,EAAE,SAAS;YACrBC,SAAS,EAAE,SAAS;YACpBlD,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,CAAC;YACdkD,SAAS,EAAE;cACTC,KAAK,EAAGC,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS;gBACtC,MAAM/F,MAAM,GAAGmD,UAAU,CAAC4C,SAAS,CAAC;gBACpC,OAAO/F,MAAM,CAACG,SAAS,GACrB,IAAIC,IAAI,CAACJ,MAAM,CAACG,SAAS,CAAC,CAAC6F,kBAAkB,CAAC,OAAO,CAAC,GACtD,KAAKD,SAAS,GAAG,CAAC,MAAM;cAC5B,CAAC;cACDpD,KAAK,EAAGmD,OAAO,IAAK;gBAClB,MAAMC,SAAS,GAAGD,OAAO,CAACC,SAAS;gBACnC,MAAM/F,MAAM,GAAGmD,UAAU,CAAC4C,SAAS,CAAC;gBACpC,OAAO,CACL,OAAO/F,MAAM,CAACM,CAAC,CAACwC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACS,CAAC,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACW,CAAC,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,OAAO9C,MAAM,CAACa,CAAC,CAACiC,OAAO,CAAC,CAAC,CAAC,EAAE,EAC5B,QAAQ9C,MAAM,CAACe,CAAC,CAACkF,cAAc,CAAC,CAAC,EAAE,CACpC;cACH;YACF;UACF;QACF;MACF;IACF,CAAC;EACH,CAAC;;EAED;EACAtI,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,QAAQ,CAAC+G,OAAO,IAAI,CAAClH,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;;IAErD;IACA,IAAIV,aAAa,CAAC8G,OAAO,EAAE;MACzB9G,aAAa,CAAC8G,OAAO,CAACC,OAAO,CAAC,CAAC;IACjC;;IAEA;IACA,MAAMC,GAAG,GAAGjH,QAAQ,CAAC+G,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGpD,iBAAiB,CAAC,CAAC;IAElC9D,aAAa,CAAC8G,OAAO,GAAG,IAAI9H,KAAK,CAACgI,GAAG,EAAEE,MAAM,CAAC;IAE9C,OAAO,MAAM;MACX,IAAIlH,aAAa,CAAC8G,OAAO,EAAE;QACzB9G,aAAa,CAAC8G,OAAO,CAACC,OAAO,CAAC,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACnH,IAAI,EAAEC,QAAQ,EAAEI,YAAY,EAAEE,eAAe,EAAEE,SAAS,CAAC,CAAC;;EAE9D;EACA,MAAM8G,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACtH,QAAQ,IAAIA,QAAQ,CAACa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IACjD,MAAM0G,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACxH,QAAQ,CAACc,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IACnE,OAAOmF,WAAW,CAACE,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED,IAAI,CAAC1H,IAAI,IAAIA,IAAI,CAACc,MAAM,KAAK,CAAC,EAAE;IAC9B,oBACEnB,OAAA,CAACb,IAAI;MAAA6I,QAAA,eACHhI,OAAA;QAAKiI,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,eACnDhI,OAAA;UAAAgI,QAAA,EAAG;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEX;EAEA,oBACEvI,OAAA;IAAAgI,QAAA,gBAEEhI,OAAA,CAACb,IAAI;MAACsH,IAAI,EAAC,OAAO;MAACwB,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAG,CAAE;MAAAR,QAAA,eAC7ChI,OAAA;QAAKiI,KAAK,EAAE;UAAEzC,OAAO,EAAE,MAAM;UAAEiD,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAX,QAAA,gBAC/EhI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAMiI,KAAK,EAAE;cAAEW,WAAW,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CvI,OAAA,CAACZ,MAAM;YACLuH,KAAK,EAAE7F,SAAU;YACjB+H,QAAQ,EAAE9H,YAAa;YACvBkH,KAAK,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAAd,QAAA,gBAEtBhI,OAAA,CAACE,MAAM;cAACyG,KAAK,EAAC,aAAa;cAAAqB,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCvI,OAAA,CAACE,MAAM;cAACyG,KAAK,EAAC,MAAM;cAAAqB,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENvI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAMiI,KAAK,EAAE;cAAEW,WAAW,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CvI,OAAA,CAACX,MAAM;YACL0J,OAAO,EAAErI,YAAa;YACtBmI,QAAQ,EAAElI,eAAgB;YAC1BqI,eAAe,EAAC,QAAG;YACnBC,iBAAiB,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL7H,YAAY,iBACXV,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAMiI,KAAK,EAAE;cAAEW,WAAW,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CvI,OAAA,CAACZ,MAAM;YACLuH,KAAK,EAAE/F,eAAgB;YACvBiI,QAAQ,EAAEhI,kBAAmB;YAC7BoH,KAAK,EAAE;cAAEa,KAAK,EAAE;YAAI,CAAE;YAAAd,QAAA,gBAEtBhI,OAAA,CAACE,MAAM;cAACyG,KAAK,EAAC,KAAK;cAAAqB,QAAA,EAAC;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCX,iBAAiB,CAAC,CAAC,CAACxG,GAAG,CAACuB,OAAO,iBAC9B3C,OAAA,CAACE,MAAM;cAAeyG,KAAK,EAAEhE,OAAQ;cAAAqF,QAAA,EAAErF;YAAO,GAAjCA,OAAO;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAEDvI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAMiI,KAAK,EAAE;cAAEW,WAAW,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CvI,OAAA,CAACT,GAAG;YAAC2D,KAAK,EAAC,MAAM;YAAA8E,QAAA,EAAE3H,IAAI,CAACc;UAAM;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAELjI,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9BnB,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAMiI,KAAK,EAAE;cAAEW,WAAW,EAAE;YAAE,CAAE;YAAAZ,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CvI,OAAA,CAACT,GAAG;YAAC2D,KAAK,EAAC,OAAO;YAAA8E,QAAA,EAAE1H,QAAQ,CAACa;UAAM;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvI,OAAA,CAACb,IAAI;MAAA6I,QAAA,eACHhI,OAAA;QAAKiI,KAAK,EAAE;UAAEiB,MAAM,EAAE,OAAO;UAAE7E,QAAQ,EAAE;QAAW,CAAE;QAAA2D,QAAA,eACpDhI,OAAA;UAAQmJ,GAAG,EAAE3I;QAAS;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGN7H,YAAY,IAAIJ,QAAQ,IAAIA,QAAQ,CAACa,MAAM,GAAG,CAAC,iBAC9CnB,OAAA,CAACb,IAAI;MAACsH,IAAI,EAAC,OAAO;MAACwB,KAAK,EAAE;QAAEmB,SAAS,EAAE;MAAG,CAAE;MAAApB,QAAA,eAC1ChI,OAAA;QAAKiI,KAAK,EAAE;UAAEzC,OAAO,EAAE,MAAM;UAAEmD,QAAQ,EAAE,MAAM;UAAED,GAAG,EAAE;QAAE,CAAE;QAAAV,QAAA,gBACxDhI,OAAA;UAAMiI,KAAK,EAAE;YAAEoB,UAAU,EAAE,MAAM;YAAET,WAAW,EAAE;UAAG,CAAE;UAAAZ,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACjEX,iBAAiB,CAAC,CAAC,CAACxG,GAAG,CAACkI,WAAW,IAAI;UACtC,MAAM3G,OAAO,GAAGrC,QAAQ,CAACiJ,IAAI,CAAC9G,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAK4G,WAAW,CAAC;UAClE,IAAI,CAAC3G,OAAO,EAAE,OAAO,IAAI;UAEzB,MAAMO,KAAK,GAAGC,eAAe,CAACR,OAAO,CAACS,MAAM,CAAC;UAC7C,oBACEpD,OAAA,CAACV,OAAO;YAAmB4H,KAAK,EAAEvE,OAAO,CAAC6G,WAAY;YAAAxB,QAAA,eACpDhI,OAAA,CAACT,GAAG;cACF2D,KAAK,EAAEP,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,OAAO,GAAGT,OAAO,CAACS,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;cAAA4E,QAAA,EAE/FsB;YAAW;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC,GALMe,WAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMhB,CAAC;QAEd,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChI,EAAA,CA9WIH,gBAAgB;AAAAqJ,EAAA,GAAhBrJ,gBAAgB;AAgXtB,eAAeA,gBAAgB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}