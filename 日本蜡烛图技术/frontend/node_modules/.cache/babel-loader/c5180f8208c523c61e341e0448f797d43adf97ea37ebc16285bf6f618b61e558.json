{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,Row,Col,Statistic,Progress,Tag,Alert,Spin,Button,Input,Select,Tabs,Table,Badge,Tooltip,Space}from'antd';import{RiseOutlined as TrendingUpOutlined,FallOutlined as TrendingDownOutlined,<PERSON><PERSON>Outlined,<PERSON><PERSON><PERSON>Outlined,<PERSON><PERSON><PERSON>Outlined,ReloadOutlined,StarOutlined,WarningOutlined}from'@ant-design/icons';import'./MarketAnalysis.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Option}=Select;const{TabPane}=Tabs;const MarketAnalysis=()=>{var _fundamental_analysis,_fundamental_analysis2,_fundamental_analysis3,_fundamental_analysis4,_fundamental_analysis5,_fundamental_analysis6,_fundamental_analysis7,_fundamental_analysis8,_fundamental_analysis9,_fundamental_analysis0,_fundamental_analysis1;const[loading,setLoading]=useState(false);const[analysisData,setAnalysisData]=useState(null);const[symbol,setSymbol]=useState('AAPL');const[timeInterval,setTimeInterval]=useState('1h');const[error,setError]=useState(null);// 获取综合市场分析\nconst fetchMarketAnalysis=async()=>{setLoading(true);setError(null);try{const response=await fetch('/api/v1/market/comprehensive-analysis',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({symbol:symbol,interval:interval,include_fundamentals:true})});if(!response.ok){throw new Error(\"HTTP error! status: \".concat(response.status));}const data=await response.json();setAnalysisData(data);}catch(err){setError(err.message);console.error('Market analysis error:',err);}finally{setLoading(false);}};useEffect(()=>{fetchMarketAnalysis();},[]);// 获取推荐颜色\nconst getRecommendationColor=action=>{switch(action){case'BUY':return'success';case'SELL':return'error';case'HOLD':return'warning';default:return'default';}};// 获取情绪颜色\nconst getSentimentColor=sentiment=>{switch(sentiment){case'bullish':return'success';case'bearish':return'error';case'neutral':return'default';default:return'default';}};// 获取评分颜色\nconst getScoreColor=score=>{if(score>0.3)return'#52c41a';if(score<-0.3)return'#ff4d4f';return'#faad14';};// 技术指标表格列\nconst technicalColumns=[{title:'指标',dataIndex:'indicator',key:'indicator'},{title:'当前值',dataIndex:'value',key:'value',render:value=>value?value.toFixed(2):'N/A'},{title:'信号',dataIndex:'signal',key:'signal',render:signal=>/*#__PURE__*/_jsx(Tag,{color:signal==='buy'?'green':signal==='sell'?'red':'blue',children:signal||'neutral'})}];// 支撑阻力位表格列\nconst levelsColumns=[{title:'类型',dataIndex:'type',key:'type',render:type=>/*#__PURE__*/_jsx(Tag,{color:type==='support'?'green':'red',children:type==='support'?'支撑':'阻力'})},{title:'价位',dataIndex:'level',key:'level',render:level=>\"$\".concat(level.toFixed(2))},{title:'强度',dataIndex:'strength',key:'strength',render:strength=>/*#__PURE__*/_jsx(Progress,{percent:strength*100,size:\"small\",showInfo:false,strokeColor:strength>0.7?'#52c41a':strength>0.4?'#faad14':'#ff4d4f'})}];if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"market-analysis-loading\",children:[/*#__PURE__*/_jsx(Spin,{size:\"large\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u5206\\u6790\\u5E02\\u573A\\u6570\\u636E...\"})]});}if(error){return/*#__PURE__*/_jsx(Alert,{message:\"\\u5206\\u6790\\u5931\\u8D25\",description:error,type:\"error\",showIcon:true,action:/*#__PURE__*/_jsx(Button,{size:\"small\",onClick:fetchMarketAnalysis,children:\"\\u91CD\\u8BD5\"})});}if(!analysisData){return/*#__PURE__*/_jsxs(\"div\",{className:\"market-analysis-empty\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u6682\\u65E0\\u5206\\u6790\\u6570\\u636E\"}),/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:fetchMarketAnalysis,children:\"\\u5F00\\u59CB\\u5206\\u6790\"})]});}const{price_data,technical_analysis,fundamental_analysis,sentiment_analysis,overall_score,recommendation,data_quality}=analysisData;// 准备技术指标数据\nconst technicalData=[];if(technical_analysis.indicators){const indicators=technical_analysis.indicators;if(indicators.sma_20)technicalData.push({indicator:'SMA(20)',value:indicators.sma_20,signal:'neutral'});if(indicators.sma_50)technicalData.push({indicator:'SMA(50)',value:indicators.sma_50,signal:'neutral'});if(indicators.ema_12)technicalData.push({indicator:'EMA(12)',value:indicators.ema_12,signal:'neutral'});if(indicators.rsi)technicalData.push({indicator:'RSI(14)',value:indicators.rsi,signal:indicators.rsi>70?'sell':indicators.rsi<30?'buy':'neutral'});}// 准备支撑阻力位数据\nconst levelsData=[];if(technical_analysis.support_resistance){const{support,resistance}=technical_analysis.support_resistance;support.forEach((level,index)=>{levelsData.push({key:\"support-\".concat(index),type:'support',level:level,strength:0.8-index*0.2});});resistance.forEach((level,index)=>{levelsData.push({key:\"resistance-\".concat(index),type:'resistance',level:level,strength:0.8-index*0.2});});}return/*#__PURE__*/_jsxs(\"div\",{className:\"market-analysis\",children:[/*#__PURE__*/_jsx(Card,{className:\"control-panel\",size:\"small\",children:/*#__PURE__*/_jsxs(Row,{gutter:16,align:\"middle\",children:[/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u80A1\\u7968\\u4EE3\\u7801:\"}),/*#__PURE__*/_jsx(Input,{value:symbol,onChange:e=>setSymbol(e.target.value.toUpperCase()),placeholder:\"\\u8F93\\u5165\\u80A1\\u7968\\u4EE3\\u7801\",style:{width:120}})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u65F6\\u95F4\\u95F4\\u9694:\"}),/*#__PURE__*/_jsxs(Select,{value:timeInterval,onChange:setTimeInterval,style:{width:100},children:[/*#__PURE__*/_jsx(Option,{value:\"1m\",children:\"1\\u5206\\u949F\"}),/*#__PURE__*/_jsx(Option,{value:\"5m\",children:\"5\\u5206\\u949F\"}),/*#__PURE__*/_jsx(Option,{value:\"15m\",children:\"15\\u5206\\u949F\"}),/*#__PURE__*/_jsx(Option,{value:\"1h\",children:\"1\\u5C0F\\u65F6\"}),/*#__PURE__*/_jsx(Option,{value:\"1d\",children:\"1\\u5929\"})]})]})}),/*#__PURE__*/_jsx(Col,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(ReloadOutlined,{}),onClick:fetchMarketAnalysis,loading:loading,children:\"\\u5237\\u65B0\\u5206\\u6790\"})})]})}),/*#__PURE__*/_jsxs(Row,{gutter:[16,16],className:\"overview-section\",children:[/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u5F53\\u524D\\u4EF7\\u683C\",value:price_data.current_price,precision:2,prefix:\"$\",valueStyle:{color:price_data.change>=0?'#3f8600':'#cf1322'},suffix:/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'14px'},children:[price_data.change>=0?/*#__PURE__*/_jsx(TrendingUpOutlined,{}):/*#__PURE__*/_jsx(TrendingDownOutlined,{}),price_data.change_percent.toFixed(2),\"%\"]})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u7EFC\\u5408\\u8BC4\\u5206\",value:overall_score.overall,precision:2,valueStyle:{color:getScoreColor(overall_score.overall)},suffix:/*#__PURE__*/_jsx(Progress,{type:\"circle\",percent:Math.abs(overall_score.overall)*100,width:60,strokeColor:getScoreColor(overall_score.overall),format:()=>''})})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"recommendation-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"recommendation-title\",children:\"\\u6295\\u8D44\\u5EFA\\u8BAE\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"recommendation-content\",children:[/*#__PURE__*/_jsx(Tag,{color:getRecommendationColor(recommendation.action),style:{fontSize:'16px',padding:'4px 12px'},children:recommendation.action}),/*#__PURE__*/_jsxs(\"div\",{className:\"recommendation-confidence\",children:[\"\\u7F6E\\u4FE1\\u5EA6: \",recommendation.confidence]})]})]})})}),/*#__PURE__*/_jsx(Col,{xs:24,sm:12,md:6,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"sentiment-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"sentiment-title\",children:\"\\u5E02\\u573A\\u60C5\\u7EEA\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"sentiment-content\",children:[/*#__PURE__*/_jsx(Badge,{color:getSentimentColor(sentiment_analysis.sentiment),text:sentiment_analysis.sentiment==='bullish'?'看涨':sentiment_analysis.sentiment==='bearish'?'看跌':'中性',style:{fontSize:'14px'}}),/*#__PURE__*/_jsx(\"div\",{className:\"sentiment-confidence\",children:/*#__PURE__*/_jsx(Progress,{percent:sentiment_analysis.confidence*100,size:\"small\",showInfo:false})})]})]})})})]}),/*#__PURE__*/_jsx(Card,{className:\"analysis-tabs\",children:/*#__PURE__*/_jsxs(Tabs,{defaultActiveKey:\"technical\",type:\"card\",children:[/*#__PURE__*/_jsxs(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(BarChartOutlined,{}),\"\\u6280\\u672F\\u5206\\u6790\"]}),children:[/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6280\\u672F\\u6307\\u6807\",size:\"small\",children:/*#__PURE__*/_jsx(Table,{columns:technicalColumns,dataSource:technicalData,pagination:false,size:\"small\"})})}),/*#__PURE__*/_jsx(Col,{xs:24,lg:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u652F\\u6491\\u963B\\u529B\\u4F4D\",size:\"small\",children:/*#__PURE__*/_jsx(Table,{columns:levelsColumns,dataSource:levelsData,pagination:false,size:\"small\"})})})]}),technical_analysis.signals&&technical_analysis.signals.length>0&&/*#__PURE__*/_jsx(Card,{title:\"\\u6280\\u672F\\u4FE1\\u53F7\",size:\"small\",style:{marginTop:16},children:/*#__PURE__*/_jsx(Space,{wrap:true,children:technical_analysis.signals.map((signal,index)=>/*#__PURE__*/_jsx(Tooltip,{title:\"\".concat(signal.indicator,\": \").concat(signal.type),children:/*#__PURE__*/_jsxs(Tag,{color:signal.signal==='buy'?'green':'red',icon:signal.signal==='buy'?/*#__PURE__*/_jsx(TrendingUpOutlined,{}):/*#__PURE__*/_jsx(TrendingDownOutlined,{}),children:[signal.indicator,\" \",signal.signal]})},index))})})]},\"technical\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(DashboardOutlined,{}),\"\\u57FA\\u672C\\u9762\"]}),children:fundamental_analysis&&Object.keys(fundamental_analysis).length>0?/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsxs(Card,{title:\"\\u4F30\\u503C\\u5206\\u6790\",size:\"small\",children:[/*#__PURE__*/_jsx(Statistic,{title:\"\\u5E02\\u76C8\\u7387 (P/E)\",value:((_fundamental_analysis=fundamental_analysis.key_metrics)===null||_fundamental_analysis===void 0?void 0:_fundamental_analysis.pe_ratio)||0,precision:2}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:16},children:/*#__PURE__*/_jsx(Tag,{color:((_fundamental_analysis2=fundamental_analysis.valuation)===null||_fundamental_analysis2===void 0?void 0:_fundamental_analysis2.pe_evaluation)==='undervalued'?'green':((_fundamental_analysis3=fundamental_analysis.valuation)===null||_fundamental_analysis3===void 0?void 0:_fundamental_analysis3.pe_evaluation)==='overvalued'?'red':'blue',children:((_fundamental_analysis4=fundamental_analysis.valuation)===null||_fundamental_analysis4===void 0?void 0:_fundamental_analysis4.pe_evaluation)||'fair'})})]})}),/*#__PURE__*/_jsx(Col,{xs:24,md:12,children:/*#__PURE__*/_jsx(Card,{title:\"\\u8D22\\u52A1\\u5065\\u5EB7\",size:\"small\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"financial-metrics\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"metric-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u76C8\\u5229\\u80FD\\u529B:\"}),/*#__PURE__*/_jsx(Tag,{color:((_fundamental_analysis5=fundamental_analysis.financial_health)===null||_fundamental_analysis5===void 0?void 0:_fundamental_analysis5.profitability)==='excellent'?'green':((_fundamental_analysis6=fundamental_analysis.financial_health)===null||_fundamental_analysis6===void 0?void 0:_fundamental_analysis6.profitability)==='good'?'blue':((_fundamental_analysis7=fundamental_analysis.financial_health)===null||_fundamental_analysis7===void 0?void 0:_fundamental_analysis7.profitability)==='poor'?'red':'orange',children:((_fundamental_analysis8=fundamental_analysis.financial_health)===null||_fundamental_analysis8===void 0?void 0:_fundamental_analysis8.profitability)||'average'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"metric-item\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u98CE\\u9669\\u6C34\\u5E73:\"}),/*#__PURE__*/_jsx(Tag,{color:((_fundamental_analysis9=fundamental_analysis.financial_health)===null||_fundamental_analysis9===void 0?void 0:_fundamental_analysis9.risk_level)==='low'?'green':((_fundamental_analysis0=fundamental_analysis.financial_health)===null||_fundamental_analysis0===void 0?void 0:_fundamental_analysis0.risk_level)==='high'?'red':'orange',children:((_fundamental_analysis1=fundamental_analysis.financial_health)===null||_fundamental_analysis1===void 0?void 0:_fundamental_analysis1.risk_level)||'medium'})]})]})})})]}):/*#__PURE__*/_jsx(Alert,{message:\"\\u57FA\\u672C\\u9762\\u6570\\u636E\\u4E0D\\u53EF\\u7528\",description:\"\\u5F53\\u524D\\u4F7F\\u7528\\u6A21\\u62DF\\u6570\\u636E\\uFF0C\\u57FA\\u672C\\u9762\\u5206\\u6790\\u529F\\u80FD\\u6709\\u9650\",type:\"info\",showIcon:true})},\"fundamental\"),/*#__PURE__*/_jsx(TabPane,{tab:/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(LineChartOutlined,{}),\"\\u60C5\\u7EEA\\u5206\\u6790\"]}),children:/*#__PURE__*/_jsxs(Row,{gutter:[16,16],children:[/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Card,{title:\"\\u4EF7\\u683C\\u52A8\\u91CF\",size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u52A8\\u91CF\\u6307\\u6807\",value:sentiment_analysis.price_momentum*100,precision:2,suffix:\"%\",valueStyle:{color:sentiment_analysis.price_momentum>=0?'#3f8600':'#cf1322'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Card,{title:\"\\u6210\\u4EA4\\u91CF\\u8D8B\\u52BF\",size:\"small\",children:/*#__PURE__*/_jsx(Statistic,{title:\"\\u6210\\u4EA4\\u91CF\\u53D8\\u5316\",value:sentiment_analysis.volume_trend*100,precision:2,suffix:\"%\",valueStyle:{color:sentiment_analysis.volume_trend>=0?'#3f8600':'#cf1322'}})})}),/*#__PURE__*/_jsx(Col,{xs:24,md:8,children:/*#__PURE__*/_jsx(Card,{title:\"RSI\\u60C5\\u7EEA\",size:\"small\",children:/*#__PURE__*/_jsx(\"div\",{className:\"rsi-sentiment\",children:/*#__PURE__*/_jsx(Tag,{color:sentiment_analysis.rsi_sentiment==='oversold'?'green':sentiment_analysis.rsi_sentiment==='overbought'?'red':'blue',children:sentiment_analysis.rsi_sentiment==='oversold'?'超卖':sentiment_analysis.rsi_sentiment==='overbought'?'超买':'中性'})})})})]})},\"sentiment\")]})}),data_quality&&/*#__PURE__*/_jsx(Card,{size:\"small\",className:\"data-quality\",children:/*#__PURE__*/_jsxs(Row,{align:\"middle\",children:[/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u6570\\u636E\\u8D28\\u91CF: \"}),/*#__PURE__*/_jsx(Progress,{percent:data_quality.score*100,size:\"small\",status:data_quality.score>0.8?'success':data_quality.score>0.5?'normal':'exception'})]}),data_quality.issues&&data_quality.issues.length>0&&/*#__PURE__*/_jsx(Col,{offset:1,children:/*#__PURE__*/_jsx(Tooltip,{title:data_quality.issues.join(', '),children:/*#__PURE__*/_jsx(WarningOutlined,{style:{color:'#faad14'}})})})]})})]});};export default MarketAnalysis;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "Progress", "Tag", "<PERSON><PERSON>", "Spin", "<PERSON><PERSON>", "Input", "Select", "Tabs", "Table", "Badge", "<PERSON><PERSON><PERSON>", "Space", "RiseOutlined", "TrendingUpOutlined", "FallOutlined", "TrendingDownOutlined", "DashboardOutlined", "BarChartOutlined", "LineChartOutlined", "ReloadOutlined", "StarOutlined", "WarningOutlined", "jsx", "_jsx", "jsxs", "_jsxs", "Option", "TabPane", "MarketAnalysis", "_fundamental_analysis", "_fundamental_analysis2", "_fundamental_analysis3", "_fundamental_analysis4", "_fundamental_analysis5", "_fundamental_analysis6", "_fundamental_analysis7", "_fundamental_analysis8", "_fundamental_analysis9", "_fundamental_analysis0", "_fundamental_analysis1", "loading", "setLoading", "analysisData", "setAnalysisData", "symbol", "setSymbol", "timeInterval", "setTimeInterval", "error", "setError", "fetchMarketAnalysis", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "interval", "include_fundamentals", "ok", "Error", "concat", "status", "data", "json", "err", "message", "console", "getRecommendationColor", "action", "getSentimentColor", "sentiment", "getScoreColor", "score", "technicalColumns", "title", "dataIndex", "key", "render", "value", "toFixed", "signal", "color", "children", "levelsColumns", "type", "level", "strength", "percent", "size", "showInfo", "strokeColor", "className", "description", "showIcon", "onClick", "price_data", "technical_analysis", "fundamental_analysis", "sentiment_analysis", "overall_score", "recommendation", "data_quality", "technicalData", "indicators", "sma_20", "push", "indicator", "sma_50", "ema_12", "rsi", "levelsData", "support_resistance", "support", "resistance", "for<PERSON>ach", "index", "gutter", "align", "onChange", "e", "target", "toUpperCase", "placeholder", "style", "width", "icon", "xs", "sm", "md", "current_price", "precision", "prefix", "valueStyle", "change", "suffix", "fontSize", "change_percent", "overall", "Math", "abs", "format", "padding", "confidence", "text", "defaultActiveKey", "tab", "lg", "columns", "dataSource", "pagination", "signals", "length", "marginTop", "wrap", "map", "Object", "keys", "key_metrics", "pe_ratio", "valuation", "pe_evaluation", "financial_health", "profitability", "risk_level", "price_momentum", "volume_trend", "rsi_sentiment", "issues", "offset", "join"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tag,\n  Alert,\n  Spin,\n  Button,\n  Input,\n  Select,\n  Tabs,\n  Table,\n  Badge,\n  Tooltip,\n  Space\n} from 'antd';\nimport {\n  RiseOutlined as TrendingUpOutlined,\n  FallOutlined as TrendingDownOutlined,\n  <PERSON><PERSON>Outlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  ReloadOutlined,\n  StarOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\nimport './MarketAnalysis.css';\n\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\nconst MarketAnalysis = () => {\n  const [loading, setLoading] = useState(false);\n  const [analysisData, setAnalysisData] = useState(null);\n  const [symbol, setSymbol] = useState('AAPL');\n  const [timeInterval, setTimeInterval] = useState('1h');\n  const [error, setError] = useState(null);\n\n  // 获取综合市场分析\n  const fetchMarketAnalysis = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('/api/v1/market/comprehensive-analysis', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          symbol: symbol,\n          interval: interval,\n          include_fundamentals: true\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      setAnalysisData(data);\n    } catch (err) {\n      setError(err.message);\n      console.error('Market analysis error:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchMarketAnalysis();\n  }, []);\n\n  // 获取推荐颜色\n  const getRecommendationColor = (action) => {\n    switch (action) {\n      case 'BUY': return 'success';\n      case 'SELL': return 'error';\n      case 'HOLD': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  // 获取情绪颜色\n  const getSentimentColor = (sentiment) => {\n    switch (sentiment) {\n      case 'bullish': return 'success';\n      case 'bearish': return 'error';\n      case 'neutral': return 'default';\n      default: return 'default';\n    }\n  };\n\n  // 获取评分颜色\n  const getScoreColor = (score) => {\n    if (score > 0.3) return '#52c41a';\n    if (score < -0.3) return '#ff4d4f';\n    return '#faad14';\n  };\n\n  // 技术指标表格列\n  const technicalColumns = [\n    {\n      title: '指标',\n      dataIndex: 'indicator',\n      key: 'indicator',\n    },\n    {\n      title: '当前值',\n      dataIndex: 'value',\n      key: 'value',\n      render: (value) => value ? value.toFixed(2) : 'N/A'\n    },\n    {\n      title: '信号',\n      dataIndex: 'signal',\n      key: 'signal',\n      render: (signal) => (\n        <Tag color={signal === 'buy' ? 'green' : signal === 'sell' ? 'red' : 'blue'}>\n          {signal || 'neutral'}\n        </Tag>\n      )\n    }\n  ];\n\n  // 支撑阻力位表格列\n  const levelsColumns = [\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => (\n        <Tag color={type === 'support' ? 'green' : 'red'}>\n          {type === 'support' ? '支撑' : '阻力'}\n        </Tag>\n      )\n    },\n    {\n      title: '价位',\n      dataIndex: 'level',\n      key: 'level',\n      render: (level) => `$${level.toFixed(2)}`\n    },\n    {\n      title: '强度',\n      dataIndex: 'strength',\n      key: 'strength',\n      render: (strength) => (\n        <Progress \n          percent={strength * 100} \n          size=\"small\" \n          showInfo={false}\n          strokeColor={strength > 0.7 ? '#52c41a' : strength > 0.4 ? '#faad14' : '#ff4d4f'}\n        />\n      )\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"market-analysis-loading\">\n        <Spin size=\"large\" />\n        <p>正在分析市场数据...</p>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Alert\n        message=\"分析失败\"\n        description={error}\n        type=\"error\"\n        showIcon\n        action={\n          <Button size=\"small\" onClick={fetchMarketAnalysis}>\n            重试\n          </Button>\n        }\n      />\n    );\n  }\n\n  if (!analysisData) {\n    return (\n      <div className=\"market-analysis-empty\">\n        <p>暂无分析数据</p>\n        <Button type=\"primary\" onClick={fetchMarketAnalysis}>\n          开始分析\n        </Button>\n      </div>\n    );\n  }\n\n  const { \n    price_data, \n    technical_analysis, \n    fundamental_analysis, \n    sentiment_analysis, \n    overall_score, \n    recommendation,\n    data_quality \n  } = analysisData;\n\n  // 准备技术指标数据\n  const technicalData = [];\n  if (technical_analysis.indicators) {\n    const indicators = technical_analysis.indicators;\n    if (indicators.sma_20) technicalData.push({ indicator: 'SMA(20)', value: indicators.sma_20, signal: 'neutral' });\n    if (indicators.sma_50) technicalData.push({ indicator: 'SMA(50)', value: indicators.sma_50, signal: 'neutral' });\n    if (indicators.ema_12) technicalData.push({ indicator: 'EMA(12)', value: indicators.ema_12, signal: 'neutral' });\n    if (indicators.rsi) technicalData.push({ \n      indicator: 'RSI(14)', \n      value: indicators.rsi, \n      signal: indicators.rsi > 70 ? 'sell' : indicators.rsi < 30 ? 'buy' : 'neutral' \n    });\n  }\n\n  // 准备支撑阻力位数据\n  const levelsData = [];\n  if (technical_analysis.support_resistance) {\n    const { support, resistance } = technical_analysis.support_resistance;\n    support.forEach((level, index) => {\n      levelsData.push({\n        key: `support-${index}`,\n        type: 'support',\n        level: level,\n        strength: 0.8 - index * 0.2\n      });\n    });\n    resistance.forEach((level, index) => {\n      levelsData.push({\n        key: `resistance-${index}`,\n        type: 'resistance',\n        level: level,\n        strength: 0.8 - index * 0.2\n      });\n    });\n  }\n\n  return (\n    <div className=\"market-analysis\">\n      {/* 控制面板 */}\n      <Card className=\"control-panel\" size=\"small\">\n        <Row gutter={16} align=\"middle\">\n          <Col>\n            <Space>\n              <span>股票代码:</span>\n              <Input\n                value={symbol}\n                onChange={(e) => setSymbol(e.target.value.toUpperCase())}\n                placeholder=\"输入股票代码\"\n                style={{ width: 120 }}\n              />\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <span>时间间隔:</span>\n              <Select value={timeInterval} onChange={setTimeInterval} style={{ width: 100 }}>\n                <Option value=\"1m\">1分钟</Option>\n                <Option value=\"5m\">5分钟</Option>\n                <Option value=\"15m\">15分钟</Option>\n                <Option value=\"1h\">1小时</Option>\n                <Option value=\"1d\">1天</Option>\n              </Select>\n            </Space>\n          </Col>\n          <Col>\n            <Button \n              type=\"primary\" \n              icon={<ReloadOutlined />}\n              onClick={fetchMarketAnalysis}\n              loading={loading}\n            >\n              刷新分析\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 主要指标概览 */}\n      <Row gutter={[16, 16]} className=\"overview-section\">\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"当前价格\"\n              value={price_data.current_price}\n              precision={2}\n              prefix=\"$\"\n              valueStyle={{ \n                color: price_data.change >= 0 ? '#3f8600' : '#cf1322' \n              }}\n              suffix={\n                <span style={{ fontSize: '14px' }}>\n                  {price_data.change >= 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />}\n                  {price_data.change_percent.toFixed(2)}%\n                </span>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"综合评分\"\n              value={overall_score.overall}\n              precision={2}\n              valueStyle={{ color: getScoreColor(overall_score.overall) }}\n              suffix={\n                <Progress\n                  type=\"circle\"\n                  percent={Math.abs(overall_score.overall) * 100}\n                  width={60}\n                  strokeColor={getScoreColor(overall_score.overall)}\n                  format={() => ''}\n                />\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div className=\"recommendation-card\">\n              <div className=\"recommendation-title\">投资建议</div>\n              <div className=\"recommendation-content\">\n                <Tag \n                  color={getRecommendationColor(recommendation.action)}\n                  style={{ fontSize: '16px', padding: '4px 12px' }}\n                >\n                  {recommendation.action}\n                </Tag>\n                <div className=\"recommendation-confidence\">\n                  置信度: {recommendation.confidence}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <div className=\"sentiment-card\">\n              <div className=\"sentiment-title\">市场情绪</div>\n              <div className=\"sentiment-content\">\n                <Badge \n                  color={getSentimentColor(sentiment_analysis.sentiment)}\n                  text={\n                    sentiment_analysis.sentiment === 'bullish' ? '看涨' :\n                    sentiment_analysis.sentiment === 'bearish' ? '看跌' : '中性'\n                  }\n                  style={{ fontSize: '14px' }}\n                />\n                <div className=\"sentiment-confidence\">\n                  <Progress \n                    percent={sentiment_analysis.confidence * 100}\n                    size=\"small\"\n                    showInfo={false}\n                  />\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 详细分析标签页 */}\n      <Card className=\"analysis-tabs\">\n        <Tabs defaultActiveKey=\"technical\" type=\"card\">\n          <TabPane tab={<span><BarChartOutlined />技术分析</span>} key=\"technical\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} lg={12}>\n                <Card title=\"技术指标\" size=\"small\">\n                  <Table\n                    columns={technicalColumns}\n                    dataSource={technicalData}\n                    pagination={false}\n                    size=\"small\"\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} lg={12}>\n                <Card title=\"支撑阻力位\" size=\"small\">\n                  <Table\n                    columns={levelsColumns}\n                    dataSource={levelsData}\n                    pagination={false}\n                    size=\"small\"\n                  />\n                </Card>\n              </Col>\n            </Row>\n            \n            {technical_analysis.signals && technical_analysis.signals.length > 0 && (\n              <Card title=\"技术信号\" size=\"small\" style={{ marginTop: 16 }}>\n                <Space wrap>\n                  {technical_analysis.signals.map((signal, index) => (\n                    <Tooltip key={index} title={`${signal.indicator}: ${signal.type}`}>\n                      <Tag \n                        color={signal.signal === 'buy' ? 'green' : 'red'}\n                        icon={signal.signal === 'buy' ? <TrendingUpOutlined /> : <TrendingDownOutlined />}\n                      >\n                        {signal.indicator} {signal.signal}\n                      </Tag>\n                    </Tooltip>\n                  ))}\n                </Space>\n              </Card>\n            )}\n          </TabPane>\n\n          <TabPane tab={<span><DashboardOutlined />基本面</span>} key=\"fundamental\">\n            {fundamental_analysis && Object.keys(fundamental_analysis).length > 0 ? (\n              <Row gutter={[16, 16]}>\n                <Col xs={24} md={12}>\n                  <Card title=\"估值分析\" size=\"small\">\n                    <Statistic\n                      title=\"市盈率 (P/E)\"\n                      value={fundamental_analysis.key_metrics?.pe_ratio || 0}\n                      precision={2}\n                    />\n                    <div style={{ marginTop: 16 }}>\n                      <Tag color={\n                        fundamental_analysis.valuation?.pe_evaluation === 'undervalued' ? 'green' :\n                        fundamental_analysis.valuation?.pe_evaluation === 'overvalued' ? 'red' : 'blue'\n                      }>\n                        {fundamental_analysis.valuation?.pe_evaluation || 'fair'}\n                      </Tag>\n                    </div>\n                  </Card>\n                </Col>\n                <Col xs={24} md={12}>\n                  <Card title=\"财务健康\" size=\"small\">\n                    <div className=\"financial-metrics\">\n                      <div className=\"metric-item\">\n                        <span>盈利能力:</span>\n                        <Tag color={\n                          fundamental_analysis.financial_health?.profitability === 'excellent' ? 'green' :\n                          fundamental_analysis.financial_health?.profitability === 'good' ? 'blue' :\n                          fundamental_analysis.financial_health?.profitability === 'poor' ? 'red' : 'orange'\n                        }>\n                          {fundamental_analysis.financial_health?.profitability || 'average'}\n                        </Tag>\n                      </div>\n                      <div className=\"metric-item\">\n                        <span>风险水平:</span>\n                        <Tag color={\n                          fundamental_analysis.financial_health?.risk_level === 'low' ? 'green' :\n                          fundamental_analysis.financial_health?.risk_level === 'high' ? 'red' : 'orange'\n                        }>\n                          {fundamental_analysis.financial_health?.risk_level || 'medium'}\n                        </Tag>\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              </Row>\n            ) : (\n              <Alert\n                message=\"基本面数据不可用\"\n                description=\"当前使用模拟数据，基本面分析功能有限\"\n                type=\"info\"\n                showIcon\n              />\n            )}\n          </TabPane>\n\n          <TabPane tab={<span><LineChartOutlined />情绪分析</span>} key=\"sentiment\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} md={8}>\n                <Card title=\"价格动量\" size=\"small\">\n                  <Statistic\n                    title=\"动量指标\"\n                    value={sentiment_analysis.price_momentum * 100}\n                    precision={2}\n                    suffix=\"%\"\n                    valueStyle={{ \n                      color: sentiment_analysis.price_momentum >= 0 ? '#3f8600' : '#cf1322' \n                    }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} md={8}>\n                <Card title=\"成交量趋势\" size=\"small\">\n                  <Statistic\n                    title=\"成交量变化\"\n                    value={sentiment_analysis.volume_trend * 100}\n                    precision={2}\n                    suffix=\"%\"\n                    valueStyle={{ \n                      color: sentiment_analysis.volume_trend >= 0 ? '#3f8600' : '#cf1322' \n                    }}\n                  />\n                </Card>\n              </Col>\n              <Col xs={24} md={8}>\n                <Card title=\"RSI情绪\" size=\"small\">\n                  <div className=\"rsi-sentiment\">\n                    <Tag color={\n                      sentiment_analysis.rsi_sentiment === 'oversold' ? 'green' :\n                      sentiment_analysis.rsi_sentiment === 'overbought' ? 'red' : 'blue'\n                    }>\n                      {sentiment_analysis.rsi_sentiment === 'oversold' ? '超卖' :\n                       sentiment_analysis.rsi_sentiment === 'overbought' ? '超买' : '中性'}\n                    </Tag>\n                  </div>\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n        </Tabs>\n      </Card>\n\n      {/* 数据质量指示器 */}\n      {data_quality && (\n        <Card size=\"small\" className=\"data-quality\">\n          <Row align=\"middle\">\n            <Col>\n              <span>数据质量: </span>\n              <Progress\n                percent={data_quality.score * 100}\n                size=\"small\"\n                status={data_quality.score > 0.8 ? 'success' : data_quality.score > 0.5 ? 'normal' : 'exception'}\n              />\n            </Col>\n            {data_quality.issues && data_quality.issues.length > 0 && (\n              <Col offset={1}>\n                <Tooltip title={data_quality.issues.join(', ')}>\n                  <WarningOutlined style={{ color: '#faad14' }} />\n                </Tooltip>\n              </Col>\n            )}\n          </Row>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default MarketAnalysis;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,GAAG,CACHC,GAAG,CACHC,SAAS,CACTC,QAAQ,CACRC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,OAAO,CACPC,KAAK,KACA,MAAM,CACb,OACEC,YAAY,GAAI,CAAAC,kBAAkB,CAClCC,YAAY,GAAI,CAAAC,oBAAoB,CACpCC,iBAAiB,CACjBC,gBAAgB,CAChBC,iBAAiB,CACjBC,cAAc,CACdC,YAAY,CACZC,eAAe,KACV,mBAAmB,CAC1B,MAAO,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9B,KAAM,CAAEC,MAAO,CAAC,CAAGpB,MAAM,CACzB,KAAM,CAAEqB,OAAQ,CAAC,CAAGpB,IAAI,CAExB,KAAM,CAAAqB,cAAc,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC3B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,YAAY,CAAEC,eAAe,CAAC,CAAGjD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACkD,MAAM,CAAEC,SAAS,CAAC,CAAGnD,QAAQ,CAAC,MAAM,CAAC,CAC5C,KAAM,CAACoD,YAAY,CAAEC,eAAe,CAAC,CAAGrD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACsD,KAAK,CAAEC,QAAQ,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAAwD,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtCT,UAAU,CAAC,IAAI,CAAC,CAChBQ,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,uCAAuC,CAAE,CACpEC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBb,MAAM,CAAEA,MAAM,CACdc,QAAQ,CAAEA,QAAQ,CAClBC,oBAAoB,CAAE,IACxB,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAACR,QAAQ,CAACS,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,wBAAAC,MAAA,CAAwBX,QAAQ,CAACY,MAAM,CAAE,CAAC,CAC3D,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAb,QAAQ,CAACc,IAAI,CAAC,CAAC,CAClCtB,eAAe,CAACqB,IAAI,CAAC,CACvB,CAAE,MAAOE,GAAG,CAAE,CACZjB,QAAQ,CAACiB,GAAG,CAACC,OAAO,CAAC,CACrBC,OAAO,CAACpB,KAAK,CAAC,wBAAwB,CAAEkB,GAAG,CAAC,CAC9C,CAAC,OAAS,CACRzB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED9C,SAAS,CAAC,IAAM,CACduD,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAmB,sBAAsB,CAAIC,MAAM,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,KAAK,CAAE,MAAO,SAAS,CAC5B,IAAK,MAAM,CAAE,MAAO,OAAO,CAC3B,IAAK,MAAM,CAAE,MAAO,SAAS,CAC7B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIC,SAAS,EAAK,CACvC,OAAQA,SAAS,EACf,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,IAAK,SAAS,CAAE,MAAO,OAAO,CAC9B,IAAK,SAAS,CAAE,MAAO,SAAS,CAChC,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAIC,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAAG,GAAG,CAAE,MAAO,SAAS,CACjC,GAAIA,KAAK,CAAG,CAAC,GAAG,CAAE,MAAO,SAAS,CAClC,MAAO,SAAS,CAClB,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAG,CACvB,CACEC,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WACP,CAAC,CACD,CACEF,KAAK,CAAE,KAAK,CACZC,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,MAAM,CAAGC,KAAK,EAAKA,KAAK,CAAGA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,KAChD,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAGG,MAAM,eACb3D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CAAED,MAAM,GAAK,KAAK,CAAG,OAAO,CAAGA,MAAM,GAAK,MAAM,CAAG,KAAK,CAAG,MAAO,CAAAE,QAAA,CACzEF,MAAM,EAAI,SAAS,CACjB,CAET,CAAC,CACF,CAED;AACA,KAAM,CAAAG,aAAa,CAAG,CACpB,CACET,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,MAAM,CAAGO,IAAI,eACX/D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CAAEG,IAAI,GAAK,SAAS,CAAG,OAAO,CAAG,KAAM,CAAAF,QAAA,CAC9CE,IAAI,GAAK,SAAS,CAAG,IAAI,CAAG,IAAI,CAC9B,CAET,CAAC,CACD,CACEV,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,OAAO,CAClBC,GAAG,CAAE,OAAO,CACZC,MAAM,CAAGQ,KAAK,MAAAzB,MAAA,CAASyB,KAAK,CAACN,OAAO,CAAC,CAAC,CAAC,CACzC,CAAC,CACD,CACEL,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,MAAM,CAAGS,QAAQ,eACfjE,IAAA,CAACvB,QAAQ,EACPyF,OAAO,CAAED,QAAQ,CAAG,GAAI,CACxBE,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE,KAAM,CAChBC,WAAW,CAAEJ,QAAQ,CAAG,GAAG,CAAG,SAAS,CAAGA,QAAQ,CAAG,GAAG,CAAG,SAAS,CAAG,SAAU,CAClF,CAEL,CAAC,CACF,CAED,GAAIhD,OAAO,CAAE,CACX,mBACEf,KAAA,QAAKoE,SAAS,CAAC,yBAAyB,CAAAT,QAAA,eACtC7D,IAAA,CAACpB,IAAI,EAACuF,IAAI,CAAC,OAAO,CAAE,CAAC,cACrBnE,IAAA,MAAA6D,QAAA,CAAG,qDAAW,CAAG,CAAC,EACf,CAAC,CAEV,CAEA,GAAIpC,KAAK,CAAE,CACT,mBACEzB,IAAA,CAACrB,KAAK,EACJiE,OAAO,CAAC,0BAAM,CACd2B,WAAW,CAAE9C,KAAM,CACnBsC,IAAI,CAAC,OAAO,CACZS,QAAQ,MACRzB,MAAM,cACJ/C,IAAA,CAACnB,MAAM,EAACsF,IAAI,CAAC,OAAO,CAACM,OAAO,CAAE9C,mBAAoB,CAAAkC,QAAA,CAAC,cAEnD,CAAQ,CACT,CACF,CAAC,CAEN,CAEA,GAAI,CAAC1C,YAAY,CAAE,CACjB,mBACEjB,KAAA,QAAKoE,SAAS,CAAC,uBAAuB,CAAAT,QAAA,eACpC7D,IAAA,MAAA6D,QAAA,CAAG,sCAAM,CAAG,CAAC,cACb7D,IAAA,CAACnB,MAAM,EAACkF,IAAI,CAAC,SAAS,CAACU,OAAO,CAAE9C,mBAAoB,CAAAkC,QAAA,CAAC,0BAErD,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,KAAM,CACJa,UAAU,CACVC,kBAAkB,CAClBC,oBAAoB,CACpBC,kBAAkB,CAClBC,aAAa,CACbC,cAAc,CACdC,YACF,CAAC,CAAG7D,YAAY,CAEhB;AACA,KAAM,CAAA8D,aAAa,CAAG,EAAE,CACxB,GAAIN,kBAAkB,CAACO,UAAU,CAAE,CACjC,KAAM,CAAAA,UAAU,CAAGP,kBAAkB,CAACO,UAAU,CAChD,GAAIA,UAAU,CAACC,MAAM,CAAEF,aAAa,CAACG,IAAI,CAAC,CAAEC,SAAS,CAAE,SAAS,CAAE5B,KAAK,CAAEyB,UAAU,CAACC,MAAM,CAAExB,MAAM,CAAE,SAAU,CAAC,CAAC,CAChH,GAAIuB,UAAU,CAACI,MAAM,CAAEL,aAAa,CAACG,IAAI,CAAC,CAAEC,SAAS,CAAE,SAAS,CAAE5B,KAAK,CAAEyB,UAAU,CAACI,MAAM,CAAE3B,MAAM,CAAE,SAAU,CAAC,CAAC,CAChH,GAAIuB,UAAU,CAACK,MAAM,CAAEN,aAAa,CAACG,IAAI,CAAC,CAAEC,SAAS,CAAE,SAAS,CAAE5B,KAAK,CAAEyB,UAAU,CAACK,MAAM,CAAE5B,MAAM,CAAE,SAAU,CAAC,CAAC,CAChH,GAAIuB,UAAU,CAACM,GAAG,CAAEP,aAAa,CAACG,IAAI,CAAC,CACrCC,SAAS,CAAE,SAAS,CACpB5B,KAAK,CAAEyB,UAAU,CAACM,GAAG,CACrB7B,MAAM,CAAEuB,UAAU,CAACM,GAAG,CAAG,EAAE,CAAG,MAAM,CAAGN,UAAU,CAACM,GAAG,CAAG,EAAE,CAAG,KAAK,CAAG,SACvE,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,EAAE,CACrB,GAAId,kBAAkB,CAACe,kBAAkB,CAAE,CACzC,KAAM,CAAEC,OAAO,CAAEC,UAAW,CAAC,CAAGjB,kBAAkB,CAACe,kBAAkB,CACrEC,OAAO,CAACE,OAAO,CAAC,CAAC7B,KAAK,CAAE8B,KAAK,GAAK,CAChCL,UAAU,CAACL,IAAI,CAAC,CACd7B,GAAG,YAAAhB,MAAA,CAAauD,KAAK,CAAE,CACvB/B,IAAI,CAAE,SAAS,CACfC,KAAK,CAAEA,KAAK,CACZC,QAAQ,CAAE,GAAG,CAAG6B,KAAK,CAAG,GAC1B,CAAC,CAAC,CACJ,CAAC,CAAC,CACFF,UAAU,CAACC,OAAO,CAAC,CAAC7B,KAAK,CAAE8B,KAAK,GAAK,CACnCL,UAAU,CAACL,IAAI,CAAC,CACd7B,GAAG,eAAAhB,MAAA,CAAgBuD,KAAK,CAAE,CAC1B/B,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAEA,KAAK,CACZC,QAAQ,CAAE,GAAG,CAAG6B,KAAK,CAAG,GAC1B,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAEA,mBACE5F,KAAA,QAAKoE,SAAS,CAAC,iBAAiB,CAAAT,QAAA,eAE9B7D,IAAA,CAAC3B,IAAI,EAACiG,SAAS,CAAC,eAAe,CAACH,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC1C3D,KAAA,CAAC5B,GAAG,EAACyH,MAAM,CAAE,EAAG,CAACC,KAAK,CAAC,QAAQ,CAAAnC,QAAA,eAC7B7D,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF3D,KAAA,CAACd,KAAK,EAAAyE,QAAA,eACJ7D,IAAA,SAAA6D,QAAA,CAAM,2BAAK,CAAM,CAAC,cAClB7D,IAAA,CAAClB,KAAK,EACJ2E,KAAK,CAAEpC,MAAO,CACd4E,QAAQ,CAAGC,CAAC,EAAK5E,SAAS,CAAC4E,CAAC,CAACC,MAAM,CAAC1C,KAAK,CAAC2C,WAAW,CAAC,CAAC,CAAE,CACzDC,WAAW,CAAC,sCAAQ,CACpBC,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CACvB,CAAC,EACG,CAAC,CACL,CAAC,cACNvG,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF3D,KAAA,CAACd,KAAK,EAAAyE,QAAA,eACJ7D,IAAA,SAAA6D,QAAA,CAAM,2BAAK,CAAM,CAAC,cAClB3D,KAAA,CAACnB,MAAM,EAAC0E,KAAK,CAAElC,YAAa,CAAC0E,QAAQ,CAAEzE,eAAgB,CAAC8E,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAI,CAAE,CAAA1C,QAAA,eAC5E7D,IAAA,CAACG,MAAM,EAACsD,KAAK,CAAC,IAAI,CAAAI,QAAA,CAAC,eAAG,CAAQ,CAAC,cAC/B7D,IAAA,CAACG,MAAM,EAACsD,KAAK,CAAC,IAAI,CAAAI,QAAA,CAAC,eAAG,CAAQ,CAAC,cAC/B7D,IAAA,CAACG,MAAM,EAACsD,KAAK,CAAC,KAAK,CAAAI,QAAA,CAAC,gBAAI,CAAQ,CAAC,cACjC7D,IAAA,CAACG,MAAM,EAACsD,KAAK,CAAC,IAAI,CAAAI,QAAA,CAAC,eAAG,CAAQ,CAAC,cAC/B7D,IAAA,CAACG,MAAM,EAACsD,KAAK,CAAC,IAAI,CAAAI,QAAA,CAAC,SAAE,CAAQ,CAAC,EACxB,CAAC,EACJ,CAAC,CACL,CAAC,cACN7D,IAAA,CAACzB,GAAG,EAAAsF,QAAA,cACF7D,IAAA,CAACnB,MAAM,EACLkF,IAAI,CAAC,SAAS,CACdyC,IAAI,cAAExG,IAAA,CAACJ,cAAc,GAAE,CAAE,CACzB6E,OAAO,CAAE9C,mBAAoB,CAC7BV,OAAO,CAAEA,OAAQ,CAAA4C,QAAA,CAClB,0BAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACF,CAAC,cAGP3D,KAAA,CAAC5B,GAAG,EAACyH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAACzB,SAAS,CAAC,kBAAkB,CAAAT,QAAA,eACjD7D,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACzB7D,IAAA,CAAC3B,IAAI,EAAAwF,QAAA,cACH7D,IAAA,CAACxB,SAAS,EACR6E,KAAK,CAAC,0BAAM,CACZI,KAAK,CAAEiB,UAAU,CAACkC,aAAc,CAChCC,SAAS,CAAE,CAAE,CACbC,MAAM,CAAC,GAAG,CACVC,UAAU,CAAE,CACVnD,KAAK,CAAEc,UAAU,CAACsC,MAAM,EAAI,CAAC,CAAG,SAAS,CAAG,SAC9C,CAAE,CACFC,MAAM,cACJ/G,KAAA,SAAMoG,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAO,CAAE,CAAArD,QAAA,EAC/Ba,UAAU,CAACsC,MAAM,EAAI,CAAC,cAAGhH,IAAA,CAACV,kBAAkB,GAAE,CAAC,cAAGU,IAAA,CAACR,oBAAoB,GAAE,CAAC,CAC1EkF,UAAU,CAACyC,cAAc,CAACzD,OAAO,CAAC,CAAC,CAAC,CAAC,GACxC,EAAM,CACP,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cACN1D,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACzB7D,IAAA,CAAC3B,IAAI,EAAAwF,QAAA,cACH7D,IAAA,CAACxB,SAAS,EACR6E,KAAK,CAAC,0BAAM,CACZI,KAAK,CAAEqB,aAAa,CAACsC,OAAQ,CAC7BP,SAAS,CAAE,CAAE,CACbE,UAAU,CAAE,CAAEnD,KAAK,CAAEV,aAAa,CAAC4B,aAAa,CAACsC,OAAO,CAAE,CAAE,CAC5DH,MAAM,cACJjH,IAAA,CAACvB,QAAQ,EACPsF,IAAI,CAAC,QAAQ,CACbG,OAAO,CAAEmD,IAAI,CAACC,GAAG,CAACxC,aAAa,CAACsC,OAAO,CAAC,CAAG,GAAI,CAC/Cb,KAAK,CAAE,EAAG,CACVlC,WAAW,CAAEnB,aAAa,CAAC4B,aAAa,CAACsC,OAAO,CAAE,CAClDG,MAAM,CAAEA,CAAA,GAAM,EAAG,CAClB,CACF,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cACNvH,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACzB7D,IAAA,CAAC3B,IAAI,EAAAwF,QAAA,cACH3D,KAAA,QAAKoE,SAAS,CAAC,qBAAqB,CAAAT,QAAA,eAClC7D,IAAA,QAAKsE,SAAS,CAAC,sBAAsB,CAAAT,QAAA,CAAC,0BAAI,CAAK,CAAC,cAChD3D,KAAA,QAAKoE,SAAS,CAAC,wBAAwB,CAAAT,QAAA,eACrC7D,IAAA,CAACtB,GAAG,EACFkF,KAAK,CAAEd,sBAAsB,CAACiC,cAAc,CAAChC,MAAM,CAAE,CACrDuD,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAM,CAAEM,OAAO,CAAE,UAAW,CAAE,CAAA3D,QAAA,CAEhDkB,cAAc,CAAChC,MAAM,CACnB,CAAC,cACN7C,KAAA,QAAKoE,SAAS,CAAC,2BAA2B,CAAAT,QAAA,EAAC,sBACpC,CAACkB,cAAc,CAAC0C,UAAU,EAC5B,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,cACNzH,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACzB7D,IAAA,CAAC3B,IAAI,EAAAwF,QAAA,cACH3D,KAAA,QAAKoE,SAAS,CAAC,gBAAgB,CAAAT,QAAA,eAC7B7D,IAAA,QAAKsE,SAAS,CAAC,iBAAiB,CAAAT,QAAA,CAAC,0BAAI,CAAK,CAAC,cAC3C3D,KAAA,QAAKoE,SAAS,CAAC,mBAAmB,CAAAT,QAAA,eAChC7D,IAAA,CAACd,KAAK,EACJ0E,KAAK,CAAEZ,iBAAiB,CAAC6B,kBAAkB,CAAC5B,SAAS,CAAE,CACvDyE,IAAI,CACF7C,kBAAkB,CAAC5B,SAAS,GAAK,SAAS,CAAG,IAAI,CACjD4B,kBAAkB,CAAC5B,SAAS,GAAK,SAAS,CAAG,IAAI,CAAG,IACrD,CACDqD,KAAK,CAAE,CAAEY,QAAQ,CAAE,MAAO,CAAE,CAC7B,CAAC,cACFlH,IAAA,QAAKsE,SAAS,CAAC,sBAAsB,CAAAT,QAAA,cACnC7D,IAAA,CAACvB,QAAQ,EACPyF,OAAO,CAAEW,kBAAkB,CAAC4C,UAAU,CAAG,GAAI,CAC7CtD,IAAI,CAAC,OAAO,CACZC,QAAQ,CAAE,KAAM,CACjB,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,EACH,CAAC,cAGNpE,IAAA,CAAC3B,IAAI,EAACiG,SAAS,CAAC,eAAe,CAAAT,QAAA,cAC7B3D,KAAA,CAAClB,IAAI,EAAC2I,gBAAgB,CAAC,WAAW,CAAC5D,IAAI,CAAC,MAAM,CAAAF,QAAA,eAC5C3D,KAAA,CAACE,OAAO,EAACwH,GAAG,cAAE1H,KAAA,SAAA2D,QAAA,eAAM7D,IAAA,CAACN,gBAAgB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAmE,QAAA,eAClD3D,KAAA,CAAC5B,GAAG,EAACyH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAlC,QAAA,eACpB7D,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,EAAG,CAAAhE,QAAA,cAClB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC7B7D,IAAA,CAACf,KAAK,EACJ6I,OAAO,CAAE1E,gBAAiB,CAC1B2E,UAAU,CAAE9C,aAAc,CAC1B+C,UAAU,CAAE,KAAM,CAClB7D,IAAI,CAAC,OAAO,CACb,CAAC,CACE,CAAC,CACJ,CAAC,cACNnE,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,EAAG,CAAAhE,QAAA,cAClB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,gCAAO,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC9B7D,IAAA,CAACf,KAAK,EACJ6I,OAAO,CAAEhE,aAAc,CACvBiE,UAAU,CAAEtC,UAAW,CACvBuC,UAAU,CAAE,KAAM,CAClB7D,IAAI,CAAC,OAAO,CACb,CAAC,CACE,CAAC,CACJ,CAAC,EACH,CAAC,CAELQ,kBAAkB,CAACsD,OAAO,EAAItD,kBAAkB,CAACsD,OAAO,CAACC,MAAM,CAAG,CAAC,eAClElI,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACc,IAAI,CAAC,OAAO,CAACmC,KAAK,CAAE,CAAE6B,SAAS,CAAE,EAAG,CAAE,CAAAtE,QAAA,cACvD7D,IAAA,CAACZ,KAAK,EAACgJ,IAAI,MAAAvE,QAAA,CACRc,kBAAkB,CAACsD,OAAO,CAACI,GAAG,CAAC,CAAC1E,MAAM,CAAEmC,KAAK,gBAC5C9F,IAAA,CAACb,OAAO,EAAakE,KAAK,IAAAd,MAAA,CAAKoB,MAAM,CAAC0B,SAAS,OAAA9C,MAAA,CAAKoB,MAAM,CAACI,IAAI,CAAG,CAAAF,QAAA,cAChE3D,KAAA,CAACxB,GAAG,EACFkF,KAAK,CAAED,MAAM,CAACA,MAAM,GAAK,KAAK,CAAG,OAAO,CAAG,KAAM,CACjD6C,IAAI,CAAE7C,MAAM,CAACA,MAAM,GAAK,KAAK,cAAG3D,IAAA,CAACV,kBAAkB,GAAE,CAAC,cAAGU,IAAA,CAACR,oBAAoB,GAAE,CAAE,CAAAqE,QAAA,EAEjFF,MAAM,CAAC0B,SAAS,CAAC,GAAC,CAAC1B,MAAM,CAACA,MAAM,EAC9B,CAAC,EANMmC,KAOL,CACV,CAAC,CACG,CAAC,CACJ,CACP,GAvCsD,WAwChD,CAAC,cAEV9F,IAAA,CAACI,OAAO,EAACwH,GAAG,cAAE1H,KAAA,SAAA2D,QAAA,eAAM7D,IAAA,CAACP,iBAAiB,GAAE,CAAC,qBAAG,EAAM,CAAE,CAAAoE,QAAA,CACjDe,oBAAoB,EAAI0D,MAAM,CAACC,IAAI,CAAC3D,oBAAoB,CAAC,CAACsD,MAAM,CAAG,CAAC,cACnEhI,KAAA,CAAC5B,GAAG,EAACyH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAlC,QAAA,eACpB7D,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClB3D,KAAA,CAAC7B,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,eAC7B7D,IAAA,CAACxB,SAAS,EACR6E,KAAK,CAAC,0BAAW,CACjBI,KAAK,CAAE,EAAAnD,qBAAA,CAAAsE,oBAAoB,CAAC4D,WAAW,UAAAlI,qBAAA,iBAAhCA,qBAAA,CAAkCmI,QAAQ,GAAI,CAAE,CACvD5B,SAAS,CAAE,CAAE,CACd,CAAC,cACF7G,IAAA,QAAKsG,KAAK,CAAE,CAAE6B,SAAS,CAAE,EAAG,CAAE,CAAAtE,QAAA,cAC5B7D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CACR,EAAArD,sBAAA,CAAAqE,oBAAoB,CAAC8D,SAAS,UAAAnI,sBAAA,iBAA9BA,sBAAA,CAAgCoI,aAAa,IAAK,aAAa,CAAG,OAAO,CACzE,EAAAnI,sBAAA,CAAAoE,oBAAoB,CAAC8D,SAAS,UAAAlI,sBAAA,iBAA9BA,sBAAA,CAAgCmI,aAAa,IAAK,YAAY,CAAG,KAAK,CAAG,MAC1E,CAAA9E,QAAA,CACE,EAAApD,sBAAA,CAAAmE,oBAAoB,CAAC8D,SAAS,UAAAjI,sBAAA,iBAA9BA,sBAAA,CAAgCkI,aAAa,GAAI,MAAM,CACrD,CAAC,CACH,CAAC,EACF,CAAC,CACJ,CAAC,cACN3I,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,EAAG,CAAA9C,QAAA,cAClB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC7B3D,KAAA,QAAKoE,SAAS,CAAC,mBAAmB,CAAAT,QAAA,eAChC3D,KAAA,QAAKoE,SAAS,CAAC,aAAa,CAAAT,QAAA,eAC1B7D,IAAA,SAAA6D,QAAA,CAAM,2BAAK,CAAM,CAAC,cAClB7D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CACR,EAAAlD,sBAAA,CAAAkE,oBAAoB,CAACgE,gBAAgB,UAAAlI,sBAAA,iBAArCA,sBAAA,CAAuCmI,aAAa,IAAK,WAAW,CAAG,OAAO,CAC9E,EAAAlI,sBAAA,CAAAiE,oBAAoB,CAACgE,gBAAgB,UAAAjI,sBAAA,iBAArCA,sBAAA,CAAuCkI,aAAa,IAAK,MAAM,CAAG,MAAM,CACxE,EAAAjI,sBAAA,CAAAgE,oBAAoB,CAACgE,gBAAgB,UAAAhI,sBAAA,iBAArCA,sBAAA,CAAuCiI,aAAa,IAAK,MAAM,CAAG,KAAK,CAAG,QAC3E,CAAAhF,QAAA,CACE,EAAAhD,sBAAA,CAAA+D,oBAAoB,CAACgE,gBAAgB,UAAA/H,sBAAA,iBAArCA,sBAAA,CAAuCgI,aAAa,GAAI,SAAS,CAC/D,CAAC,EACH,CAAC,cACN3I,KAAA,QAAKoE,SAAS,CAAC,aAAa,CAAAT,QAAA,eAC1B7D,IAAA,SAAA6D,QAAA,CAAM,2BAAK,CAAM,CAAC,cAClB7D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CACR,EAAA9C,sBAAA,CAAA8D,oBAAoB,CAACgE,gBAAgB,UAAA9H,sBAAA,iBAArCA,sBAAA,CAAuCgI,UAAU,IAAK,KAAK,CAAG,OAAO,CACrE,EAAA/H,sBAAA,CAAA6D,oBAAoB,CAACgE,gBAAgB,UAAA7H,sBAAA,iBAArCA,sBAAA,CAAuC+H,UAAU,IAAK,MAAM,CAAG,KAAK,CAAG,QACxE,CAAAjF,QAAA,CACE,EAAA7C,sBAAA,CAAA4D,oBAAoB,CAACgE,gBAAgB,UAAA5H,sBAAA,iBAArCA,sBAAA,CAAuC8H,UAAU,GAAI,QAAQ,CAC3D,CAAC,EACH,CAAC,EACH,CAAC,CACF,CAAC,CACJ,CAAC,EACH,CAAC,cAEN9I,IAAA,CAACrB,KAAK,EACJiE,OAAO,CAAC,kDAAU,CAClB2B,WAAW,CAAC,8GAAoB,CAChCR,IAAI,CAAC,MAAM,CACXS,QAAQ,MACT,CACF,EArDsD,aAsDhD,CAAC,cAEVxE,IAAA,CAACI,OAAO,EAACwH,GAAG,cAAE1H,KAAA,SAAA2D,QAAA,eAAM7D,IAAA,CAACL,iBAAiB,GAAE,CAAC,2BAAI,EAAM,CAAE,CAAAkE,QAAA,cACnD3D,KAAA,CAAC5B,GAAG,EAACyH,MAAM,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CAAAlC,QAAA,eACpB7D,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACjB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,0BAAM,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC7B7D,IAAA,CAACxB,SAAS,EACR6E,KAAK,CAAC,0BAAM,CACZI,KAAK,CAAEoB,kBAAkB,CAACkE,cAAc,CAAG,GAAI,CAC/ClC,SAAS,CAAE,CAAE,CACbI,MAAM,CAAC,GAAG,CACVF,UAAU,CAAE,CACVnD,KAAK,CAAEiB,kBAAkB,CAACkE,cAAc,EAAI,CAAC,CAAG,SAAS,CAAG,SAC9D,CAAE,CACH,CAAC,CACE,CAAC,CACJ,CAAC,cACN/I,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACjB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,gCAAO,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC9B7D,IAAA,CAACxB,SAAS,EACR6E,KAAK,CAAC,gCAAO,CACbI,KAAK,CAAEoB,kBAAkB,CAACmE,YAAY,CAAG,GAAI,CAC7CnC,SAAS,CAAE,CAAE,CACbI,MAAM,CAAC,GAAG,CACVF,UAAU,CAAE,CACVnD,KAAK,CAAEiB,kBAAkB,CAACmE,YAAY,EAAI,CAAC,CAAG,SAAS,CAAG,SAC5D,CAAE,CACH,CAAC,CACE,CAAC,CACJ,CAAC,cACNhJ,IAAA,CAACzB,GAAG,EAACkI,EAAE,CAAE,EAAG,CAACE,EAAE,CAAE,CAAE,CAAA9C,QAAA,cACjB7D,IAAA,CAAC3B,IAAI,EAACgF,KAAK,CAAC,iBAAO,CAACc,IAAI,CAAC,OAAO,CAAAN,QAAA,cAC9B7D,IAAA,QAAKsE,SAAS,CAAC,eAAe,CAAAT,QAAA,cAC5B7D,IAAA,CAACtB,GAAG,EAACkF,KAAK,CACRiB,kBAAkB,CAACoE,aAAa,GAAK,UAAU,CAAG,OAAO,CACzDpE,kBAAkB,CAACoE,aAAa,GAAK,YAAY,CAAG,KAAK,CAAG,MAC7D,CAAApF,QAAA,CACEgB,kBAAkB,CAACoE,aAAa,GAAK,UAAU,CAAG,IAAI,CACtDpE,kBAAkB,CAACoE,aAAa,GAAK,YAAY,CAAG,IAAI,CAAG,IAAI,CAC7D,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CAAC,EACH,CAAC,EAzCkD,WA0CjD,CAAC,EACN,CAAC,CACH,CAAC,CAGNjE,YAAY,eACXhF,IAAA,CAAC3B,IAAI,EAAC8F,IAAI,CAAC,OAAO,CAACG,SAAS,CAAC,cAAc,CAAAT,QAAA,cACzC3D,KAAA,CAAC5B,GAAG,EAAC0H,KAAK,CAAC,QAAQ,CAAAnC,QAAA,eACjB3D,KAAA,CAAC3B,GAAG,EAAAsF,QAAA,eACF7D,IAAA,SAAA6D,QAAA,CAAM,4BAAM,CAAM,CAAC,cACnB7D,IAAA,CAACvB,QAAQ,EACPyF,OAAO,CAAEc,YAAY,CAAC7B,KAAK,CAAG,GAAI,CAClCgB,IAAI,CAAC,OAAO,CACZ3B,MAAM,CAAEwC,YAAY,CAAC7B,KAAK,CAAG,GAAG,CAAG,SAAS,CAAG6B,YAAY,CAAC7B,KAAK,CAAG,GAAG,CAAG,QAAQ,CAAG,WAAY,CAClG,CAAC,EACC,CAAC,CACL6B,YAAY,CAACkE,MAAM,EAAIlE,YAAY,CAACkE,MAAM,CAAChB,MAAM,CAAG,CAAC,eACpDlI,IAAA,CAACzB,GAAG,EAAC4K,MAAM,CAAE,CAAE,CAAAtF,QAAA,cACb7D,IAAA,CAACb,OAAO,EAACkE,KAAK,CAAE2B,YAAY,CAACkE,MAAM,CAACE,IAAI,CAAC,IAAI,CAAE,CAAAvF,QAAA,cAC7C7D,IAAA,CAACF,eAAe,EAACwG,KAAK,CAAE,CAAE1C,KAAK,CAAE,SAAU,CAAE,CAAE,CAAC,CACzC,CAAC,CACP,CACN,EACE,CAAC,CACF,CACP,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAvD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}