{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,CardH<PERSON>er,CardT<PERSON>le,CardContent}from'../ui/card';import{But<PERSON>}from'../ui/button';import{Badge}from'../ui/badge';import{Tabs,Ta<PERSON>Content,Ta<PERSON>List,TabsTrigger}from'../ui/tabs';import{Alert,AlertDescription}from'../ui/alert';import{Brain,Users,MessageSquare,TrendingUp,Shield,CheckCircle,AlertTriangle,Loader2,BarChart3}from'lucide-react';import PatternValidation from'./PatternValidation';import AgentAnalysis from'./AgentAnalysis';import DebateViewer from'./DebateViewer';import SystemStatus from'./SystemStatus';import AgentDebateViewer from'./AgentDebateViewer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TradingAgentsPanel=_ref=>{var _finalRecommendation$;let{detectedPatterns=[],currentSymbol='AAPL',candleData=[],onValidationComplete,stockInfo=null}=_ref;const[activeTab,setActiveTab]=useState('validation');const[systemStatus,setSystemStatus]=useState(null);const[isLoading,setIsLoading]=useState(false);const[finalRecommendation,setFinalRecommendation]=useState(null);// 获取系统状态\nuseEffect(()=>{fetchSystemStatus();},[]);const fetchSystemStatus=async()=>{try{const response=await fetch('/api/trading-agents/status');const data=await response.json();setSystemStatus(data);}catch(error){console.error('获取系统状态失败:',error);}};const getStatusBadge=()=>{var _systemStatus$data;if(!systemStatus)return/*#__PURE__*/_jsx(Badge,{variant:\"secondary\",children:\"\\u68C0\\u67E5\\u4E2D...\"});if(systemStatus.status==='success'&&(_systemStatus$data=systemStatus.data)!==null&&_systemStatus$data!==void 0&&_systemStatus$data.initialized){return/*#__PURE__*/_jsxs(Badge,{variant:\"success\",className:\"bg-green-100 text-green-800\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 mr-1\"}),\"\\u7CFB\\u7EDF\\u5C31\\u7EEA\"]});}else{return/*#__PURE__*/_jsxs(Badge,{variant:\"destructive\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"w-3 h-3 mr-1\"}),\"\\u7CFB\\u7EDF\\u5F02\\u5E38\"]});}};const getAgentCount=()=>{var _systemStatus$data2;if(!(systemStatus!==null&&systemStatus!==void 0&&(_systemStatus$data2=systemStatus.data)!==null&&_systemStatus$data2!==void 0&&_systemStatus$data2.agents))return 0;return systemStatus.data.agents.active_agents||0;};return/*#__PURE__*/_jsxs(\"div\",{className:\"w-full space-y-4\",children:[/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsx(CardHeader,{className:\"pb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Brain,{className:\"w-5 h-5 text-blue-600\"}),/*#__PURE__*/_jsx(CardTitle,{className:\"text-lg\",children:\"TradingAgents \\u591A\\u667A\\u80FD\\u4F53\\u7CFB\\u7EDF\"}),getStatusBadge()]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 text-sm text-gray-600\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(Users,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[getAgentCount(),\" \\u4E2A\\u667A\\u80FD\\u4F53\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(BarChart3,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[detectedPatterns.length,\" \\u4E2A\\u5F62\\u6001\"]})]})]})]})})}),/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onValueChange:setActiveTab,className:\"w-full\",children:[/*#__PURE__*/_jsxs(TabsList,{className:\"grid w-full grid-cols-4\",children:[/*#__PURE__*/_jsxs(TabsTrigger,{value:\"validation\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Shield,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5F62\\u6001\\u9A8C\\u8BC1\"})]}),/*#__PURE__*/_jsxs(TabsTrigger,{value:\"analysis\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(TrendingUp,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u667A\\u80FD\\u4F53\\u5206\\u6790\"})]}),/*#__PURE__*/_jsxs(TabsTrigger,{value:\"debate\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(MessageSquare,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u667A\\u80FD\\u4F53\\u8FA9\\u8BBA\"})]}),/*#__PURE__*/_jsxs(TabsTrigger,{value:\"status\",className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Brain,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7CFB\\u7EDF\\u72B6\\u6001\"})]})]}),/*#__PURE__*/_jsx(TabsContent,{value:\"validation\",className:\"space-y-4\",children:/*#__PURE__*/_jsx(PatternValidation,{detectedPatterns:detectedPatterns,currentSymbol:currentSymbol,candleData:candleData,onValidationComplete:onValidationComplete})}),/*#__PURE__*/_jsx(TabsContent,{value:\"analysis\",className:\"space-y-4\",children:/*#__PURE__*/_jsx(AgentAnalysis,{currentSymbol:currentSymbol,candleData:candleData})}),/*#__PURE__*/_jsx(TabsContent,{value:\"debate\",className:\"space-y-4\",children:/*#__PURE__*/_jsx(AgentDebateViewer,{stockSymbol:currentSymbol,patternData:detectedPatterns[0],onRecommendationChange:setFinalRecommendation})}),/*#__PURE__*/_jsx(TabsContent,{value:\"status\",className:\"space-y-4\",children:/*#__PURE__*/_jsx(SystemStatus,{systemStatus:systemStatus,onRefresh:fetchSystemStatus})})]}),finalRecommendation&&/*#__PURE__*/_jsxs(Card,{className:\"border-2 border-blue-200 bg-blue-50\",children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsxs(CardTitle,{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-5 h-5 text-green-600\"}),\"AI\\u667A\\u80FD\\u4F53\\u6295\\u8D44\\u5EFA\\u8BAE\"]})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"px-4 py-2 rounded-lg font-bold text-lg \".concat(finalRecommendation.decision==='buy'?'bg-green-100 text-green-800':finalRecommendation.decision==='sell'?'bg-red-100 text-red-800':'bg-yellow-100 text-yellow-800'),children:finalRecommendation.decision==='buy'?'🚀 建议买入':finalRecommendation.decision==='sell'?'📉 建议卖出':'⏸️ 建议持有'}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600\",children:[\"\\u7F6E\\u4FE1\\u5EA6: \",(_finalRecommendation$=finalRecommendation.confidence)===null||_finalRecommendation$===void 0?void 0:_finalRecommendation$.toFixed(1),\"%\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:finalRecommendation.suggestedPosition})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 text-sm text-gray-700\",children:finalRecommendation.reasoning})]})]}),systemStatus&&systemStatus.status!=='success'&&/*#__PURE__*/_jsxs(Alert,{children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(AlertDescription,{children:\"TradingAgents\\u7CFB\\u7EDF\\u672A\\u5B8C\\u5168\\u5C31\\u7EEA\\u3002\\u67D0\\u4E9B\\u529F\\u80FD\\u53EF\\u80FD\\u4E0D\\u53EF\\u7528\\u3002 \\u8BF7\\u68C0\\u67E5\\u7CFB\\u7EDF\\u914D\\u7F6E\\u6216\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\\u3002\"})]})]});};export default TradingAgentsPanel;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Tabs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabsList", "TabsTrigger", "<PERSON><PERSON>", "AlertDescription", "Brain", "Users", "MessageSquare", "TrendingUp", "Shield", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Loader2", "BarChart3", "PatternValidation", "AgentAnalysis", "DebateViewer", "SystemStatus", "AgentDebateViewer", "jsx", "_jsx", "jsxs", "_jsxs", "TradingAgentsPanel", "_ref", "_finalRecommendation$", "detectedPatterns", "currentSymbol", "candleData", "onValidationComplete", "stockInfo", "activeTab", "setActiveTab", "systemStatus", "setSystemStatus", "isLoading", "setIsLoading", "finalRecommendation", "setFinalRecommendation", "fetchSystemStatus", "response", "fetch", "data", "json", "error", "console", "getStatusBadge", "_systemStatus$data", "variant", "children", "status", "initialized", "className", "getAgentCount", "_systemStatus$data2", "agents", "active_agents", "length", "value", "onValueChange", "stockSymbol", "patternData", "onRecommendationChange", "onRefresh", "concat", "decision", "confidence", "toFixed", "suggestedPosition", "reasoning"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent } from '../ui/card';\nimport { But<PERSON> } from '../ui/button';\nimport { Badge } from '../ui/badge';\nimport { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';\nimport { Alert, AlertDescription } from '../ui/alert';\nimport { \n  Brain, \n  Users, \n  MessageSquare, \n  TrendingUp, \n  Shield, \n  CheckCircle, \n  AlertTriangle,\n  Loader2,\n  BarChart3\n} from 'lucide-react';\n\nimport PatternValidation from './PatternValidation';\nimport AgentAnalysis from './AgentAnalysis';\nimport DebateViewer from './DebateViewer';\nimport SystemStatus from './SystemStatus';\nimport AgentDebateViewer from './AgentDebateViewer';\n\nconst TradingAgentsPanel = ({\n  detectedPatterns = [],\n  currentSymbol = 'AAPL',\n  candleData = [],\n  onValidationComplete,\n  stockInfo = null\n}) => {\n  const [activeTab, setActiveTab] = useState('validation');\n  const [systemStatus, setSystemStatus] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [finalRecommendation, setFinalRecommendation] = useState(null);\n\n  // 获取系统状态\n  useEffect(() => {\n    fetchSystemStatus();\n  }, []);\n\n  const fetchSystemStatus = async () => {\n    try {\n      const response = await fetch('/api/trading-agents/status');\n      const data = await response.json();\n      setSystemStatus(data);\n    } catch (error) {\n      console.error('获取系统状态失败:', error);\n    }\n  };\n\n  const getStatusBadge = () => {\n    if (!systemStatus) return <Badge variant=\"secondary\">检查中...</Badge>;\n    \n    if (systemStatus.status === 'success' && systemStatus.data?.initialized) {\n      return <Badge variant=\"success\" className=\"bg-green-100 text-green-800\">\n        <CheckCircle className=\"w-3 h-3 mr-1\" />\n        系统就绪\n      </Badge>;\n    } else {\n      return <Badge variant=\"destructive\">\n        <AlertTriangle className=\"w-3 h-3 mr-1\" />\n        系统异常\n      </Badge>;\n    }\n  };\n\n  const getAgentCount = () => {\n    if (!systemStatus?.data?.agents) return 0;\n    return systemStatus.data.agents.active_agents || 0;\n  };\n\n  return (\n    <div className=\"w-full space-y-4\">\n      {/* 系统状态头部 */}\n      <Card>\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Brain className=\"w-5 h-5 text-blue-600\" />\n              <CardTitle className=\"text-lg\">TradingAgents 多智能体系统</CardTitle>\n              {getStatusBadge()}\n            </div>\n            <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n              <div className=\"flex items-center space-x-1\">\n                <Users className=\"w-4 h-4\" />\n                <span>{getAgentCount()} 个智能体</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <BarChart3 className=\"w-4 h-4\" />\n                <span>{detectedPatterns.length} 个形态</span>\n              </div>\n            </div>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* 功能选项卡 */}\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"validation\" className=\"flex items-center space-x-2\">\n            <Shield className=\"w-4 h-4\" />\n            <span>形态验证</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"analysis\" className=\"flex items-center space-x-2\">\n            <TrendingUp className=\"w-4 h-4\" />\n            <span>智能体分析</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"debate\" className=\"flex items-center space-x-2\">\n            <MessageSquare className=\"w-4 h-4\" />\n            <span>智能体辩论</span>\n          </TabsTrigger>\n          <TabsTrigger value=\"status\" className=\"flex items-center space-x-2\">\n            <Brain className=\"w-4 h-4\" />\n            <span>系统状态</span>\n          </TabsTrigger>\n        </TabsList>\n\n        {/* 形态验证 */}\n        <TabsContent value=\"validation\" className=\"space-y-4\">\n          <PatternValidation\n            detectedPatterns={detectedPatterns}\n            currentSymbol={currentSymbol}\n            candleData={candleData}\n            onValidationComplete={onValidationComplete}\n          />\n        </TabsContent>\n\n        {/* 智能体分析 */}\n        <TabsContent value=\"analysis\" className=\"space-y-4\">\n          <AgentAnalysis\n            currentSymbol={currentSymbol}\n            candleData={candleData}\n          />\n        </TabsContent>\n\n        {/* 智能体辩论 */}\n        <TabsContent value=\"debate\" className=\"space-y-4\">\n          <AgentDebateViewer\n            stockSymbol={currentSymbol}\n            patternData={detectedPatterns[0]}\n            onRecommendationChange={setFinalRecommendation}\n          />\n        </TabsContent>\n\n        {/* 系统状态 */}\n        <TabsContent value=\"status\" className=\"space-y-4\">\n          <SystemStatus\n            systemStatus={systemStatus}\n            onRefresh={fetchSystemStatus}\n          />\n        </TabsContent>\n      </Tabs>\n\n      {/* 最终投资建议摘要 */}\n      {finalRecommendation && (\n        <Card className=\"border-2 border-blue-200 bg-blue-50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CheckCircle className=\"w-5 h-5 text-green-600\" />\n              AI智能体投资建议\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-4\">\n                <div className={`px-4 py-2 rounded-lg font-bold text-lg ${\n                  finalRecommendation.decision === 'buy' ? 'bg-green-100 text-green-800' :\n                  finalRecommendation.decision === 'sell' ? 'bg-red-100 text-red-800' :\n                  'bg-yellow-100 text-yellow-800'\n                }`}>\n                  {finalRecommendation.decision === 'buy' ? '🚀 建议买入' :\n                   finalRecommendation.decision === 'sell' ? '📉 建议卖出' :\n                   '⏸️ 建议持有'}\n                </div>\n                <div className=\"text-sm text-gray-600\">\n                  置信度: {finalRecommendation.confidence?.toFixed(1)}%\n                </div>\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                {finalRecommendation.suggestedPosition}\n              </div>\n            </div>\n            <div className=\"mt-2 text-sm text-gray-700\">\n              {finalRecommendation.reasoning}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 系统未就绪提示 */}\n      {systemStatus && systemStatus.status !== 'success' && (\n        <Alert>\n          <AlertTriangle className=\"h-4 w-4\" />\n          <AlertDescription>\n            TradingAgents系统未完全就绪。某些功能可能不可用。\n            请检查系统配置或联系管理员。\n          </AlertDescription>\n        </Alert>\n      )}\n    </div>\n  );\n};\n\nexport default TradingAgentsPanel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,UAAU,CAAEC,SAAS,CAAEC,WAAW,KAAQ,YAAY,CACrE,OAASC,MAAM,KAAQ,cAAc,CACrC,OAASC,KAAK,KAAQ,aAAa,CACnC,OAASC,IAAI,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,YAAY,CACrE,OAASC,KAAK,CAAEC,gBAAgB,KAAQ,aAAa,CACrD,OACEC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,UAAU,CACVC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,OAAO,CACPC,SAAS,KACJ,cAAc,CAErB,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,MAAO,CAAAC,aAAa,KAAM,iBAAiB,CAC3C,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAMrB,KAAAC,qBAAA,IANsB,CAC1BC,gBAAgB,CAAG,EAAE,CACrBC,aAAa,CAAG,MAAM,CACtBC,UAAU,CAAG,EAAE,CACfC,oBAAoB,CACpBC,SAAS,CAAG,IACd,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,SAAS,CAAEC,YAAY,CAAC,CAAGzC,QAAQ,CAAC,YAAY,CAAC,CACxD,KAAM,CAAC0C,YAAY,CAAEC,eAAe,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC4C,SAAS,CAAEC,YAAY,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8C,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG/C,QAAQ,CAAC,IAAI,CAAC,CAEpE;AACAC,SAAS,CAAC,IAAM,CACd+C,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,4BAA4B,CAAC,CAC1D,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClCT,eAAe,CAACQ,IAAI,CAAC,CACvB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAGA,CAAA,GAAM,KAAAC,kBAAA,CAC3B,GAAI,CAACd,YAAY,CAAE,mBAAOb,IAAA,CAACtB,KAAK,EAACkD,OAAO,CAAC,WAAW,CAAAC,QAAA,CAAC,uBAAM,CAAO,CAAC,CAEnE,GAAIhB,YAAY,CAACiB,MAAM,GAAK,SAAS,GAAAH,kBAAA,CAAId,YAAY,CAACS,IAAI,UAAAK,kBAAA,WAAjBA,kBAAA,CAAmBI,WAAW,CAAE,CACvE,mBAAO7B,KAAA,CAACxB,KAAK,EAACkD,OAAO,CAAC,SAAS,CAACI,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eACrE7B,IAAA,CAACV,WAAW,EAAC0C,SAAS,CAAC,cAAc,CAAE,CAAC,2BAE1C,EAAO,CAAC,CACV,CAAC,IAAM,CACL,mBAAO9B,KAAA,CAACxB,KAAK,EAACkD,OAAO,CAAC,aAAa,CAAAC,QAAA,eACjC7B,IAAA,CAACT,aAAa,EAACyC,SAAS,CAAC,cAAc,CAAE,CAAC,2BAE5C,EAAO,CAAC,CACV,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CAC1B,GAAI,EAACrB,YAAY,SAAZA,YAAY,YAAAqB,mBAAA,CAAZrB,YAAY,CAAES,IAAI,UAAAY,mBAAA,WAAlBA,mBAAA,CAAoBC,MAAM,EAAE,MAAO,EAAC,CACzC,MAAO,CAAAtB,YAAY,CAACS,IAAI,CAACa,MAAM,CAACC,aAAa,EAAI,CAAC,CACpD,CAAC,CAED,mBACElC,KAAA,QAAK8B,SAAS,CAAC,kBAAkB,CAAAH,QAAA,eAE/B7B,IAAA,CAAC3B,IAAI,EAAAwD,QAAA,cACH7B,IAAA,CAAC1B,UAAU,EAAC0D,SAAS,CAAC,MAAM,CAAAH,QAAA,cAC1B3B,KAAA,QAAK8B,SAAS,CAAC,mCAAmC,CAAAH,QAAA,eAChD3B,KAAA,QAAK8B,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eAC1C7B,IAAA,CAACf,KAAK,EAAC+C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC3ChC,IAAA,CAACzB,SAAS,EAACyD,SAAS,CAAC,SAAS,CAAAH,QAAA,CAAC,oDAAoB,CAAW,CAAC,CAC9DH,cAAc,CAAC,CAAC,EACd,CAAC,cACNxB,KAAA,QAAK8B,SAAS,CAAC,mDAAmD,CAAAH,QAAA,eAChE3B,KAAA,QAAK8B,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eAC1C7B,IAAA,CAACd,KAAK,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7B9B,KAAA,SAAA2B,QAAA,EAAOI,aAAa,CAAC,CAAC,CAAC,2BAAK,EAAM,CAAC,EAChC,CAAC,cACN/B,KAAA,QAAK8B,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eAC1C7B,IAAA,CAACP,SAAS,EAACuC,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC9B,KAAA,SAAA2B,QAAA,EAAOvB,gBAAgB,CAAC+B,MAAM,CAAC,qBAAI,EAAM,CAAC,EACvC,CAAC,EACH,CAAC,EACH,CAAC,CACI,CAAC,CACT,CAAC,cAGPnC,KAAA,CAACvB,IAAI,EAAC2D,KAAK,CAAE3B,SAAU,CAAC4B,aAAa,CAAE3B,YAAa,CAACoB,SAAS,CAAC,QAAQ,CAAAH,QAAA,eACrE3B,KAAA,CAACrB,QAAQ,EAACmD,SAAS,CAAC,yBAAyB,CAAAH,QAAA,eAC3C3B,KAAA,CAACpB,WAAW,EAACwD,KAAK,CAAC,YAAY,CAACN,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eACrE7B,IAAA,CAACX,MAAM,EAAC2C,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9BhC,IAAA,SAAA6B,QAAA,CAAM,0BAAI,CAAM,CAAC,EACN,CAAC,cACd3B,KAAA,CAACpB,WAAW,EAACwD,KAAK,CAAC,UAAU,CAACN,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eACnE7B,IAAA,CAACZ,UAAU,EAAC4C,SAAS,CAAC,SAAS,CAAE,CAAC,cAClChC,IAAA,SAAA6B,QAAA,CAAM,gCAAK,CAAM,CAAC,EACP,CAAC,cACd3B,KAAA,CAACpB,WAAW,EAACwD,KAAK,CAAC,QAAQ,CAACN,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eACjE7B,IAAA,CAACb,aAAa,EAAC6C,SAAS,CAAC,SAAS,CAAE,CAAC,cACrChC,IAAA,SAAA6B,QAAA,CAAM,gCAAK,CAAM,CAAC,EACP,CAAC,cACd3B,KAAA,CAACpB,WAAW,EAACwD,KAAK,CAAC,QAAQ,CAACN,SAAS,CAAC,6BAA6B,CAAAH,QAAA,eACjE7B,IAAA,CAACf,KAAK,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7BhC,IAAA,SAAA6B,QAAA,CAAM,0BAAI,CAAM,CAAC,EACN,CAAC,EACN,CAAC,cAGX7B,IAAA,CAACpB,WAAW,EAAC0D,KAAK,CAAC,YAAY,CAACN,SAAS,CAAC,WAAW,CAAAH,QAAA,cACnD7B,IAAA,CAACN,iBAAiB,EAChBY,gBAAgB,CAAEA,gBAAiB,CACnCC,aAAa,CAAEA,aAAc,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,oBAAoB,CAAEA,oBAAqB,CAC5C,CAAC,CACS,CAAC,cAGdT,IAAA,CAACpB,WAAW,EAAC0D,KAAK,CAAC,UAAU,CAACN,SAAS,CAAC,WAAW,CAAAH,QAAA,cACjD7B,IAAA,CAACL,aAAa,EACZY,aAAa,CAAEA,aAAc,CAC7BC,UAAU,CAAEA,UAAW,CACxB,CAAC,CACS,CAAC,cAGdR,IAAA,CAACpB,WAAW,EAAC0D,KAAK,CAAC,QAAQ,CAACN,SAAS,CAAC,WAAW,CAAAH,QAAA,cAC/C7B,IAAA,CAACF,iBAAiB,EAChB0C,WAAW,CAAEjC,aAAc,CAC3BkC,WAAW,CAAEnC,gBAAgB,CAAC,CAAC,CAAE,CACjCoC,sBAAsB,CAAExB,sBAAuB,CAChD,CAAC,CACS,CAAC,cAGdlB,IAAA,CAACpB,WAAW,EAAC0D,KAAK,CAAC,QAAQ,CAACN,SAAS,CAAC,WAAW,CAAAH,QAAA,cAC/C7B,IAAA,CAACH,YAAY,EACXgB,YAAY,CAAEA,YAAa,CAC3B8B,SAAS,CAAExB,iBAAkB,CAC9B,CAAC,CACS,CAAC,EACV,CAAC,CAGNF,mBAAmB,eAClBf,KAAA,CAAC7B,IAAI,EAAC2D,SAAS,CAAC,qCAAqC,CAAAH,QAAA,eACnD7B,IAAA,CAAC1B,UAAU,EAAAuD,QAAA,cACT3B,KAAA,CAAC3B,SAAS,EAACyD,SAAS,CAAC,yBAAyB,CAAAH,QAAA,eAC5C7B,IAAA,CAACV,WAAW,EAAC0C,SAAS,CAAC,wBAAwB,CAAE,CAAC,+CAEpD,EAAW,CAAC,CACF,CAAC,cACb9B,KAAA,CAAC1B,WAAW,EAAAqD,QAAA,eACV3B,KAAA,QAAK8B,SAAS,CAAC,mCAAmC,CAAAH,QAAA,eAChD3B,KAAA,QAAK8B,SAAS,CAAC,yBAAyB,CAAAH,QAAA,eACtC7B,IAAA,QAAKgC,SAAS,2CAAAY,MAAA,CACZ3B,mBAAmB,CAAC4B,QAAQ,GAAK,KAAK,CAAG,6BAA6B,CACtE5B,mBAAmB,CAAC4B,QAAQ,GAAK,MAAM,CAAG,yBAAyB,CACnE,+BAA+B,CAC9B,CAAAhB,QAAA,CACAZ,mBAAmB,CAAC4B,QAAQ,GAAK,KAAK,CAAG,SAAS,CAClD5B,mBAAmB,CAAC4B,QAAQ,GAAK,MAAM,CAAG,SAAS,CACnD,SAAS,CACP,CAAC,cACN3C,KAAA,QAAK8B,SAAS,CAAC,uBAAuB,CAAAH,QAAA,EAAC,sBAChC,EAAAxB,qBAAA,CAACY,mBAAmB,CAAC6B,UAAU,UAAAzC,qBAAA,iBAA9BA,qBAAA,CAAgC0C,OAAO,CAAC,CAAC,CAAC,CAAC,GACnD,EAAK,CAAC,EACH,CAAC,cACN/C,IAAA,QAAKgC,SAAS,CAAC,uBAAuB,CAAAH,QAAA,CACnCZ,mBAAmB,CAAC+B,iBAAiB,CACnC,CAAC,EACH,CAAC,cACNhD,IAAA,QAAKgC,SAAS,CAAC,4BAA4B,CAAAH,QAAA,CACxCZ,mBAAmB,CAACgC,SAAS,CAC3B,CAAC,EACK,CAAC,EACV,CACP,CAGApC,YAAY,EAAIA,YAAY,CAACiB,MAAM,GAAK,SAAS,eAChD5B,KAAA,CAACnB,KAAK,EAAA8C,QAAA,eACJ7B,IAAA,CAACT,aAAa,EAACyC,SAAS,CAAC,SAAS,CAAE,CAAC,cACrChC,IAAA,CAAChB,gBAAgB,EAAA6C,QAAA,CAAC,gNAGlB,CAAkB,CAAC,EACd,CACR,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}