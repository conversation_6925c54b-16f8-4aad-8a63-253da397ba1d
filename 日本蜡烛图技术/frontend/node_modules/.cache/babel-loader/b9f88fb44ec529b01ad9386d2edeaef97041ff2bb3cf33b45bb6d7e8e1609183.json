{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Card,CardHeader,CardTitle,CardContent}from'../ui/card';import{But<PERSON>}from'../ui/button';import{Badge}from'../ui/badge';import{Alert,AlertDescription}from'../ui/alert';import{MessageSquare,Users,TrendingUp,TrendingDown,Minus,Play,Pause,RotateCcw,CheckCircle,AlertTriangle,Clock,Target}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AgentDebateViewer=_ref=>{let{stockSymbol,patternData,onRecommendationChange}=_ref;const[debateState,setDebateState]=useState('idle');// idle, running, completed\nconst[currentRound,setCurrentRound]=useState(0);const[debateHistory,setDebateHistory]=useState([]);const[finalRecommendation,setFinalRecommendation]=useState(null);const[participants,setParticipants]=useState([]);const[isAutoPlay,setIsAutoPlay]=useState(false);// 智能体参与者配置\nconst agentProfiles={'BullResearcher':{name:'多头研究员',role:'看涨分析',color:'bg-green-100 text-green-800',icon:TrendingUp,bias:'bullish'},'BearResearcher':{name:'空头研究员',role:'看跌分析',color:'bg-red-100 text-red-800',icon:TrendingDown,bias:'bearish'},'MarketAnalyst':{name:'市场分析师',role:'技术分析',color:'bg-blue-100 text-blue-800',icon:Target,bias:'neutral'},'PortfolioManager':{name:'投资组合经理',role:'风险管理',color:'bg-purple-100 text-purple-800',icon:CheckCircle,bias:'conservative'},'DebateModerator':{name:'辩论主持人',role:'共识建立',color:'bg-gray-100 text-gray-800',icon:Users,bias:'moderator'}};// 模拟辩论数据\nconst simulateDebateRound=round=>{const agents=Object.keys(agentProfiles);const roundData={round:round+1,timestamp:new Date().toLocaleTimeString(),discussions:[]};// 生成每个智能体的发言\nagents.forEach(agentId=>{if(agentId==='DebateModerator'&&round===0){roundData.discussions.push({agent:agentId,message:\"\\u6B22\\u8FCE\\u53C2\\u52A0\".concat(stockSymbol,\"\\u80A1\\u7968\\u6295\\u8D44\\u51B3\\u7B56\\u8FA9\\u8BBA\\u3002\\u8BF7\\u5404\\u4F4D\\u667A\\u80FD\\u4F53\\u57FA\\u4E8E\\u5F53\\u524D\\u5F62\\u6001\\u5206\\u6790\\u53D1\\u8868\\u89C2\\u70B9\\u3002\"),timestamp:new Date().toLocaleTimeString(),type:'moderation'});}else if(agentId!=='DebateModerator'){const profile=agentProfiles[agentId];let message='';switch(profile.bias){case'bullish':message=\"\\u57FA\\u4E8E\\u5F53\\u524D\".concat((patternData===null||patternData===void 0?void 0:patternData.pattern_name)||'形态',\"\\u5206\\u6790\\uFF0C\\u6211\\u8BA4\\u4E3A\\u8FD9\\u662F\\u4E00\\u4E2A\\u770B\\u6DA8\\u4FE1\\u53F7\\u3002\\u6280\\u672F\\u6307\\u6807\\u663E\\u793A\\u4E0A\\u6DA8\\u52A8\\u80FD\\u5F3A\\u52B2\\uFF0C\\u5EFA\\u8BAE\\u4E70\\u5165\\u3002\");break;case'bearish':message=\"\\u4ECE\\u98CE\\u9669\\u89D2\\u5EA6\\u5206\\u6790\\uFF0C\\u5F53\\u524D\\u5F62\\u6001\\u53EF\\u80FD\\u662F\\u5047\\u7A81\\u7834\\u3002\\u5E02\\u573A\\u60C5\\u7EEA\\u8FC7\\u4E8E\\u4E50\\u89C2\\uFF0C\\u5EFA\\u8BAE\\u8C28\\u614E\\u6216\\u8003\\u8651\\u5356\\u51FA\\u3002\";break;case'neutral':message=\"\\u6280\\u672F\\u9762\\u663E\\u793A\".concat(stockSymbol,\"\\u5904\\u4E8E\\u5173\\u952E\\u4F4D\\u7F6E\\u3002\\u9700\\u8981\\u7ED3\\u5408\\u6210\\u4EA4\\u91CF\\u548C\\u5176\\u4ED6\\u6307\\u6807\\u7EFC\\u5408\\u5224\\u65AD\\uFF0C\\u5EFA\\u8BAE\\u89C2\\u671B\\u3002\");break;case'conservative':message=\"\\u4ECE\\u6295\\u8D44\\u7EC4\\u5408\\u98CE\\u9669\\u7BA1\\u7406\\u89D2\\u5EA6\\uFF0C\\u5EFA\\u8BAE\\u63A7\\u5236\\u4ED3\\u4F4D\\u3002\\u5373\\u4F7F\\u770B\\u6DA8\\u4E5F\\u4E0D\\u5E94\\u8D85\\u8FC7\\u603B\\u8D44\\u4EA7\\u76845%\\u3002\";break;}roundData.discussions.push({agent:agentId,message:message,timestamp:new Date().toLocaleTimeString(),type:'argument',stance:profile.bias==='bullish'?'buy':profile.bias==='bearish'?'sell':'hold'});}});return roundData;};// 开始辩论\nconst startDebate=async()=>{setDebateState('running');setCurrentRound(0);setDebateHistory([]);setFinalRecommendation(null);// 设置参与者\nsetParticipants(Object.keys(agentProfiles));if(isAutoPlay){// 自动播放模式\nfor(let i=0;i<3;i++){await new Promise(resolve=>setTimeout(resolve,2000));const roundData=simulateDebateRound(i);setDebateHistory(prev=>[...prev,roundData]);setCurrentRound(i+1);}// 生成最终建议\nawait generateFinalRecommendation();}else{// 手动模式 - 只生成第一轮\nconst roundData=simulateDebateRound(0);setDebateHistory([roundData]);setCurrentRound(1);}};// 下一轮辩论\nconst nextRound=async()=>{if(currentRound<3){const roundData=simulateDebateRound(currentRound);setDebateHistory(prev=>[...prev,roundData]);setCurrentRound(prev=>prev+1);if(currentRound===2){// 最后一轮后生成建议\nsetTimeout(()=>generateFinalRecommendation(),1000);}}};// 生成最终投资建议\nconst generateFinalRecommendation=async()=>{await new Promise(resolve=>setTimeout(resolve,1500));// 模拟智能体投票\nconst votes={buy:Math.floor(Math.random()*3)+1,sell:Math.floor(Math.random()*2)+1,hold:Math.floor(Math.random()*2)+1};const totalVotes=votes.buy+votes.sell+votes.hold;const confidence=Math.max(votes.buy,votes.sell,votes.hold)/totalVotes;let decision='hold';if(votes.buy>votes.sell&&votes.buy>votes.hold){decision='buy';}else if(votes.sell>votes.buy&&votes.sell>votes.hold){decision='sell';}const recommendation={decision,confidence:confidence*100,votes,reasoning:\"\\u7ECF\\u8FC7\".concat(currentRound,\"\\u8F6E\\u8FA9\\u8BBA\\uFF0C\\u667A\\u80FD\\u4F53\\u8FBE\\u6210\\u5171\\u8BC6\\uFF1A\").concat(decision==='buy'?'买入':decision==='sell'?'卖出':'持有'),riskLevel:confidence>0.7?'low':confidence>0.5?'medium':'high',suggestedPosition:decision==='buy'?'建议仓位：3-5%':decision==='sell'?'建议减仓：50-100%':'维持当前仓位',timeHorizon:decision==='buy'?'持有期：1-3个月':decision==='sell'?'立即执行':'继续观察1-2周'};setFinalRecommendation(recommendation);setDebateState('completed');// 通知父组件\nif(onRecommendationChange){onRecommendationChange(recommendation);}};// 重置辩论\nconst resetDebate=()=>{setDebateState('idle');setCurrentRound(0);setDebateHistory([]);setFinalRecommendation(null);setParticipants([]);};// 获取决策颜色\nconst getDecisionColor=decision=>{switch(decision){case'buy':return'bg-green-100 text-green-800 border-green-200';case'sell':return'bg-red-100 text-red-800 border-red-200';default:return'bg-yellow-100 text-yellow-800 border-yellow-200';}};// 获取决策图标\nconst getDecisionIcon=decision=>{switch(decision){case'buy':return/*#__PURE__*/_jsx(TrendingUp,{className:\"w-4 h-4\"});case'sell':return/*#__PURE__*/_jsx(TrendingDown,{className:\"w-4 h-4\"});default:return/*#__PURE__*/_jsx(Minus,{className:\"w-4 h-4\"});}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsxs(CardTitle,{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(MessageSquare,{className:\"w-5 h-5\"}),\"\\u667A\\u80FD\\u4F53\\u8FA9\\u8BBA\\u7CFB\\u7EDF\"]})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:\"\\u80A1\\u7968:\"}),/*#__PURE__*/_jsx(Badge,{variant:\"outline\",children:stockSymbol||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:\"\\u5F62\\u6001:\"}),/*#__PURE__*/_jsx(Badge,{variant:\"outline\",children:(patternData===null||patternData===void 0?void 0:patternData.chinese_name)||'N/A'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium\",children:\"\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(Badge,{className:debateState==='running'?'bg-blue-100 text-blue-800':debateState==='completed'?'bg-green-100 text-green-800':'bg-gray-100 text-gray-800',children:debateState==='running'?'辩论中':debateState==='completed'?'已完成':'待开始'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2\",children:[debateState==='idle'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Button,{onClick:startDebate,className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(Play,{className:\"w-4 h-4\"}),\"\\u5F00\\u59CB\\u8FA9\\u8BBA\"]}),/*#__PURE__*/_jsxs(\"label\",{className:\"flex items-center gap-2 text-sm\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isAutoPlay,onChange:e=>setIsAutoPlay(e.target.checked)}),\"\\u81EA\\u52A8\\u64AD\\u653E\"]})]}),debateState==='running'&&!isAutoPlay&&currentRound<3&&/*#__PURE__*/_jsxs(Button,{onClick:nextRound,className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(MessageSquare,{className:\"w-4 h-4\"}),\"\\u4E0B\\u4E00\\u8F6E (\",currentRound,\"/3)\"]}),debateState==='running'&&isAutoPlay&&/*#__PURE__*/_jsxs(Button,{disabled:true,className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(Clock,{className:\"w-4 h-4 animate-spin\"}),\"\\u81EA\\u52A8\\u8FDB\\u884C\\u4E2D...\"]}),(debateState==='completed'||debateState==='running')&&/*#__PURE__*/_jsxs(Button,{onClick:resetDebate,variant:\"outline\",className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(RotateCcw,{className:\"w-4 h-4\"}),\"\\u91CD\\u65B0\\u5F00\\u59CB\"]})]})]})]}),participants.length>0&&/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsxs(CardTitle,{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(Users,{className:\"w-5 h-5\"}),\"\\u53C2\\u4E0E\\u667A\\u80FD\\u4F53 (\",participants.length,\")\"]})}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 md:grid-cols-3 gap-3\",children:participants.map(agentId=>{const profile=agentProfiles[agentId];const IconComponent=profile.icon;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 p-2 border rounded-lg\",children:[/*#__PURE__*/_jsx(IconComponent,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:profile.name}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs text-gray-500\",children:profile.role})]})]},agentId);})})})]}),debateHistory.length>0&&/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsx(CardTitle,{children:\"\\u8FA9\\u8BBA\\u8FC7\\u7A0B\"})}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:debateHistory.map((round,roundIndex)=>/*#__PURE__*/_jsxs(\"div\",{className:\"border-l-2 border-blue-200 pl-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"font-medium text-sm text-blue-600 mb-2\",children:[\"\\u7B2C \",round.round,\" \\u8F6E - \",round.timestamp]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:round.discussions.map((discussion,discussionIndex)=>{const profile=agentProfiles[discussion.agent];const IconComponent=profile.icon;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-3 p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full flex items-center justify-center \".concat(profile.color),children:/*#__PURE__*/_jsx(IconComponent,{className:\"w-4 h-4\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-2 mb-1\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-sm\",children:profile.name}),discussion.stance&&/*#__PURE__*/_jsxs(Badge,{className:getDecisionColor(discussion.stance),children:[getDecisionIcon(discussion.stance),/*#__PURE__*/_jsx(\"span\",{className:\"ml-1\",children:discussion.stance==='buy'?'买入':discussion.stance==='sell'?'卖出':'持有'})]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:discussion.timestamp})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-700\",children:discussion.message})]})]},discussionIndex);})})]},roundIndex))})})]}),finalRecommendation&&/*#__PURE__*/_jsxs(Card,{className:\"border-2 border-blue-200\",children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsxs(CardTitle,{className:\"flex items-center gap-2\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-5 h-5 text-green-600\"}),\"\\u6700\\u7EC8\\u6295\\u8D44\\u5EFA\\u8BAE\"]})}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg border-2 \".concat(getDecisionColor(finalRecommendation.decision)),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-2\",children:[getDecisionIcon(finalRecommendation.decision),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-bold\",children:finalRecommendation.decision==='buy'?'建议买入':finalRecommendation.decision==='sell'?'建议卖出':'建议持有'}),/*#__PURE__*/_jsxs(Badge,{variant:\"outline\",children:[\"\\u7F6E\\u4FE1\\u5EA6: \",finalRecommendation.confidence.toFixed(1),\"%\"]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:finalRecommendation.reasoning})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-3 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-3 bg-green-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-green-600\",children:finalRecommendation.votes.buy}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-green-600\",children:\"\\u4E70\\u5165\\u7968\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-3 bg-red-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-red-600\",children:finalRecommendation.votes.sell}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-red-600\",children:\"\\u5356\\u51FA\\u7968\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center p-3 bg-yellow-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-yellow-600\",children:finalRecommendation.votes.hold}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-yellow-600\",children:\"\\u6301\\u6709\\u7968\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"\\u98CE\\u9669\\u7B49\\u7EA7:\"}),/*#__PURE__*/_jsx(Badge,{className:finalRecommendation.riskLevel==='low'?'bg-green-100 text-green-800 ml-2':finalRecommendation.riskLevel==='medium'?'bg-yellow-100 text-yellow-800 ml-2':'bg-red-100 text-red-800 ml-2',children:finalRecommendation.riskLevel==='low'?'低风险':finalRecommendation.riskLevel==='medium'?'中风险':'高风险'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"\\u5EFA\\u8BAE\\u4ED3\\u4F4D:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:finalRecommendation.suggestedPosition})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"\\u65F6\\u95F4\\u6846\\u67B6:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2\",children:finalRecommendation.timeHorizon})]})]})]})})]})]});};export default AgentDebateViewer;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "<PERSON><PERSON>", "AlertDescription", "MessageSquare", "Users", "TrendingUp", "TrendingDown", "Minus", "Play", "Pause", "RotateCcw", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Target", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AgentDebateViewer", "_ref", "stockSymbol", "patternData", "onRecommendationChange", "debateState", "setDebateState", "currentRound", "setCurrentRound", "debateHistory", "setDebateHistory", "finalRecommendation", "setFinalRecommendation", "participants", "setParticipants", "isAutoPlay", "setIsAutoPlay", "agentProfiles", "name", "role", "color", "icon", "bias", "simulateDebateRound", "round", "agents", "Object", "keys", "roundData", "timestamp", "Date", "toLocaleTimeString", "discussions", "for<PERSON>ach", "agentId", "push", "agent", "message", "concat", "type", "profile", "pattern_name", "stance", "startDebate", "i", "Promise", "resolve", "setTimeout", "prev", "generateFinalRecommendation", "nextRound", "votes", "buy", "Math", "floor", "random", "sell", "hold", "totalVotes", "confidence", "max", "decision", "recommendation", "reasoning", "riskLevel", "suggestedPosition", "timeHorizon", "resetDebate", "getDecisionColor", "getDecisionIcon", "className", "children", "variant", "chinese_name", "onClick", "checked", "onChange", "e", "target", "disabled", "length", "map", "IconComponent", "roundIndex", "discussion", "discussionIndex", "toFixed"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentDebateViewer.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  CardHeader, \n  CardTitle, \n  CardContent \n} from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Badge } from '../ui/badge';\nimport { Alert, AlertDescription } from '../ui/alert';\nimport { \n  MessageSquare, \n  Users, \n  TrendingUp, \n  TrendingDown, \n  Minus,\n  Play,\n  Pause,\n  RotateCcw,\n  CheckCircle,\n  AlertTriangle,\n  Clock,\n  Target\n} from 'lucide-react';\n\nconst AgentDebateViewer = ({ stockSymbol, patternData, onRecommendationChange }) => {\n  const [debateState, setDebateState] = useState('idle'); // idle, running, completed\n  const [currentRound, setCurrentRound] = useState(0);\n  const [debateHistory, setDebateHistory] = useState([]);\n  const [finalRecommendation, setFinalRecommendation] = useState(null);\n  const [participants, setParticipants] = useState([]);\n  const [isAutoPlay, setIsAutoPlay] = useState(false);\n\n  // 智能体参与者配置\n  const agentProfiles = {\n    'BullResearcher': {\n      name: '多头研究员',\n      role: '看涨分析',\n      color: 'bg-green-100 text-green-800',\n      icon: TrendingUp,\n      bias: 'bullish'\n    },\n    'BearResearcher': {\n      name: '空头研究员', \n      role: '看跌分析',\n      color: 'bg-red-100 text-red-800',\n      icon: TrendingDown,\n      bias: 'bearish'\n    },\n    'MarketAnalyst': {\n      name: '市场分析师',\n      role: '技术分析',\n      color: 'bg-blue-100 text-blue-800',\n      icon: Target,\n      bias: 'neutral'\n    },\n    'PortfolioManager': {\n      name: '投资组合经理',\n      role: '风险管理',\n      color: 'bg-purple-100 text-purple-800',\n      icon: CheckCircle,\n      bias: 'conservative'\n    },\n    'DebateModerator': {\n      name: '辩论主持人',\n      role: '共识建立',\n      color: 'bg-gray-100 text-gray-800',\n      icon: Users,\n      bias: 'moderator'\n    }\n  };\n\n  // 模拟辩论数据\n  const simulateDebateRound = (round) => {\n    const agents = Object.keys(agentProfiles);\n    const roundData = {\n      round: round + 1,\n      timestamp: new Date().toLocaleTimeString(),\n      discussions: []\n    };\n\n    // 生成每个智能体的发言\n    agents.forEach(agentId => {\n      if (agentId === 'DebateModerator' && round === 0) {\n        roundData.discussions.push({\n          agent: agentId,\n          message: `欢迎参加${stockSymbol}股票投资决策辩论。请各位智能体基于当前形态分析发表观点。`,\n          timestamp: new Date().toLocaleTimeString(),\n          type: 'moderation'\n        });\n      } else if (agentId !== 'DebateModerator') {\n        const profile = agentProfiles[agentId];\n        let message = '';\n        \n        switch (profile.bias) {\n          case 'bullish':\n            message = `基于当前${patternData?.pattern_name || '形态'}分析，我认为这是一个看涨信号。技术指标显示上涨动能强劲，建议买入。`;\n            break;\n          case 'bearish':\n            message = `从风险角度分析，当前形态可能是假突破。市场情绪过于乐观，建议谨慎或考虑卖出。`;\n            break;\n          case 'neutral':\n            message = `技术面显示${stockSymbol}处于关键位置。需要结合成交量和其他指标综合判断，建议观望。`;\n            break;\n          case 'conservative':\n            message = `从投资组合风险管理角度，建议控制仓位。即使看涨也不应超过总资产的5%。`;\n            break;\n        }\n\n        roundData.discussions.push({\n          agent: agentId,\n          message: message,\n          timestamp: new Date().toLocaleTimeString(),\n          type: 'argument',\n          stance: profile.bias === 'bullish' ? 'buy' : profile.bias === 'bearish' ? 'sell' : 'hold'\n        });\n      }\n    });\n\n    return roundData;\n  };\n\n  // 开始辩论\n  const startDebate = async () => {\n    setDebateState('running');\n    setCurrentRound(0);\n    setDebateHistory([]);\n    setFinalRecommendation(null);\n    \n    // 设置参与者\n    setParticipants(Object.keys(agentProfiles));\n\n    if (isAutoPlay) {\n      // 自动播放模式\n      for (let i = 0; i < 3; i++) {\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        const roundData = simulateDebateRound(i);\n        setDebateHistory(prev => [...prev, roundData]);\n        setCurrentRound(i + 1);\n      }\n      \n      // 生成最终建议\n      await generateFinalRecommendation();\n    } else {\n      // 手动模式 - 只生成第一轮\n      const roundData = simulateDebateRound(0);\n      setDebateHistory([roundData]);\n      setCurrentRound(1);\n    }\n  };\n\n  // 下一轮辩论\n  const nextRound = async () => {\n    if (currentRound < 3) {\n      const roundData = simulateDebateRound(currentRound);\n      setDebateHistory(prev => [...prev, roundData]);\n      setCurrentRound(prev => prev + 1);\n      \n      if (currentRound === 2) {\n        // 最后一轮后生成建议\n        setTimeout(() => generateFinalRecommendation(), 1000);\n      }\n    }\n  };\n\n  // 生成最终投资建议\n  const generateFinalRecommendation = async () => {\n    await new Promise(resolve => setTimeout(resolve, 1500));\n    \n    // 模拟智能体投票\n    const votes = {\n      buy: Math.floor(Math.random() * 3) + 1,\n      sell: Math.floor(Math.random() * 2) + 1,\n      hold: Math.floor(Math.random() * 2) + 1\n    };\n\n    const totalVotes = votes.buy + votes.sell + votes.hold;\n    const confidence = Math.max(votes.buy, votes.sell, votes.hold) / totalVotes;\n    \n    let decision = 'hold';\n    if (votes.buy > votes.sell && votes.buy > votes.hold) {\n      decision = 'buy';\n    } else if (votes.sell > votes.buy && votes.sell > votes.hold) {\n      decision = 'sell';\n    }\n\n    const recommendation = {\n      decision,\n      confidence: confidence * 100,\n      votes,\n      reasoning: `经过${currentRound}轮辩论，智能体达成共识：${decision === 'buy' ? '买入' : decision === 'sell' ? '卖出' : '持有'}`,\n      riskLevel: confidence > 0.7 ? 'low' : confidence > 0.5 ? 'medium' : 'high',\n      suggestedPosition: decision === 'buy' ? '建议仓位：3-5%' : decision === 'sell' ? '建议减仓：50-100%' : '维持当前仓位',\n      timeHorizon: decision === 'buy' ? '持有期：1-3个月' : decision === 'sell' ? '立即执行' : '继续观察1-2周'\n    };\n\n    setFinalRecommendation(recommendation);\n    setDebateState('completed');\n    \n    // 通知父组件\n    if (onRecommendationChange) {\n      onRecommendationChange(recommendation);\n    }\n  };\n\n  // 重置辩论\n  const resetDebate = () => {\n    setDebateState('idle');\n    setCurrentRound(0);\n    setDebateHistory([]);\n    setFinalRecommendation(null);\n    setParticipants([]);\n  };\n\n  // 获取决策颜色\n  const getDecisionColor = (decision) => {\n    switch (decision) {\n      case 'buy': return 'bg-green-100 text-green-800 border-green-200';\n      case 'sell': return 'bg-red-100 text-red-800 border-red-200';\n      default: return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n    }\n  };\n\n  // 获取决策图标\n  const getDecisionIcon = (decision) => {\n    switch (decision) {\n      case 'buy': return <TrendingUp className=\"w-4 h-4\" />;\n      case 'sell': return <TrendingDown className=\"w-4 h-4\" />;\n      default: return <Minus className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 控制面板 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <MessageSquare className=\"w-5 h-5\" />\n            智能体辩论系统\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center gap-4 mb-4\">\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\">股票:</span>\n              <Badge variant=\"outline\">{stockSymbol || 'N/A'}</Badge>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\">形态:</span>\n              <Badge variant=\"outline\">{patternData?.chinese_name || 'N/A'}</Badge>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm font-medium\">状态:</span>\n              <Badge className={\n                debateState === 'running' ? 'bg-blue-100 text-blue-800' :\n                debateState === 'completed' ? 'bg-green-100 text-green-800' :\n                'bg-gray-100 text-gray-800'\n              }>\n                {debateState === 'running' ? '辩论中' : \n                 debateState === 'completed' ? '已完成' : '待开始'}\n              </Badge>\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            {debateState === 'idle' && (\n              <>\n                <Button onClick={startDebate} className=\"flex items-center gap-2\">\n                  <Play className=\"w-4 h-4\" />\n                  开始辩论\n                </Button>\n                <label className=\"flex items-center gap-2 text-sm\">\n                  <input\n                    type=\"checkbox\"\n                    checked={isAutoPlay}\n                    onChange={(e) => setIsAutoPlay(e.target.checked)}\n                  />\n                  自动播放\n                </label>\n              </>\n            )}\n            \n            {debateState === 'running' && !isAutoPlay && currentRound < 3 && (\n              <Button onClick={nextRound} className=\"flex items-center gap-2\">\n                <MessageSquare className=\"w-4 h-4\" />\n                下一轮 ({currentRound}/3)\n              </Button>\n            )}\n            \n            {debateState === 'running' && isAutoPlay && (\n              <Button disabled className=\"flex items-center gap-2\">\n                <Clock className=\"w-4 h-4 animate-spin\" />\n                自动进行中...\n              </Button>\n            )}\n            \n            {(debateState === 'completed' || debateState === 'running') && (\n              <Button onClick={resetDebate} variant=\"outline\" className=\"flex items-center gap-2\">\n                <RotateCcw className=\"w-4 h-4\" />\n                重新开始\n              </Button>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 参与者列表 */}\n      {participants.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Users className=\"w-5 h-5\" />\n              参与智能体 ({participants.length})\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n              {participants.map(agentId => {\n                const profile = agentProfiles[agentId];\n                const IconComponent = profile.icon;\n                return (\n                  <div key={agentId} className=\"flex items-center gap-2 p-2 border rounded-lg\">\n                    <IconComponent className=\"w-4 h-4\" />\n                    <div>\n                      <div className=\"font-medium text-sm\">{profile.name}</div>\n                      <div className=\"text-xs text-gray-500\">{profile.role}</div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 辩论历史 */}\n      {debateHistory.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>辩论过程</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {debateHistory.map((round, roundIndex) => (\n                <div key={roundIndex} className=\"border-l-2 border-blue-200 pl-4\">\n                  <div className=\"font-medium text-sm text-blue-600 mb-2\">\n                    第 {round.round} 轮 - {round.timestamp}\n                  </div>\n                  <div className=\"space-y-3\">\n                    {round.discussions.map((discussion, discussionIndex) => {\n                      const profile = agentProfiles[discussion.agent];\n                      const IconComponent = profile.icon;\n                      return (\n                        <div key={discussionIndex} className=\"flex gap-3 p-3 bg-gray-50 rounded-lg\">\n                          <div className=\"flex-shrink-0\">\n                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${profile.color}`}>\n                              <IconComponent className=\"w-4 h-4\" />\n                            </div>\n                          </div>\n                          <div className=\"flex-1\">\n                            <div className=\"flex items-center gap-2 mb-1\">\n                              <span className=\"font-medium text-sm\">{profile.name}</span>\n                              {discussion.stance && (\n                                <Badge className={getDecisionColor(discussion.stance)}>\n                                  {getDecisionIcon(discussion.stance)}\n                                  <span className=\"ml-1\">\n                                    {discussion.stance === 'buy' ? '买入' : \n                                     discussion.stance === 'sell' ? '卖出' : '持有'}\n                                  </span>\n                                </Badge>\n                              )}\n                              <span className=\"text-xs text-gray-500\">{discussion.timestamp}</span>\n                            </div>\n                            <p className=\"text-sm text-gray-700\">{discussion.message}</p>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 最终建议 */}\n      {finalRecommendation && (\n        <Card className=\"border-2 border-blue-200\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CheckCircle className=\"w-5 h-5 text-green-600\" />\n              最终投资建议\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {/* 主要决策 */}\n              <div className={`p-4 rounded-lg border-2 ${getDecisionColor(finalRecommendation.decision)}`}>\n                <div className=\"flex items-center gap-3 mb-2\">\n                  {getDecisionIcon(finalRecommendation.decision)}\n                  <span className=\"text-lg font-bold\">\n                    {finalRecommendation.decision === 'buy' ? '建议买入' : \n                     finalRecommendation.decision === 'sell' ? '建议卖出' : '建议持有'}\n                  </span>\n                  <Badge variant=\"outline\">\n                    置信度: {finalRecommendation.confidence.toFixed(1)}%\n                  </Badge>\n                </div>\n                <p className=\"text-sm\">{finalRecommendation.reasoning}</p>\n              </div>\n\n              {/* 投票详情 */}\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div className=\"text-center p-3 bg-green-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-green-600\">{finalRecommendation.votes.buy}</div>\n                  <div className=\"text-sm text-green-600\">买入票</div>\n                </div>\n                <div className=\"text-center p-3 bg-red-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-red-600\">{finalRecommendation.votes.sell}</div>\n                  <div className=\"text-sm text-red-600\">卖出票</div>\n                </div>\n                <div className=\"text-center p-3 bg-yellow-50 rounded-lg\">\n                  <div className=\"text-2xl font-bold text-yellow-600\">{finalRecommendation.votes.hold}</div>\n                  <div className=\"text-sm text-yellow-600\">持有票</div>\n                </div>\n              </div>\n\n              {/* 详细建议 */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium\">风险等级:</span>\n                  <Badge className={\n                    finalRecommendation.riskLevel === 'low' ? 'bg-green-100 text-green-800 ml-2' :\n                    finalRecommendation.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 ml-2' :\n                    'bg-red-100 text-red-800 ml-2'\n                  }>\n                    {finalRecommendation.riskLevel === 'low' ? '低风险' :\n                     finalRecommendation.riskLevel === 'medium' ? '中风险' : '高风险'}\n                  </Badge>\n                </div>\n                <div>\n                  <span className=\"font-medium\">建议仓位:</span>\n                  <span className=\"ml-2\">{finalRecommendation.suggestedPosition}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium\">时间框架:</span>\n                  <span className=\"ml-2\">{finalRecommendation.timeHorizon}</span>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default AgentDebateViewer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,UAAU,CACVC,SAAS,CACTC,WAAW,KACN,YAAY,CACnB,OAASC,MAAM,KAAQ,cAAc,CACrC,OAASC,KAAK,KAAQ,aAAa,CACnC,OAASC,KAAK,CAAEC,gBAAgB,KAAQ,aAAa,CACrD,OACEC,aAAa,CACbC,KAAK,CACLC,UAAU,CACVC,YAAY,CACZC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,SAAS,CACTC,WAAW,CACXC,aAAa,CACbC,KAAK,CACLC,MAAM,KACD,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtB,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAA0D,IAAzD,CAAEC,WAAW,CAAEC,WAAW,CAAEC,sBAAuB,CAAC,CAAAH,IAAA,CAC7E,KAAM,CAACI,WAAW,CAAEC,cAAc,CAAC,CAAGlC,QAAQ,CAAC,MAAM,CAAC,CAAE;AACxD,KAAM,CAACmC,YAAY,CAAEC,eAAe,CAAC,CAAGpC,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CACpE,KAAM,CAACyC,YAAY,CAAEC,eAAe,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC2C,UAAU,CAAEC,aAAa,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAA6C,aAAa,CAAG,CACpB,gBAAgB,CAAE,CAChBC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,6BAA6B,CACpCC,IAAI,CAAErC,UAAU,CAChBsC,IAAI,CAAE,SACR,CAAC,CACD,gBAAgB,CAAE,CAChBJ,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,yBAAyB,CAChCC,IAAI,CAAEpC,YAAY,CAClBqC,IAAI,CAAE,SACR,CAAC,CACD,eAAe,CAAE,CACfJ,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,2BAA2B,CAClCC,IAAI,CAAE5B,MAAM,CACZ6B,IAAI,CAAE,SACR,CAAC,CACD,kBAAkB,CAAE,CAClBJ,IAAI,CAAE,QAAQ,CACdC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,+BAA+B,CACtCC,IAAI,CAAE/B,WAAW,CACjBgC,IAAI,CAAE,cACR,CAAC,CACD,iBAAiB,CAAE,CACjBJ,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,2BAA2B,CAClCC,IAAI,CAAEtC,KAAK,CACXuC,IAAI,CAAE,WACR,CACF,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAIC,KAAK,EAAK,CACrC,KAAM,CAAAC,MAAM,CAAGC,MAAM,CAACC,IAAI,CAACV,aAAa,CAAC,CACzC,KAAM,CAAAW,SAAS,CAAG,CAChBJ,KAAK,CAAEA,KAAK,CAAG,CAAC,CAChBK,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC1CC,WAAW,CAAE,EACf,CAAC,CAED;AACAP,MAAM,CAACQ,OAAO,CAACC,OAAO,EAAI,CACxB,GAAIA,OAAO,GAAK,iBAAiB,EAAIV,KAAK,GAAK,CAAC,CAAE,CAChDI,SAAS,CAACI,WAAW,CAACG,IAAI,CAAC,CACzBC,KAAK,CAAEF,OAAO,CACdG,OAAO,4BAAAC,MAAA,CAASpC,WAAW,4KAA8B,CACzD2B,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC1CQ,IAAI,CAAE,YACR,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIL,OAAO,GAAK,iBAAiB,CAAE,CACxC,KAAM,CAAAM,OAAO,CAAGvB,aAAa,CAACiB,OAAO,CAAC,CACtC,GAAI,CAAAG,OAAO,CAAG,EAAE,CAEhB,OAAQG,OAAO,CAAClB,IAAI,EAClB,IAAK,SAAS,CACZe,OAAO,4BAAAC,MAAA,CAAU,CAAAnC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsC,YAAY,GAAI,IAAI,0MAAmC,CACrF,MACF,IAAK,SAAS,CACZJ,OAAO,uOAA2C,CAClD,MACF,IAAK,SAAS,CACZA,OAAO,kCAAAC,MAAA,CAAWpC,WAAW,kLAA+B,CAC5D,MACF,IAAK,cAAc,CACjBmC,OAAO,2MAAwC,CAC/C,MACJ,CAEAT,SAAS,CAACI,WAAW,CAACG,IAAI,CAAC,CACzBC,KAAK,CAAEF,OAAO,CACdG,OAAO,CAAEA,OAAO,CAChBR,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAC1CQ,IAAI,CAAE,UAAU,CAChBG,MAAM,CAAEF,OAAO,CAAClB,IAAI,GAAK,SAAS,CAAG,KAAK,CAAGkB,OAAO,CAAClB,IAAI,GAAK,SAAS,CAAG,MAAM,CAAG,MACrF,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAAM,SAAS,CAClB,CAAC,CAED;AACA,KAAM,CAAAe,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9BrC,cAAc,CAAC,SAAS,CAAC,CACzBE,eAAe,CAAC,CAAC,CAAC,CAClBE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,sBAAsB,CAAC,IAAI,CAAC,CAE5B;AACAE,eAAe,CAACY,MAAM,CAACC,IAAI,CAACV,aAAa,CAAC,CAAC,CAE3C,GAAIF,UAAU,CAAE,CACd;AACA,IAAK,GAAI,CAAA6B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC1B,KAAM,IAAI,CAAAC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CACvD,KAAM,CAAAlB,SAAS,CAAGL,mBAAmB,CAACqB,CAAC,CAAC,CACxClC,gBAAgB,CAACsC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEpB,SAAS,CAAC,CAAC,CAC9CpB,eAAe,CAACoC,CAAC,CAAG,CAAC,CAAC,CACxB,CAEA;AACA,KAAM,CAAAK,2BAA2B,CAAC,CAAC,CACrC,CAAC,IAAM,CACL;AACA,KAAM,CAAArB,SAAS,CAAGL,mBAAmB,CAAC,CAAC,CAAC,CACxCb,gBAAgB,CAAC,CAACkB,SAAS,CAAC,CAAC,CAC7BpB,eAAe,CAAC,CAAC,CAAC,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAA0C,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI3C,YAAY,CAAG,CAAC,CAAE,CACpB,KAAM,CAAAqB,SAAS,CAAGL,mBAAmB,CAAChB,YAAY,CAAC,CACnDG,gBAAgB,CAACsC,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEpB,SAAS,CAAC,CAAC,CAC9CpB,eAAe,CAACwC,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAEjC,GAAIzC,YAAY,GAAK,CAAC,CAAE,CACtB;AACAwC,UAAU,CAAC,IAAME,2BAA2B,CAAC,CAAC,CAAE,IAAI,CAAC,CACvD,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAA,2BAA2B,CAAG,KAAAA,CAAA,GAAY,CAC9C,KAAM,IAAI,CAAAJ,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAK,KAAK,CAAG,CACZC,GAAG,CAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACtCC,IAAI,CAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACvCE,IAAI,CAAEJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAG,CAAC,CAAC,CAAG,CACxC,CAAC,CAED,KAAM,CAAAG,UAAU,CAAGP,KAAK,CAACC,GAAG,CAAGD,KAAK,CAACK,IAAI,CAAGL,KAAK,CAACM,IAAI,CACtD,KAAM,CAAAE,UAAU,CAAGN,IAAI,CAACO,GAAG,CAACT,KAAK,CAACC,GAAG,CAAED,KAAK,CAACK,IAAI,CAAEL,KAAK,CAACM,IAAI,CAAC,CAAGC,UAAU,CAE3E,GAAI,CAAAG,QAAQ,CAAG,MAAM,CACrB,GAAIV,KAAK,CAACC,GAAG,CAAGD,KAAK,CAACK,IAAI,EAAIL,KAAK,CAACC,GAAG,CAAGD,KAAK,CAACM,IAAI,CAAE,CACpDI,QAAQ,CAAG,KAAK,CAClB,CAAC,IAAM,IAAIV,KAAK,CAACK,IAAI,CAAGL,KAAK,CAACC,GAAG,EAAID,KAAK,CAACK,IAAI,CAAGL,KAAK,CAACM,IAAI,CAAE,CAC5DI,QAAQ,CAAG,MAAM,CACnB,CAEA,KAAM,CAAAC,cAAc,CAAG,CACrBD,QAAQ,CACRF,UAAU,CAAEA,UAAU,CAAG,GAAG,CAC5BR,KAAK,CACLY,SAAS,gBAAAzB,MAAA,CAAO/B,YAAY,6EAAA+B,MAAA,CAAeuB,QAAQ,GAAK,KAAK,CAAG,IAAI,CAAGA,QAAQ,GAAK,MAAM,CAAG,IAAI,CAAG,IAAI,CAAE,CAC1GG,SAAS,CAAEL,UAAU,CAAG,GAAG,CAAG,KAAK,CAAGA,UAAU,CAAG,GAAG,CAAG,QAAQ,CAAG,MAAM,CAC1EM,iBAAiB,CAAEJ,QAAQ,GAAK,KAAK,CAAG,WAAW,CAAGA,QAAQ,GAAK,MAAM,CAAG,cAAc,CAAG,QAAQ,CACrGK,WAAW,CAAEL,QAAQ,GAAK,KAAK,CAAG,WAAW,CAAGA,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,UACjF,CAAC,CAEDjD,sBAAsB,CAACkD,cAAc,CAAC,CACtCxD,cAAc,CAAC,WAAW,CAAC,CAE3B;AACA,GAAIF,sBAAsB,CAAE,CAC1BA,sBAAsB,CAAC0D,cAAc,CAAC,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACxB7D,cAAc,CAAC,MAAM,CAAC,CACtBE,eAAe,CAAC,CAAC,CAAC,CAClBE,gBAAgB,CAAC,EAAE,CAAC,CACpBE,sBAAsB,CAAC,IAAI,CAAC,CAC5BE,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAsD,gBAAgB,CAAIP,QAAQ,EAAK,CACrC,OAAQA,QAAQ,EACd,IAAK,KAAK,CAAE,MAAO,8CAA8C,CACjE,IAAK,MAAM,CAAE,MAAO,wCAAwC,CAC5D,QAAS,MAAO,iDAAiD,CACnE,CACF,CAAC,CAED;AACA,KAAM,CAAAQ,eAAe,CAAIR,QAAQ,EAAK,CACpC,OAAQA,QAAQ,EACd,IAAK,KAAK,CAAE,mBAAOlE,IAAA,CAACX,UAAU,EAACsF,SAAS,CAAC,SAAS,CAAE,CAAC,CACrD,IAAK,MAAM,CAAE,mBAAO3E,IAAA,CAACV,YAAY,EAACqF,SAAS,CAAC,SAAS,CAAE,CAAC,CACxD,QAAS,mBAAO3E,IAAA,CAACT,KAAK,EAACoF,SAAS,CAAC,SAAS,CAAE,CAAC,CAC/C,CACF,CAAC,CAED,mBACEzE,KAAA,QAAKyE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1E,KAAA,CAACvB,IAAI,EAAAiG,QAAA,eACH5E,IAAA,CAACpB,UAAU,EAAAgG,QAAA,cACT1E,KAAA,CAACrB,SAAS,EAAC8F,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC5C5E,IAAA,CAACb,aAAa,EAACwF,SAAS,CAAC,SAAS,CAAE,CAAC,6CAEvC,EAAW,CAAC,CACF,CAAC,cACbzE,KAAA,CAACpB,WAAW,EAAA8F,QAAA,eACV1E,KAAA,QAAKyE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C1E,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5E,IAAA,SAAM2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,cAChD5E,IAAA,CAAChB,KAAK,EAAC6F,OAAO,CAAC,SAAS,CAAAD,QAAA,CAAErE,WAAW,EAAI,KAAK,CAAQ,CAAC,EACpD,CAAC,cACNL,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5E,IAAA,SAAM2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,cAChD5E,IAAA,CAAChB,KAAK,EAAC6F,OAAO,CAAC,SAAS,CAAAD,QAAA,CAAE,CAAApE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsE,YAAY,GAAI,KAAK,CAAQ,CAAC,EAClE,CAAC,cACN5E,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtC5E,IAAA,SAAM2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,cAChD5E,IAAA,CAAChB,KAAK,EAAC2F,SAAS,CACdjE,WAAW,GAAK,SAAS,CAAG,2BAA2B,CACvDA,WAAW,GAAK,WAAW,CAAG,6BAA6B,CAC3D,2BACD,CAAAkE,QAAA,CACElE,WAAW,GAAK,SAAS,CAAG,KAAK,CACjCA,WAAW,GAAK,WAAW,CAAG,KAAK,CAAG,KAAK,CACvC,CAAC,EACL,CAAC,EACH,CAAC,cAENR,KAAA,QAAKyE,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EACrClE,WAAW,GAAK,MAAM,eACrBR,KAAA,CAAAE,SAAA,EAAAwE,QAAA,eACE1E,KAAA,CAACnB,MAAM,EAACgG,OAAO,CAAE/B,WAAY,CAAC2B,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC/D5E,IAAA,CAACR,IAAI,EAACmF,SAAS,CAAC,SAAS,CAAE,CAAC,2BAE9B,EAAQ,CAAC,cACTzE,KAAA,UAAOyE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAChD5E,IAAA,UACE4C,IAAI,CAAC,UAAU,CACfoC,OAAO,CAAE5D,UAAW,CACpB6D,QAAQ,CAAGC,CAAC,EAAK7D,aAAa,CAAC6D,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE,CAClD,CAAC,2BAEJ,EAAO,CAAC,EACR,CACH,CAEAtE,WAAW,GAAK,SAAS,EAAI,CAACU,UAAU,EAAIR,YAAY,CAAG,CAAC,eAC3DV,KAAA,CAACnB,MAAM,EAACgG,OAAO,CAAExB,SAAU,CAACoB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC7D5E,IAAA,CAACb,aAAa,EAACwF,SAAS,CAAC,SAAS,CAAE,CAAC,uBAChC,CAAC/D,YAAY,CAAC,KACrB,EAAQ,CACT,CAEAF,WAAW,GAAK,SAAS,EAAIU,UAAU,eACtClB,KAAA,CAACnB,MAAM,EAACqG,QAAQ,MAACT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAClD5E,IAAA,CAACH,KAAK,EAAC8E,SAAS,CAAC,sBAAsB,CAAE,CAAC,oCAE5C,EAAQ,CACT,CAEA,CAACjE,WAAW,GAAK,WAAW,EAAIA,WAAW,GAAK,SAAS,gBACxDR,KAAA,CAACnB,MAAM,EAACgG,OAAO,CAAEP,WAAY,CAACK,OAAO,CAAC,SAAS,CAACF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACjF5E,IAAA,CAACN,SAAS,EAACiF,SAAS,CAAC,SAAS,CAAE,CAAC,2BAEnC,EAAQ,CACT,EACE,CAAC,EACK,CAAC,EACV,CAAC,CAGNzD,YAAY,CAACmE,MAAM,CAAG,CAAC,eACtBnF,KAAA,CAACvB,IAAI,EAAAiG,QAAA,eACH5E,IAAA,CAACpB,UAAU,EAAAgG,QAAA,cACT1E,KAAA,CAACrB,SAAS,EAAC8F,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC5C5E,IAAA,CAACZ,KAAK,EAACuF,SAAS,CAAC,SAAS,CAAE,CAAC,mCACtB,CAACzD,YAAY,CAACmE,MAAM,CAAC,GAC9B,EAAW,CAAC,CACF,CAAC,cACbrF,IAAA,CAAClB,WAAW,EAAA8F,QAAA,cACV5E,IAAA,QAAK2E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnD1D,YAAY,CAACoE,GAAG,CAAC/C,OAAO,EAAI,CAC3B,KAAM,CAAAM,OAAO,CAAGvB,aAAa,CAACiB,OAAO,CAAC,CACtC,KAAM,CAAAgD,aAAa,CAAG1C,OAAO,CAACnB,IAAI,CAClC,mBACExB,KAAA,QAAmByE,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC1E5E,IAAA,CAACuF,aAAa,EAACZ,SAAS,CAAC,SAAS,CAAE,CAAC,cACrCzE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,QAAK2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE/B,OAAO,CAACtB,IAAI,CAAM,CAAC,cACzDvB,IAAA,QAAK2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAE/B,OAAO,CAACrB,IAAI,CAAM,CAAC,EACxD,CAAC,GALEe,OAML,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CACK,CAAC,EACV,CACP,CAGAzB,aAAa,CAACuE,MAAM,CAAG,CAAC,eACvBnF,KAAA,CAACvB,IAAI,EAAAiG,QAAA,eACH5E,IAAA,CAACpB,UAAU,EAAAgG,QAAA,cACT5E,IAAA,CAACnB,SAAS,EAAA+F,QAAA,CAAC,0BAAI,CAAW,CAAC,CACjB,CAAC,cACb5E,IAAA,CAAClB,WAAW,EAAA8F,QAAA,cACV5E,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB9D,aAAa,CAACwE,GAAG,CAAC,CAACzD,KAAK,CAAE2D,UAAU,gBACnCtF,KAAA,QAAsByE,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC/D1E,KAAA,QAAKyE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,EAAC,SACpD,CAAC/C,KAAK,CAACA,KAAK,CAAC,YAAK,CAACA,KAAK,CAACK,SAAS,EACjC,CAAC,cACNlC,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB/C,KAAK,CAACQ,WAAW,CAACiD,GAAG,CAAC,CAACG,UAAU,CAAEC,eAAe,GAAK,CACtD,KAAM,CAAA7C,OAAO,CAAGvB,aAAa,CAACmE,UAAU,CAAChD,KAAK,CAAC,CAC/C,KAAM,CAAA8C,aAAa,CAAG1C,OAAO,CAACnB,IAAI,CAClC,mBACExB,KAAA,QAA2ByE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACzE5E,IAAA,QAAK2E,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B5E,IAAA,QAAK2E,SAAS,0DAAAhC,MAAA,CAA2DE,OAAO,CAACpB,KAAK,CAAG,CAAAmD,QAAA,cACvF5E,IAAA,CAACuF,aAAa,EAACZ,SAAS,CAAC,SAAS,CAAE,CAAC,CAClC,CAAC,CACH,CAAC,cACNzE,KAAA,QAAKyE,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrB1E,KAAA,QAAKyE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C5E,IAAA,SAAM2E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE/B,OAAO,CAACtB,IAAI,CAAO,CAAC,CAC1DkE,UAAU,CAAC1C,MAAM,eAChB7C,KAAA,CAAClB,KAAK,EAAC2F,SAAS,CAAEF,gBAAgB,CAACgB,UAAU,CAAC1C,MAAM,CAAE,CAAA6B,QAAA,EACnDF,eAAe,CAACe,UAAU,CAAC1C,MAAM,CAAC,cACnC/C,IAAA,SAAM2E,SAAS,CAAC,MAAM,CAAAC,QAAA,CACnBa,UAAU,CAAC1C,MAAM,GAAK,KAAK,CAAG,IAAI,CAClC0C,UAAU,CAAC1C,MAAM,GAAK,MAAM,CAAG,IAAI,CAAG,IAAI,CACvC,CAAC,EACF,CACR,cACD/C,IAAA,SAAM2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEa,UAAU,CAACvD,SAAS,CAAO,CAAC,EAClE,CAAC,cACNlC,IAAA,MAAG2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEa,UAAU,CAAC/C,OAAO,CAAI,CAAC,EAC1D,CAAC,GArBEgD,eAsBL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,GAlCEF,UAmCL,CACN,CAAC,CACC,CAAC,CACK,CAAC,EACV,CACP,CAGAxE,mBAAmB,eAClBd,KAAA,CAACvB,IAAI,EAACgG,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACxC5E,IAAA,CAACpB,UAAU,EAAAgG,QAAA,cACT1E,KAAA,CAACrB,SAAS,EAAC8F,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC5C5E,IAAA,CAACL,WAAW,EAACgF,SAAS,CAAC,wBAAwB,CAAE,CAAC,uCAEpD,EAAW,CAAC,CACF,CAAC,cACb3E,IAAA,CAAClB,WAAW,EAAA8F,QAAA,cACV1E,KAAA,QAAKyE,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB1E,KAAA,QAAKyE,SAAS,4BAAAhC,MAAA,CAA6B8B,gBAAgB,CAACzD,mBAAmB,CAACkD,QAAQ,CAAC,CAAG,CAAAU,QAAA,eAC1F1E,KAAA,QAAKyE,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAC1CF,eAAe,CAAC1D,mBAAmB,CAACkD,QAAQ,CAAC,cAC9ClE,IAAA,SAAM2E,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAChC5D,mBAAmB,CAACkD,QAAQ,GAAK,KAAK,CAAG,MAAM,CAC/ClD,mBAAmB,CAACkD,QAAQ,GAAK,MAAM,CAAG,MAAM,CAAG,MAAM,CACtD,CAAC,cACPhE,KAAA,CAAClB,KAAK,EAAC6F,OAAO,CAAC,SAAS,CAAAD,QAAA,EAAC,sBAClB,CAAC5D,mBAAmB,CAACgD,UAAU,CAAC2B,OAAO,CAAC,CAAC,CAAC,CAAC,GAClD,EAAO,CAAC,EACL,CAAC,cACN3F,IAAA,MAAG2E,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAE5D,mBAAmB,CAACoD,SAAS,CAAI,CAAC,EACvD,CAAC,cAGNlE,KAAA,QAAKyE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC1E,KAAA,QAAKyE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrD5E,IAAA,QAAK2E,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAE5D,mBAAmB,CAACwC,KAAK,CAACC,GAAG,CAAM,CAAC,cACxFzD,IAAA,QAAK2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,EAC9C,CAAC,cACN1E,KAAA,QAAKyE,SAAS,CAAC,sCAAsC,CAAAC,QAAA,eACnD5E,IAAA,QAAK2E,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAE5D,mBAAmB,CAACwC,KAAK,CAACK,IAAI,CAAM,CAAC,cACvF7D,IAAA,QAAK2E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,EAC5C,CAAC,cACN1E,KAAA,QAAKyE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtD5E,IAAA,QAAK2E,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAE5D,mBAAmB,CAACwC,KAAK,CAACM,IAAI,CAAM,CAAC,cAC1F9D,IAAA,QAAK2E,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,EAC/C,CAAC,EACH,CAAC,cAGN1E,KAAA,QAAKyE,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5D1E,KAAA,QAAA0E,QAAA,eACE5E,IAAA,SAAM2E,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC1C5E,IAAA,CAAChB,KAAK,EAAC2F,SAAS,CACd3D,mBAAmB,CAACqD,SAAS,GAAK,KAAK,CAAG,kCAAkC,CAC5ErD,mBAAmB,CAACqD,SAAS,GAAK,QAAQ,CAAG,oCAAoC,CACjF,8BACD,CAAAO,QAAA,CACE5D,mBAAmB,CAACqD,SAAS,GAAK,KAAK,CAAG,KAAK,CAC/CrD,mBAAmB,CAACqD,SAAS,GAAK,QAAQ,CAAG,KAAK,CAAG,KAAK,CACtD,CAAC,EACL,CAAC,cACNnE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,SAAM2E,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC1C5E,IAAA,SAAM2E,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE5D,mBAAmB,CAACsD,iBAAiB,CAAO,CAAC,EAClE,CAAC,cACNpE,KAAA,QAAA0E,QAAA,eACE5E,IAAA,SAAM2E,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC1C5E,IAAA,SAAM2E,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE5D,mBAAmB,CAACuD,WAAW,CAAO,CAAC,EAC5D,CAAC,EACH,CAAC,EACH,CAAC,CACK,CAAC,EACV,CACP,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}