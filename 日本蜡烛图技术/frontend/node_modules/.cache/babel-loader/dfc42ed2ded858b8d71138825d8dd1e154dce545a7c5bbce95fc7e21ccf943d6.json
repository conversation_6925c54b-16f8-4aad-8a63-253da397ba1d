{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useContext } from '@rc-component/context';\nimport TableContext from \"../context/TableContext\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport { useEvent } from 'rc-util';\nimport classNames from 'classnames';\nexport default function useRowInfo(record, rowKey, recordIndex, indent) {\n  var context = useContext(TableContext, ['prefixCls', 'fixedInfoList', 'flattenColumns', 'expandableType', 'expandRowByClick', 'onTriggerExpand', 'rowClassName', 'expandedRowClassName', 'indentSize', 'expandIcon', 'expandedRowRender', 'expandIconColumnIndex', 'expandedKeys', 'childrenColumnName', 'rowExpandable', 'onRow']);\n  var flattenColumns = context.flattenColumns,\n    expandableType = context.expandableType,\n    expandedKeys = context.expandedKeys,\n    childrenColumnName = context.childrenColumnName,\n    onTriggerExpand = context.onTriggerExpand,\n    rowExpandable = context.rowExpandable,\n    onRow = context.onRow,\n    expandRowByClick = context.expandRowByClick,\n    rowClassName = context.rowClassName;\n\n  // ======================= Expandable =======================\n  // Only when row is not expandable and `children` exist in record\n  var nestExpandable = expandableType === 'nest';\n  var rowSupportExpand = expandableType === 'row' && (!rowExpandable || rowExpandable(record));\n  var mergedExpandable = rowSupportExpand || nestExpandable;\n  var expanded = expandedKeys && expandedKeys.has(rowKey);\n  var hasNestChildren = childrenColumnName && record && record[childrenColumnName];\n  var onInternalTriggerExpand = useEvent(onTriggerExpand);\n\n  // ========================= onRow ==========================\n  var rowProps = onRow === null || onRow === void 0 ? void 0 : onRow(record, recordIndex);\n  var onRowClick = rowProps === null || rowProps === void 0 ? void 0 : rowProps.onClick;\n  var onClick = function onClick(event) {\n    if (expandRowByClick && mergedExpandable) {\n      onTriggerExpand(record, event);\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    onRowClick === null || onRowClick === void 0 || onRowClick.apply(void 0, [event].concat(args));\n  };\n\n  // ====================== RowClassName ======================\n  var computeRowClassName;\n  if (typeof rowClassName === 'string') {\n    computeRowClassName = rowClassName;\n  } else if (typeof rowClassName === 'function') {\n    computeRowClassName = rowClassName(record, recordIndex, indent);\n  }\n\n  // ========================= Column =========================\n  var columnsKey = getColumnsKey(flattenColumns);\n  return _objectSpread(_objectSpread({}, context), {}, {\n    columnsKey: columnsKey,\n    nestExpandable: nestExpandable,\n    expanded: expanded,\n    hasNestChildren: hasNestChildren,\n    record: record,\n    onTriggerExpand: onInternalTriggerExpand,\n    rowSupportExpand: rowSupportExpand,\n    expandable: mergedExpandable,\n    rowProps: _objectSpread(_objectSpread({}, rowProps), {}, {\n      className: classNames(computeRowClassName, rowProps === null || rowProps === void 0 ? void 0 : rowProps.className),\n      onClick: onClick\n    })\n  });\n}", "map": {"version": 3, "names": ["_objectSpread", "useContext", "TableContext", "getColumnsKey", "useEvent", "classNames", "useRowInfo", "record", "<PERSON><PERSON><PERSON>", "recordIndex", "indent", "context", "flattenColumns", "expandableType", "expandedKeys", "childrenColumnName", "onTriggerExpand", "rowExpandable", "onRow", "expandRowByClick", "rowClassName", "nestExpandable", "rowSupportExpand", "mergedExpandable", "expanded", "has", "hasNestC<PERSON><PERSON>n", "onInternalTriggerExpand", "rowProps", "onRowClick", "onClick", "event", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "computeRowClassName", "columnsKey", "expandable", "className"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/rc-table/es/hooks/useRowInfo.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useContext } from '@rc-component/context';\nimport TableContext from \"../context/TableContext\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport { useEvent } from 'rc-util';\nimport classNames from 'classnames';\nexport default function useRowInfo(record, rowKey, recordIndex, indent) {\n  var context = useContext(TableContext, ['prefixCls', 'fixedInfoList', 'flattenColumns', 'expandableType', 'expandRowByClick', 'onTriggerExpand', 'rowClassName', 'expandedRowClassName', 'indentSize', 'expandIcon', 'expandedRowRender', 'expandIconColumnIndex', 'expandedKeys', 'childrenColumnName', 'rowExpandable', 'onRow']);\n  var flattenColumns = context.flattenColumns,\n    expandableType = context.expandableType,\n    expandedKeys = context.expandedKeys,\n    childrenColumnName = context.childrenColumnName,\n    onTriggerExpand = context.onTriggerExpand,\n    rowExpandable = context.rowExpandable,\n    onRow = context.onRow,\n    expandRowByClick = context.expandRowByClick,\n    rowClassName = context.rowClassName;\n\n  // ======================= Expandable =======================\n  // Only when row is not expandable and `children` exist in record\n  var nestExpandable = expandableType === 'nest';\n  var rowSupportExpand = expandableType === 'row' && (!rowExpandable || rowExpandable(record));\n  var mergedExpandable = rowSupportExpand || nestExpandable;\n  var expanded = expandedKeys && expandedKeys.has(rowKey);\n  var hasNestChildren = childrenColumnName && record && record[childrenColumnName];\n  var onInternalTriggerExpand = useEvent(onTriggerExpand);\n\n  // ========================= onRow ==========================\n  var rowProps = onRow === null || onRow === void 0 ? void 0 : onRow(record, recordIndex);\n  var onRowClick = rowProps === null || rowProps === void 0 ? void 0 : rowProps.onClick;\n  var onClick = function onClick(event) {\n    if (expandRowByClick && mergedExpandable) {\n      onTriggerExpand(record, event);\n    }\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    onRowClick === null || onRowClick === void 0 || onRowClick.apply(void 0, [event].concat(args));\n  };\n\n  // ====================== RowClassName ======================\n  var computeRowClassName;\n  if (typeof rowClassName === 'string') {\n    computeRowClassName = rowClassName;\n  } else if (typeof rowClassName === 'function') {\n    computeRowClassName = rowClassName(record, recordIndex, indent);\n  }\n\n  // ========================= Column =========================\n  var columnsKey = getColumnsKey(flattenColumns);\n  return _objectSpread(_objectSpread({}, context), {}, {\n    columnsKey: columnsKey,\n    nestExpandable: nestExpandable,\n    expanded: expanded,\n    hasNestChildren: hasNestChildren,\n    record: record,\n    onTriggerExpand: onInternalTriggerExpand,\n    rowSupportExpand: rowSupportExpand,\n    expandable: mergedExpandable,\n    rowProps: _objectSpread(_objectSpread({}, rowProps), {}, {\n      className: classNames(computeRowClassName, rowProps === null || rowProps === void 0 ? void 0 : rowProps.className),\n      onClick: onClick\n    })\n  });\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,UAAU,MAAM,YAAY;AACnC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACtE,IAAIC,OAAO,GAAGV,UAAU,CAACC,YAAY,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,cAAc,EAAE,sBAAsB,EAAE,YAAY,EAAE,YAAY,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,cAAc,EAAE,oBAAoB,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;EACnU,IAAIU,cAAc,GAAGD,OAAO,CAACC,cAAc;IACzCC,cAAc,GAAGF,OAAO,CAACE,cAAc;IACvCC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACnCC,kBAAkB,GAAGJ,OAAO,CAACI,kBAAkB;IAC/CC,eAAe,GAAGL,OAAO,CAACK,eAAe;IACzCC,aAAa,GAAGN,OAAO,CAACM,aAAa;IACrCC,KAAK,GAAGP,OAAO,CAACO,KAAK;IACrBC,gBAAgB,GAAGR,OAAO,CAACQ,gBAAgB;IAC3CC,YAAY,GAAGT,OAAO,CAACS,YAAY;;EAErC;EACA;EACA,IAAIC,cAAc,GAAGR,cAAc,KAAK,MAAM;EAC9C,IAAIS,gBAAgB,GAAGT,cAAc,KAAK,KAAK,KAAK,CAACI,aAAa,IAAIA,aAAa,CAACV,MAAM,CAAC,CAAC;EAC5F,IAAIgB,gBAAgB,GAAGD,gBAAgB,IAAID,cAAc;EACzD,IAAIG,QAAQ,GAAGV,YAAY,IAAIA,YAAY,CAACW,GAAG,CAACjB,MAAM,CAAC;EACvD,IAAIkB,eAAe,GAAGX,kBAAkB,IAAIR,MAAM,IAAIA,MAAM,CAACQ,kBAAkB,CAAC;EAChF,IAAIY,uBAAuB,GAAGvB,QAAQ,CAACY,eAAe,CAAC;;EAEvD;EACA,IAAIY,QAAQ,GAAGV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACX,MAAM,EAAEE,WAAW,CAAC;EACvF,IAAIoB,UAAU,GAAGD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,OAAO;EACrF,IAAIA,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;IACpC,IAAIZ,gBAAgB,IAAII,gBAAgB,EAAE;MACxCP,eAAe,CAACT,MAAM,EAAEwB,KAAK,CAAC;IAChC;IACA,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IACAR,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACS,KAAK,CAAC,KAAK,CAAC,EAAE,CAACP,KAAK,CAAC,CAACQ,MAAM,CAACJ,IAAI,CAAC,CAAC;EAChG,CAAC;;EAED;EACA,IAAIK,mBAAmB;EACvB,IAAI,OAAOpB,YAAY,KAAK,QAAQ,EAAE;IACpCoB,mBAAmB,GAAGpB,YAAY;EACpC,CAAC,MAAM,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;IAC7CoB,mBAAmB,GAAGpB,YAAY,CAACb,MAAM,EAAEE,WAAW,EAAEC,MAAM,CAAC;EACjE;;EAEA;EACA,IAAI+B,UAAU,GAAGtC,aAAa,CAACS,cAAc,CAAC;EAC9C,OAAOZ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEW,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACnD8B,UAAU,EAAEA,UAAU;IACtBpB,cAAc,EAAEA,cAAc;IAC9BG,QAAQ,EAAEA,QAAQ;IAClBE,eAAe,EAAEA,eAAe;IAChCnB,MAAM,EAAEA,MAAM;IACdS,eAAe,EAAEW,uBAAuB;IACxCL,gBAAgB,EAAEA,gBAAgB;IAClCoB,UAAU,EAAEnB,gBAAgB;IAC5BK,QAAQ,EAAE5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;MACvDe,SAAS,EAAEtC,UAAU,CAACmC,mBAAmB,EAAEZ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACe,SAAS,CAAC;MAClHb,OAAO,EAAEA;IACX,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}