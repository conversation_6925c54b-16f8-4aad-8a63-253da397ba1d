{"ast": null, "code": "import React,{useState,useEffect}from'react';import{<PERSON>,<PERSON>H<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,CardContent}from'../ui/card';import{But<PERSON>}from'../ui/button';import{Badge}from'../ui/badge';import{Progress}from'../ui/progress';import{Alert,AlertDescription}from'../ui/alert';import{Brain,CheckCircle,AlertTriangle,RefreshCw,Server,Users,MessageSquare,Shield,Activity,Clock,Zap,TrendingUp,TrendingDown}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SystemStatus=_ref=>{var _systemStatus$data,_systemStatus$data2,_systemStatus$data2$a,_systemStatus$data3,_systemStatus$data3$a,_systemStatus$data4,_systemStatus$data4$d,_systemStatus$data5,_systemStatus$data5$d,_systemStatus$data6,_systemStatus$data7,_systemStatus$data7$d,_systemStatus$data8,_systemStatus$data8$p;let{systemStatus,onRefresh}=_ref;const[agents,setAgents]=useState([]);const[isLoading,setIsLoading]=useState(false);useEffect(()=>{fetchAgents();},[]);const fetchAgents=async()=>{setIsLoading(true);try{const response=await fetch('/api/trading-agents/agents');const data=await response.json();if(data.status==='success'){setAgents(data.data.agents||[]);}}catch(error){console.error('获取智能体列表失败:',error);}finally{setIsLoading(false);}};const getStatusBadge=status=>{switch(status){case'running':return/*#__PURE__*/_jsxs(Badge,{variant:\"success\",className:\"bg-green-100 text-green-800\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 mr-1\"}),\"\\u8FD0\\u884C\\u4E2D\"]});case'not_initialized':return/*#__PURE__*/_jsxs(Badge,{variant:\"warning\",className:\"bg-yellow-100 text-yellow-800\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"w-3 h-3 mr-1\"}),\"\\u672A\\u521D\\u59CB\\u5316\"]});default:return/*#__PURE__*/_jsxs(Badge,{variant:\"destructive\",children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"w-3 h-3 mr-1\"}),\"\\u5F02\\u5E38\"]});}};const getAgentIcon=agentName=>{const icons={'MarketAnalyst':/*#__PURE__*/_jsx(Activity,{className:\"w-4 h-4\"}),'BullResearcher':/*#__PURE__*/_jsx(TrendingUp,{className:\"w-4 h-4\"}),'BearResearcher':/*#__PURE__*/_jsx(TrendingDown,{className:\"w-4 h-4\"}),'PortfolioManager':/*#__PURE__*/_jsx(Shield,{className:\"w-4 h-4\"}),'DebateModerator':/*#__PURE__*/_jsx(MessageSquare,{className:\"w-4 h-4\"}),'ExecutionAgent':/*#__PURE__*/_jsx(Zap,{className:\"w-4 h-4\"}),'CandlestickAnalyst':/*#__PURE__*/_jsx(Brain,{className:\"w-4 h-4\"})};return icons[agentName]||/*#__PURE__*/_jsx(Brain,{className:\"w-4 h-4\"});};const getAgentName=agentName=>{const names={'MarketAnalyst':'市场分析师','BullResearcher':'多头研究员','BearResearcher':'空头研究员','PortfolioManager':'投资组合经理','DebateModerator':'辩论主持人','ExecutionAgent':'执行智能体','CandlestickAnalyst':'蜡烛图分析师'};return names[agentName]||agentName;};const getSpecializationDescription=specialization=>{const descriptions={'technical_analysis':'技术分析和市场趋势','bullish_analysis':'看涨机会识别','bearish_analysis':'风险识别和看跌分析','risk_management':'风险管理和资产配置','consensus_building':'共识建立和观点平衡','trade_execution':'交易执行和订单管理','candlestick_analysis':'蜡烛图形态分析'};return descriptions[specialization]||specialization;};const formatLastActivity=lastActivity=>{if(!lastActivity)return'无活动记录';try{const date=new Date(lastActivity);const now=new Date();const diffMs=now-date;const diffMins=Math.floor(diffMs/60000);if(diffMins<1)return'刚刚';if(diffMins<60)return\"\".concat(diffMins,\"\\u5206\\u949F\\u524D\");if(diffMins<1440)return\"\".concat(Math.floor(diffMins/60),\"\\u5C0F\\u65F6\\u524D\");return\"\".concat(Math.floor(diffMins/1440),\"\\u5929\\u524D\");}catch(_unused){return'时间格式错误';}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-semibold flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Server,{className:\"w-5 h-5 text-blue-600\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7CFB\\u7EDF\\u72B6\\u6001\\u76D1\\u63A7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsxs(Button,{onClick:fetchAgents,disabled:isLoading,size:\"sm\",variant:\"outline\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-4 h-4 mr-2 \".concat(isLoading?'animate-spin':'')}),\"\\u5237\\u65B0\\u667A\\u80FD\\u4F53\"]}),/*#__PURE__*/_jsxs(Button,{onClick:onRefresh,size:\"sm\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-4 h-4 mr-2\"}),\"\\u5237\\u65B0\\u72B6\\u6001\"]})]})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsx(CardTitle,{className:\"text-base\",children:\"\\u7CFB\\u7EDF\\u603B\\u89C8\"})}),/*#__PURE__*/_jsx(CardContent,{children:systemStatus?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"\\u7CFB\\u7EDF\\u72B6\\u6001:\"}),getStatusBadge((_systemStatus$data=systemStatus.data)===null||_systemStatus$data===void 0?void 0:_systemStatus$data.status)]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 md:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-blue-600\",children:((_systemStatus$data2=systemStatus.data)===null||_systemStatus$data2===void 0?void 0:(_systemStatus$data2$a=_systemStatus$data2.agents)===null||_systemStatus$data2$a===void 0?void 0:_systemStatus$data2$a.total_agents)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"\\u603B\\u667A\\u80FD\\u4F53\\u6570\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-green-600\",children:((_systemStatus$data3=systemStatus.data)===null||_systemStatus$data3===void 0?void 0:(_systemStatus$data3$a=_systemStatus$data3.agents)===null||_systemStatus$data3$a===void 0?void 0:_systemStatus$data3$a.active_agents)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"\\u6D3B\\u8DC3\\u667A\\u80FD\\u4F53\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-purple-600\",children:((_systemStatus$data4=systemStatus.data)===null||_systemStatus$data4===void 0?void 0:(_systemStatus$data4$d=_systemStatus$data4.debate_system)===null||_systemStatus$data4$d===void 0?void 0:_systemStatus$data4$d.active_debates)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"\\u8FDB\\u884C\\u4E2D\\u8FA9\\u8BBA\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold text-orange-600\",children:((_systemStatus$data5=systemStatus.data)===null||_systemStatus$data5===void 0?void 0:(_systemStatus$data5$d=_systemStatus$data5.debate_system)===null||_systemStatus$data5$d===void 0?void 0:_systemStatus$data5$d.total_debates)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-600\",children:\"\\u603B\\u8FA9\\u8BBA\\u6B21\\u6570\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium\",children:\"\\u7EC4\\u4EF6\\u72B6\\u6001:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u667A\\u80FD\\u4F53\\u7BA1\\u7406\\u5668\"}),(_systemStatus$data6=systemStatus.data)!==null&&_systemStatus$data6!==void 0&&_systemStatus$data6.agents?/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u6B63\\u5E38\"}):/*#__PURE__*/_jsx(Badge,{variant:\"destructive\",children:\"\\u5F02\\u5E38\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u8FA9\\u8BBA\\u7CFB\\u7EDF\"}),((_systemStatus$data7=systemStatus.data)===null||_systemStatus$data7===void 0?void 0:(_systemStatus$data7$d=_systemStatus$data7.debate_system)===null||_systemStatus$data7$d===void 0?void 0:_systemStatus$data7$d.status)==='active'?/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u6B63\\u5E38\"}):/*#__PURE__*/_jsx(Badge,{variant:\"destructive\",children:\"\\u5F02\\u5E38\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-2 bg-gray-50 rounded\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u5F62\\u6001\\u9A8C\\u8BC1\\u5668\"}),(_systemStatus$data8=systemStatus.data)!==null&&_systemStatus$data8!==void 0&&(_systemStatus$data8$p=_systemStatus$data8.pattern_validator)!==null&&_systemStatus$data8$p!==void 0&&_systemStatus$data8$p.initialized?/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u6B63\\u5E38\"}):/*#__PURE__*/_jsx(Badge,{variant:\"destructive\",children:\"\\u5F02\\u5E38\"})]})]})]})]}):/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u6B63\\u5728\\u83B7\\u53D6\\u7CFB\\u7EDF\\u72B6\\u6001...\"})})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsxs(CardTitle,{className:\"text-base flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Users,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u667A\\u80FD\\u4F53\\u8BE6\\u60C5\"})]})}),/*#__PURE__*/_jsx(CardContent,{children:isLoading?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u667A\\u80FD\\u4F53\\u4FE1\\u606F...\"})}):agents.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:agents.map((agent,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 border rounded-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[getAgentIcon(agent.name),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-medium\",children:getAgentName(agent.name)}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600\",children:getSpecializationDescription(agent.specialization)})]})]}),/*#__PURE__*/_jsx(Badge,{variant:agent.is_active?\"success\":\"secondary\",children:agent.is_active?'活跃':'非活跃'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium\",children:agent.status||'未知'})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"\\u5206\\u6790\\u6B21\\u6570:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium\",children:agent.analysis_count||0})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"col-span-2\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"\\u6700\\u540E\\u6D3B\\u52A8:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 font-medium\",children:formatLastActivity(agent.last_activity)})]})]})]},index))}):/*#__PURE__*/_jsxs(Alert,{children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(AlertDescription,{children:\"\\u6682\\u65E0\\u667A\\u80FD\\u4F53\\u4FE1\\u606F\\u3002\\u8BF7\\u68C0\\u67E5\\u7CFB\\u7EDF\\u914D\\u7F6E\\u3002\"})]})})]}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsx(CardHeader,{children:/*#__PURE__*/_jsx(CardTitle,{className:\"text-base\",children:\"\\u7CFB\\u7EDF\\u5065\\u5EB7\\u68C0\\u67E5\"})}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"API\\u8FDE\\u63A5\"}),/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u6B63\\u5E38\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"LLM\\u670D\\u52A1\"}),/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"DeepSeek-V3\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u914D\\u7F6E\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u5DF2\\u52A0\\u8F7D\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm\",children:\"\\u73AF\\u5883\\u53D8\\u91CF\"}),/*#__PURE__*/_jsx(Badge,{variant:\"success\",children:\"\\u5DF2\\u914D\\u7F6E\"})]})]})})]}),systemStatus&&systemStatus.status!=='success'&&/*#__PURE__*/_jsxs(Alert,{children:[/*#__PURE__*/_jsx(AlertTriangle,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsxs(AlertDescription,{children:[\"\\u7CFB\\u7EDF\\u68C0\\u6D4B\\u5230\\u5F02\\u5E38: \",systemStatus.error||'未知错误',/*#__PURE__*/_jsx(\"br\",{}),\"\\u8BF7\\u68C0\\u67E5\\u7CFB\\u7EDF\\u914D\\u7F6E\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\"]})]})]});};export default SystemStatus;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Badge", "Progress", "<PERSON><PERSON>", "AlertDescription", "Brain", "CheckCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "Server", "Users", "MessageSquare", "Shield", "Activity", "Clock", "Zap", "TrendingUp", "TrendingDown", "jsx", "_jsx", "jsxs", "_jsxs", "SystemStatus", "_ref", "_systemStatus$data", "_systemStatus$data2", "_systemStatus$data2$a", "_systemStatus$data3", "_systemStatus$data3$a", "_systemStatus$data4", "_systemStatus$data4$d", "_systemStatus$data5", "_systemStatus$data5$d", "_systemStatus$data6", "_systemStatus$data7", "_systemStatus$data7$d", "_systemStatus$data8", "_systemStatus$data8$p", "systemStatus", "onRefresh", "agents", "setAgents", "isLoading", "setIsLoading", "fetchAgents", "response", "fetch", "data", "json", "status", "error", "console", "getStatusBadge", "variant", "className", "children", "getAgentIcon", "<PERSON><PERSON><PERSON>", "icons", "getAgentName", "names", "getSpecializationDescription", "specialization", "descriptions", "formatLastActivity", "lastActivity", "date", "Date", "now", "diffMs", "diffMins", "Math", "floor", "concat", "_unused", "onClick", "disabled", "size", "total_agents", "active_agents", "debate_system", "active_debates", "total_debates", "pattern_validator", "initialized", "length", "map", "agent", "index", "name", "is_active", "analysis_count", "last_activity"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent } from '../ui/card';\nimport { Button } from '../ui/button';\nimport { Badge } from '../ui/badge';\nimport { Progress } from '../ui/progress';\nimport { Alert, AlertDescription } from '../ui/alert';\nimport {\n  Brain,\n  CheckCircle,\n  AlertTriangle,\n  RefreshCw,\n  Server,\n  Users,\n  MessageSquare,\n  Shield,\n  Activity,\n  Clock,\n  Zap,\n  TrendingUp,\n  TrendingDown\n} from 'lucide-react';\n\nconst SystemStatus = ({ systemStatus, onRefresh }) => {\n  const [agents, setAgents] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  useEffect(() => {\n    fetchAgents();\n  }, []);\n\n  const fetchAgents = async () => {\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/trading-agents/agents');\n      const data = await response.json();\n      if (data.status === 'success') {\n        setAgents(data.data.agents || []);\n      }\n    } catch (error) {\n      console.error('获取智能体列表失败:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStatusBadge = (status) => {\n    switch (status) {\n      case 'running':\n        return <Badge variant=\"success\" className=\"bg-green-100 text-green-800\">\n          <CheckCircle className=\"w-3 h-3 mr-1\" />\n          运行中\n        </Badge>;\n      case 'not_initialized':\n        return <Badge variant=\"warning\" className=\"bg-yellow-100 text-yellow-800\">\n          <AlertTriangle className=\"w-3 h-3 mr-1\" />\n          未初始化\n        </Badge>;\n      default:\n        return <Badge variant=\"destructive\">\n          <AlertTriangle className=\"w-3 h-3 mr-1\" />\n          异常\n        </Badge>;\n    }\n  };\n\n  const getAgentIcon = (agentName) => {\n    const icons = {\n      'MarketAnalyst': <Activity className=\"w-4 h-4\" />,\n      'BullResearcher': <TrendingUp className=\"w-4 h-4\" />,\n      'BearResearcher': <TrendingDown className=\"w-4 h-4\" />,\n      'PortfolioManager': <Shield className=\"w-4 h-4\" />,\n      'DebateModerator': <MessageSquare className=\"w-4 h-4\" />,\n      'ExecutionAgent': <Zap className=\"w-4 h-4\" />,\n      'CandlestickAnalyst': <Brain className=\"w-4 h-4\" />\n    };\n    return icons[agentName] || <Brain className=\"w-4 h-4\" />;\n  };\n\n  const getAgentName = (agentName) => {\n    const names = {\n      'MarketAnalyst': '市场分析师',\n      'BullResearcher': '多头研究员',\n      'BearResearcher': '空头研究员',\n      'PortfolioManager': '投资组合经理',\n      'DebateModerator': '辩论主持人',\n      'ExecutionAgent': '执行智能体',\n      'CandlestickAnalyst': '蜡烛图分析师'\n    };\n    return names[agentName] || agentName;\n  };\n\n  const getSpecializationDescription = (specialization) => {\n    const descriptions = {\n      'technical_analysis': '技术分析和市场趋势',\n      'bullish_analysis': '看涨机会识别',\n      'bearish_analysis': '风险识别和看跌分析',\n      'risk_management': '风险管理和资产配置',\n      'consensus_building': '共识建立和观点平衡',\n      'trade_execution': '交易执行和订单管理',\n      'candlestick_analysis': '蜡烛图形态分析'\n    };\n    return descriptions[specialization] || specialization;\n  };\n\n  const formatLastActivity = (lastActivity) => {\n    if (!lastActivity) return '无活动记录';\n    \n    try {\n      const date = new Date(lastActivity);\n      const now = new Date();\n      const diffMs = now - date;\n      const diffMins = Math.floor(diffMs / 60000);\n      \n      if (diffMins < 1) return '刚刚';\n      if (diffMins < 60) return `${diffMins}分钟前`;\n      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;\n      return `${Math.floor(diffMins / 1440)}天前`;\n    } catch {\n      return '时间格式错误';\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 操作栏 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold flex items-center space-x-2\">\n          <Server className=\"w-5 h-5 text-blue-600\" />\n          <span>系统状态监控</span>\n        </h3>\n        <div className=\"flex space-x-2\">\n          <Button \n            onClick={fetchAgents}\n            disabled={isLoading}\n            size=\"sm\"\n            variant=\"outline\"\n          >\n            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />\n            刷新智能体\n          </Button>\n          <Button \n            onClick={onRefresh}\n            size=\"sm\"\n          >\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\n            刷新状态\n          </Button>\n        </div>\n      </div>\n\n      {/* 系统总览 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-base\">系统总览</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {systemStatus ? (\n            <div className=\"space-y-4\">\n              {/* 状态指示 */}\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">系统状态:</span>\n                {getStatusBadge(systemStatus.data?.status)}\n              </div>\n\n              {/* 统计信息 */}\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    {systemStatus.data?.agents?.total_agents || 0}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">总智能体数</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {systemStatus.data?.agents?.active_agents || 0}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">活跃智能体</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {systemStatus.data?.debate_system?.active_debates || 0}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">进行中辩论</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-600\">\n                    {systemStatus.data?.debate_system?.total_debates || 0}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">总辩论次数</div>\n                </div>\n              </div>\n\n              {/* 组件状态 */}\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium\">组件状态:</h4>\n                <div className=\"grid gap-2\">\n                  <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                    <span className=\"text-sm\">智能体管理器</span>\n                    {systemStatus.data?.agents ? \n                      <Badge variant=\"success\">正常</Badge> : \n                      <Badge variant=\"destructive\">异常</Badge>\n                    }\n                  </div>\n                  <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                    <span className=\"text-sm\">辩论系统</span>\n                    {systemStatus.data?.debate_system?.status === 'active' ? \n                      <Badge variant=\"success\">正常</Badge> : \n                      <Badge variant=\"destructive\">异常</Badge>\n                    }\n                  </div>\n                  <div className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                    <span className=\"text-sm\">形态验证器</span>\n                    {systemStatus.data?.pattern_validator?.initialized ? \n                      <Badge variant=\"success\">正常</Badge> : \n                      <Badge variant=\"destructive\">异常</Badge>\n                    }\n                  </div>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-4\">\n              <p className=\"text-gray-600\">正在获取系统状态...</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* 智能体详情 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-base flex items-center space-x-2\">\n            <Users className=\"w-4 h-4\" />\n            <span>智能体详情</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {isLoading ? (\n            <div className=\"text-center py-4\">\n              <p className=\"text-gray-600\">正在加载智能体信息...</p>\n            </div>\n          ) : agents.length > 0 ? (\n            <div className=\"space-y-3\">\n              {agents.map((agent, index) => (\n                <div key={index} className=\"p-3 border rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      {getAgentIcon(agent.name)}\n                      <div>\n                        <h4 className=\"font-medium\">{getAgentName(agent.name)}</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          {getSpecializationDescription(agent.specialization)}\n                        </p>\n                      </div>\n                    </div>\n                    <Badge variant={agent.is_active ? \"success\" : \"secondary\"}>\n                      {agent.is_active ? '活跃' : '非活跃'}\n                    </Badge>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600\">状态:</span>\n                      <span className=\"ml-2 font-medium\">{agent.status || '未知'}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600\">分析次数:</span>\n                      <span className=\"ml-2 font-medium\">{agent.analysis_count || 0}</span>\n                    </div>\n                    <div className=\"col-span-2\">\n                      <span className=\"text-gray-600\">最后活动:</span>\n                      <span className=\"ml-2 font-medium\">\n                        {formatLastActivity(agent.last_activity)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <Alert>\n              <AlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                暂无智能体信息。请检查系统配置。\n              </AlertDescription>\n            </Alert>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* 系统健康检查 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-base\">系统健康检查</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm\">API连接</span>\n              <Badge variant=\"success\">正常</Badge>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm\">LLM服务</span>\n              <Badge variant=\"success\">DeepSeek-V3</Badge>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm\">配置文件</span>\n              <Badge variant=\"success\">已加载</Badge>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm\">环境变量</span>\n              <Badge variant=\"success\">已配置</Badge>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 系统错误提示 */}\n      {systemStatus && systemStatus.status !== 'success' && (\n        <Alert>\n          <AlertTriangle className=\"h-4 w-4\" />\n          <AlertDescription>\n            系统检测到异常: {systemStatus.error || '未知错误'}\n            <br />\n            请检查系统配置或联系技术支持。\n          </AlertDescription>\n        </Alert>\n      )}\n    </div>\n  );\n};\n\nexport default SystemStatus;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,UAAU,CAAEC,SAAS,CAAEC,WAAW,KAAQ,YAAY,CACrE,OAASC,MAAM,KAAQ,cAAc,CACrC,OAASC,KAAK,KAAQ,aAAa,CACnC,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,KAAK,CAAEC,gBAAgB,KAAQ,aAAa,CACrD,OACEC,KAAK,CACLC,WAAW,CACXC,aAAa,CACbC,SAAS,CACTC,MAAM,CACNC,KAAK,CACLC,aAAa,CACbC,MAAM,CACNC,QAAQ,CACRC,KAAK,CACLC,GAAG,CACHC,UAAU,CACVC,YAAY,KACP,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtB,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAiC,KAAAC,kBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,IAAhC,CAAEC,YAAY,CAAEC,SAAU,CAAC,CAAAhB,IAAA,CAC/C,KAAM,CAACiB,MAAM,CAAEC,SAAS,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACgD,SAAS,CAAEC,YAAY,CAAC,CAAGjD,QAAQ,CAAC,KAAK,CAAC,CAEjDC,SAAS,CAAC,IAAM,CACdiD,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9BD,YAAY,CAAC,IAAI,CAAC,CAClB,GAAI,CACF,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,4BAA4B,CAAC,CAC1D,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAClC,GAAID,IAAI,CAACE,MAAM,GAAK,SAAS,CAAE,CAC7BR,SAAS,CAACM,IAAI,CAACA,IAAI,CAACP,MAAM,EAAI,EAAE,CAAC,CACnC,CACF,CAAE,MAAOU,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CAAC,OAAS,CACRP,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAS,cAAc,CAAIH,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,SAAS,CACZ,mBAAO5B,KAAA,CAACpB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAACC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eACrEpC,IAAA,CAACb,WAAW,EAACgD,SAAS,CAAC,cAAc,CAAE,CAAC,qBAE1C,EAAO,CAAC,CACV,IAAK,iBAAiB,CACpB,mBAAOjC,KAAA,CAACpB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAACC,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eACvEpC,IAAA,CAACZ,aAAa,EAAC+C,SAAS,CAAC,cAAc,CAAE,CAAC,2BAE5C,EAAO,CAAC,CACV,QACE,mBAAOjC,KAAA,CAACpB,KAAK,EAACoD,OAAO,CAAC,aAAa,CAAAE,QAAA,eACjCpC,IAAA,CAACZ,aAAa,EAAC+C,SAAS,CAAC,cAAc,CAAE,CAAC,eAE5C,EAAO,CAAC,CACZ,CACF,CAAC,CAED,KAAM,CAAAE,YAAY,CAAIC,SAAS,EAAK,CAClC,KAAM,CAAAC,KAAK,CAAG,CACZ,eAAe,cAAEvC,IAAA,CAACN,QAAQ,EAACyC,SAAS,CAAC,SAAS,CAAE,CAAC,CACjD,gBAAgB,cAAEnC,IAAA,CAACH,UAAU,EAACsC,SAAS,CAAC,SAAS,CAAE,CAAC,CACpD,gBAAgB,cAAEnC,IAAA,CAACF,YAAY,EAACqC,SAAS,CAAC,SAAS,CAAE,CAAC,CACtD,kBAAkB,cAAEnC,IAAA,CAACP,MAAM,EAAC0C,SAAS,CAAC,SAAS,CAAE,CAAC,CAClD,iBAAiB,cAAEnC,IAAA,CAACR,aAAa,EAAC2C,SAAS,CAAC,SAAS,CAAE,CAAC,CACxD,gBAAgB,cAAEnC,IAAA,CAACJ,GAAG,EAACuC,SAAS,CAAC,SAAS,CAAE,CAAC,CAC7C,oBAAoB,cAAEnC,IAAA,CAACd,KAAK,EAACiD,SAAS,CAAC,SAAS,CAAE,CACpD,CAAC,CACD,MAAO,CAAAI,KAAK,CAACD,SAAS,CAAC,eAAItC,IAAA,CAACd,KAAK,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC1D,CAAC,CAED,KAAM,CAAAK,YAAY,CAAIF,SAAS,EAAK,CAClC,KAAM,CAAAG,KAAK,CAAG,CACZ,eAAe,CAAE,OAAO,CACxB,gBAAgB,CAAE,OAAO,CACzB,gBAAgB,CAAE,OAAO,CACzB,kBAAkB,CAAE,QAAQ,CAC5B,iBAAiB,CAAE,OAAO,CAC1B,gBAAgB,CAAE,OAAO,CACzB,oBAAoB,CAAE,QACxB,CAAC,CACD,MAAO,CAAAA,KAAK,CAACH,SAAS,CAAC,EAAIA,SAAS,CACtC,CAAC,CAED,KAAM,CAAAI,4BAA4B,CAAIC,cAAc,EAAK,CACvD,KAAM,CAAAC,YAAY,CAAG,CACnB,oBAAoB,CAAE,WAAW,CACjC,kBAAkB,CAAE,QAAQ,CAC5B,kBAAkB,CAAE,WAAW,CAC/B,iBAAiB,CAAE,WAAW,CAC9B,oBAAoB,CAAE,WAAW,CACjC,iBAAiB,CAAE,WAAW,CAC9B,sBAAsB,CAAE,SAC1B,CAAC,CACD,MAAO,CAAAA,YAAY,CAACD,cAAc,CAAC,EAAIA,cAAc,CACvD,CAAC,CAED,KAAM,CAAAE,kBAAkB,CAAIC,YAAY,EAAK,CAC3C,GAAI,CAACA,YAAY,CAAE,MAAO,OAAO,CAEjC,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,YAAY,CAAC,CACnC,KAAM,CAAAG,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAE,MAAM,CAAGD,GAAG,CAAGF,IAAI,CACzB,KAAM,CAAAI,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,CAAG,KAAK,CAAC,CAE3C,GAAIC,QAAQ,CAAG,CAAC,CAAE,MAAO,IAAI,CAC7B,GAAIA,QAAQ,CAAG,EAAE,CAAE,SAAAG,MAAA,CAAUH,QAAQ,uBACrC,GAAIA,QAAQ,CAAG,IAAI,CAAE,SAAAG,MAAA,CAAUF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAG,EAAE,CAAC,uBACxD,SAAAG,MAAA,CAAUF,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAG,IAAI,CAAC,iBACvC,CAAE,MAAAI,OAAA,CAAM,CACN,MAAO,QAAQ,CACjB,CACF,CAAC,CAED,mBACErD,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDlC,KAAA,OAAIiC,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC/DpC,IAAA,CAACV,MAAM,EAAC6C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAC5CnC,IAAA,SAAAoC,QAAA,CAAM,sCAAM,CAAM,CAAC,EACjB,CAAC,cACLlC,KAAA,QAAKiC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BlC,KAAA,CAACrB,MAAM,EACL2E,OAAO,CAAE/B,WAAY,CACrBgC,QAAQ,CAAElC,SAAU,CACpBmC,IAAI,CAAC,IAAI,CACTxB,OAAO,CAAC,SAAS,CAAAE,QAAA,eAEjBpC,IAAA,CAACX,SAAS,EAAC8C,SAAS,iBAAAmB,MAAA,CAAkB/B,SAAS,CAAG,cAAc,CAAG,EAAE,CAAG,CAAE,CAAC,iCAE7E,EAAQ,CAAC,cACTrB,KAAA,CAACrB,MAAM,EACL2E,OAAO,CAAEpC,SAAU,CACnBsC,IAAI,CAAC,IAAI,CAAAtB,QAAA,eAETpC,IAAA,CAACX,SAAS,EAAC8C,SAAS,CAAC,cAAc,CAAE,CAAC,2BAExC,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNjC,KAAA,CAACzB,IAAI,EAAA2D,QAAA,eACHpC,IAAA,CAACtB,UAAU,EAAA0D,QAAA,cACTpC,IAAA,CAACrB,SAAS,EAACwD,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,0BAAI,CAAW,CAAC,CACvC,CAAC,cACbpC,IAAA,CAACpB,WAAW,EAAAwD,QAAA,CACTjB,YAAY,cACXjB,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpC,IAAA,SAAMmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,CACnDH,cAAc,EAAA5B,kBAAA,CAACc,YAAY,CAACS,IAAI,UAAAvB,kBAAA,iBAAjBA,kBAAA,CAAmByB,MAAM,CAAC,EACvC,CAAC,cAGN5B,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,QAAKmC,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9C,EAAA9B,mBAAA,CAAAa,YAAY,CAACS,IAAI,UAAAtB,mBAAA,kBAAAC,qBAAA,CAAjBD,mBAAA,CAAmBe,MAAM,UAAAd,qBAAA,iBAAzBA,qBAAA,CAA2BoD,YAAY,GAAI,CAAC,CAC1C,CAAC,cACN3D,IAAA,QAAKmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,EAC/C,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,QAAKmC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C,EAAA5B,mBAAA,CAAAW,YAAY,CAACS,IAAI,UAAApB,mBAAA,kBAAAC,qBAAA,CAAjBD,mBAAA,CAAmBa,MAAM,UAAAZ,qBAAA,iBAAzBA,qBAAA,CAA2BmD,aAAa,GAAI,CAAC,CAC3C,CAAC,cACN5D,IAAA,QAAKmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,EAC/C,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,QAAKmC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChD,EAAA1B,mBAAA,CAAAS,YAAY,CAACS,IAAI,UAAAlB,mBAAA,kBAAAC,qBAAA,CAAjBD,mBAAA,CAAmBmD,aAAa,UAAAlD,qBAAA,iBAAhCA,qBAAA,CAAkCmD,cAAc,GAAI,CAAC,CACnD,CAAC,cACN9D,IAAA,QAAKmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,EAC/C,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BpC,IAAA,QAAKmC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChD,EAAAxB,mBAAA,CAAAO,YAAY,CAACS,IAAI,UAAAhB,mBAAA,kBAAAC,qBAAA,CAAjBD,mBAAA,CAAmBiD,aAAa,UAAAhD,qBAAA,iBAAhCA,qBAAA,CAAkCkD,aAAa,GAAI,CAAC,CAClD,CAAC,cACN/D,IAAA,QAAKmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,EAC/C,CAAC,EACH,CAAC,cAGNlC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBpC,IAAA,OAAImC,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAK,CAAI,CAAC,cACtClC,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBlC,KAAA,QAAKiC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,sCAAM,CAAM,CAAC,CACtC,CAAAtB,mBAAA,CAAAK,YAAY,CAACS,IAAI,UAAAd,mBAAA,WAAjBA,mBAAA,CAAmBO,MAAM,cACxBrB,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,cACnCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,aAAa,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,EAEtC,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,CACpC,EAAArB,mBAAA,CAAAI,YAAY,CAACS,IAAI,UAAAb,mBAAA,kBAAAC,qBAAA,CAAjBD,mBAAA,CAAmB8C,aAAa,UAAA7C,qBAAA,iBAAhCA,qBAAA,CAAkCc,MAAM,IAAK,QAAQ,cACpD9B,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,cACnCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,aAAa,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,EAEtC,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,gCAAK,CAAM,CAAC,CACrC,CAAAnB,mBAAA,CAAAE,YAAY,CAACS,IAAI,UAAAX,mBAAA,YAAAC,qBAAA,CAAjBD,mBAAA,CAAmB+C,iBAAiB,UAAA9C,qBAAA,WAApCA,qBAAA,CAAsC+C,WAAW,cAChDjE,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,cACnCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,aAAa,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,EAEtC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENpC,IAAA,QAAKmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BpC,IAAA,MAAGmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,qDAAW,CAAG,CAAC,CACzC,CACN,CACU,CAAC,EACV,CAAC,cAGPlC,KAAA,CAACzB,IAAI,EAAA2D,QAAA,eACHpC,IAAA,CAACtB,UAAU,EAAA0D,QAAA,cACTlC,KAAA,CAACvB,SAAS,EAACwD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAC1DpC,IAAA,CAACT,KAAK,EAAC4C,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7BnC,IAAA,SAAAoC,QAAA,CAAM,gCAAK,CAAM,CAAC,EACT,CAAC,CACF,CAAC,cACbpC,IAAA,CAACpB,WAAW,EAAAwD,QAAA,CACTb,SAAS,cACRvB,IAAA,QAAKmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BpC,IAAA,MAAGmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2DAAY,CAAG,CAAC,CAC1C,CAAC,CACJf,MAAM,CAAC6C,MAAM,CAAG,CAAC,cACnBlE,IAAA,QAAKmC,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBf,MAAM,CAAC8C,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACvBnE,KAAA,QAAiBiC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAChDlC,KAAA,QAAKiC,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDlC,KAAA,QAAKiC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EACzCC,YAAY,CAAC+B,KAAK,CAACE,IAAI,CAAC,cACzBpE,KAAA,QAAAkC,QAAA,eACEpC,IAAA,OAAImC,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEI,YAAY,CAAC4B,KAAK,CAACE,IAAI,CAAC,CAAK,CAAC,cAC3DtE,IAAA,MAAGmC,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACjCM,4BAA4B,CAAC0B,KAAK,CAACzB,cAAc,CAAC,CAClD,CAAC,EACD,CAAC,EACH,CAAC,cACN3C,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAEkC,KAAK,CAACG,SAAS,CAAG,SAAS,CAAG,WAAY,CAAAnC,QAAA,CACvDgC,KAAK,CAACG,SAAS,CAAG,IAAI,CAAG,KAAK,CAC1B,CAAC,EACL,CAAC,cAENrE,KAAA,QAAKiC,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ClC,KAAA,QAAAkC,QAAA,eACEpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,cAC1CpC,IAAA,SAAMmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEgC,KAAK,CAACtC,MAAM,EAAI,IAAI,CAAO,CAAC,EAC7D,CAAC,cACN5B,KAAA,QAAAkC,QAAA,eACEpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC5CpC,IAAA,SAAMmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAEgC,KAAK,CAACI,cAAc,EAAI,CAAC,CAAO,CAAC,EAClE,CAAC,cACNtE,KAAA,QAAKiC,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBpC,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC5CpC,IAAA,SAAMmC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC/BS,kBAAkB,CAACuB,KAAK,CAACK,aAAa,CAAC,CACpC,CAAC,EACJ,CAAC,EACH,CAAC,GA/BEJ,KAgCL,CACN,CAAC,CACC,CAAC,cAENnE,KAAA,CAAClB,KAAK,EAAAoD,QAAA,eACJpC,IAAA,CAACZ,aAAa,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,cACrCnC,IAAA,CAACf,gBAAgB,EAAAmD,QAAA,CAAC,kGAElB,CAAkB,CAAC,EACd,CACR,CACU,CAAC,EACV,CAAC,cAGPlC,KAAA,CAACzB,IAAI,EAAA2D,QAAA,eACHpC,IAAA,CAACtB,UAAU,EAAA0D,QAAA,cACTpC,IAAA,CAACrB,SAAS,EAACwD,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,sCAAM,CAAW,CAAC,CACzC,CAAC,cACbpC,IAAA,CAACpB,WAAW,EAAAwD,QAAA,cACVlC,KAAA,QAAKiC,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,iBAAK,CAAM,CAAC,cACtCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,cAAE,CAAO,CAAC,EAChC,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,iBAAK,CAAM,CAAC,cACtCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,aAAW,CAAO,CAAC,EACzC,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACrCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,oBAAG,CAAO,CAAC,EACjC,CAAC,cACNlC,KAAA,QAAKiC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDpC,IAAA,SAAMmC,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACrCpC,IAAA,CAAClB,KAAK,EAACoD,OAAO,CAAC,SAAS,CAAAE,QAAA,CAAC,oBAAG,CAAO,CAAC,EACjC,CAAC,EACH,CAAC,CACK,CAAC,EACV,CAAC,CAGNjB,YAAY,EAAIA,YAAY,CAACW,MAAM,GAAK,SAAS,eAChD5B,KAAA,CAAClB,KAAK,EAAAoD,QAAA,eACJpC,IAAA,CAACZ,aAAa,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,cACrCjC,KAAA,CAACjB,gBAAgB,EAAAmD,QAAA,EAAC,8CACP,CAACjB,YAAY,CAACY,KAAK,EAAI,MAAM,cACtC/B,IAAA,QAAK,CAAC,6FAER,EAAkB,CAAC,EACd,CACR,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}