{"ast": null, "code": "/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\nexport function isString(o) {\n  return typeof o === \"string\";\n}\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\nexport function hasLocaleWeekInfo() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.Locale && (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype);\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (!integerBetween(settings.firstDay, 1, 7) || !integerBetween(settings.minimalDays, 1, 7) || !Array.isArray(settings.weekend) || settings.weekend.some(v => !integerBetween(v, 1, 7))) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend)\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\nexport function roundTo(number, digits, towardZero = false) {\n  const factor = 10 ** digits,\n    rounder = towardZero ? Math.trunc : Math.round;\n  return rounder(number * factor) / factor;\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(obj.year, obj.month - 1, obj.day, obj.hour, obj.minute, obj.second, obj.millisecond);\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    };\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n  const modified = {\n    timeZoneName: offsetFormat,\n    ...intlOpts\n  };\n  const parsed = new Intl.DateTimeFormat(locale, modified).formatToParts(date).find(m => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || Number.isNaN(numericValue)) throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}", "map": {"version": 3, "names": ["InvalidArgumentError", "Settings", "dayOfWeek", "isoWeekdayToLocal", "isUndefined", "o", "isNumber", "isInteger", "isString", "isDate", "Object", "prototype", "toString", "call", "hasRelative", "Intl", "RelativeTimeFormat", "e", "hasLocaleWeekInfo", "Locale", "maybeA<PERSON>y", "thing", "Array", "isArray", "bestBy", "arr", "by", "compare", "length", "undefined", "reduce", "best", "next", "pair", "pick", "obj", "keys", "a", "k", "hasOwnProperty", "prop", "validateWeekSettings", "settings", "integerBetween", "firstDay", "minimalDays", "weekend", "some", "v", "from", "bottom", "top", "floorMod", "x", "n", "Math", "floor", "padStart", "input", "isNeg", "padded", "parseInteger", "string", "parseInt", "parseFloating", "parseFloat", "parse<PERSON><PERSON><PERSON>", "fraction", "f", "roundTo", "number", "digits", "towardZero", "factor", "rounder", "trunc", "round", "isLeapYear", "year", "daysInYear", "daysInMonth", "month", "mod<PERSON>onth", "modYear", "objToLocalTS", "d", "Date", "UTC", "day", "hour", "minute", "second", "millisecond", "setUTCFullYear", "firstWeekOffset", "minDaysInFirstWeek", "startOfWeek", "fwdlw", "weeksInWeekYear", "weekYear", "weekOffset", "weekOffsetNext", "untruncateYear", "twoDigitCutoffYear", "parseZoneInfo", "ts", "offsetFormat", "locale", "timeZone", "date", "intlOpts", "hourCycle", "modified", "timeZoneName", "parsed", "DateTimeFormat", "formatToParts", "find", "m", "type", "toLowerCase", "value", "signedOffset", "offHourStr", "offMinuteStr", "offHour", "Number", "isNaN", "offMin", "offMinSigned", "is", "asNumber", "numericValue", "normalizeObject", "normalizer", "normalized", "u", "formatOffset", "offset", "format", "hours", "abs", "minutes", "sign", "RangeError", "timeObject"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/impl/util.js"], "sourcesContent": ["/*\n  This is just a junk drawer, containing anything used across multiple classes.\n  Because <PERSON>xon is small(ish), this should stay small and we won't worry about splitting\n  it up into, say, parsingUtil.js and basicUtil.js and so on. But they are divided up by feature area.\n*/\n\nimport { InvalidArgumentError } from \"../errors.js\";\nimport Settings from \"../settings.js\";\nimport { dayOfWeek, isoWeekdayToLocal } from \"./conversions.js\";\n\n/**\n * @private\n */\n\n// TYPES\n\nexport function isUndefined(o) {\n  return typeof o === \"undefined\";\n}\n\nexport function isNumber(o) {\n  return typeof o === \"number\";\n}\n\nexport function isInteger(o) {\n  return typeof o === \"number\" && o % 1 === 0;\n}\n\nexport function isString(o) {\n  return typeof o === \"string\";\n}\n\nexport function isDate(o) {\n  return Object.prototype.toString.call(o) === \"[object Date]\";\n}\n\n// CAPABILITIES\n\nexport function hasRelative() {\n  try {\n    return typeof Intl !== \"undefined\" && !!Intl.RelativeTimeFormat;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function hasLocaleWeekInfo() {\n  try {\n    return (\n      typeof Intl !== \"undefined\" &&\n      !!Intl.Locale &&\n      (\"weekInfo\" in Intl.Locale.prototype || \"getWeekInfo\" in Intl.Locale.prototype)\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\n// OBJECTS AND ARRAYS\n\nexport function maybeArray(thing) {\n  return Array.isArray(thing) ? thing : [thing];\n}\n\nexport function bestBy(arr, by, compare) {\n  if (arr.length === 0) {\n    return undefined;\n  }\n  return arr.reduce((best, next) => {\n    const pair = [by(next), next];\n    if (!best) {\n      return pair;\n    } else if (compare(best[0], pair[0]) === best[0]) {\n      return best;\n    } else {\n      return pair;\n    }\n  }, null)[1];\n}\n\nexport function pick(obj, keys) {\n  return keys.reduce((a, k) => {\n    a[k] = obj[k];\n    return a;\n  }, {});\n}\n\nexport function hasOwnProperty(obj, prop) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n\nexport function validateWeekSettings(settings) {\n  if (settings == null) {\n    return null;\n  } else if (typeof settings !== \"object\") {\n    throw new InvalidArgumentError(\"Week settings must be an object\");\n  } else {\n    if (\n      !integerBetween(settings.firstDay, 1, 7) ||\n      !integerBetween(settings.minimalDays, 1, 7) ||\n      !Array.isArray(settings.weekend) ||\n      settings.weekend.some((v) => !integerBetween(v, 1, 7))\n    ) {\n      throw new InvalidArgumentError(\"Invalid week settings\");\n    }\n    return {\n      firstDay: settings.firstDay,\n      minimalDays: settings.minimalDays,\n      weekend: Array.from(settings.weekend),\n    };\n  }\n}\n\n// NUMBERS AND STRINGS\n\nexport function integerBetween(thing, bottom, top) {\n  return isInteger(thing) && thing >= bottom && thing <= top;\n}\n\n// x % n but takes the sign of n instead of x\nexport function floorMod(x, n) {\n  return x - n * Math.floor(x / n);\n}\n\nexport function padStart(input, n = 2) {\n  const isNeg = input < 0;\n  let padded;\n  if (isNeg) {\n    padded = \"-\" + (\"\" + -input).padStart(n, \"0\");\n  } else {\n    padded = (\"\" + input).padStart(n, \"0\");\n  }\n  return padded;\n}\n\nexport function parseInteger(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseInt(string, 10);\n  }\n}\n\nexport function parseFloating(string) {\n  if (isUndefined(string) || string === null || string === \"\") {\n    return undefined;\n  } else {\n    return parseFloat(string);\n  }\n}\n\nexport function parseMillis(fraction) {\n  // Return undefined (instead of 0) in these cases, where fraction is not set\n  if (isUndefined(fraction) || fraction === null || fraction === \"\") {\n    return undefined;\n  } else {\n    const f = parseFloat(\"0.\" + fraction) * 1000;\n    return Math.floor(f);\n  }\n}\n\nexport function roundTo(number, digits, towardZero = false) {\n  const factor = 10 ** digits,\n    rounder = towardZero ? Math.trunc : Math.round;\n  return rounder(number * factor) / factor;\n}\n\n// DATE BASICS\n\nexport function isLeapYear(year) {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function daysInYear(year) {\n  return isLeapYear(year) ? 366 : 365;\n}\n\nexport function daysInMonth(year, month) {\n  const modMonth = floorMod(month - 1, 12) + 1,\n    modYear = year + (month - modMonth) / 12;\n\n  if (modMonth === 2) {\n    return isLeapYear(modYear) ? 29 : 28;\n  } else {\n    return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][modMonth - 1];\n  }\n}\n\n// convert a calendar object to a local timestamp (epoch, but with the offset baked in)\nexport function objToLocalTS(obj) {\n  let d = Date.UTC(\n    obj.year,\n    obj.month - 1,\n    obj.day,\n    obj.hour,\n    obj.minute,\n    obj.second,\n    obj.millisecond\n  );\n\n  // for legacy reasons, years between 0 and 99 are interpreted as 19XX; revert that\n  if (obj.year < 100 && obj.year >= 0) {\n    d = new Date(d);\n    // set the month and day again, this is necessary because year 2000 is a leap year, but year 100 is not\n    // so if obj.year is in 99, but obj.day makes it roll over into year 100,\n    // the calculations done by Date.UTC are using year 2000 - which is incorrect\n    d.setUTCFullYear(obj.year, obj.month - 1, obj.day);\n  }\n  return +d;\n}\n\n// adapted from moment.js: https://github.com/moment/moment/blob/000ac1800e620f770f4eb31b5ae908f6167b0ab2/src/lib/units/week-calendar-utils.js\nfunction firstWeekOffset(year, minDaysInFirstWeek, startOfWeek) {\n  const fwdlw = isoWeekdayToLocal(dayOfWeek(year, 1, minDaysInFirstWeek), startOfWeek);\n  return -fwdlw + minDaysInFirstWeek - 1;\n}\n\nexport function weeksInWeekYear(weekYear, minDaysInFirstWeek = 4, startOfWeek = 1) {\n  const weekOffset = firstWeekOffset(weekYear, minDaysInFirstWeek, startOfWeek);\n  const weekOffsetNext = firstWeekOffset(weekYear + 1, minDaysInFirstWeek, startOfWeek);\n  return (daysInYear(weekYear) - weekOffset + weekOffsetNext) / 7;\n}\n\nexport function untruncateYear(year) {\n  if (year > 99) {\n    return year;\n  } else return year > Settings.twoDigitCutoffYear ? 1900 + year : 2000 + year;\n}\n\n// PARSING\n\nexport function parseZoneInfo(ts, offsetFormat, locale, timeZone = null) {\n  const date = new Date(ts),\n    intlOpts = {\n      hourCycle: \"h23\",\n      year: \"numeric\",\n      month: \"2-digit\",\n      day: \"2-digit\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    };\n\n  if (timeZone) {\n    intlOpts.timeZone = timeZone;\n  }\n\n  const modified = { timeZoneName: offsetFormat, ...intlOpts };\n\n  const parsed = new Intl.DateTimeFormat(locale, modified)\n    .formatToParts(date)\n    .find((m) => m.type.toLowerCase() === \"timezonename\");\n  return parsed ? parsed.value : null;\n}\n\n// signedOffset('-5', '30') -> -330\nexport function signedOffset(offHourStr, offMinuteStr) {\n  let offHour = parseInt(offHourStr, 10);\n\n  // don't || this because we want to preserve -0\n  if (Number.isNaN(offHour)) {\n    offHour = 0;\n  }\n\n  const offMin = parseInt(offMinuteStr, 10) || 0,\n    offMinSigned = offHour < 0 || Object.is(offHour, -0) ? -offMin : offMin;\n  return offHour * 60 + offMinSigned;\n}\n\n// COERCION\n\nexport function asNumber(value) {\n  const numericValue = Number(value);\n  if (typeof value === \"boolean\" || value === \"\" || Number.isNaN(numericValue))\n    throw new InvalidArgumentError(`Invalid unit value ${value}`);\n  return numericValue;\n}\n\nexport function normalizeObject(obj, normalizer) {\n  const normalized = {};\n  for (const u in obj) {\n    if (hasOwnProperty(obj, u)) {\n      const v = obj[u];\n      if (v === undefined || v === null) continue;\n      normalized[normalizer(u)] = asNumber(v);\n    }\n  }\n  return normalized;\n}\n\n/**\n * Returns the offset's value as a string\n * @param {number} ts - Epoch milliseconds for which to get the offset\n * @param {string} format - What style of offset to return.\n *                          Accepts 'narrow', 'short', or 'techie'. Returning '+6', '+06:00', or '+0600' respectively\n * @return {string}\n */\nexport function formatOffset(offset, format) {\n  const hours = Math.trunc(Math.abs(offset / 60)),\n    minutes = Math.trunc(Math.abs(offset % 60)),\n    sign = offset >= 0 ? \"+\" : \"-\";\n\n  switch (format) {\n    case \"short\":\n      return `${sign}${padStart(hours, 2)}:${padStart(minutes, 2)}`;\n    case \"narrow\":\n      return `${sign}${hours}${minutes > 0 ? `:${minutes}` : \"\"}`;\n    case \"techie\":\n      return `${sign}${padStart(hours, 2)}${padStart(minutes, 2)}`;\n    default:\n      throw new RangeError(`Value format ${format} is out of range for property format`);\n  }\n}\n\nexport function timeObject(obj) {\n  return pick(obj, [\"hour\", \"minute\", \"second\", \"millisecond\"]);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,oBAAoB,QAAQ,cAAc;AACnD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,SAAS,EAAEC,iBAAiB,QAAQ,kBAAkB;;AAE/D;AACA;AACA;;AAEA;;AAEA,OAAO,SAASC,WAAWA,CAACC,CAAC,EAAE;EAC7B,OAAO,OAAOA,CAAC,KAAK,WAAW;AACjC;AAEA,OAAO,SAASC,QAAQA,CAACD,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B;AAEA,OAAO,SAASE,SAASA,CAACF,CAAC,EAAE;EAC3B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7C;AAEA,OAAO,SAASG,QAAQA,CAACH,CAAC,EAAE;EAC1B,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B;AAEA,OAAO,SAASI,MAAMA,CAACJ,CAAC,EAAE;EACxB,OAAOK,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,CAAC,CAAC,KAAK,eAAe;AAC9D;;AAEA;;AAEA,OAAO,SAASS,WAAWA,CAAA,EAAG;EAC5B,IAAI;IACF,OAAO,OAAOC,IAAI,KAAK,WAAW,IAAI,CAAC,CAACA,IAAI,CAACC,kBAAkB;EACjE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,IAAI;IACF,OACE,OAAOH,IAAI,KAAK,WAAW,IAC3B,CAAC,CAACA,IAAI,CAACI,MAAM,KACZ,UAAU,IAAIJ,IAAI,CAACI,MAAM,CAACR,SAAS,IAAI,aAAa,IAAII,IAAI,CAACI,MAAM,CAACR,SAAS,CAAC;EAEnF,CAAC,CAAC,OAAOM,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;;AAEA;;AAEA,OAAO,SAASG,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AAC/C;AAEA,OAAO,SAASG,MAAMA,CAACC,GAAG,EAAEC,EAAE,EAAEC,OAAO,EAAE;EACvC,IAAIF,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IACpB,OAAOC,SAAS;EAClB;EACA,OAAOJ,GAAG,CAACK,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;IAChC,MAAMC,IAAI,GAAG,CAACP,EAAE,CAACM,IAAI,CAAC,EAAEA,IAAI,CAAC;IAC7B,IAAI,CAACD,IAAI,EAAE;MACT,OAAOE,IAAI;IACb,CAAC,MAAM,IAAIN,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKF,IAAI,CAAC,CAAC,CAAC,EAAE;MAChD,OAAOA,IAAI;IACb,CAAC,MAAM;MACL,OAAOE,IAAI;IACb;EACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACb;AAEA,OAAO,SAASC,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACN,MAAM,CAAC,CAACO,CAAC,EAAEC,CAAC,KAAK;IAC3BD,CAAC,CAACC,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC;IACb,OAAOD,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AAEA,OAAO,SAASE,cAAcA,CAACJ,GAAG,EAAEK,IAAI,EAAE;EACxC,OAAO9B,MAAM,CAACC,SAAS,CAAC4B,cAAc,CAAC1B,IAAI,CAACsB,GAAG,EAAEK,IAAI,CAAC;AACxD;AAEA,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;EAC7C,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,IAAI;EACb,CAAC,MAAM,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IACvC,MAAM,IAAI1C,oBAAoB,CAAC,iCAAiC,CAAC;EACnE,CAAC,MAAM;IACL,IACE,CAAC2C,cAAc,CAACD,QAAQ,CAACE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,IACxC,CAACD,cAAc,CAACD,QAAQ,CAACG,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,IAC3C,CAACvB,KAAK,CAACC,OAAO,CAACmB,QAAQ,CAACI,OAAO,CAAC,IAChCJ,QAAQ,CAACI,OAAO,CAACC,IAAI,CAAEC,CAAC,IAAK,CAACL,cAAc,CAACK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtD;MACA,MAAM,IAAIhD,oBAAoB,CAAC,uBAAuB,CAAC;IACzD;IACA,OAAO;MACL4C,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;MAC3BC,WAAW,EAAEH,QAAQ,CAACG,WAAW;MACjCC,OAAO,EAAExB,KAAK,CAAC2B,IAAI,CAACP,QAAQ,CAACI,OAAO;IACtC,CAAC;EACH;AACF;;AAEA;;AAEA,OAAO,SAASH,cAAcA,CAACtB,KAAK,EAAE6B,MAAM,EAAEC,GAAG,EAAE;EACjD,OAAO5C,SAAS,CAACc,KAAK,CAAC,IAAIA,KAAK,IAAI6B,MAAM,IAAI7B,KAAK,IAAI8B,GAAG;AAC5D;;AAEA;AACA,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B,OAAOD,CAAC,GAAGC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,GAAGC,CAAC,CAAC;AAClC;AAEA,OAAO,SAASG,QAAQA,CAACC,KAAK,EAAEJ,CAAC,GAAG,CAAC,EAAE;EACrC,MAAMK,KAAK,GAAGD,KAAK,GAAG,CAAC;EACvB,IAAIE,MAAM;EACV,IAAID,KAAK,EAAE;IACTC,MAAM,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,CAACF,KAAK,EAAED,QAAQ,CAACH,CAAC,EAAE,GAAG,CAAC;EAC/C,CAAC,MAAM;IACLM,MAAM,GAAG,CAAC,EAAE,GAAGF,KAAK,EAAED,QAAQ,CAACH,CAAC,EAAE,GAAG,CAAC;EACxC;EACA,OAAOM,MAAM;AACf;AAEA,OAAO,SAASC,YAAYA,CAACC,MAAM,EAAE;EACnC,IAAI1D,WAAW,CAAC0D,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,EAAE,EAAE;IAC3D,OAAOjC,SAAS;EAClB,CAAC,MAAM;IACL,OAAOkC,QAAQ,CAACD,MAAM,EAAE,EAAE,CAAC;EAC7B;AACF;AAEA,OAAO,SAASE,aAAaA,CAACF,MAAM,EAAE;EACpC,IAAI1D,WAAW,CAAC0D,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,EAAE,EAAE;IAC3D,OAAOjC,SAAS;EAClB,CAAC,MAAM;IACL,OAAOoC,UAAU,CAACH,MAAM,CAAC;EAC3B;AACF;AAEA,OAAO,SAASI,WAAWA,CAACC,QAAQ,EAAE;EACpC;EACA,IAAI/D,WAAW,CAAC+D,QAAQ,CAAC,IAAIA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,EAAE,EAAE;IACjE,OAAOtC,SAAS;EAClB,CAAC,MAAM;IACL,MAAMuC,CAAC,GAAGH,UAAU,CAAC,IAAI,GAAGE,QAAQ,CAAC,GAAG,IAAI;IAC5C,OAAOZ,IAAI,CAACC,KAAK,CAACY,CAAC,CAAC;EACtB;AACF;AAEA,OAAO,SAASC,OAAOA,CAACC,MAAM,EAAEC,MAAM,EAAEC,UAAU,GAAG,KAAK,EAAE;EAC1D,MAAMC,MAAM,GAAG,EAAE,IAAIF,MAAM;IACzBG,OAAO,GAAGF,UAAU,GAAGjB,IAAI,CAACoB,KAAK,GAAGpB,IAAI,CAACqB,KAAK;EAChD,OAAOF,OAAO,CAACJ,MAAM,GAAGG,MAAM,CAAC,GAAGA,MAAM;AAC1C;;AAEA;;AAEA,OAAO,SAASI,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOA,IAAI,GAAG,CAAC,KAAK,CAAC,KAAKA,IAAI,GAAG,GAAG,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC;AACjE;AAEA,OAAO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAOD,UAAU,CAACC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AACrC;AAEA,OAAO,SAASE,WAAWA,CAACF,IAAI,EAAEG,KAAK,EAAE;EACvC,MAAMC,QAAQ,GAAG9B,QAAQ,CAAC6B,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;IAC1CE,OAAO,GAAGL,IAAI,GAAG,CAACG,KAAK,GAAGC,QAAQ,IAAI,EAAE;EAE1C,IAAIA,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAOL,UAAU,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;EACtC,CAAC,MAAM;IACL,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACD,QAAQ,GAAG,CAAC,CAAC;EACzE;AACF;;AAEA;AACA,OAAO,SAASE,YAAYA,CAACjD,GAAG,EAAE;EAChC,IAAIkD,CAAC,GAAGC,IAAI,CAACC,GAAG,CACdpD,GAAG,CAAC2C,IAAI,EACR3C,GAAG,CAAC8C,KAAK,GAAG,CAAC,EACb9C,GAAG,CAACqD,GAAG,EACPrD,GAAG,CAACsD,IAAI,EACRtD,GAAG,CAACuD,MAAM,EACVvD,GAAG,CAACwD,MAAM,EACVxD,GAAG,CAACyD,WACN,CAAC;;EAED;EACA,IAAIzD,GAAG,CAAC2C,IAAI,GAAG,GAAG,IAAI3C,GAAG,CAAC2C,IAAI,IAAI,CAAC,EAAE;IACnCO,CAAC,GAAG,IAAIC,IAAI,CAACD,CAAC,CAAC;IACf;IACA;IACA;IACAA,CAAC,CAACQ,cAAc,CAAC1D,GAAG,CAAC2C,IAAI,EAAE3C,GAAG,CAAC8C,KAAK,GAAG,CAAC,EAAE9C,GAAG,CAACqD,GAAG,CAAC;EACpD;EACA,OAAO,CAACH,CAAC;AACX;;AAEA;AACA,SAASS,eAAeA,CAAChB,IAAI,EAAEiB,kBAAkB,EAAEC,WAAW,EAAE;EAC9D,MAAMC,KAAK,GAAG9F,iBAAiB,CAACD,SAAS,CAAC4E,IAAI,EAAE,CAAC,EAAEiB,kBAAkB,CAAC,EAAEC,WAAW,CAAC;EACpF,OAAO,CAACC,KAAK,GAAGF,kBAAkB,GAAG,CAAC;AACxC;AAEA,OAAO,SAASG,eAAeA,CAACC,QAAQ,EAAEJ,kBAAkB,GAAG,CAAC,EAAEC,WAAW,GAAG,CAAC,EAAE;EACjF,MAAMI,UAAU,GAAGN,eAAe,CAACK,QAAQ,EAAEJ,kBAAkB,EAAEC,WAAW,CAAC;EAC7E,MAAMK,cAAc,GAAGP,eAAe,CAACK,QAAQ,GAAG,CAAC,EAAEJ,kBAAkB,EAAEC,WAAW,CAAC;EACrF,OAAO,CAACjB,UAAU,CAACoB,QAAQ,CAAC,GAAGC,UAAU,GAAGC,cAAc,IAAI,CAAC;AACjE;AAEA,OAAO,SAASC,cAAcA,CAACxB,IAAI,EAAE;EACnC,IAAIA,IAAI,GAAG,EAAE,EAAE;IACb,OAAOA,IAAI;EACb,CAAC,MAAM,OAAOA,IAAI,GAAG7E,QAAQ,CAACsG,kBAAkB,GAAG,IAAI,GAAGzB,IAAI,GAAG,IAAI,GAAGA,IAAI;AAC9E;;AAEA;;AAEA,OAAO,SAAS0B,aAAaA,CAACC,EAAE,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,GAAG,IAAI,EAAE;EACvE,MAAMC,IAAI,GAAG,IAAIvB,IAAI,CAACmB,EAAE,CAAC;IACvBK,QAAQ,GAAG;MACTC,SAAS,EAAE,KAAK;MAChBjC,IAAI,EAAE,SAAS;MACfG,KAAK,EAAE,SAAS;MAChBO,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;EAEH,IAAIkB,QAAQ,EAAE;IACZE,QAAQ,CAACF,QAAQ,GAAGA,QAAQ;EAC9B;EAEA,MAAMI,QAAQ,GAAG;IAAEC,YAAY,EAAEP,YAAY;IAAE,GAAGI;EAAS,CAAC;EAE5D,MAAMI,MAAM,GAAG,IAAInG,IAAI,CAACoG,cAAc,CAACR,MAAM,EAAEK,QAAQ,CAAC,CACrDI,aAAa,CAACP,IAAI,CAAC,CACnBQ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,CAACC,WAAW,CAAC,CAAC,KAAK,cAAc,CAAC;EACvD,OAAON,MAAM,GAAGA,MAAM,CAACO,KAAK,GAAG,IAAI;AACrC;;AAEA;AACA,OAAO,SAASC,YAAYA,CAACC,UAAU,EAAEC,YAAY,EAAE;EACrD,IAAIC,OAAO,GAAG9D,QAAQ,CAAC4D,UAAU,EAAE,EAAE,CAAC;;EAEtC;EACA,IAAIG,MAAM,CAACC,KAAK,CAACF,OAAO,CAAC,EAAE;IACzBA,OAAO,GAAG,CAAC;EACb;EAEA,MAAMG,MAAM,GAAGjE,QAAQ,CAAC6D,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;IAC5CK,YAAY,GAAGJ,OAAO,GAAG,CAAC,IAAInH,MAAM,CAACwH,EAAE,CAACL,OAAO,EAAE,CAAC,CAAC,CAAC,GAAG,CAACG,MAAM,GAAGA,MAAM;EACzE,OAAOH,OAAO,GAAG,EAAE,GAAGI,YAAY;AACpC;;AAEA;;AAEA,OAAO,SAASE,QAAQA,CAACV,KAAK,EAAE;EAC9B,MAAMW,YAAY,GAAGN,MAAM,CAACL,KAAK,CAAC;EAClC,IAAI,OAAOA,KAAK,KAAK,SAAS,IAAIA,KAAK,KAAK,EAAE,IAAIK,MAAM,CAACC,KAAK,CAACK,YAAY,CAAC,EAC1E,MAAM,IAAIpI,oBAAoB,CAAC,sBAAsByH,KAAK,EAAE,CAAC;EAC/D,OAAOW,YAAY;AACrB;AAEA,OAAO,SAASC,eAAeA,CAAClG,GAAG,EAAEmG,UAAU,EAAE;EAC/C,MAAMC,UAAU,GAAG,CAAC,CAAC;EACrB,KAAK,MAAMC,CAAC,IAAIrG,GAAG,EAAE;IACnB,IAAII,cAAc,CAACJ,GAAG,EAAEqG,CAAC,CAAC,EAAE;MAC1B,MAAMxF,CAAC,GAAGb,GAAG,CAACqG,CAAC,CAAC;MAChB,IAAIxF,CAAC,KAAKnB,SAAS,IAAImB,CAAC,KAAK,IAAI,EAAE;MACnCuF,UAAU,CAACD,UAAU,CAACE,CAAC,CAAC,CAAC,GAAGL,QAAQ,CAACnF,CAAC,CAAC;IACzC;EACF;EACA,OAAOuF,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC3C,MAAMC,KAAK,GAAGrF,IAAI,CAACoB,KAAK,CAACpB,IAAI,CAACsF,GAAG,CAACH,MAAM,GAAG,EAAE,CAAC,CAAC;IAC7CI,OAAO,GAAGvF,IAAI,CAACoB,KAAK,CAACpB,IAAI,CAACsF,GAAG,CAACH,MAAM,GAAG,EAAE,CAAC,CAAC;IAC3CK,IAAI,GAAGL,MAAM,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;EAEhC,QAAQC,MAAM;IACZ,KAAK,OAAO;MACV,OAAO,GAAGI,IAAI,GAAGtF,QAAQ,CAACmF,KAAK,EAAE,CAAC,CAAC,IAAInF,QAAQ,CAACqF,OAAO,EAAE,CAAC,CAAC,EAAE;IAC/D,KAAK,QAAQ;MACX,OAAO,GAAGC,IAAI,GAAGH,KAAK,GAAGE,OAAO,GAAG,CAAC,GAAG,IAAIA,OAAO,EAAE,GAAG,EAAE,EAAE;IAC7D,KAAK,QAAQ;MACX,OAAO,GAAGC,IAAI,GAAGtF,QAAQ,CAACmF,KAAK,EAAE,CAAC,CAAC,GAAGnF,QAAQ,CAACqF,OAAO,EAAE,CAAC,CAAC,EAAE;IAC9D;MACE,MAAM,IAAIE,UAAU,CAAC,gBAAgBL,MAAM,sCAAsC,CAAC;EACtF;AACF;AAEA,OAAO,SAASM,UAAUA,CAAC9G,GAAG,EAAE;EAC9B,OAAOD,IAAI,CAACC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}