{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Voicemail = createLucideIcon(\"Voicemail\", [[\"circle\", {\n  cx: \"6\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"1ehtga\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"12\",\n  r: \"4\",\n  key: \"4vafl8\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"18\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"pmt8us\"\n}]]);\nexport { Voicemail as default };", "map": {"version": 3, "names": ["Voicemail", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/lucide-react/src/icons/voicemail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Voicemail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTIiIHI9IjQiIC8+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSIxMiIgcj0iNCIgLz4KICA8bGluZSB4MT0iNiIgeDI9IjE4IiB5MT0iMTYiIHkyPSIxNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/voicemail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Voicemail = createLucideIcon('Voicemail', [\n  ['circle', { cx: '6', cy: '12', r: '4', key: '1ehtga' }],\n  ['circle', { cx: '18', cy: '12', r: '4', key: '4vafl8' }],\n  ['line', { x1: '6', x2: '18', y1: '16', y2: '16', key: 'pmt8us' }],\n]);\n\nexport default Voicemail;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}