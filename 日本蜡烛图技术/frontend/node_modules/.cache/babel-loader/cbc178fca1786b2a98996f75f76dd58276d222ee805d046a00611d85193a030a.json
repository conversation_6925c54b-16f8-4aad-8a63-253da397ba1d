{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calcOffset, calculateColor } from \"../util\";\nimport { useEvent } from 'rc-util';\nimport Handler from \"./Handler\";\nimport Palette from \"./Palette\";\nimport Transform from \"./Transform\";\nvar Picker = function Picker(_ref) {\n  var color = _ref.color,\n    onChange = _ref.onChange,\n    prefixCls = _ref.prefixCls,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled;\n  var pickerRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var onDragChange = useEvent(function (offsetValue) {\n    var calcColor = calculateColor({\n      offset: offsetValue,\n      targetRef: transformRef,\n      containerRef: pickerRef,\n      color: color\n    });\n    colorRef.current = calcColor;\n    onChange(calcColor);\n  });\n  var _useColorDrag = useColorDrag({\n      color: color,\n      containerRef: pickerRef,\n      targetRef: transformRef,\n      calculate: function calculate() {\n        return calcOffset(color);\n      },\n      onDragChange: onDragChange,\n      onDragChangeComplete: function onDragChangeComplete() {\n        return onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current);\n      },\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: pickerRef,\n    className: \"\".concat(prefixCls, \"-select\"),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    x: offset.x,\n    y: offset.y,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    color: color.toRgbString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-saturation\"),\n    style: {\n      backgroundColor: \"hsl(\".concat(color.toHsb().h, \",100%, 50%)\"),\n      backgroundImage: 'linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))'\n    }\n  })));\n};\nexport default Picker;", "map": {"version": 3, "names": ["_slicedToArray", "React", "useRef", "useColorDrag", "calcOffset", "calculateColor", "useEvent", "Handler", "Palette", "Transform", "Picker", "_ref", "color", "onChange", "prefixCls", "onChangeComplete", "disabled", "pickerRef", "transformRef", "colorRef", "onDragChange", "offsetValue", "calcColor", "offset", "targetRef", "containerRef", "current", "_useColorDrag", "calculate", "onDragChangeComplete", "disabledDrag", "_useColorDrag2", "dragStartHandle", "createElement", "ref", "className", "concat", "onMouseDown", "onTouchStart", "x", "y", "toRgbString", "style", "backgroundColor", "toHsb", "h", "backgroundImage"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/@rc-component/color-picker/es/components/Picker.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport { calcOffset, calculateColor } from \"../util\";\nimport { useEvent } from 'rc-util';\nimport Handler from \"./Handler\";\nimport Palette from \"./Palette\";\nimport Transform from \"./Transform\";\nvar Picker = function Picker(_ref) {\n  var color = _ref.color,\n    onChange = _ref.onChange,\n    prefixCls = _ref.prefixCls,\n    onChangeComplete = _ref.onChangeComplete,\n    disabled = _ref.disabled;\n  var pickerRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var onDragChange = useEvent(function (offsetValue) {\n    var calcColor = calculateColor({\n      offset: offsetValue,\n      targetRef: transformRef,\n      containerRef: pickerRef,\n      color: color\n    });\n    colorRef.current = calcColor;\n    onChange(calcColor);\n  });\n  var _useColorDrag = useColorDrag({\n      color: color,\n      containerRef: pickerRef,\n      targetRef: transformRef,\n      calculate: function calculate() {\n        return calcOffset(color);\n      },\n      onDragChange: onDragChange,\n      onDragChangeComplete: function onDragChangeComplete() {\n        return onChangeComplete === null || onChangeComplete === void 0 ? void 0 : onChangeComplete(colorRef.current);\n      },\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: pickerRef,\n    className: \"\".concat(prefixCls, \"-select\"),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    x: offset.x,\n    y: offset.y,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    color: color.toRgbString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-saturation\"),\n    style: {\n      backgroundColor: \"hsl(\".concat(color.toHsb().h, \",100%, 50%)\"),\n      backgroundImage: 'linear-gradient(0deg, #000, transparent),linear-gradient(90deg, #fff, hsla(0, 0%, 100%, 0))'\n    }\n  })));\n};\nexport default Picker;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,UAAU,EAAEC,cAAc,QAAQ,SAAS;AACpD,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAE;EACjC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,SAAS,GAAGH,IAAI,CAACG,SAAS;IAC1BC,gBAAgB,GAAGJ,IAAI,CAACI,gBAAgB;IACxCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;EAC1B,IAAIC,SAAS,GAAGf,MAAM,CAAC,CAAC;EACxB,IAAIgB,YAAY,GAAGhB,MAAM,CAAC,CAAC;EAC3B,IAAIiB,QAAQ,GAAGjB,MAAM,CAACU,KAAK,CAAC;EAC5B,IAAIQ,YAAY,GAAGd,QAAQ,CAAC,UAAUe,WAAW,EAAE;IACjD,IAAIC,SAAS,GAAGjB,cAAc,CAAC;MAC7BkB,MAAM,EAAEF,WAAW;MACnBG,SAAS,EAAEN,YAAY;MACvBO,YAAY,EAAER,SAAS;MACvBL,KAAK,EAAEA;IACT,CAAC,CAAC;IACFO,QAAQ,CAACO,OAAO,GAAGJ,SAAS;IAC5BT,QAAQ,CAACS,SAAS,CAAC;EACrB,CAAC,CAAC;EACF,IAAIK,aAAa,GAAGxB,YAAY,CAAC;MAC7BS,KAAK,EAAEA,KAAK;MACZa,YAAY,EAAER,SAAS;MACvBO,SAAS,EAAEN,YAAY;MACvBU,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;QAC9B,OAAOxB,UAAU,CAACQ,KAAK,CAAC;MAC1B,CAAC;MACDQ,YAAY,EAAEA,YAAY;MAC1BS,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;QACpD,OAAOd,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACI,QAAQ,CAACO,OAAO,CAAC;MAC/G,CAAC;MACDI,YAAY,EAAEd;IAChB,CAAC,CAAC;IACFe,cAAc,GAAG/B,cAAc,CAAC2B,aAAa,EAAE,CAAC,CAAC;IACjDJ,MAAM,GAAGQ,cAAc,CAAC,CAAC,CAAC;IAC1BC,eAAe,GAAGD,cAAc,CAAC,CAAC,CAAC;EACrC,OAAO,aAAa9B,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7CC,GAAG,EAAEjB,SAAS;IACdkB,SAAS,EAAE,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,SAAS,CAAC;IAC1CuB,WAAW,EAAEL,eAAe;IAC5BM,YAAY,EAAEN;EAChB,CAAC,EAAE,aAAa/B,KAAK,CAACgC,aAAa,CAACzB,OAAO,EAAE;IAC3CM,SAAS,EAAEA;EACb,CAAC,EAAE,aAAab,KAAK,CAACgC,aAAa,CAACxB,SAAS,EAAE;IAC7C8B,CAAC,EAAEhB,MAAM,CAACgB,CAAC;IACXC,CAAC,EAAEjB,MAAM,CAACiB,CAAC;IACXN,GAAG,EAAEhB;EACP,CAAC,EAAE,aAAajB,KAAK,CAACgC,aAAa,CAAC1B,OAAO,EAAE;IAC3CK,KAAK,EAAEA,KAAK,CAAC6B,WAAW,CAAC,CAAC;IAC1B3B,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,EAAE,aAAab,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC3CE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACtB,SAAS,EAAE,aAAa,CAAC;IAC9C4B,KAAK,EAAE;MACLC,eAAe,EAAE,MAAM,CAACP,MAAM,CAACxB,KAAK,CAACgC,KAAK,CAAC,CAAC,CAACC,CAAC,EAAE,aAAa,CAAC;MAC9DC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAepC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}