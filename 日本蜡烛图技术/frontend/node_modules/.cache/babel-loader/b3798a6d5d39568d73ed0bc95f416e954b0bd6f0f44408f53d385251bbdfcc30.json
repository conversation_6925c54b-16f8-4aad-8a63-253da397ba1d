{"ast": null, "code": "import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport { asNumber, hasOwnProperty, isNumber, isUndefined, normalizeObject, roundTo } from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000\n    },\n    hours: {\n      minutes: 60,\n      seconds: 60 * 60,\n      milliseconds: 60 * 60 * 1000\n    },\n    minutes: {\n      seconds: 60,\n      milliseconds: 60 * 1000\n    },\n    seconds: {\n      milliseconds: 1000\n    }\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000\n    },\n    ...lowOrderMatrix\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: daysInYearAccurate * 24 / 4,\n      minutes: daysInYearAccurate * 24 * 60 / 4,\n      seconds: daysInYearAccurate * 24 * 60 * 60 / 4,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000 / 4\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000\n    },\n    ...lowOrderMatrix\n  };\n\n// units ordered by size\nconst orderedUnits = [\"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\"];\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : {\n      ...dur.values,\n      ...(alts.values || {})\n    },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix\n  };\n  return new Duration(conf);\n}\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({\n      milliseconds: count\n    }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(`Duration.fromObject: argument expected to be an object, got ${obj === null ? \"null\" : typeof obj}`);\n    }\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(`Unknown duration argument ${durationLike} of type ${typeof durationLike}`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({\n        invalid\n      });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\"\n    }[unit ? unit.toLowerCase() : unit];\n    if (!normalized) throw new InvalidUnitError(unit);\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return o && o.isLuxonDuration || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false\n    };\n    return this.isValid ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt) : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ days: 1, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 day, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 day, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 day, 5 hr, 6 min'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n    const l = orderedUnits.map(unit => {\n      const val = this.values[unit];\n      if (isUndefined(val)) {\n        return null;\n      }\n      return this.loc.numberFormatter({\n        style: \"unit\",\n        unitDisplay: \"long\",\n        ...opts,\n        unit: unit.slice(0, -1)\n      }).format(val);\n    }).filter(n => n);\n    return this.loc.listFormatter({\n      type: \"conjunction\",\n      style: opts.listStyle || \"narrow\",\n      ...opts\n    }).format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return {\n      ...this.values\n    };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0) s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false\n    };\n    const dateTime = DateTime.fromMillis(millis, {\n      zone: \"UTC\"\n    });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n    return clone(this, {\n      values: result\n    }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, {\n      values: result\n    }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n    const mixed = {\n      ...this.values,\n      ...normalizeObject(values, Duration.normalizeUnit)\n    };\n    return clone(this, {\n      values: mixed\n    });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({\n    locale,\n    numberingSystem,\n    conversionAccuracy,\n    matrix\n  } = {}) {\n    const loc = this.loc.clone({\n      locale,\n      numberingSystem\n    });\n    const opts = {\n      loc,\n      matrix,\n      conversionAccuracy\n    };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, {\n      values: vals\n    }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, {\n      values: vals\n    }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n    if (units.length === 0) {\n      return this;\n    }\n    units = units.map(u => Duration.normalizeUnit(u));\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] += key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n    normalizeValues(this.matrix, built);\n    return clone(this, {\n      values: built\n    }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\");\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, {\n      values: negated\n    }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}", "map": {"version": 3, "names": ["InvalidArgumentError", "InvalidDurationError", "InvalidUnitError", "<PERSON><PERSON><PERSON>", "Invalid", "Locale", "parseISODuration", "parseISOTimeOnly", "asNumber", "hasOwnProperty", "isNumber", "isUndefined", "normalizeObject", "roundTo", "Settings", "DateTime", "INVALID", "lowOrderMatrix", "weeks", "days", "hours", "minutes", "seconds", "milliseconds", "casualMatrix", "years", "quarters", "months", "daysInYearAccurate", "daysInMonthAccurate", "accurateMatrix", "orderedUnits", "reverseUnits", "slice", "reverse", "clone", "dur", "alts", "clear", "conf", "values", "loc", "conversionAccuracy", "matrix", "Duration", "durationTo<PERSON>illis", "vals", "sum", "unit", "normalizeValues", "factor", "reduceRight", "previous", "current", "previousVal", "conv", "rollUp", "Math", "floor", "reduce", "fraction", "removeZeroes", "newVals", "key", "value", "Object", "entries", "constructor", "config", "accurate", "create", "invalid", "isLuxonDuration", "fromMillis", "count", "opts", "fromObject", "obj", "normalizeUnit", "fromDurationLike", "durationLike", "isDuration", "fromISO", "text", "parsed", "fromISOTime", "reason", "explanation", "throwOnInvalid", "normalized", "year", "quarter", "month", "week", "day", "hour", "minute", "second", "millisecond", "toLowerCase", "o", "locale", "<PERSON><PERSON><PERSON><PERSON>", "numberingSystem", "toFormat", "fmt", "fmtOpts", "round", "formatDurationFromString", "toHuman", "l", "map", "val", "numberF<PERSON>atter", "style", "unitDisplay", "format", "filter", "n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "listStyle", "toObject", "toISO", "s", "toISOTime", "millis", "<PERSON><PERSON><PERSON><PERSON>", "suppressMilliseconds", "suppressSeconds", "includePrefix", "includeOffset", "dateTime", "zone", "toJSON", "toString", "Symbol", "for", "JSON", "stringify", "invalidReason", "NaN", "valueOf", "plus", "duration", "result", "k", "get", "minus", "negate", "mapUnits", "fn", "keys", "set", "mixed", "reconfigure", "as", "shiftTo", "normalize", "rescale", "shiftToAll", "units", "length", "u", "built", "accumulated", "lastUnit", "indexOf", "own", "ak", "i", "trunc", "negated", "invalidExplanation", "equals", "other", "eq", "v1", "v2", "undefined"], "sources": ["/Users/<USER>/Desktop/日本蜡烛图技术/frontend/node_modules/luxon/src/duration.js"], "sourcesContent": ["import { InvalidArgumentError, InvalidDurationError, InvalidUnitError } from \"./errors.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Locale from \"./impl/locale.js\";\nimport { parseISODuration, parseISOTimeOnly } from \"./impl/regexParser.js\";\nimport {\n  asNumber,\n  hasOwnProperty,\n  isNumber,\n  isUndefined,\n  normalizeObject,\n  roundTo,\n} from \"./impl/util.js\";\nimport Settings from \"./settings.js\";\nimport DateTime from \"./datetime.js\";\n\nconst INVALID = \"Invalid Duration\";\n\n// unit conversion constants\nexport const lowOrderMatrix = {\n    weeks: {\n      days: 7,\n      hours: 7 * 24,\n      minutes: 7 * 24 * 60,\n      seconds: 7 * 24 * 60 * 60,\n      milliseconds: 7 * 24 * 60 * 60 * 1000,\n    },\n    days: {\n      hours: 24,\n      minutes: 24 * 60,\n      seconds: 24 * 60 * 60,\n      milliseconds: 24 * 60 * 60 * 1000,\n    },\n    hours: { minutes: 60, seconds: 60 * 60, milliseconds: 60 * 60 * 1000 },\n    minutes: { seconds: 60, milliseconds: 60 * 1000 },\n    seconds: { milliseconds: 1000 },\n  },\n  casualMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: 52,\n      days: 365,\n      hours: 365 * 24,\n      minutes: 365 * 24 * 60,\n      seconds: 365 * 24 * 60 * 60,\n      milliseconds: 365 * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: 13,\n      days: 91,\n      hours: 91 * 24,\n      minutes: 91 * 24 * 60,\n      seconds: 91 * 24 * 60 * 60,\n      milliseconds: 91 * 24 * 60 * 60 * 1000,\n    },\n    months: {\n      weeks: 4,\n      days: 30,\n      hours: 30 * 24,\n      minutes: 30 * 24 * 60,\n      seconds: 30 * 24 * 60 * 60,\n      milliseconds: 30 * 24 * 60 * 60 * 1000,\n    },\n\n    ...lowOrderMatrix,\n  },\n  daysInYearAccurate = 146097.0 / 400,\n  daysInMonthAccurate = 146097.0 / 4800,\n  accurateMatrix = {\n    years: {\n      quarters: 4,\n      months: 12,\n      weeks: daysInYearAccurate / 7,\n      days: daysInYearAccurate,\n      hours: daysInYearAccurate * 24,\n      minutes: daysInYearAccurate * 24 * 60,\n      seconds: daysInYearAccurate * 24 * 60 * 60,\n      milliseconds: daysInYearAccurate * 24 * 60 * 60 * 1000,\n    },\n    quarters: {\n      months: 3,\n      weeks: daysInYearAccurate / 28,\n      days: daysInYearAccurate / 4,\n      hours: (daysInYearAccurate * 24) / 4,\n      minutes: (daysInYearAccurate * 24 * 60) / 4,\n      seconds: (daysInYearAccurate * 24 * 60 * 60) / 4,\n      milliseconds: (daysInYearAccurate * 24 * 60 * 60 * 1000) / 4,\n    },\n    months: {\n      weeks: daysInMonthAccurate / 7,\n      days: daysInMonthAccurate,\n      hours: daysInMonthAccurate * 24,\n      minutes: daysInMonthAccurate * 24 * 60,\n      seconds: daysInMonthAccurate * 24 * 60 * 60,\n      milliseconds: daysInMonthAccurate * 24 * 60 * 60 * 1000,\n    },\n    ...lowOrderMatrix,\n  };\n\n// units ordered by size\nconst orderedUnits = [\n  \"years\",\n  \"quarters\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n  \"milliseconds\",\n];\n\nconst reverseUnits = orderedUnits.slice(0).reverse();\n\n// clone really means \"create another instance just like this one, but with these changes\"\nfunction clone(dur, alts, clear = false) {\n  // deep merge for vals\n  const conf = {\n    values: clear ? alts.values : { ...dur.values, ...(alts.values || {}) },\n    loc: dur.loc.clone(alts.loc),\n    conversionAccuracy: alts.conversionAccuracy || dur.conversionAccuracy,\n    matrix: alts.matrix || dur.matrix,\n  };\n  return new Duration(conf);\n}\n\nfunction durationToMillis(matrix, vals) {\n  let sum = vals.milliseconds ?? 0;\n  for (const unit of reverseUnits.slice(1)) {\n    if (vals[unit]) {\n      sum += vals[unit] * matrix[unit][\"milliseconds\"];\n    }\n  }\n  return sum;\n}\n\n// NB: mutates parameters\nfunction normalizeValues(matrix, vals) {\n  // the logic below assumes the overall value of the duration is positive\n  // if this is not the case, factor is used to make it so\n  const factor = durationToMillis(matrix, vals) < 0 ? -1 : 1;\n\n  orderedUnits.reduceRight((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const previousVal = vals[previous] * factor;\n        const conv = matrix[current][previous];\n\n        // if (previousVal < 0):\n        // lower order unit is negative (e.g. { years: 2, days: -2 })\n        // normalize this by reducing the higher order unit by the appropriate amount\n        // and increasing the lower order unit\n        // this can never make the higher order unit negative, because this function only operates\n        // on positive durations, so the amount of time represented by the lower order unit cannot\n        // be larger than the higher order unit\n        // else:\n        // lower order unit is positive (e.g. { years: 2, days: 450 } or { years: -2, days: 450 })\n        // in this case we attempt to convert as much as possible from the lower order unit into\n        // the higher order one\n        //\n        // Math.floor takes care of both of these cases, rounding away from 0\n        // if previousVal < 0 it makes the absolute value larger\n        // if previousVal >= it makes the absolute value smaller\n        const rollUp = Math.floor(previousVal / conv);\n        vals[current] += rollUp * factor;\n        vals[previous] -= rollUp * conv * factor;\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n\n  // try to convert any decimals into smaller units if possible\n  // for example for { years: 2.5, days: 0, seconds: 0 } we want to get { years: 2, days: 182, hours: 12 }\n  orderedUnits.reduce((previous, current) => {\n    if (!isUndefined(vals[current])) {\n      if (previous) {\n        const fraction = vals[previous] % 1;\n        vals[previous] -= fraction;\n        vals[current] += fraction * matrix[previous][current];\n      }\n      return current;\n    } else {\n      return previous;\n    }\n  }, null);\n}\n\n// Remove all properties with a value of 0 from an object\nfunction removeZeroes(vals) {\n  const newVals = {};\n  for (const [key, value] of Object.entries(vals)) {\n    if (value !== 0) {\n      newVals[key] = value;\n    }\n  }\n  return newVals;\n}\n\n/**\n * A Duration object represents a period of time, like \"2 months\" or \"1 day, 1 hour\". Conceptually, it's just a map of units to their quantities, accompanied by some additional configuration and methods for creating, parsing, interrogating, transforming, and formatting them. They can be used on their own or in conjunction with other Luxon types; for example, you can use {@link DateTime#plus} to add a Duration object to a DateTime, producing another DateTime.\n *\n * Here is a brief overview of commonly used methods and getters in Duration:\n *\n * * **Creation** To create a Duration, use {@link Duration.fromMillis}, {@link Duration.fromObject}, or {@link Duration.fromISO}.\n * * **Unit values** See the {@link Duration#years}, {@link Duration#months}, {@link Duration#weeks}, {@link Duration#days}, {@link Duration#hours}, {@link Duration#minutes}, {@link Duration#seconds}, {@link Duration#milliseconds} accessors.\n * * **Configuration** See  {@link Duration#locale} and {@link Duration#numberingSystem} accessors.\n * * **Transformation** To create new Durations out of old ones use {@link Duration#plus}, {@link Duration#minus}, {@link Duration#normalize}, {@link Duration#set}, {@link Duration#reconfigure}, {@link Duration#shiftTo}, and {@link Duration#negate}.\n * * **Output** To convert the Duration into other representations, see {@link Duration#as}, {@link Duration#toISO}, {@link Duration#toFormat}, and {@link Duration#toJSON}\n *\n * There's are more methods documented below. In addition, for more information on subtler topics like internationalization and validity, see the external documentation.\n */\nexport default class Duration {\n  /**\n   * @private\n   */\n  constructor(config) {\n    const accurate = config.conversionAccuracy === \"longterm\" || false;\n    let matrix = accurate ? accurateMatrix : casualMatrix;\n\n    if (config.matrix) {\n      matrix = config.matrix;\n    }\n\n    /**\n     * @access private\n     */\n    this.values = config.values;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.conversionAccuracy = accurate ? \"longterm\" : \"casual\";\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.matrix = matrix;\n    /**\n     * @access private\n     */\n    this.isLuxonDuration = true;\n  }\n\n  /**\n   * Create Duration from a number of milliseconds.\n   * @param {number} count of milliseconds\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  static fromMillis(count, opts) {\n    return Duration.fromObject({ milliseconds: count }, opts);\n  }\n\n  /**\n   * Create a Duration from a JavaScript object with keys like 'years' and 'hours'.\n   * If this object is empty then a zero milliseconds duration is returned.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.years\n   * @param {number} obj.quarters\n   * @param {number} obj.months\n   * @param {number} obj.weeks\n   * @param {number} obj.days\n   * @param {number} obj.hours\n   * @param {number} obj.minutes\n   * @param {number} obj.seconds\n   * @param {number} obj.milliseconds\n   * @param {Object} [opts=[]] - options for creating this Duration\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the custom conversion system to use\n   * @return {Duration}\n   */\n  static fromObject(obj, opts = {}) {\n    if (obj == null || typeof obj !== \"object\") {\n      throw new InvalidArgumentError(\n        `Duration.fromObject: argument expected to be an object, got ${\n          obj === null ? \"null\" : typeof obj\n        }`\n      );\n    }\n\n    return new Duration({\n      values: normalizeObject(obj, Duration.normalizeUnit),\n      loc: Locale.fromObject(opts),\n      conversionAccuracy: opts.conversionAccuracy,\n      matrix: opts.matrix,\n    });\n  }\n\n  /**\n   * Create a Duration from DurationLike.\n   *\n   * @param {Object | number | Duration} durationLike\n   * One of:\n   * - object with keys like 'years' and 'hours'.\n   * - number representing milliseconds\n   * - Duration instance\n   * @return {Duration}\n   */\n  static fromDurationLike(durationLike) {\n    if (isNumber(durationLike)) {\n      return Duration.fromMillis(durationLike);\n    } else if (Duration.isDuration(durationLike)) {\n      return durationLike;\n    } else if (typeof durationLike === \"object\") {\n      return Duration.fromObject(durationLike);\n    } else {\n      throw new InvalidArgumentError(\n        `Unknown duration argument ${durationLike} of type ${typeof durationLike}`\n      );\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 duration string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the preset conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromISO('P3Y6M1W4DT12H30M5S').toObject() //=> { years: 3, months: 6, weeks: 1, days: 4, hours: 12, minutes: 30, seconds: 5 }\n   * @example Duration.fromISO('PT23H').toObject() //=> { hours: 23 }\n   * @example Duration.fromISO('P5Y3M').toObject() //=> { years: 5, months: 3 }\n   * @return {Duration}\n   */\n  static fromISO(text, opts) {\n    const [parsed] = parseISODuration(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create a Duration from an ISO 8601 time string.\n   * @param {string} text - text to parse\n   * @param {Object} opts - options for parsing\n   * @param {string} [opts.locale='en-US'] - the locale to use\n   * @param {string} opts.numberingSystem - the numbering system to use\n   * @param {string} [opts.conversionAccuracy='casual'] - the preset conversion system to use\n   * @param {string} [opts.matrix=Object] - the conversion system to use\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @example Duration.fromISOTime('11:22:33.444').toObject() //=> { hours: 11, minutes: 22, seconds: 33, milliseconds: 444 }\n   * @example Duration.fromISOTime('11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T11:00').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @example Duration.fromISOTime('T1100').toObject() //=> { hours: 11, minutes: 0, seconds: 0 }\n   * @return {Duration}\n   */\n  static fromISOTime(text, opts) {\n    const [parsed] = parseISOTimeOnly(text);\n    if (parsed) {\n      return Duration.fromObject(parsed, opts);\n    } else {\n      return Duration.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n    }\n  }\n\n  /**\n   * Create an invalid Duration.\n   * @param {string} reason - simple string of why this datetime is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Duration}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Duration is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDurationError(invalid);\n    } else {\n      return new Duration({ invalid });\n    }\n  }\n\n  /**\n   * @private\n   */\n  static normalizeUnit(unit) {\n    const normalized = {\n      year: \"years\",\n      years: \"years\",\n      quarter: \"quarters\",\n      quarters: \"quarters\",\n      month: \"months\",\n      months: \"months\",\n      week: \"weeks\",\n      weeks: \"weeks\",\n      day: \"days\",\n      days: \"days\",\n      hour: \"hours\",\n      hours: \"hours\",\n      minute: \"minutes\",\n      minutes: \"minutes\",\n      second: \"seconds\",\n      seconds: \"seconds\",\n      millisecond: \"milliseconds\",\n      milliseconds: \"milliseconds\",\n    }[unit ? unit.toLowerCase() : unit];\n\n    if (!normalized) throw new InvalidUnitError(unit);\n\n    return normalized;\n  }\n\n  /**\n   * Check if an object is a Duration. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDuration(o) {\n    return (o && o.isLuxonDuration) || false;\n  }\n\n  /**\n   * Get  the locale of a Duration, such 'en-GB'\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a Duration, such 'beng'. The numbering system is used when formatting the Duration\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Returns a string representation of this Duration formatted according to the specified format string. You may use these tokens:\n   * * `S` for milliseconds\n   * * `s` for seconds\n   * * `m` for minutes\n   * * `h` for hours\n   * * `d` for days\n   * * `w` for weeks\n   * * `M` for months\n   * * `y` for years\n   * Notes:\n   * * Add padding by repeating the token, e.g. \"yy\" pads the years to two digits, \"hhhh\" pads the hours out to four digits\n   * * Tokens can be escaped by wrapping with single quotes.\n   * * The duration will be converted to the set of units in the format string using {@link Duration#shiftTo} and the Durations's conversion accuracy setting.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - options\n   * @param {boolean} [opts.floor=true] - floor numerical values\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"y d s\") //=> \"1 6 2\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"yy dd sss\") //=> \"01 06 002\"\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toFormat(\"M S\") //=> \"12 518402000\"\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    // reverse-compat since 1.2; we always round down now, never up, and we do it by default\n    const fmtOpts = {\n      ...opts,\n      floor: opts.round !== false && opts.floor !== false,\n    };\n    return this.isValid\n      ? Formatter.create(this.loc, fmtOpts).formatDurationFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a string representation of a Duration with all units included.\n   * To modify its behavior, use `listStyle` and any Intl.NumberFormat option, though `unitDisplay` is especially relevant.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat#options\n   * @param {Object} opts - Formatting options. Accepts the same keys as the options parameter of the native `Intl.NumberFormat` constructor, as well as `listStyle`.\n   * @param {string} [opts.listStyle='narrow'] - How to format the merged list. Corresponds to the `style` property of the options parameter of the native `Intl.ListFormat` constructor.\n   * @example\n   * ```js\n   * var dur = Duration.fromObject({ days: 1, hours: 5, minutes: 6 })\n   * dur.toHuman() //=> '1 day, 5 hours, 6 minutes'\n   * dur.toHuman({ listStyle: \"long\" }) //=> '1 day, 5 hours, and 6 minutes'\n   * dur.toHuman({ unitDisplay: \"short\" }) //=> '1 day, 5 hr, 6 min'\n   * ```\n   */\n  toHuman(opts = {}) {\n    if (!this.isValid) return INVALID;\n\n    const l = orderedUnits\n      .map((unit) => {\n        const val = this.values[unit];\n        if (isUndefined(val)) {\n          return null;\n        }\n        return this.loc\n          .numberFormatter({ style: \"unit\", unitDisplay: \"long\", ...opts, unit: unit.slice(0, -1) })\n          .format(val);\n      })\n      .filter((n) => n);\n\n    return this.loc\n      .listFormatter({ type: \"conjunction\", style: opts.listStyle || \"narrow\", ...opts })\n      .format(l);\n  }\n\n  /**\n   * Returns a JavaScript object with this Duration's values.\n   * @example Duration.fromObject({ years: 1, days: 6, seconds: 2 }).toObject() //=> { years: 1, days: 6, seconds: 2 }\n   * @return {Object}\n   */\n  toObject() {\n    if (!this.isValid) return {};\n    return { ...this.values };\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Durations\n   * @example Duration.fromObject({ years: 3, seconds: 45 }).toISO() //=> 'P3YT45S'\n   * @example Duration.fromObject({ months: 4, seconds: 45 }).toISO() //=> 'P4MT45S'\n   * @example Duration.fromObject({ months: 5 }).toISO() //=> 'P5M'\n   * @example Duration.fromObject({ minutes: 5 }).toISO() //=> 'PT5M'\n   * @example Duration.fromObject({ milliseconds: 6 }).toISO() //=> 'PT0.006S'\n   * @return {string}\n   */\n  toISO() {\n    // we could use the formatter, but this is an easier way to get the minimum string\n    if (!this.isValid) return null;\n\n    let s = \"P\";\n    if (this.years !== 0) s += this.years + \"Y\";\n    if (this.months !== 0 || this.quarters !== 0) s += this.months + this.quarters * 3 + \"M\";\n    if (this.weeks !== 0) s += this.weeks + \"W\";\n    if (this.days !== 0) s += this.days + \"D\";\n    if (this.hours !== 0 || this.minutes !== 0 || this.seconds !== 0 || this.milliseconds !== 0)\n      s += \"T\";\n    if (this.hours !== 0) s += this.hours + \"H\";\n    if (this.minutes !== 0) s += this.minutes + \"M\";\n    if (this.seconds !== 0 || this.milliseconds !== 0)\n      // this will handle \"floating point madness\" by removing extra decimal places\n      // https://stackoverflow.com/questions/588004/is-floating-point-math-broken\n      s += roundTo(this.seconds + this.milliseconds / 1000, 3) + \"S\";\n    if (s === \"P\") s += \"T0S\";\n    return s;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Duration, formatted as a time of day.\n   * Note that this will return null if the duration is invalid, negative, or equal to or greater than 24 hours.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Times\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example Duration.fromObject({ hours: 11 }).toISOTime() //=> '11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressMilliseconds: true }) //=> '11:00:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ suppressSeconds: true }) //=> '11:00'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ includePrefix: true }) //=> 'T11:00:00.000'\n   * @example Duration.fromObject({ hours: 11 }).toISOTime({ format: 'basic' }) //=> '110000.000'\n   * @return {string}\n   */\n  toISOTime(opts = {}) {\n    if (!this.isValid) return null;\n\n    const millis = this.toMillis();\n    if (millis < 0 || millis >= 86400000) return null;\n\n    opts = {\n      suppressMilliseconds: false,\n      suppressSeconds: false,\n      includePrefix: false,\n      format: \"extended\",\n      ...opts,\n      includeOffset: false,\n    };\n\n    const dateTime = DateTime.fromMillis(millis, { zone: \"UTC\" });\n    return dateTime.toISOTime(opts);\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this Duration appropriate for use in debugging.\n   * @return {string}\n   */\n  toString() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a string representation of this Duration appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Duration { values: ${JSON.stringify(this.values)} }`;\n    } else {\n      return `Duration { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration.\n   * @return {number}\n   */\n  toMillis() {\n    if (!this.isValid) return NaN;\n\n    return durationToMillis(this.matrix, this.values);\n  }\n\n  /**\n   * Returns an milliseconds value of this Duration. Alias of {@link toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Make this Duration longer by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration),\n      result = {};\n\n    for (const k of orderedUnits) {\n      if (hasOwnProperty(dur.values, k) || hasOwnProperty(this.values, k)) {\n        result[k] = dur.get(k) + this.get(k);\n      }\n    }\n\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Make this Duration shorter by the specified amount. Return a newly-constructed Duration.\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @return {Duration}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n\n    const dur = Duration.fromDurationLike(duration);\n    return this.plus(dur.negate());\n  }\n\n  /**\n   * Scale this Duration by the specified amount. Return a newly-constructed Duration.\n   * @param {function} fn - The function to apply to each unit. Arity is 1 or 2: the value of the unit and, optionally, the unit name. Must return a number.\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits(x => x * 2) //=> { hours: 2, minutes: 60 }\n   * @example Duration.fromObject({ hours: 1, minutes: 30 }).mapUnits((x, u) => u === \"hours\" ? x * 2 : x) //=> { hours: 2, minutes: 30 }\n   * @return {Duration}\n   */\n  mapUnits(fn) {\n    if (!this.isValid) return this;\n    const result = {};\n    for (const k of Object.keys(this.values)) {\n      result[k] = asNumber(fn(this.values[k], k));\n    }\n    return clone(this, { values: result }, true);\n  }\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example Duration.fromObject({years: 2, days: 3}).get('years') //=> 2\n   * @example Duration.fromObject({years: 2, days: 3}).get('months') //=> 0\n   * @example Duration.fromObject({years: 2, days: 3}).get('days') //=> 3\n   * @return {number}\n   */\n  get(unit) {\n    return this[Duration.normalizeUnit(unit)];\n  }\n\n  /**\n   * \"Set\" the values of specified units. Return a newly-constructed Duration.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dur.set({ years: 2017 })\n   * @example dur.set({ hours: 8, minutes: 30 })\n   * @return {Duration}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const mixed = { ...this.values, ...normalizeObject(values, Duration.normalizeUnit) };\n    return clone(this, { values: mixed });\n  }\n\n  /**\n   * \"Set\" the locale and/or numberingSystem.  Returns a newly-constructed Duration.\n   * @example dur.reconfigure({ locale: 'en-GB' })\n   * @return {Duration}\n   */\n  reconfigure({ locale, numberingSystem, conversionAccuracy, matrix } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem });\n    const opts = { loc, matrix, conversionAccuracy };\n    return clone(this, opts);\n  }\n\n  /**\n   * Return the length of the duration in the specified unit.\n   * @param {string} unit - a unit such as 'minutes' or 'days'\n   * @example Duration.fromObject({years: 1}).as('days') //=> 365\n   * @example Duration.fromObject({years: 1}).as('months') //=> 12\n   * @example Duration.fromObject({hours: 60}).as('days') //=> 2.5\n   * @return {number}\n   */\n  as(unit) {\n    return this.isValid ? this.shiftTo(unit).get(unit) : NaN;\n  }\n\n  /**\n   * Reduce this Duration to its canonical representation in its current units.\n   * Assuming the overall value of the Duration is positive, this means:\n   * - excessive values for lower-order units are converted to higher-order units (if possible, see first and second example)\n   * - negative lower-order units are converted to higher order units (there must be such a higher order unit, otherwise\n   *   the overall value would be negative, see third example)\n   * - fractional values for higher-order units are converted to lower-order units (if possible, see fourth example)\n   *\n   * If the overall value is negative, the result of this method is equivalent to `this.negate().normalize().negate()`.\n   * @example Duration.fromObject({ years: 2, days: 5000 }).normalize().toObject() //=> { years: 15, days: 255 }\n   * @example Duration.fromObject({ days: 5000 }).normalize().toObject() //=> { days: 5000 }\n   * @example Duration.fromObject({ hours: 12, minutes: -45 }).normalize().toObject() //=> { hours: 11, minutes: 15 }\n   * @example Duration.fromObject({ years: 2.5, days: 0, hours: 0 }).normalize().toObject() //=> { years: 2, days: 182, hours: 12 }\n   * @return {Duration}\n   */\n  normalize() {\n    if (!this.isValid) return this;\n    const vals = this.toObject();\n    normalizeValues(this.matrix, vals);\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Rescale units to its largest representation\n   * @example Duration.fromObject({ milliseconds: 90000 }).rescale().toObject() //=> { minutes: 1, seconds: 30 }\n   * @return {Duration}\n   */\n  rescale() {\n    if (!this.isValid) return this;\n    const vals = removeZeroes(this.normalize().shiftToAll().toObject());\n    return clone(this, { values: vals }, true);\n  }\n\n  /**\n   * Convert this Duration into its representation in a different set of units.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).shiftTo('minutes', 'milliseconds').toObject() //=> { minutes: 60, milliseconds: 30000 }\n   * @return {Duration}\n   */\n  shiftTo(...units) {\n    if (!this.isValid) return this;\n\n    if (units.length === 0) {\n      return this;\n    }\n\n    units = units.map((u) => Duration.normalizeUnit(u));\n\n    const built = {},\n      accumulated = {},\n      vals = this.toObject();\n    let lastUnit;\n\n    for (const k of orderedUnits) {\n      if (units.indexOf(k) >= 0) {\n        lastUnit = k;\n\n        let own = 0;\n\n        // anything we haven't boiled down yet should get boiled to this unit\n        for (const ak in accumulated) {\n          own += this.matrix[ak][k] * accumulated[ak];\n          accumulated[ak] = 0;\n        }\n\n        // plus anything that's already in this unit\n        if (isNumber(vals[k])) {\n          own += vals[k];\n        }\n\n        // only keep the integer part for now in the hopes of putting any decimal part\n        // into a smaller unit later\n        const i = Math.trunc(own);\n        built[k] = i;\n        accumulated[k] = (own * 1000 - i * 1000) / 1000;\n\n        // otherwise, keep it in the wings to boil it later\n      } else if (isNumber(vals[k])) {\n        accumulated[k] = vals[k];\n      }\n    }\n\n    // anything leftover becomes the decimal for the last unit\n    // lastUnit must be defined since units is not empty\n    for (const key in accumulated) {\n      if (accumulated[key] !== 0) {\n        built[lastUnit] +=\n          key === lastUnit ? accumulated[key] : accumulated[key] / this.matrix[lastUnit][key];\n      }\n    }\n\n    normalizeValues(this.matrix, built);\n    return clone(this, { values: built }, true);\n  }\n\n  /**\n   * Shift this Duration to all available units.\n   * Same as shiftTo(\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\", \"milliseconds\")\n   * @return {Duration}\n   */\n  shiftToAll() {\n    if (!this.isValid) return this;\n    return this.shiftTo(\n      \"years\",\n      \"months\",\n      \"weeks\",\n      \"days\",\n      \"hours\",\n      \"minutes\",\n      \"seconds\",\n      \"milliseconds\"\n    );\n  }\n\n  /**\n   * Return the negative of this Duration.\n   * @example Duration.fromObject({ hours: 1, seconds: 30 }).negate().toObject() //=> { hours: -1, seconds: -30 }\n   * @return {Duration}\n   */\n  negate() {\n    if (!this.isValid) return this;\n    const negated = {};\n    for (const k of Object.keys(this.values)) {\n      negated[k] = this.values[k] === 0 ? 0 : -this.values[k];\n    }\n    return clone(this, { values: negated }, true);\n  }\n\n  /**\n   * Get the years.\n   * @type {number}\n   */\n  get years() {\n    return this.isValid ? this.values.years || 0 : NaN;\n  }\n\n  /**\n   * Get the quarters.\n   * @type {number}\n   */\n  get quarters() {\n    return this.isValid ? this.values.quarters || 0 : NaN;\n  }\n\n  /**\n   * Get the months.\n   * @type {number}\n   */\n  get months() {\n    return this.isValid ? this.values.months || 0 : NaN;\n  }\n\n  /**\n   * Get the weeks\n   * @type {number}\n   */\n  get weeks() {\n    return this.isValid ? this.values.weeks || 0 : NaN;\n  }\n\n  /**\n   * Get the days.\n   * @type {number}\n   */\n  get days() {\n    return this.isValid ? this.values.days || 0 : NaN;\n  }\n\n  /**\n   * Get the hours.\n   * @type {number}\n   */\n  get hours() {\n    return this.isValid ? this.values.hours || 0 : NaN;\n  }\n\n  /**\n   * Get the minutes.\n   * @type {number}\n   */\n  get minutes() {\n    return this.isValid ? this.values.minutes || 0 : NaN;\n  }\n\n  /**\n   * Get the seconds.\n   * @return {number}\n   */\n  get seconds() {\n    return this.isValid ? this.values.seconds || 0 : NaN;\n  }\n\n  /**\n   * Get the milliseconds.\n   * @return {number}\n   */\n  get milliseconds() {\n    return this.isValid ? this.values.milliseconds || 0 : NaN;\n  }\n\n  /**\n   * Returns whether the Duration is invalid. Invalid durations are returned by diff operations\n   * on invalid DateTimes or Intervals.\n   * @return {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this Duration became invalid, or null if the Duration is valid\n   * @return {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Duration became invalid, or null if the Duration is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Equality check\n   * Two Durations are equal iff they have the same units and the same values for each unit.\n   * @param {Duration} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    if (!this.loc.equals(other.loc)) {\n      return false;\n    }\n\n    function eq(v1, v2) {\n      // Consider 0 and undefined as equal\n      if (v1 === undefined || v1 === 0) return v2 === undefined || v2 === 0;\n      return v1 === v2;\n    }\n\n    for (const u of orderedUnits) {\n      if (!eq(this.values[u], other.values[u])) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,aAAa;AAC1F,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,uBAAuB;AAC1E,SACEC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,OAAO,QACF,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,MAAMC,OAAO,GAAG,kBAAkB;;AAElC;AACA,OAAO,MAAMC,cAAc,GAAG;IAC1BC,KAAK,EAAE;MACLC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC,GAAG,EAAE;MACbC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE;MACpBC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MACzBC,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,CAAC;IACDJ,IAAI,EAAE;MACJC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE,GAAG,EAAE;MAChBC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;MACrBC,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,CAAC;IACDH,KAAK,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE,GAAG,EAAE;MAAEC,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG;IAAK,CAAC;IACtEF,OAAO,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,YAAY,EAAE,EAAE,GAAG;IAAK,CAAC;IACjDD,OAAO,EAAE;MAAEC,YAAY,EAAE;IAAK;EAChC,CAAC;EACDC,YAAY,GAAG;IACbC,KAAK,EAAE;MACLC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,EAAE;MACVT,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,GAAG;MACTC,KAAK,EAAE,GAAG,GAAG,EAAE;MACfC,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE;MACtBC,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC3BC,YAAY,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACrC,CAAC;IACDG,QAAQ,EAAE;MACRC,MAAM,EAAE,CAAC;MACTT,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE,GAAG,EAAE;MACdC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;MACrBC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1BC,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,CAAC;IACDI,MAAM,EAAE;MACNT,KAAK,EAAE,CAAC;MACRC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE,GAAG,EAAE;MACdC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;MACrBC,OAAO,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1BC,YAAY,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,CAAC;IAED,GAAGN;EACL,CAAC;EACDW,kBAAkB,GAAG,QAAQ,GAAG,GAAG;EACnCC,mBAAmB,GAAG,QAAQ,GAAG,IAAI;EACrCC,cAAc,GAAG;IACfL,KAAK,EAAE;MACLC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,EAAE;MACVT,KAAK,EAAEU,kBAAkB,GAAG,CAAC;MAC7BT,IAAI,EAAES,kBAAkB;MACxBR,KAAK,EAAEQ,kBAAkB,GAAG,EAAE;MAC9BP,OAAO,EAAEO,kBAAkB,GAAG,EAAE,GAAG,EAAE;MACrCN,OAAO,EAAEM,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC1CL,YAAY,EAAEK,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACpD,CAAC;IACDF,QAAQ,EAAE;MACRC,MAAM,EAAE,CAAC;MACTT,KAAK,EAAEU,kBAAkB,GAAG,EAAE;MAC9BT,IAAI,EAAES,kBAAkB,GAAG,CAAC;MAC5BR,KAAK,EAAGQ,kBAAkB,GAAG,EAAE,GAAI,CAAC;MACpCP,OAAO,EAAGO,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MAC3CN,OAAO,EAAGM,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAI,CAAC;MAChDL,YAAY,EAAGK,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAI;IAC7D,CAAC;IACDD,MAAM,EAAE;MACNT,KAAK,EAAEW,mBAAmB,GAAG,CAAC;MAC9BV,IAAI,EAAEU,mBAAmB;MACzBT,KAAK,EAAES,mBAAmB,GAAG,EAAE;MAC/BR,OAAO,EAAEQ,mBAAmB,GAAG,EAAE,GAAG,EAAE;MACtCP,OAAO,EAAEO,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;MAC3CN,YAAY,EAAEM,mBAAmB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACrD,CAAC;IACD,GAAGZ;EACL,CAAC;;AAEH;AACA,MAAMc,YAAY,GAAG,CACnB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,cAAc,CACf;AAED,MAAMC,YAAY,GAAGD,YAAY,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;AAEpD;AACA,SAASC,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,GAAG,KAAK,EAAE;EACvC;EACA,MAAMC,IAAI,GAAG;IACXC,MAAM,EAAEF,KAAK,GAAGD,IAAI,CAACG,MAAM,GAAG;MAAE,GAAGJ,GAAG,CAACI,MAAM;MAAE,IAAIH,IAAI,CAACG,MAAM,IAAI,CAAC,CAAC;IAAE,CAAC;IACvEC,GAAG,EAAEL,GAAG,CAACK,GAAG,CAACN,KAAK,CAACE,IAAI,CAACI,GAAG,CAAC;IAC5BC,kBAAkB,EAAEL,IAAI,CAACK,kBAAkB,IAAIN,GAAG,CAACM,kBAAkB;IACrEC,MAAM,EAAEN,IAAI,CAACM,MAAM,IAAIP,GAAG,CAACO;EAC7B,CAAC;EACD,OAAO,IAAIC,QAAQ,CAACL,IAAI,CAAC;AAC3B;AAEA,SAASM,gBAAgBA,CAACF,MAAM,EAAEG,IAAI,EAAE;EACtC,IAAIC,GAAG,GAAGD,IAAI,CAACvB,YAAY,IAAI,CAAC;EAChC,KAAK,MAAMyB,IAAI,IAAIhB,YAAY,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE;IACxC,IAAIa,IAAI,CAACE,IAAI,CAAC,EAAE;MACdD,GAAG,IAAID,IAAI,CAACE,IAAI,CAAC,GAAGL,MAAM,CAACK,IAAI,CAAC,CAAC,cAAc,CAAC;IAClD;EACF;EACA,OAAOD,GAAG;AACZ;;AAEA;AACA,SAASE,eAAeA,CAACN,MAAM,EAAEG,IAAI,EAAE;EACrC;EACA;EACA,MAAMI,MAAM,GAAGL,gBAAgB,CAACF,MAAM,EAAEG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAE1Df,YAAY,CAACoB,WAAW,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAK;IAC9C,IAAI,CAAC1C,WAAW,CAACmC,IAAI,CAACO,OAAO,CAAC,CAAC,EAAE;MAC/B,IAAID,QAAQ,EAAE;QACZ,MAAME,WAAW,GAAGR,IAAI,CAACM,QAAQ,CAAC,GAAGF,MAAM;QAC3C,MAAMK,IAAI,GAAGZ,MAAM,CAACU,OAAO,CAAC,CAACD,QAAQ,CAAC;;QAEtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMI,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACJ,WAAW,GAAGC,IAAI,CAAC;QAC7CT,IAAI,CAACO,OAAO,CAAC,IAAIG,MAAM,GAAGN,MAAM;QAChCJ,IAAI,CAACM,QAAQ,CAAC,IAAII,MAAM,GAAGD,IAAI,GAAGL,MAAM;MAC1C;MACA,OAAOG,OAAO;IAChB,CAAC,MAAM;MACL,OAAOD,QAAQ;IACjB;EACF,CAAC,EAAE,IAAI,CAAC;;EAER;EACA;EACArB,YAAY,CAAC4B,MAAM,CAAC,CAACP,QAAQ,EAAEC,OAAO,KAAK;IACzC,IAAI,CAAC1C,WAAW,CAACmC,IAAI,CAACO,OAAO,CAAC,CAAC,EAAE;MAC/B,IAAID,QAAQ,EAAE;QACZ,MAAMQ,QAAQ,GAAGd,IAAI,CAACM,QAAQ,CAAC,GAAG,CAAC;QACnCN,IAAI,CAACM,QAAQ,CAAC,IAAIQ,QAAQ;QAC1Bd,IAAI,CAACO,OAAO,CAAC,IAAIO,QAAQ,GAAGjB,MAAM,CAACS,QAAQ,CAAC,CAACC,OAAO,CAAC;MACvD;MACA,OAAOA,OAAO;IAChB,CAAC,MAAM;MACL,OAAOD,QAAQ;IACjB;EACF,CAAC,EAAE,IAAI,CAAC;AACV;;AAEA;AACA,SAASS,YAAYA,CAACf,IAAI,EAAE;EAC1B,MAAMgB,OAAO,GAAG,CAAC,CAAC;EAClB,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACpB,IAAI,CAAC,EAAE;IAC/C,IAAIkB,KAAK,KAAK,CAAC,EAAE;MACfF,OAAO,CAACC,GAAG,CAAC,GAAGC,KAAK;IACtB;EACF;EACA,OAAOF,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMlB,QAAQ,CAAC;EAC5B;AACF;AACA;EACEuB,WAAWA,CAACC,MAAM,EAAE;IAClB,MAAMC,QAAQ,GAAGD,MAAM,CAAC1B,kBAAkB,KAAK,UAAU,IAAI,KAAK;IAClE,IAAIC,MAAM,GAAG0B,QAAQ,GAAGvC,cAAc,GAAGN,YAAY;IAErD,IAAI4C,MAAM,CAACzB,MAAM,EAAE;MACjBA,MAAM,GAAGyB,MAAM,CAACzB,MAAM;IACxB;;IAEA;AACJ;AACA;IACI,IAAI,CAACH,MAAM,GAAG4B,MAAM,CAAC5B,MAAM;IAC3B;AACJ;AACA;IACI,IAAI,CAACC,GAAG,GAAG2B,MAAM,CAAC3B,GAAG,IAAIpC,MAAM,CAACiE,MAAM,CAAC,CAAC;IACxC;AACJ;AACA;IACI,IAAI,CAAC5B,kBAAkB,GAAG2B,QAAQ,GAAG,UAAU,GAAG,QAAQ;IAC1D;AACJ;AACA;IACI,IAAI,CAACE,OAAO,GAAGH,MAAM,CAACG,OAAO,IAAI,IAAI;IACrC;AACJ;AACA;IACI,IAAI,CAAC5B,MAAM,GAAGA,MAAM;IACpB;AACJ;AACA;IACI,IAAI,CAAC6B,eAAe,GAAG,IAAI;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC7B,OAAO/B,QAAQ,CAACgC,UAAU,CAAC;MAAErD,YAAY,EAAEmD;IAAM,CAAC,EAAEC,IAAI,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,UAAUA,CAACC,GAAG,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAChC,IAAIE,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC1C,MAAM,IAAI7E,oBAAoB,CAC5B,+DACE6E,GAAG,KAAK,IAAI,GAAG,MAAM,GAAG,OAAOA,GAAG,EAEtC,CAAC;IACH;IAEA,OAAO,IAAIjC,QAAQ,CAAC;MAClBJ,MAAM,EAAE5B,eAAe,CAACiE,GAAG,EAAEjC,QAAQ,CAACkC,aAAa,CAAC;MACpDrC,GAAG,EAAEpC,MAAM,CAACuE,UAAU,CAACD,IAAI,CAAC;MAC5BjC,kBAAkB,EAAEiC,IAAI,CAACjC,kBAAkB;MAC3CC,MAAM,EAAEgC,IAAI,CAAChC;IACf,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOoC,gBAAgBA,CAACC,YAAY,EAAE;IACpC,IAAItE,QAAQ,CAACsE,YAAY,CAAC,EAAE;MAC1B,OAAOpC,QAAQ,CAAC6B,UAAU,CAACO,YAAY,CAAC;IAC1C,CAAC,MAAM,IAAIpC,QAAQ,CAACqC,UAAU,CAACD,YAAY,CAAC,EAAE;MAC5C,OAAOA,YAAY;IACrB,CAAC,MAAM,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MAC3C,OAAOpC,QAAQ,CAACgC,UAAU,CAACI,YAAY,CAAC;IAC1C,CAAC,MAAM;MACL,MAAM,IAAIhF,oBAAoB,CAC5B,6BAA6BgF,YAAY,YAAY,OAAOA,YAAY,EAC1E,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,OAAOA,CAACC,IAAI,EAAER,IAAI,EAAE;IACzB,MAAM,CAACS,MAAM,CAAC,GAAG9E,gBAAgB,CAAC6E,IAAI,CAAC;IACvC,IAAIC,MAAM,EAAE;MACV,OAAOxC,QAAQ,CAACgC,UAAU,CAACQ,MAAM,EAAET,IAAI,CAAC;IAC1C,CAAC,MAAM;MACL,OAAO/B,QAAQ,CAAC2B,OAAO,CAAC,YAAY,EAAE,cAAcY,IAAI,+BAA+B,CAAC;IAC1F;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOE,WAAWA,CAACF,IAAI,EAAER,IAAI,EAAE;IAC7B,MAAM,CAACS,MAAM,CAAC,GAAG7E,gBAAgB,CAAC4E,IAAI,CAAC;IACvC,IAAIC,MAAM,EAAE;MACV,OAAOxC,QAAQ,CAACgC,UAAU,CAACQ,MAAM,EAAET,IAAI,CAAC;IAC1C,CAAC,MAAM;MACL,OAAO/B,QAAQ,CAAC2B,OAAO,CAAC,YAAY,EAAE,cAAcY,IAAI,+BAA+B,CAAC;IAC1F;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOZ,OAAOA,CAACe,MAAM,EAAEC,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAACD,MAAM,EAAE;MACX,MAAM,IAAItF,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAMuE,OAAO,GAAGe,MAAM,YAAYlF,OAAO,GAAGkF,MAAM,GAAG,IAAIlF,OAAO,CAACkF,MAAM,EAAEC,WAAW,CAAC;IAErF,IAAIzE,QAAQ,CAAC0E,cAAc,EAAE;MAC3B,MAAM,IAAIvF,oBAAoB,CAACsE,OAAO,CAAC;IACzC,CAAC,MAAM;MACL,OAAO,IAAI3B,QAAQ,CAAC;QAAE2B;MAAQ,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;EACE,OAAOO,aAAaA,CAAC9B,IAAI,EAAE;IACzB,MAAMyC,UAAU,GAAG;MACjBC,IAAI,EAAE,OAAO;MACbjE,KAAK,EAAE,OAAO;MACdkE,OAAO,EAAE,UAAU;MACnBjE,QAAQ,EAAE,UAAU;MACpBkE,KAAK,EAAE,QAAQ;MACfjE,MAAM,EAAE,QAAQ;MAChBkE,IAAI,EAAE,OAAO;MACb3E,KAAK,EAAE,OAAO;MACd4E,GAAG,EAAE,MAAM;MACX3E,IAAI,EAAE,MAAM;MACZ4E,IAAI,EAAE,OAAO;MACb3E,KAAK,EAAE,OAAO;MACd4E,MAAM,EAAE,SAAS;MACjB3E,OAAO,EAAE,SAAS;MAClB4E,MAAM,EAAE,SAAS;MACjB3E,OAAO,EAAE,SAAS;MAClB4E,WAAW,EAAE,cAAc;MAC3B3E,YAAY,EAAE;IAChB,CAAC,CAACyB,IAAI,GAAGA,IAAI,CAACmD,WAAW,CAAC,CAAC,GAAGnD,IAAI,CAAC;IAEnC,IAAI,CAACyC,UAAU,EAAE,MAAM,IAAIvF,gBAAgB,CAAC8C,IAAI,CAAC;IAEjD,OAAOyC,UAAU;EACnB;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOR,UAAUA,CAACmB,CAAC,EAAE;IACnB,OAAQA,CAAC,IAAIA,CAAC,CAAC5B,eAAe,IAAK,KAAK;EAC1C;;EAEA;AACF;AACA;AACA;EACE,IAAI6B,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC7D,GAAG,CAAC4D,MAAM,GAAG,IAAI;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIE,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACD,OAAO,GAAG,IAAI,CAAC7D,GAAG,CAAC8D,eAAe,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQA,CAACC,GAAG,EAAE9B,IAAI,GAAG,CAAC,CAAC,EAAE;IACvB;IACA,MAAM+B,OAAO,GAAG;MACd,GAAG/B,IAAI;MACPjB,KAAK,EAAEiB,IAAI,CAACgC,KAAK,KAAK,KAAK,IAAIhC,IAAI,CAACjB,KAAK,KAAK;IAChD,CAAC;IACD,OAAO,IAAI,CAAC4C,OAAO,GACfnG,SAAS,CAACmE,MAAM,CAAC,IAAI,CAAC7B,GAAG,EAAEiE,OAAO,CAAC,CAACE,wBAAwB,CAAC,IAAI,EAAEH,GAAG,CAAC,GACvEzF,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE6F,OAAOA,CAAClC,IAAI,GAAG,CAAC,CAAC,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC2B,OAAO,EAAE,OAAOtF,OAAO;IAEjC,MAAM8F,CAAC,GAAG/E,YAAY,CACnBgF,GAAG,CAAE/D,IAAI,IAAK;MACb,MAAMgE,GAAG,GAAG,IAAI,CAACxE,MAAM,CAACQ,IAAI,CAAC;MAC7B,IAAIrC,WAAW,CAACqG,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI;MACb;MACA,OAAO,IAAI,CAACvE,GAAG,CACZwE,eAAe,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEC,WAAW,EAAE,MAAM;QAAE,GAAGxC,IAAI;QAAE3B,IAAI,EAAEA,IAAI,CAACf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC,CACzFmF,MAAM,CAACJ,GAAG,CAAC;IAChB,CAAC,CAAC,CACDK,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC;IAEnB,OAAO,IAAI,CAAC7E,GAAG,CACZ8E,aAAa,CAAC;MAAEC,IAAI,EAAE,aAAa;MAAEN,KAAK,EAAEvC,IAAI,CAAC8C,SAAS,IAAI,QAAQ;MAAE,GAAG9C;IAAK,CAAC,CAAC,CAClFyC,MAAM,CAACN,CAAC,CAAC;EACd;;EAEA;AACF;AACA;AACA;AACA;EACEY,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACpB,OAAO,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO;MAAE,GAAG,IAAI,CAAC9D;IAAO,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmF,KAAKA,CAAA,EAAG;IACN;IACA,IAAI,CAAC,IAAI,CAACrB,OAAO,EAAE,OAAO,IAAI;IAE9B,IAAIsB,CAAC,GAAG,GAAG;IACX,IAAI,IAAI,CAACnG,KAAK,KAAK,CAAC,EAAEmG,CAAC,IAAI,IAAI,CAACnG,KAAK,GAAG,GAAG;IAC3C,IAAI,IAAI,CAACE,MAAM,KAAK,CAAC,IAAI,IAAI,CAACD,QAAQ,KAAK,CAAC,EAAEkG,CAAC,IAAI,IAAI,CAACjG,MAAM,GAAG,IAAI,CAACD,QAAQ,GAAG,CAAC,GAAG,GAAG;IACxF,IAAI,IAAI,CAACR,KAAK,KAAK,CAAC,EAAE0G,CAAC,IAAI,IAAI,CAAC1G,KAAK,GAAG,GAAG;IAC3C,IAAI,IAAI,CAACC,IAAI,KAAK,CAAC,EAAEyG,CAAC,IAAI,IAAI,CAACzG,IAAI,GAAG,GAAG;IACzC,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,IAAI,IAAI,CAACC,YAAY,KAAK,CAAC,EACzFqG,CAAC,IAAI,GAAG;IACV,IAAI,IAAI,CAACxG,KAAK,KAAK,CAAC,EAAEwG,CAAC,IAAI,IAAI,CAACxG,KAAK,GAAG,GAAG;IAC3C,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,EAAEuG,CAAC,IAAI,IAAI,CAACvG,OAAO,GAAG,GAAG;IAC/C,IAAI,IAAI,CAACC,OAAO,KAAK,CAAC,IAAI,IAAI,CAACC,YAAY,KAAK,CAAC;MAC/C;MACA;MACAqG,CAAC,IAAI/G,OAAO,CAAC,IAAI,CAACS,OAAO,GAAG,IAAI,CAACC,YAAY,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG;IAChE,IAAIqG,CAAC,KAAK,GAAG,EAAEA,CAAC,IAAI,KAAK;IACzB,OAAOA,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,SAASA,CAAClD,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC2B,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMwB,MAAM,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC9B,IAAID,MAAM,GAAG,CAAC,IAAIA,MAAM,IAAI,QAAQ,EAAE,OAAO,IAAI;IAEjDnD,IAAI,GAAG;MACLqD,oBAAoB,EAAE,KAAK;MAC3BC,eAAe,EAAE,KAAK;MACtBC,aAAa,EAAE,KAAK;MACpBd,MAAM,EAAE,UAAU;MAClB,GAAGzC,IAAI;MACPwD,aAAa,EAAE;IACjB,CAAC;IAED,MAAMC,QAAQ,GAAGrH,QAAQ,CAAC0D,UAAU,CAACqD,MAAM,EAAE;MAAEO,IAAI,EAAE;IAAM,CAAC,CAAC;IAC7D,OAAOD,QAAQ,CAACP,SAAS,CAAClD,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACE2D,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACX,KAAK,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACEY,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACZ,KAAK,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACE,CAACa,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,IAAI;IAC3C,IAAI,IAAI,CAACnC,OAAO,EAAE;MAChB,OAAO,sBAAsBoC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACnG,MAAM,CAAC,IAAI;IAC9D,CAAC,MAAM;MACL,OAAO,+BAA+B,IAAI,CAACoG,aAAa,IAAI;IAC9D;EACF;;EAEA;AACF;AACA;AACA;EACEb,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACzB,OAAO,EAAE,OAAOuC,GAAG;IAE7B,OAAOhG,gBAAgB,CAAC,IAAI,CAACF,MAAM,EAAE,IAAI,CAACH,MAAM,CAAC;EACnD;;EAEA;AACF;AACA;AACA;EACEsG,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACf,QAAQ,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;EACEgB,IAAIA,CAACC,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAAC1C,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMlE,GAAG,GAAGQ,QAAQ,CAACmC,gBAAgB,CAACiE,QAAQ,CAAC;MAC7CC,MAAM,GAAG,CAAC,CAAC;IAEb,KAAK,MAAMC,CAAC,IAAInH,YAAY,EAAE;MAC5B,IAAItB,cAAc,CAAC2B,GAAG,CAACI,MAAM,EAAE0G,CAAC,CAAC,IAAIzI,cAAc,CAAC,IAAI,CAAC+B,MAAM,EAAE0G,CAAC,CAAC,EAAE;QACnED,MAAM,CAACC,CAAC,CAAC,GAAG9G,GAAG,CAAC+G,GAAG,CAACD,CAAC,CAAC,GAAG,IAAI,CAACC,GAAG,CAACD,CAAC,CAAC;MACtC;IACF;IAEA,OAAO/G,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEyG;IAAO,CAAC,EAAE,IAAI,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACEG,KAAKA,CAACJ,QAAQ,EAAE;IACd,IAAI,CAAC,IAAI,CAAC1C,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMlE,GAAG,GAAGQ,QAAQ,CAACmC,gBAAgB,CAACiE,QAAQ,CAAC;IAC/C,OAAO,IAAI,CAACD,IAAI,CAAC3G,GAAG,CAACiH,MAAM,CAAC,CAAC,CAAC;EAChC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQA,CAACC,EAAE,EAAE;IACX,IAAI,CAAC,IAAI,CAACjD,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM2C,MAAM,GAAG,CAAC,CAAC;IACjB,KAAK,MAAMC,CAAC,IAAIjF,MAAM,CAACuF,IAAI,CAAC,IAAI,CAAChH,MAAM,CAAC,EAAE;MACxCyG,MAAM,CAACC,CAAC,CAAC,GAAG1I,QAAQ,CAAC+I,EAAE,CAAC,IAAI,CAAC/G,MAAM,CAAC0G,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO/G,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEyG;IAAO,CAAC,EAAE,IAAI,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,GAAGA,CAACnG,IAAI,EAAE;IACR,OAAO,IAAI,CAACJ,QAAQ,CAACkC,aAAa,CAAC9B,IAAI,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEyG,GAAGA,CAACjH,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAAC8D,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMoD,KAAK,GAAG;MAAE,GAAG,IAAI,CAAClH,MAAM;MAAE,GAAG5B,eAAe,CAAC4B,MAAM,EAAEI,QAAQ,CAACkC,aAAa;IAAE,CAAC;IACpF,OAAO3C,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEkH;IAAM,CAAC,CAAC;EACvC;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IAAEtD,MAAM;IAAEE,eAAe;IAAE7D,kBAAkB;IAAEC;EAAO,CAAC,GAAG,CAAC,CAAC,EAAE;IACxE,MAAMF,GAAG,GAAG,IAAI,CAACA,GAAG,CAACN,KAAK,CAAC;MAAEkE,MAAM;MAAEE;IAAgB,CAAC,CAAC;IACvD,MAAM5B,IAAI,GAAG;MAAElC,GAAG;MAAEE,MAAM;MAAED;IAAmB,CAAC;IAChD,OAAOP,KAAK,CAAC,IAAI,EAAEwC,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEiF,EAAEA,CAAC5G,IAAI,EAAE;IACP,OAAO,IAAI,CAACsD,OAAO,GAAG,IAAI,CAACuD,OAAO,CAAC7G,IAAI,CAAC,CAACmG,GAAG,CAACnG,IAAI,CAAC,GAAG6F,GAAG;EAC1D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiB,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACxD,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAMxD,IAAI,GAAG,IAAI,CAAC4E,QAAQ,CAAC,CAAC;IAC5BzE,eAAe,CAAC,IAAI,CAACN,MAAM,EAAEG,IAAI,CAAC;IAClC,OAAOX,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEM;IAAK,CAAC,EAAE,IAAI,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;AACA;EACEiH,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACzD,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAMxD,IAAI,GAAGe,YAAY,CAAC,IAAI,CAACiG,SAAS,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAACtC,QAAQ,CAAC,CAAC,CAAC;IACnE,OAAOvF,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEM;IAAK,CAAC,EAAE,IAAI,CAAC;EAC5C;;EAEA;AACF;AACA;AACA;AACA;EACE+G,OAAOA,CAAC,GAAGI,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC3D,OAAO,EAAE,OAAO,IAAI;IAE9B,IAAI2D,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,IAAI;IACb;IAEAD,KAAK,GAAGA,KAAK,CAAClD,GAAG,CAAEoD,CAAC,IAAKvH,QAAQ,CAACkC,aAAa,CAACqF,CAAC,CAAC,CAAC;IAEnD,MAAMC,KAAK,GAAG,CAAC,CAAC;MACdC,WAAW,GAAG,CAAC,CAAC;MAChBvH,IAAI,GAAG,IAAI,CAAC4E,QAAQ,CAAC,CAAC;IACxB,IAAI4C,QAAQ;IAEZ,KAAK,MAAMpB,CAAC,IAAInH,YAAY,EAAE;MAC5B,IAAIkI,KAAK,CAACM,OAAO,CAACrB,CAAC,CAAC,IAAI,CAAC,EAAE;QACzBoB,QAAQ,GAAGpB,CAAC;QAEZ,IAAIsB,GAAG,GAAG,CAAC;;QAEX;QACA,KAAK,MAAMC,EAAE,IAAIJ,WAAW,EAAE;UAC5BG,GAAG,IAAI,IAAI,CAAC7H,MAAM,CAAC8H,EAAE,CAAC,CAACvB,CAAC,CAAC,GAAGmB,WAAW,CAACI,EAAE,CAAC;UAC3CJ,WAAW,CAACI,EAAE,CAAC,GAAG,CAAC;QACrB;;QAEA;QACA,IAAI/J,QAAQ,CAACoC,IAAI,CAACoG,CAAC,CAAC,CAAC,EAAE;UACrBsB,GAAG,IAAI1H,IAAI,CAACoG,CAAC,CAAC;QAChB;;QAEA;QACA;QACA,MAAMwB,CAAC,GAAGjH,IAAI,CAACkH,KAAK,CAACH,GAAG,CAAC;QACzBJ,KAAK,CAAClB,CAAC,CAAC,GAAGwB,CAAC;QACZL,WAAW,CAACnB,CAAC,CAAC,GAAG,CAACsB,GAAG,GAAG,IAAI,GAAGE,CAAC,GAAG,IAAI,IAAI,IAAI;;QAE/C;MACF,CAAC,MAAM,IAAIhK,QAAQ,CAACoC,IAAI,CAACoG,CAAC,CAAC,CAAC,EAAE;QAC5BmB,WAAW,CAACnB,CAAC,CAAC,GAAGpG,IAAI,CAACoG,CAAC,CAAC;MAC1B;IACF;;IAEA;IACA;IACA,KAAK,MAAMnF,GAAG,IAAIsG,WAAW,EAAE;MAC7B,IAAIA,WAAW,CAACtG,GAAG,CAAC,KAAK,CAAC,EAAE;QAC1BqG,KAAK,CAACE,QAAQ,CAAC,IACbvG,GAAG,KAAKuG,QAAQ,GAAGD,WAAW,CAACtG,GAAG,CAAC,GAAGsG,WAAW,CAACtG,GAAG,CAAC,GAAG,IAAI,CAACpB,MAAM,CAAC2H,QAAQ,CAAC,CAACvG,GAAG,CAAC;MACvF;IACF;IAEAd,eAAe,CAAC,IAAI,CAACN,MAAM,EAAEyH,KAAK,CAAC;IACnC,OAAOjI,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAE4H;IAAM,CAAC,EAAE,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACEJ,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC1D,OAAO,EAAE,OAAO,IAAI;IAC9B,OAAO,IAAI,CAACuD,OAAO,CACjB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,cACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACER,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAAC/C,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAMsE,OAAO,GAAG,CAAC,CAAC;IAClB,KAAK,MAAM1B,CAAC,IAAIjF,MAAM,CAACuF,IAAI,CAAC,IAAI,CAAChH,MAAM,CAAC,EAAE;MACxCoI,OAAO,CAAC1B,CAAC,CAAC,GAAG,IAAI,CAAC1G,MAAM,CAAC0G,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC1G,MAAM,CAAC0G,CAAC,CAAC;IACzD;IACA,OAAO/G,KAAK,CAAC,IAAI,EAAE;MAAEK,MAAM,EAAEoI;IAAQ,CAAC,EAAE,IAAI,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,IAAInJ,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC6E,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACf,KAAK,IAAI,CAAC,GAAGoH,GAAG;EACpD;;EAEA;AACF;AACA;AACA;EACE,IAAInH,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC4E,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACd,QAAQ,IAAI,CAAC,GAAGmH,GAAG;EACvD;;EAEA;AACF;AACA;AACA;EACE,IAAIlH,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC2E,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACb,MAAM,IAAI,CAAC,GAAGkH,GAAG;EACrD;;EAEA;AACF;AACA;AACA;EACE,IAAI3H,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoF,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACtB,KAAK,IAAI,CAAC,GAAG2H,GAAG;EACpD;;EAEA;AACF;AACA;AACA;EACE,IAAI1H,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACmF,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACrB,IAAI,IAAI,CAAC,GAAG0H,GAAG;EACnD;;EAEA;AACF;AACA;AACA;EACE,IAAIzH,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACkF,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACpB,KAAK,IAAI,CAAC,GAAGyH,GAAG;EACpD;;EAEA;AACF;AACA;AACA;EACE,IAAIxH,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACiF,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACnB,OAAO,IAAI,CAAC,GAAGwH,GAAG;EACtD;;EAEA;AACF;AACA;AACA;EACE,IAAIvH,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACgF,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAAClB,OAAO,IAAI,CAAC,GAAGuH,GAAG;EACtD;;EAEA;AACF;AACA;AACA;EACE,IAAItH,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC+E,OAAO,GAAG,IAAI,CAAC9D,MAAM,CAACjB,YAAY,IAAI,CAAC,GAAGsH,GAAG;EAC3D;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIvC,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/B,OAAO,KAAK,IAAI;EAC9B;;EAEA;AACF;AACA;AACA;EACE,IAAIqE,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACe,MAAM,GAAG,IAAI;EAClD;;EAEA;AACF;AACA;AACA;EACE,IAAIuF,kBAAkBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACtG,OAAO,GAAG,IAAI,CAACA,OAAO,CAACgB,WAAW,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEuF,MAAMA,CAACC,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAACzE,OAAO,IAAI,CAACyE,KAAK,CAACzE,OAAO,EAAE;MACnC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAAC7D,GAAG,CAACqI,MAAM,CAACC,KAAK,CAACtI,GAAG,CAAC,EAAE;MAC/B,OAAO,KAAK;IACd;IAEA,SAASuI,EAAEA,CAACC,EAAE,EAAEC,EAAE,EAAE;MAClB;MACA,IAAID,EAAE,KAAKE,SAAS,IAAIF,EAAE,KAAK,CAAC,EAAE,OAAOC,EAAE,KAAKC,SAAS,IAAID,EAAE,KAAK,CAAC;MACrE,OAAOD,EAAE,KAAKC,EAAE;IAClB;IAEA,KAAK,MAAMf,CAAC,IAAIpI,YAAY,EAAE;MAC5B,IAAI,CAACiJ,EAAE,CAAC,IAAI,CAACxI,MAAM,CAAC2H,CAAC,CAAC,EAAEY,KAAK,CAACvI,MAAM,CAAC2H,CAAC,CAAC,CAAC,EAAE;QACxC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}