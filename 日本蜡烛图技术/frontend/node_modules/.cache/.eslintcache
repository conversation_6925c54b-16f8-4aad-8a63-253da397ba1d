[{"/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js": "1", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js": "2", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js": "3", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js": "4", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js": "5", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js": "6", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js": "7", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js": "8", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js": "9", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx": "10", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx": "11", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx": "12", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx": "13", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx": "14", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx": "15", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx": "16", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx": "17", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx": "18", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx": "19", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx": "20", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx": "21", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/StockSelector.jsx": "22"}, {"size": 262, "mtime": 1750972716424, "results": "23", "hashOfConfig": "24"}, {"size": 7731, "mtime": 1752137799605, "results": "25", "hashOfConfig": "24"}, {"size": 7152, "mtime": 1750972276236, "results": "26", "hashOfConfig": "24"}, {"size": 17827, "mtime": 1751026406976, "results": "27", "hashOfConfig": "24"}, {"size": 8490, "mtime": 1750972843751, "results": "28", "hashOfConfig": "24"}, {"size": 3922, "mtime": 1752089495109, "results": "29", "hashOfConfig": "24"}, {"size": 8760, "mtime": 1751026354603, "results": "30", "hashOfConfig": "24"}, {"size": 12153, "mtime": 1751472468205, "results": "31", "hashOfConfig": "24"}, {"size": 17337, "mtime": 1751917480405, "results": "32", "hashOfConfig": "24"}, {"size": 5422, "mtime": 1751917315339, "results": "33", "hashOfConfig": "24"}, {"size": 12489, "mtime": 1751917355452, "results": "34", "hashOfConfig": "24"}, {"size": 12581, "mtime": 1751917329419, "results": "35", "hashOfConfig": "24"}, {"size": 12303, "mtime": 1751917422222, "results": "36", "hashOfConfig": "24"}, {"size": 13606, "mtime": 1751917344878, "results": "37", "hashOfConfig": "24"}, {"size": 957, "mtime": 1751915091294, "results": "38", "hashOfConfig": "24"}, {"size": 487, "mtime": 1751915124869, "results": "39", "hashOfConfig": "24"}, {"size": 897, "mtime": 1751915067029, "results": "40", "hashOfConfig": "24"}, {"size": 1907, "mtime": 1751915103891, "results": "41", "hashOfConfig": "24"}, {"size": 1604, "mtime": 1751915081070, "results": "42", "hashOfConfig": "24"}, {"size": 437, "mtime": 1751915135231, "results": "43", "hashOfConfig": "24"}, {"size": 620, "mtime": 1751915145386, "results": "44", "hashOfConfig": "24"}, {"size": 10055, "mtime": 1752139237946, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qhqj0z", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/StockSelector.jsx", [], []]