[{"/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js": "1", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js": "2", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js": "3", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js": "4", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js": "5", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js": "6", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js": "7", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js": "8", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js": "9", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx": "10", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx": "11", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx": "12", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx": "13", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx": "14", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx": "15", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx": "16", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx": "17", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx": "18", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx": "19", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx": "20", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx": "21"}, {"size": 262, "mtime": 1750972716424, "results": "22", "hashOfConfig": "23"}, {"size": 6206, "mtime": 1751915048093, "results": "24", "hashOfConfig": "23"}, {"size": 7152, "mtime": 1750972276236, "results": "25", "hashOfConfig": "23"}, {"size": 17827, "mtime": 1751026406976, "results": "26", "hashOfConfig": "23"}, {"size": 8490, "mtime": 1750972843751, "results": "27", "hashOfConfig": "23"}, {"size": 3913, "mtime": 1750953925227, "results": "28", "hashOfConfig": "23"}, {"size": 8760, "mtime": 1751026354603, "results": "29", "hashOfConfig": "23"}, {"size": 12153, "mtime": 1751472468205, "results": "30", "hashOfConfig": "23"}, {"size": 17337, "mtime": 1751917480405, "results": "31", "hashOfConfig": "23"}, {"size": 5422, "mtime": 1751917315339, "results": "32", "hashOfConfig": "23"}, {"size": 12489, "mtime": 1751917355452, "results": "33", "hashOfConfig": "23"}, {"size": 12581, "mtime": 1751917329419, "results": "34", "hashOfConfig": "23"}, {"size": 12303, "mtime": 1751917422222, "results": "35", "hashOfConfig": "23"}, {"size": 13606, "mtime": 1751917344878, "results": "36", "hashOfConfig": "23"}, {"size": 957, "mtime": 1751915091294, "results": "37", "hashOfConfig": "23"}, {"size": 487, "mtime": 1751915124869, "results": "38", "hashOfConfig": "23"}, {"size": 897, "mtime": 1751915067029, "results": "39", "hashOfConfig": "23"}, {"size": 1907, "mtime": 1751915103891, "results": "40", "hashOfConfig": "23"}, {"size": 1604, "mtime": 1751915081070, "results": "41", "hashOfConfig": "23"}, {"size": 437, "mtime": 1751915135231, "results": "42", "hashOfConfig": "23"}, {"size": 620, "mtime": 1751915145386, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qhqj0z", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx", [], []]