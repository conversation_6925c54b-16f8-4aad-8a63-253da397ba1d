[{"/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js": "1", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js": "2", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js": "3", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js": "4", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js": "5", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js": "6", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js": "7", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js": "8", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js": "9", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx": "10", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx": "11", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx": "12", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx": "13", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx": "14", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx": "15", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx": "16", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx": "17", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx": "18", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx": "19", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx": "20", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx": "21", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/StockSelector.jsx": "22", "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentDebateViewer.jsx": "23"}, {"size": 262, "mtime": 1750972716424, "results": "24", "hashOfConfig": "25"}, {"size": 7731, "mtime": 1752137799605, "results": "26", "hashOfConfig": "25"}, {"size": 7152, "mtime": 1750972276236, "results": "27", "hashOfConfig": "25"}, {"size": 17827, "mtime": 1751026406976, "results": "28", "hashOfConfig": "25"}, {"size": 8490, "mtime": 1750972843751, "results": "29", "hashOfConfig": "25"}, {"size": 3922, "mtime": 1752089495109, "results": "30", "hashOfConfig": "25"}, {"size": 8760, "mtime": 1751026354603, "results": "31", "hashOfConfig": "25"}, {"size": 12247, "mtime": 1752142538883, "results": "32", "hashOfConfig": "25"}, {"size": 17378, "mtime": 1752227043983, "results": "33", "hashOfConfig": "25"}, {"size": 7191, "mtime": 1752142522850, "results": "34", "hashOfConfig": "25"}, {"size": 12489, "mtime": 1751917355452, "results": "35", "hashOfConfig": "25"}, {"size": 12581, "mtime": 1751917329419, "results": "36", "hashOfConfig": "25"}, {"size": 12303, "mtime": 1751917422222, "results": "37", "hashOfConfig": "25"}, {"size": 13606, "mtime": 1751917344878, "results": "38", "hashOfConfig": "25"}, {"size": 957, "mtime": 1751915091294, "results": "39", "hashOfConfig": "25"}, {"size": 487, "mtime": 1751915124869, "results": "40", "hashOfConfig": "25"}, {"size": 897, "mtime": 1751915067029, "results": "41", "hashOfConfig": "25"}, {"size": 1907, "mtime": 1751915103891, "results": "42", "hashOfConfig": "25"}, {"size": 1604, "mtime": 1751915081070, "results": "43", "hashOfConfig": "25"}, {"size": 437, "mtime": 1751915135231, "results": "44", "hashOfConfig": "25"}, {"size": 620, "mtime": 1751915145386, "results": "45", "hashOfConfig": "25"}, {"size": 10055, "mtime": 1752139237946, "results": "46", "hashOfConfig": "25"}, {"size": 17251, "mtime": 1752142416491, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qhqj0z", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/DataUpload.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/CandlestickChart.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternResults.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/services/api.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/PatternAnnotation.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/AgentWorkspace.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/MarketAnalysis.js", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/TradingAgentsPanel.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/DebateViewer.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/PatternValidation.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/SystemStatus.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentAnalysis.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/badge.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/alert.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/card.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/tabs.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/button.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/progress.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/ui/input.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/StockSelector.jsx", [], [], "/Users/<USER>/Desktop/日本蜡烛图技术/frontend/src/components/TradingAgents/AgentDebateViewer.jsx", [], []]