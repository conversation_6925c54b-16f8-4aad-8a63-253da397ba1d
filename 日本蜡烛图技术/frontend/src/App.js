import React, { useState } from 'react';
import { Layout, Typography, Card, Row, Col, Button, Upload, message } from 'antd';
import { UploadOutlined, BarChartOutlined, FileTextOutlined, RobotOutlined, DashboardOutlined, LineChartOutlined, ThunderboltOutlined, StockOutlined } from '@ant-design/icons';
import CandlestickChart from './components/CandlestickChart';
import PatternResults from './components/PatternResults';
import DataUpload from './components/DataUpload';
import AgentWorkspace from './components/AgentWorkspace';
import MarketAnalysis from './components/MarketAnalysis';
import TradingAgentsPanel from './components/TradingAgents/TradingAgentsPanel';
import StockSelector from './components/StockSelector';
import { analyzePatterns } from './services/api';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph } = Typography;

function App() {
  const [candleData, setCandleData] = useState([]);
  const [patterns, setPatterns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('stocks'); // 默认显示股票选择
  const [currentSymbol, setCurrentSymbol] = useState('AAPL');
  const [validationResults, setValidationResults] = useState({});
  const [stockInfo, setStockInfo] = useState(null); // 存储股票信息

  const handleDataUpload = async (data) => {
    try {
      setLoading(true);
      setCandleData(data);

      // 调用API进行形态识别
      const result = await analyzePatterns(data);
      console.log('API Response:', result); // 调试日志

      // 处理API响应结构
      const patterns = result.data?.patterns || result.patterns || [];
      setPatterns(patterns);
      setActiveTab('chart');

      message.success(`成功识别到 ${patterns.length} 个形态`);
    } catch (error) {
      message.error('形态识别失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleValidationComplete = (patternName, validationData) => {
    setValidationResults(prev => ({
      ...prev,
      [patternName]: validationData
    }));
    message.success(`${patternName} 形态验证完成`);
  };

  // 处理股票选择
  const handleStockSelect = async (stockData) => {
    try {
      setLoading(true);
      setCurrentSymbol(stockData.symbol);
      setStockInfo(stockData.stockInfo);
      setCandleData(stockData.candles);

      // 自动进行形态识别
      const result = await analyzePatterns(stockData.candles);
      console.log('API Response:', result);

      const patterns = result.data?.patterns || result.patterns || [];
      setPatterns(patterns);
      setActiveTab('chart');

      message.success(`成功获取 ${stockData.symbol} 数据并识别到 ${patterns.length} 个形态`);
    } catch (error) {
      message.error('股票数据处理失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'stocks':
        return (
          <StockSelector
            onStockSelect={handleStockSelect}
            loading={loading}
          />
        );
      case 'upload':
        return (
          <DataUpload
            onDataUpload={handleDataUpload}
            loading={loading}
          />
        );
      case 'chart':
        return (
          <Row gutter={[16, 16]}>
            <Col span={16}>
              <Card title="蜡烛图" bordered={false}>
                <CandlestickChart 
                  data={candleData}
                  patterns={patterns}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card title="识别结果" bordered={false}>
                <PatternResults patterns={patterns} />
              </Card>
            </Col>
          </Row>
        );
      case 'results':
        return (
          <PatternResults patterns={patterns} detailed={true} />
        );
      case 'agents':
        return (
          <AgentWorkspace />
        );
      case 'market':
        return (
          <MarketAnalysis />
        );
      case 'trading-agents':
        return (
          <TradingAgentsPanel
            detectedPatterns={patterns}
            currentSymbol={currentSymbol}
            candleData={candleData}
            onValidationComplete={handleValidationComplete}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Layout className="layout">
      <Header style={{ background: '#fff', padding: '0 50px' }}>
        <div style={{ display: 'flex', alignItems: 'center', height: '64px' }}>
          <BarChartOutlined style={{ fontSize: '24px', marginRight: '16px', color: '#1890ff' }} />
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            蜡烛图形态识别系统 - 多智能体版
          </Title>
        </div>
      </Header>
      
      <Content style={{ padding: '20px 50px' }}>
        <div style={{ marginBottom: '20px' }}>
          <Paragraph>
            基于《日本蜡烛图技术》+ TradingAgents理论的智能交易分析系统，集成多智能体协作、LLM智能解读和工作流管理。
          </Paragraph>
          
          <div style={{ marginBottom: '20px' }}>
            <Button
              type={activeTab === 'stocks' ? 'primary' : 'default'}
              icon={<StockOutlined />}
              onClick={() => setActiveTab('stocks')}
              style={{ marginRight: '8px' }}
            >
              选择股票
            </Button>
            <Button
              type={activeTab === 'upload' ? 'primary' : 'default'}
              icon={<UploadOutlined />}
              onClick={() => setActiveTab('upload')}
              style={{ marginRight: '8px' }}
            >
              上传数据
            </Button>
            <Button 
              type={activeTab === 'chart' ? 'primary' : 'default'}
              icon={<BarChartOutlined />}
              onClick={() => setActiveTab('chart')}
              disabled={candleData.length === 0}
              style={{ marginRight: '8px' }}
            >
              图表分析
            </Button>
            <Button
              type={activeTab === 'results' ? 'primary' : 'default'}
              icon={<FileTextOutlined />}
              onClick={() => setActiveTab('results')}
              disabled={patterns.length === 0}
              style={{ marginRight: '8px' }}
            >
              详细结果
            </Button>
            <Button
              type={activeTab === 'agents' ? 'primary' : 'default'}
              icon={<RobotOutlined />}
              onClick={() => setActiveTab('agents')}
              style={{ marginRight: '8px' }}
            >
              智能体工作台
            </Button>
            <Button
              type={activeTab === 'market' ? 'primary' : 'default'}
              icon={<DashboardOutlined />}
              onClick={() => setActiveTab('market')}
              style={{ marginRight: '8px' }}
            >
              市场分析
            </Button>
            <Button
              type={activeTab === 'trading-agents' ? 'primary' : 'default'}
              icon={<ThunderboltOutlined />}
              onClick={() => setActiveTab('trading-agents')}
              style={{ marginRight: '8px' }}
            >
              TradingAgents
            </Button>
          </div>
        </div>
        
        {renderContent()}
      </Content>
      
      <Footer style={{ textAlign: 'center' }}>
        多智能体交易系统 ©2024 基于《日本蜡烛图技术》+ TradingAgents理论实现
      </Footer>
    </Layout>
  );
}

export default App;
