.market-analysis {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.market-analysis-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
}

.market-analysis-loading p {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

.market-analysis-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: white;
  border-radius: 8px;
}

.control-panel {
  margin-bottom: 20px;
}

.control-panel .ant-card-body {
  padding: 16px;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-section .ant-card {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-section .ant-statistic {
  text-align: center;
}

.recommendation-card,
.sentiment-card {
  text-align: center;
  padding: 8px;
}

.recommendation-title,
.sentiment-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.recommendation-content,
.sentiment-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.recommendation-confidence,
.sentiment-confidence {
  font-size: 12px;
  color: #999;
  width: 100%;
}

.analysis-tabs {
  margin-bottom: 20px;
}

.analysis-tabs .ant-tabs-content-holder {
  padding: 16px 0;
}

.financial-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.rsi-sentiment {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.data-quality {
  margin-top: 20px;
}

.data-quality .ant-card-body {
  padding: 12px 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .market-analysis {
    padding: 10px;
  }
  
  .control-panel .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .overview-section .ant-col {
    margin-bottom: 16px;
  }
  
  .overview-section .ant-card {
    height: auto;
    min-height: 100px;
  }
}

@media (max-width: 576px) {
  .market-analysis {
    padding: 8px;
  }
  
  .overview-section .ant-statistic-title {
    font-size: 12px;
  }
  
  .overview-section .ant-statistic-content {
    font-size: 18px;
  }
  
  .analysis-tabs .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* 动画效果 */
.market-analysis .ant-card {
  transition: all 0.3s ease;
}

.market-analysis .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.market-analysis .ant-statistic-content {
  transition: all 0.3s ease;
}

.market-analysis .ant-progress-circle {
  transition: all 0.3s ease;
}

/* 自定义进度条样式 */
.market-analysis .ant-progress-line {
  margin-bottom: 0;
}

.market-analysis .ant-progress-circle .ant-progress-text {
  font-size: 12px;
}

/* 标签样式优化 */
.market-analysis .ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 表格样式优化 */
.market-analysis .ant-table-small .ant-table-tbody > tr > td {
  padding: 8px;
}

.market-analysis .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 统计卡片样式 */
.market-analysis .ant-statistic-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.market-analysis .ant-statistic-content {
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

/* 工具提示样式 */
.market-analysis .ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
}

/* 警告样式 */
.market-analysis .ant-alert {
  border-radius: 6px;
}

/* 按钮样式 */
.market-analysis .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.market-analysis .ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.market-analysis .ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 加载状态样式 */
.market-analysis .ant-spin-dot {
  font-size: 24px;
}

.market-analysis .ant-spin-text {
  color: #666;
  margin-top: 8px;
}

/* 徽章样式 */
.market-analysis .ant-badge {
  font-size: 14px;
}

/* 选择器样式 */
.market-analysis .ant-select {
  border-radius: 6px;
}

.market-analysis .ant-input {
  border-radius: 6px;
}

/* 卡片标题样式 */
.market-analysis .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 标签页样式 */
.market-analysis .ant-tabs-card .ant-tabs-tab {
  border-radius: 6px 6px 0 0;
  background: #fafafa;
}

.market-analysis .ant-tabs-card .ant-tabs-tab-active {
  background: white;
  border-bottom-color: white;
}

/* 数据质量指示器样式 */
.data-quality .ant-progress-line {
  width: 200px;
}

/* 情绪分析特殊样式 */
.sentiment-confidence .ant-progress-line {
  width: 100%;
}

/* 技术信号标签样式 */
.market-analysis .ant-space-item .ant-tag {
  margin: 2px;
  padding: 4px 8px;
  font-size: 12px;
}

/* 支撑阻力位表格样式 */
.market-analysis .ant-table-tbody > tr:nth-child(odd) {
  background: #fafafa;
}

/* 移动端优化 */
@media (max-width: 480px) {
  .market-analysis .ant-statistic-content {
    font-size: 20px;
  }
  
  .market-analysis .ant-card-head-title {
    font-size: 14px;
  }
  
  .market-analysis .ant-tabs-tab {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  .recommendation-content .ant-tag,
  .sentiment-content .ant-badge {
    font-size: 12px;
  }
}
