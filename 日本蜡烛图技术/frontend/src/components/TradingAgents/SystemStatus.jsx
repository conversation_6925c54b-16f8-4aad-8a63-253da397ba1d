import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import {
  Brain,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Server,
  Users,
  MessageSquare,
  Shield,
  Activity,
  Clock,
  Zap,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

const SystemStatus = ({ systemStatus, onRefresh }) => {
  const [agents, setAgents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchAgents();
  }, []);

  const fetchAgents = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/trading-agents/agents');
      const data = await response.json();
      if (data.status === 'success') {
        setAgents(data.data.agents || []);
      }
    } catch (error) {
      console.error('获取智能体列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'running':
        return <Badge variant="success" className="bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          运行中
        </Badge>;
      case 'not_initialized':
        return <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
          <AlertTriangle className="w-3 h-3 mr-1" />
          未初始化
        </Badge>;
      default:
        return <Badge variant="destructive">
          <AlertTriangle className="w-3 h-3 mr-1" />
          异常
        </Badge>;
    }
  };

  const getAgentIcon = (agentName) => {
    const icons = {
      'MarketAnalyst': <Activity className="w-4 h-4" />,
      'BullResearcher': <TrendingUp className="w-4 h-4" />,
      'BearResearcher': <TrendingDown className="w-4 h-4" />,
      'PortfolioManager': <Shield className="w-4 h-4" />,
      'DebateModerator': <MessageSquare className="w-4 h-4" />,
      'ExecutionAgent': <Zap className="w-4 h-4" />,
      'CandlestickAnalyst': <Brain className="w-4 h-4" />
    };
    return icons[agentName] || <Brain className="w-4 h-4" />;
  };

  const getAgentName = (agentName) => {
    const names = {
      'MarketAnalyst': '市场分析师',
      'BullResearcher': '多头研究员',
      'BearResearcher': '空头研究员',
      'PortfolioManager': '投资组合经理',
      'DebateModerator': '辩论主持人',
      'ExecutionAgent': '执行智能体',
      'CandlestickAnalyst': '蜡烛图分析师'
    };
    return names[agentName] || agentName;
  };

  const getSpecializationDescription = (specialization) => {
    const descriptions = {
      'technical_analysis': '技术分析和市场趋势',
      'bullish_analysis': '看涨机会识别',
      'bearish_analysis': '风险识别和看跌分析',
      'risk_management': '风险管理和资产配置',
      'consensus_building': '共识建立和观点平衡',
      'trade_execution': '交易执行和订单管理',
      'candlestick_analysis': '蜡烛图形态分析'
    };
    return descriptions[specialization] || specialization;
  };

  const formatLastActivity = (lastActivity) => {
    if (!lastActivity) return '无活动记录';
    
    try {
      const date = new Date(lastActivity);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 1) return '刚刚';
      if (diffMins < 60) return `${diffMins}分钟前`;
      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`;
      return `${Math.floor(diffMins / 1440)}天前`;
    } catch {
      return '时间格式错误';
    }
  };

  return (
    <div className="space-y-4">
      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center space-x-2">
          <Server className="w-5 h-5 text-blue-600" />
          <span>系统状态监控</span>
        </h3>
        <div className="flex space-x-2">
          <Button 
            onClick={fetchAgents}
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新智能体
          </Button>
          <Button 
            onClick={onRefresh}
            size="sm"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            刷新状态
          </Button>
        </div>
      </div>

      {/* 系统总览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">系统总览</CardTitle>
        </CardHeader>
        <CardContent>
          {systemStatus ? (
            <div className="space-y-4">
              {/* 状态指示 */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">系统状态:</span>
                {getStatusBadge(systemStatus.data?.status)}
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {systemStatus.data?.agents?.total_agents || 0}
                  </div>
                  <div className="text-sm text-gray-600">总智能体数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {systemStatus.data?.agents?.active_agents || 0}
                  </div>
                  <div className="text-sm text-gray-600">活跃智能体</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {systemStatus.data?.debate_system?.active_debates || 0}
                  </div>
                  <div className="text-sm text-gray-600">进行中辩论</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {systemStatus.data?.debate_system?.total_debates || 0}
                  </div>
                  <div className="text-sm text-gray-600">总辩论次数</div>
                </div>
              </div>

              {/* 组件状态 */}
              <div className="space-y-2">
                <h4 className="font-medium">组件状态:</h4>
                <div className="grid gap-2">
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">智能体管理器</span>
                    {systemStatus.data?.agents ? 
                      <Badge variant="success">正常</Badge> : 
                      <Badge variant="destructive">异常</Badge>
                    }
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">辩论系统</span>
                    {systemStatus.data?.debate_system?.status === 'active' ? 
                      <Badge variant="success">正常</Badge> : 
                      <Badge variant="destructive">异常</Badge>
                    }
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">形态验证器</span>
                    {systemStatus.data?.pattern_validator?.initialized ? 
                      <Badge variant="success">正常</Badge> : 
                      <Badge variant="destructive">异常</Badge>
                    }
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-600">正在获取系统状态...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 智能体详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>智能体详情</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-4">
              <p className="text-gray-600">正在加载智能体信息...</p>
            </div>
          ) : agents.length > 0 ? (
            <div className="space-y-3">
              {agents.map((agent, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getAgentIcon(agent.name)}
                      <div>
                        <h4 className="font-medium">{getAgentName(agent.name)}</h4>
                        <p className="text-sm text-gray-600">
                          {getSpecializationDescription(agent.specialization)}
                        </p>
                      </div>
                    </div>
                    <Badge variant={agent.is_active ? "success" : "secondary"}>
                      {agent.is_active ? '活跃' : '非活跃'}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">状态:</span>
                      <span className="ml-2 font-medium">{agent.status || '未知'}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">分析次数:</span>
                      <span className="ml-2 font-medium">{agent.analysis_count || 0}</span>
                    </div>
                    <div className="col-span-2">
                      <span className="text-gray-600">最后活动:</span>
                      <span className="ml-2 font-medium">
                        {formatLastActivity(agent.last_activity)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                暂无智能体信息。请检查系统配置。
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 系统健康检查 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">系统健康检查</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">API连接</span>
              <Badge variant="success">正常</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">LLM服务</span>
              <Badge variant="success">DeepSeek-V3</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">配置文件</span>
              <Badge variant="success">已加载</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">环境变量</span>
              <Badge variant="success">已配置</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 系统错误提示 */}
      {systemStatus && systemStatus.status !== 'success' && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            系统检测到异常: {systemStatus.error || '未知错误'}
            <br />
            请检查系统配置或联系技术支持。
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default SystemStatus;
