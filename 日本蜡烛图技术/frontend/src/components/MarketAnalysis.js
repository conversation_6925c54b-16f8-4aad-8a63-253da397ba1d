import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Alert,
  Spin,
  Button,
  Input,
  Select,
  Tabs,
  Table,
  Badge,
  Tooltip,
  Space
} from 'antd';
import {
  RiseOutlined as TrendingUpOutlined,
  FallOutlined as TrendingDownOutlined,
  <PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  ReloadOutlined,
  StarOutlined,
  WarningOutlined
} from '@ant-design/icons';
import './MarketAnalysis.css';

const { Option } = Select;
const { TabPane } = Tabs;

const MarketAnalysis = () => {
  const [loading, setLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const [symbol, setSymbol] = useState('AAPL');
  const [timeInterval, setTimeInterval] = useState('1h');
  const [error, setError] = useState(null);

  // 获取综合市场分析
  const fetchMarketAnalysis = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/v1/market/comprehensive-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          symbol: symbol,
          interval: interval,
          include_fundamentals: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setAnalysisData(data);
    } catch (err) {
      setError(err.message);
      console.error('Market analysis error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMarketAnalysis();
  }, []);

  // 获取推荐颜色
  const getRecommendationColor = (action) => {
    switch (action) {
      case 'BUY': return 'success';
      case 'SELL': return 'error';
      case 'HOLD': return 'warning';
      default: return 'default';
    }
  };

  // 获取情绪颜色
  const getSentimentColor = (sentiment) => {
    switch (sentiment) {
      case 'bullish': return 'success';
      case 'bearish': return 'error';
      case 'neutral': return 'default';
      default: return 'default';
    }
  };

  // 获取评分颜色
  const getScoreColor = (score) => {
    if (score > 0.3) return '#52c41a';
    if (score < -0.3) return '#ff4d4f';
    return '#faad14';
  };

  // 技术指标表格列
  const technicalColumns = [
    {
      title: '指标',
      dataIndex: 'indicator',
      key: 'indicator',
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      render: (value) => value ? value.toFixed(2) : 'N/A'
    },
    {
      title: '信号',
      dataIndex: 'signal',
      key: 'signal',
      render: (signal) => (
        <Tag color={signal === 'buy' ? 'green' : signal === 'sell' ? 'red' : 'blue'}>
          {signal || 'neutral'}
        </Tag>
      )
    }
  ];

  // 支撑阻力位表格列
  const levelsColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'support' ? 'green' : 'red'}>
          {type === 'support' ? '支撑' : '阻力'}
        </Tag>
      )
    },
    {
      title: '价位',
      dataIndex: 'level',
      key: 'level',
      render: (level) => `$${level.toFixed(2)}`
    },
    {
      title: '强度',
      dataIndex: 'strength',
      key: 'strength',
      render: (strength) => (
        <Progress 
          percent={strength * 100} 
          size="small" 
          showInfo={false}
          strokeColor={strength > 0.7 ? '#52c41a' : strength > 0.4 ? '#faad14' : '#ff4d4f'}
        />
      )
    }
  ];

  if (loading) {
    return (
      <div className="market-analysis-loading">
        <Spin size="large" />
        <p>正在分析市场数据...</p>
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="分析失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={fetchMarketAnalysis}>
            重试
          </Button>
        }
      />
    );
  }

  if (!analysisData) {
    return (
      <div className="market-analysis-empty">
        <p>暂无分析数据</p>
        <Button type="primary" onClick={fetchMarketAnalysis}>
          开始分析
        </Button>
      </div>
    );
  }

  const { 
    price_data, 
    technical_analysis, 
    fundamental_analysis, 
    sentiment_analysis, 
    overall_score, 
    recommendation,
    data_quality 
  } = analysisData;

  // 准备技术指标数据
  const technicalData = [];
  if (technical_analysis.indicators) {
    const indicators = technical_analysis.indicators;
    if (indicators.sma_20) technicalData.push({ indicator: 'SMA(20)', value: indicators.sma_20, signal: 'neutral' });
    if (indicators.sma_50) technicalData.push({ indicator: 'SMA(50)', value: indicators.sma_50, signal: 'neutral' });
    if (indicators.ema_12) technicalData.push({ indicator: 'EMA(12)', value: indicators.ema_12, signal: 'neutral' });
    if (indicators.rsi) technicalData.push({ 
      indicator: 'RSI(14)', 
      value: indicators.rsi, 
      signal: indicators.rsi > 70 ? 'sell' : indicators.rsi < 30 ? 'buy' : 'neutral' 
    });
  }

  // 准备支撑阻力位数据
  const levelsData = [];
  if (technical_analysis.support_resistance) {
    const { support, resistance } = technical_analysis.support_resistance;
    support.forEach((level, index) => {
      levelsData.push({
        key: `support-${index}`,
        type: 'support',
        level: level,
        strength: 0.8 - index * 0.2
      });
    });
    resistance.forEach((level, index) => {
      levelsData.push({
        key: `resistance-${index}`,
        type: 'resistance',
        level: level,
        strength: 0.8 - index * 0.2
      });
    });
  }

  return (
    <div className="market-analysis">
      {/* 控制面板 */}
      <Card className="control-panel" size="small">
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <span>股票代码:</span>
              <Input
                value={symbol}
                onChange={(e) => setSymbol(e.target.value.toUpperCase())}
                placeholder="输入股票代码"
                style={{ width: 120 }}
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <span>时间间隔:</span>
              <Select value={timeInterval} onChange={setTimeInterval} style={{ width: 100 }}>
                <Option value="1m">1分钟</Option>
                <Option value="5m">5分钟</Option>
                <Option value="15m">15分钟</Option>
                <Option value="1h">1小时</Option>
                <Option value="1d">1天</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Button 
              type="primary" 
              icon={<ReloadOutlined />}
              onClick={fetchMarketAnalysis}
              loading={loading}
            >
              刷新分析
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 主要指标概览 */}
      <Row gutter={[16, 16]} className="overview-section">
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="当前价格"
              value={price_data.current_price}
              precision={2}
              prefix="$"
              valueStyle={{ 
                color: price_data.change >= 0 ? '#3f8600' : '#cf1322' 
              }}
              suffix={
                <span style={{ fontSize: '14px' }}>
                  {price_data.change >= 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
                  {price_data.change_percent.toFixed(2)}%
                </span>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="综合评分"
              value={overall_score.overall}
              precision={2}
              valueStyle={{ color: getScoreColor(overall_score.overall) }}
              suffix={
                <Progress
                  type="circle"
                  percent={Math.abs(overall_score.overall) * 100}
                  width={60}
                  strokeColor={getScoreColor(overall_score.overall)}
                  format={() => ''}
                />
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <div className="recommendation-card">
              <div className="recommendation-title">投资建议</div>
              <div className="recommendation-content">
                <Tag 
                  color={getRecommendationColor(recommendation.action)}
                  style={{ fontSize: '16px', padding: '4px 12px' }}
                >
                  {recommendation.action}
                </Tag>
                <div className="recommendation-confidence">
                  置信度: {recommendation.confidence}
                </div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <div className="sentiment-card">
              <div className="sentiment-title">市场情绪</div>
              <div className="sentiment-content">
                <Badge 
                  color={getSentimentColor(sentiment_analysis.sentiment)}
                  text={
                    sentiment_analysis.sentiment === 'bullish' ? '看涨' :
                    sentiment_analysis.sentiment === 'bearish' ? '看跌' : '中性'
                  }
                  style={{ fontSize: '14px' }}
                />
                <div className="sentiment-confidence">
                  <Progress 
                    percent={sentiment_analysis.confidence * 100}
                    size="small"
                    showInfo={false}
                  />
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 详细分析标签页 */}
      <Card className="analysis-tabs">
        <Tabs defaultActiveKey="technical" type="card">
          <TabPane tab={<span><BarChartOutlined />技术分析</span>} key="technical">
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card title="技术指标" size="small">
                  <Table
                    columns={technicalColumns}
                    dataSource={technicalData}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card title="支撑阻力位" size="small">
                  <Table
                    columns={levelsColumns}
                    dataSource={levelsData}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
            </Row>
            
            {technical_analysis.signals && technical_analysis.signals.length > 0 && (
              <Card title="技术信号" size="small" style={{ marginTop: 16 }}>
                <Space wrap>
                  {technical_analysis.signals.map((signal, index) => (
                    <Tooltip key={index} title={`${signal.indicator}: ${signal.type}`}>
                      <Tag 
                        color={signal.signal === 'buy' ? 'green' : 'red'}
                        icon={signal.signal === 'buy' ? <TrendingUpOutlined /> : <TrendingDownOutlined />}
                      >
                        {signal.indicator} {signal.signal}
                      </Tag>
                    </Tooltip>
                  ))}
                </Space>
              </Card>
            )}
          </TabPane>

          <TabPane tab={<span><DashboardOutlined />基本面</span>} key="fundamental">
            {fundamental_analysis && Object.keys(fundamental_analysis).length > 0 ? (
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Card title="估值分析" size="small">
                    <Statistic
                      title="市盈率 (P/E)"
                      value={fundamental_analysis.key_metrics?.pe_ratio || 0}
                      precision={2}
                    />
                    <div style={{ marginTop: 16 }}>
                      <Tag color={
                        fundamental_analysis.valuation?.pe_evaluation === 'undervalued' ? 'green' :
                        fundamental_analysis.valuation?.pe_evaluation === 'overvalued' ? 'red' : 'blue'
                      }>
                        {fundamental_analysis.valuation?.pe_evaluation || 'fair'}
                      </Tag>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} md={12}>
                  <Card title="财务健康" size="small">
                    <div className="financial-metrics">
                      <div className="metric-item">
                        <span>盈利能力:</span>
                        <Tag color={
                          fundamental_analysis.financial_health?.profitability === 'excellent' ? 'green' :
                          fundamental_analysis.financial_health?.profitability === 'good' ? 'blue' :
                          fundamental_analysis.financial_health?.profitability === 'poor' ? 'red' : 'orange'
                        }>
                          {fundamental_analysis.financial_health?.profitability || 'average'}
                        </Tag>
                      </div>
                      <div className="metric-item">
                        <span>风险水平:</span>
                        <Tag color={
                          fundamental_analysis.financial_health?.risk_level === 'low' ? 'green' :
                          fundamental_analysis.financial_health?.risk_level === 'high' ? 'red' : 'orange'
                        }>
                          {fundamental_analysis.financial_health?.risk_level || 'medium'}
                        </Tag>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            ) : (
              <Alert
                message="基本面数据不可用"
                description="当前使用模拟数据，基本面分析功能有限"
                type="info"
                showIcon
              />
            )}
          </TabPane>

          <TabPane tab={<span><LineChartOutlined />情绪分析</span>} key="sentiment">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <Card title="价格动量" size="small">
                  <Statistic
                    title="动量指标"
                    value={sentiment_analysis.price_momentum * 100}
                    precision={2}
                    suffix="%"
                    valueStyle={{ 
                      color: sentiment_analysis.price_momentum >= 0 ? '#3f8600' : '#cf1322' 
                    }}
                  />
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card title="成交量趋势" size="small">
                  <Statistic
                    title="成交量变化"
                    value={sentiment_analysis.volume_trend * 100}
                    precision={2}
                    suffix="%"
                    valueStyle={{ 
                      color: sentiment_analysis.volume_trend >= 0 ? '#3f8600' : '#cf1322' 
                    }}
                  />
                </Card>
              </Col>
              <Col xs={24} md={8}>
                <Card title="RSI情绪" size="small">
                  <div className="rsi-sentiment">
                    <Tag color={
                      sentiment_analysis.rsi_sentiment === 'oversold' ? 'green' :
                      sentiment_analysis.rsi_sentiment === 'overbought' ? 'red' : 'blue'
                    }>
                      {sentiment_analysis.rsi_sentiment === 'oversold' ? '超卖' :
                       sentiment_analysis.rsi_sentiment === 'overbought' ? '超买' : '中性'}
                    </Tag>
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* 数据质量指示器 */}
      {data_quality && (
        <Card size="small" className="data-quality">
          <Row align="middle">
            <Col>
              <span>数据质量: </span>
              <Progress
                percent={data_quality.score * 100}
                size="small"
                status={data_quality.score > 0.8 ? 'success' : data_quality.score > 0.5 ? 'normal' : 'exception'}
              />
            </Col>
            {data_quality.issues && data_quality.issues.length > 0 && (
              <Col offset={1}>
                <Tooltip title={data_quality.issues.join(', ')}>
                  <WarningOutlined style={{ color: '#faad14' }} />
                </Tooltip>
              </Col>
            )}
          </Row>
        </Card>
      )}
    </div>
  );
};

export default MarketAnalysis;
