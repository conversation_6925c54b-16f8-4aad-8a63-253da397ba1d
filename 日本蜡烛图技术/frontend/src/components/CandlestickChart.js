import React, { useEffect, useRef, useState } from 'react';
import { Card, Select, Switch, Tooltip, Tag, Button } from 'antd';
import { Chart, registerables } from 'chart.js';
import { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';
import annotationPlugin from 'chartjs-plugin-annotation';
import PatternAnnotation, { PATTERN_DETAILS, getSignalColor } from './PatternAnnotation';
import 'chartjs-adapter-luxon';

// 注册Chart.js组件和金融图表插件
Chart.register(...registerables, CandlestickController, CandlestickElement, OhlcController, OhlcElement, annotationPlugin);

const { Option } = Select;

// 形态名称中英文对照
const PATTERN_NAMES = {
  'hammer': '锤子线',
  'hanging_man': '上吊线',
  'doji': '十字线',
  'long_legged_doji': '长腿十字线',
  'gravestone_doji': '墓碑十字线',
  'dragonfly_doji': '蜻蜓十字线',
  'spinning_top': '纺锤线',
  'marubozu': '光头光脚线',
  'engulfing_bullish': '看涨吞没',
  'engulfing_bearish': '看跌吞没',
  'dark_cloud_cover': '乌云盖顶',
  'piercing_pattern': '刺透形态',
  'harami_bullish': '看涨孕线',
  'harami_bearish': '看跌孕线',
  'harami_cross': '十字孕线',
  'tweezers_top': '平头顶部',
  'tweezers_bottom': '平头底部',
  'morning_star': '启明星',
  'evening_star': '黄昏星',
  'morning_doji_star': '十字启明星',
  'evening_doji_star': '十字黄昏星',
  'three_white_soldiers': '前进白色三兵',
  'three_black_crows': '三只乌鸦',
  'rising_three_methods': '上升三法',
  'falling_three_methods': '下降三法',
  'shooting_star': '流星',
  'inverted_hammer': '倒锤子'
};

// 经典图表样式配置
const getClassicChartStyle = () => ({
  backgroundColor: '#FFFFFF',
  gridColor: '#E0E0E0',
  textColor: '#333333',
  borderColor: '#CCCCCC',
  titleColor: '#000000'
});

const getModernChartStyle = () => ({
  backgroundColor: '#FAFAFA',
  gridColor: '#F0F0F0',
  textColor: '#666666',
  borderColor: '#D9D9D9',
  titleColor: '#1890FF'
});

const CandlestickChart = ({ data, patterns = [] }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);
  const [showPatterns, setShowPatterns] = useState(true);
  const [selectedPattern, setSelectedPattern] = useState('all');
  const [chartType, setChartType] = useState('candlestick');
  const [classicStyle, setClassicStyle] = useState(true);

  // 处理蜡烛图数据
  const processCandleData = () => {
    if (!data || data.length === 0) return [];

    return data.map((candle, index) => ({
      x: candle.timestamp ? new Date(candle.timestamp).getTime() : index,
      o: parseFloat(candle.open),
      h: parseFloat(candle.high),
      l: parseFloat(candle.low),
      c: parseFloat(candle.close),
      v: parseFloat(candle.volume || 1000),
      timestamp: candle.timestamp
    }));
  };

  // 处理形态标注
  const processPatternAnnotations = () => {
    if (!showPatterns || !patterns || patterns.length === 0) return [];

    const filteredPatterns = selectedPattern === 'all'
      ? patterns
      : patterns.filter(p => p.pattern_name === selectedPattern);

    const annotations = [];

    filteredPatterns.forEach((pattern, index) => {
      const startIndex = pattern.start_index || 0;
      const endIndex = pattern.end_index || startIndex;

      // 获取对应的数据点
      const startData = data[startIndex];
      const endData = data[endIndex];

      if (!startData || !endData) return;

      const patternDetails = PATTERN_DETAILS[pattern.pattern_name];
      const color = getSignalColor(patternDetails?.signal || pattern.signal);
      const patternName = patternDetails?.chinese || PATTERN_NAMES[pattern.pattern_name] || pattern.pattern_name;

      // 添加背景高亮框
      annotations.push({
        type: 'box',
        xMin: startData.timestamp ? new Date(startData.timestamp).getTime() : startIndex,
        xMax: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,
        yMin: Math.min(parseFloat(startData.low), parseFloat(endData.low)) * 0.998,
        yMax: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.002,
        backgroundColor: color + '15',
        borderColor: color,
        borderWidth: 1.5,
        borderDash: [5, 5]
      });

      // 添加形态名称标签
      annotations.push({
        type: 'label',
        xValue: endData.timestamp ? new Date(endData.timestamp).getTime() : endIndex,
        yValue: Math.max(parseFloat(startData.high), parseFloat(endData.high)) * 1.005,
        backgroundColor: color,
        borderColor: color,
        borderWidth: 1,
        borderRadius: 4,
        color: '#FFFFFF',
        content: [`${patternName}`, `置信度: ${(pattern.confidence * 100).toFixed(1)}%`],
        font: {
          size: 11,
          weight: 'bold'
        },
        padding: 6,
        position: 'center'
      });
    });

    return annotations;
  };

  // 获取形态颜色
  const getPatternColor = (signal) => {
    switch (signal) {
      case 'bullish': return '#52c41a';
      case 'bearish': return '#ff4d4f';
      case 'neutral': return '#faad14';
      default: return '#1890ff';
    }
  };

  // 创建图表配置
  const createChartConfig = () => {
    const candleData = processCandleData();
    const annotations = processPatternAnnotations();
    const styleConfig = classicStyle ? getClassicChartStyle() : getModernChartStyle();

    if (chartType === 'line') {
      return {
        type: 'line',
        data: {
          datasets: [{
            label: '收盘价',
            data: candleData.map(d => ({ x: d.x, y: d.c })),
            borderColor: '#1890ff',
            backgroundColor: '#1890ff20',
            fill: false,
            tension: 0.1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              type: 'time',
              time: {
                unit: 'day'
              }
            },
            y: {
              beginAtZero: false
            }
          },
          plugins: {
            legend: {
              display: true
            },
            annotation: {
              annotations: annotations
            }
          }
        }
      };
    }

    // 真正的蜡烛图配置
    return {
      type: 'candlestick',
      data: {
        datasets: [
          {
            label: '日本蜡烛图',
            data: candleData,
            // 根据样式选择配置颜色
            color: classicStyle ? {
              up: '#000000',    // 阳线边框：黑色
              down: '#000000',  // 阴线边框：黑色
              unchanged: '#000000'
            } : {
              up: '#52c41a',    // 阳线边框：绿色
              down: '#ff4d4f',  // 阴线边框：红色
              unchanged: '#faad14'
            },
            backgroundColor: classicStyle ? {
              up: '#FFFFFF',    // 阳线填充：白色
              down: '#000000',  // 阴线填充：黑色
              unchanged: '#FFFFFF'
            } : {
              up: '#52c41a20',  // 阳线填充：淡绿色
              down: '#ff4d4f20', // 阴线填充：淡红色
              unchanged: '#faad1420'
            },
            borderColor: classicStyle ? {
              up: '#000000',
              down: '#000000',
              unchanged: '#000000'
            } : {
              up: '#52c41a',
              down: '#ff4d4f',
              unchanged: '#faad14'
            },
            borderWidth: 1.5,
            // 蜡烛图样式
            candlestick: {
              bodyWidth: 0.8,
              wickWidth: 1
            }
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          intersect: false,
          mode: 'index'
        },
        scales: {
          x: {
            type: 'time',
            time: {
              unit: 'day',
              displayFormats: {
                day: 'MM/dd',
                hour: 'HH:mm'
              }
            },
            grid: {
              display: true,
              color: styleConfig.gridColor,
              lineWidth: classicStyle ? 1 : 0.5,
              drawBorder: true,
              borderColor: styleConfig.borderColor,
              borderWidth: 1
            },
            ticks: {
              color: styleConfig.textColor,
              font: {
                size: classicStyle ? 10 : 11,
                family: classicStyle ? 'monospace' : 'Arial'
              },
              maxTicksLimit: 10
            },
            title: {
              display: classicStyle,
              text: '时间',
              color: styleConfig.textColor,
              font: {
                size: 12,
                weight: 'bold'
              }
            }
          },
          y: {
            beginAtZero: false,
            position: 'right',
            grid: {
              display: true,
              color: styleConfig.gridColor,
              lineWidth: classicStyle ? 1 : 0.5,
              drawBorder: true,
              borderColor: styleConfig.borderColor,
              borderWidth: 1
            },
            ticks: {
              color: styleConfig.textColor,
              font: {
                size: classicStyle ? 10 : 11,
                family: classicStyle ? 'monospace' : 'Arial'
              },
              callback: function(value) {
                return value.toFixed(2);
              },
              maxTicksLimit: 8
            },
            title: {
              display: classicStyle,
              text: '价格',
              color: styleConfig.textColor,
              font: {
                size: 12,
                weight: 'bold'
              }
            }
          }
        },
        plugins: {
          title: {
            display: classicStyle,
            text: '日蜡烛线图',
            color: styleConfig.titleColor,
            font: {
              size: 16,
              weight: 'bold',
              family: classicStyle ? 'serif' : 'Arial'
            },
            padding: {
              top: 10,
              bottom: 20
            }
          },
          legend: {
            display: true,
            position: 'top',
            labels: {
              color: styleConfig.textColor,
              font: {
                size: classicStyle ? 11 : 12,
                weight: 'bold',
                family: classicStyle ? 'monospace' : 'Arial'
              },
              usePointStyle: true,
              padding: 15
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#FFFFFF',
            bodyColor: '#FFFFFF',
            borderColor: '#333333',
            borderWidth: 1,
            callbacks: {
              title: (context) => {
                const dataIndex = context[0].dataIndex;
                const candle = candleData[dataIndex];
                return candle.timestamp ?
                  new Date(candle.timestamp).toLocaleDateString('zh-CN') :
                  `第 ${dataIndex + 1} 根K线`;
              },
              label: (context) => {
                const dataIndex = context.dataIndex;
                const candle = candleData[dataIndex];
                return [
                  `开盘: ${candle.o.toFixed(2)}`,
                  `最高: ${candle.h.toFixed(2)}`,
                  `最低: ${candle.l.toFixed(2)}`,
                  `收盘: ${candle.c.toFixed(2)}`,
                  `成交量: ${candle.v.toLocaleString()}`
                ];
              }
            }
          },
          annotation: {
            annotations: annotations
          }
        }
      }
    };
  };

  // 初始化图表
  useEffect(() => {
    if (!chartRef.current || !data || data.length === 0) return;

    // 销毁现有图表
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // 创建新图表
    const ctx = chartRef.current.getContext('2d');
    const config = createChartConfig();
    
    chartInstance.current = new Chart(ctx, config);

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, patterns, showPatterns, selectedPattern, chartType, classicStyle]);

  // 获取唯一的形态名称
  const getUniquePatterns = () => {
    if (!patterns || patterns.length === 0) return [];
    const uniqueNames = [...new Set(patterns.map(p => p.pattern_name))];
    return uniqueNames.sort();
  };

  if (!data || data.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <p>暂无数据，请先上传蜡烛图数据</p>
        </div>
      </Card>
    );
  }

  return (
    <div>
      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16, flexWrap: 'wrap' }}>
          <div>
            <span style={{ marginRight: 8 }}>图表类型:</span>
            <Select
              value={chartType}
              onChange={setChartType}
              style={{ width: 120 }}
            >
              <Option value="candlestick">蜡烛图</Option>
              <Option value="line">折线图</Option>
            </Select>
          </div>

          <div>
            <span style={{ marginRight: 8 }}>经典样式:</span>
            <Switch
              checked={classicStyle}
              onChange={setClassicStyle}
              checkedChildren="黑白"
              unCheckedChildren="彩色"
            />
          </div>

          <div>
            <span style={{ marginRight: 8 }}>显示形态:</span>
            <Switch
              checked={showPatterns}
              onChange={setShowPatterns}
              checkedChildren="开"
              unCheckedChildren="关"
            />
          </div>
          
          {showPatterns && (
            <div>
              <span style={{ marginRight: 8 }}>形态过滤:</span>
              <Select 
                value={selectedPattern} 
                onChange={setSelectedPattern}
                style={{ width: 150 }}
              >
                <Option value="all">全部形态</Option>
                {getUniquePatterns().map(pattern => (
                  <Option key={pattern} value={pattern}>{pattern}</Option>
                ))}
              </Select>
            </div>
          )}
          
          <div>
            <span style={{ marginRight: 8 }}>数据点数:</span>
            <Tag color="blue">{data.length}</Tag>
          </div>
          
          {patterns && patterns.length > 0 && (
            <div>
              <span style={{ marginRight: 8 }}>识别形态:</span>
              <Tag color="green">{patterns.length}</Tag>
            </div>
          )}
        </div>
      </Card>

      {/* 图表区域 */}
      <Card>
        <div style={{ height: '500px', position: 'relative' }}>
          <canvas ref={chartRef} />
        </div>
      </Card>

      {/* 形态图例 */}
      {showPatterns && patterns && patterns.length > 0 && (
        <Card
          size="small"
          style={{ marginTop: 16 }}
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <span>识别到的蜡烛图形态</span>
              <Tag color="blue">{patterns.length}个</Tag>
            </div>
          }
        >
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8, alignItems: 'center' }}>
            {getUniquePatterns().map(patternName => {
              const pattern = patterns.find(p => p.pattern_name === patternName);
              if (!pattern) return null;

              return (
                <PatternAnnotation
                  key={patternName}
                  pattern={pattern}
                  showEnglish={true}
                  compact={false}
                />
              );
            })}
          </div>

          {/* 形态统计 */}
          <div style={{ marginTop: 16, padding: '12px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
            <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
              <div>
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>
                  看涨形态: {patterns.filter(p => {
                    const details = PATTERN_DETAILS[p.pattern_name];
                    return details?.signal === 'bullish' || p.signal === 'bullish';
                  }).length}
                </span>
              </div>
              <div>
                <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
                  看跌形态: {patterns.filter(p => {
                    const details = PATTERN_DETAILS[p.pattern_name];
                    return details?.signal === 'bearish' || p.signal === 'bearish';
                  }).length}
                </span>
              </div>
              <div>
                <span style={{ color: '#722ed1', fontWeight: 'bold' }}>
                  反转形态: {patterns.filter(p => {
                    const details = PATTERN_DETAILS[p.pattern_name];
                    return details?.signal === 'reversal';
                  }).length}
                </span>
              </div>
              <div>
                <span style={{ color: '#faad14', fontWeight: 'bold' }}>
                  高置信度: {patterns.filter(p => p.confidence >= 0.8).length}
                </span>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default CandlestickChart;
