#!/usr/bin/env python3
"""
启动集成的蜡烛图形态识别 + TradingAgents系统
"""

import asyncio
import subprocess
import sys
import time
import os
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("🚀" + "="*80)
    print("🕯️  蜡烛图形态识别系统 + TradingAgents 多智能体集成版")
    print("📊  基于《日本蜡烛图技术》+ TradingAgents理论")
    print("🤖  AI智能体验证 + 结构化辩论 + 投资建议")
    print("="*82)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    # 检查必要的包
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'aiohttp', 'python-dotenv', 'pyyaml'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("💡 请运行: pip install -r backend/requirements.txt")
        return False
    
    print("✅ 依赖检查通过")
    return True

def setup_environment():
    """设置环境"""
    print("🔧 设置环境变量...")
    
    # 检查.env文件
    env_file = Path("TradingAgents/.env")
    if not env_file.exists():
        print("⚠️ 未找到.env文件，使用默认配置")
    else:
        print("✅ 找到.env配置文件")
    
    # 设置Python路径
    current_dir = Path.cwd()
    backend_path = current_dir / "backend" / "src"
    trading_agents_path = current_dir / "TradingAgents"
    
    python_path = os.environ.get('PYTHONPATH', '')
    new_paths = [str(backend_path), str(trading_agents_path)]
    
    for path in new_paths:
        if path not in python_path:
            python_path = f"{path}:{python_path}" if python_path else path
    
    os.environ['PYTHONPATH'] = python_path
    print(f"✅ 设置PYTHONPATH: {python_path}")

def start_backend():
    """启动后端服务"""
    print("🔄 启动后端API服务...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ 未找到backend目录")
        return None
    
    try:
        # 启动FastAPI服务
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "src.api.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print("✅ 后端服务启动中... (端口: 8000)")
        return process
        
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🔄 启动前端React应用...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ 未找到frontend目录")
        return None
    
    # 检查node_modules
    node_modules = frontend_dir / "node_modules"
    if not node_modules.exists():
        print("📦 安装前端依赖...")
        try:
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        except subprocess.CalledProcessError:
            print("❌ 前端依赖安装失败")
            return None
    
    try:
        # 启动React开发服务器
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print("✅ 前端服务启动中... (端口: 3000)")
        return process
        
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def wait_for_services():
    """等待服务启动"""
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    # 检查后端服务
    try:
        import requests
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务就绪")
        else:
            print("⚠️ 后端服务响应异常")
    except Exception:
        print("⚠️ 后端服务连接失败")
    
    print("✅ 系统启动完成!")

def show_access_info():
    """显示访问信息"""
    print("\n🌐 访问信息:")
    print("="*50)
    print("🖥️  前端界面: http://localhost:3000")
    print("🔧 后端API:  http://localhost:8000")
    print("📚 API文档:  http://localhost:8000/docs")
    print("📖 ReDoc:   http://localhost:8000/redoc")
    print("="*50)
    print("\n🎯 功能特色:")
    print("✅ 传统蜡烛图形态识别")
    print("✅ TradingAgents多智能体验证")
    print("✅ AI智能体协作分析")
    print("✅ 结构化辩论系统")
    print("✅ 投资建议生成")
    print("✅ 批量形态验证")
    print("\n💡 使用提示:")
    print("1. 上传蜡烛图数据或使用示例数据")
    print("2. 查看形态识别结果")
    print("3. 点击'TradingAgents'标签页体验AI验证")
    print("4. 尝试智能体分析和辩论功能")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖")
        return
    
    # 设置环境
    setup_environment()
    
    # 启动服务
    backend_process = start_backend()
    if not backend_process:
        print("❌ 后端启动失败")
        return
    
    frontend_process = start_frontend()
    if not frontend_process:
        print("❌ 前端启动失败")
        if backend_process:
            backend_process.terminate()
        return
    
    try:
        # 等待服务启动
        wait_for_services()
        
        # 显示访问信息
        show_access_info()
        
        print("\n🔄 系统运行中... (按Ctrl+C停止)")
        
        # 保持运行
        while True:
            time.sleep(1)
            
            # 检查进程状态
            if backend_process.poll() is not None:
                print("❌ 后端进程意外退出")
                break
            if frontend_process.poll() is not None:
                print("❌ 前端进程意外退出")
                break
                
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        if backend_process:
            backend_process.terminate()
            print("✅ 后端服务已停止")
            
        if frontend_process:
            frontend_process.terminate()
            print("✅ 前端服务已停止")
            
        print("👋 系统已关闭")

if __name__ == "__main__":
    main()
