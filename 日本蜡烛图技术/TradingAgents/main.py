#!/usr/bin/env python3
"""
TradingAgents - Multi-Agent Trading System
Main application entry point

Based on TauricResearch/TradingAgents architecture
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.agent_manager import Agent<PERSON>anager
from core.debate_system import DebateSystem
from core.workflow_engine import WorkflowEngine
from data.market_data import MarketDataProvider
from utils.config import Config
from utils.logging import setup_logging


class TradingAgentsSystem:
    """
    Main TradingAgents system orchestrator
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the trading agents system"""
        self.config = Config(config_path)
        self.logger = setup_logging(self.config.get('logging', {}))
        
        # Core components
        self.agent_manager = AgentManager(self.config)
        self.debate_system = DebateSystem(self.config)
        self.workflow_engine = WorkflowEngine(self.config)
        self.market_data = MarketDataProvider(self.config)
        
        self.logger.info("TradingAgents system initialized")
    
    async def initialize(self):
        """Initialize all system components"""
        self.logger.info("Initializing TradingAgents system...")
        
        try:
            # Initialize market data provider
            await self.market_data.initialize()
            self.logger.info("✅ Market data provider initialized")
            
            # Initialize agents
            await self.agent_manager.initialize_agents()
            self.logger.info("✅ Agents initialized")
            
            # Initialize debate system
            await self.debate_system.initialize()
            self.logger.info("✅ Debate system initialized")
            
            # Initialize workflow engine
            await self.workflow_engine.initialize()
            self.logger.info("✅ Workflow engine initialized")
            
            self.logger.info("🚀 TradingAgents system ready!")
            
        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            raise
    
    async def run_market_analysis(self, symbol: str) -> Dict[str, Any]:
        """Run comprehensive market analysis for a symbol"""
        self.logger.info(f"🔍 Starting market analysis for {symbol}")
        
        try:
            # Get market data
            market_data = await self.market_data.get_symbol_data(symbol)
            
            # Run agent analysis
            analysis_results = await self.agent_manager.analyze_symbol(symbol, market_data)
            
            # Conduct debate if needed
            if len(analysis_results) > 1:
                debate_result = await self.debate_system.conduct_debate(
                    topic=f"Investment decision for {symbol}",
                    analysis_results=analysis_results
                )
                analysis_results['debate'] = debate_result
            
            self.logger.info(f"✅ Market analysis completed for {symbol}")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"❌ Market analysis failed for {symbol}: {e}")
            raise
    
    async def run_trading_workflow(self, symbol: str, strategy: str = "default") -> Dict[str, Any]:
        """Execute complete trading workflow"""
        self.logger.info(f"🔄 Starting trading workflow for {symbol} with {strategy} strategy")
        
        try:
            # Execute workflow
            workflow_result = await self.workflow_engine.execute_workflow(
                workflow_type="trading_decision",
                symbol=symbol,
                strategy=strategy
            )
            
            self.logger.info(f"✅ Trading workflow completed for {symbol}")
            return workflow_result
            
        except Exception as e:
            self.logger.error(f"❌ Trading workflow failed for {symbol}: {e}")
            raise
    
    async def run_agent_debate(self, topic: str, participants: List[str] = None) -> Dict[str, Any]:
        """Run a structured debate between agents"""
        self.logger.info(f"🎭 Starting agent debate: {topic}")
        
        try:
            # Get participating agents
            if participants is None:
                participants = ["bull_researcher", "bear_researcher", "market_analyst"]
            
            agents = [self.agent_manager.get_agent(name) for name in participants]
            
            # Conduct debate
            debate_result = await self.debate_system.conduct_debate(
                topic=topic,
                participants=agents
            )
            
            self.logger.info(f"✅ Agent debate completed: {topic}")
            return debate_result
            
        except Exception as e:
            self.logger.error(f"❌ Agent debate failed: {e}")
            raise
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                "system": "TradingAgents",
                "status": "running",
                "agents": await self.agent_manager.get_agent_status(),
                "market_data": await self.market_data.get_status(),
                "debate_system": self.debate_system.get_status(),
                "workflow_engine": self.workflow_engine.get_status()
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get system status: {e}")
            return {"status": "error", "error": str(e)}
    
    async def shutdown(self):
        """Gracefully shutdown the system"""
        self.logger.info("🔄 Shutting down TradingAgents system...")
        
        try:
            await self.workflow_engine.shutdown()
            await self.debate_system.shutdown()
            await self.agent_manager.shutdown()
            await self.market_data.shutdown()
            
            self.logger.info("✅ TradingAgents system shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")


async def run_interactive_mode(system: TradingAgentsSystem):
    """Run the system in interactive mode"""
    print("\n🤖 TradingAgents Interactive Mode")
    print("=" * 50)
    print("Available commands:")
    print("  analyze <symbol>     - Run market analysis")
    print("  trade <symbol>       - Execute trading workflow")
    print("  debate <topic>       - Start agent debate")
    print("  status               - Show system status")
    print("  help                 - Show this help")
    print("  quit                 - Exit the system")
    print("=" * 50)
    
    while True:
        try:
            command = input("\n🤖 TradingAgents> ").strip().lower()
            
            if command == "quit" or command == "exit":
                break
            elif command == "help":
                print("Available commands: analyze, trade, debate, status, help, quit")
            elif command == "status":
                status = await system.get_system_status()
                print(f"System Status: {status}")
            elif command.startswith("analyze "):
                symbol = command.split(" ", 1)[1].upper()
                result = await system.run_market_analysis(symbol)
                print(f"Analysis Result for {symbol}: {result}")
            elif command.startswith("trade "):
                symbol = command.split(" ", 1)[1].upper()
                result = await system.run_trading_workflow(symbol)
                print(f"Trading Result for {symbol}: {result}")
            elif command.startswith("debate "):
                topic = command.split(" ", 1)[1]
                result = await system.run_agent_debate(topic)
                print(f"Debate Result: {result}")
            else:
                print("Unknown command. Type 'help' for available commands.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


async def run_demo_mode(system: TradingAgentsSystem):
    """Run demonstration scenarios"""
    print("\n🎭 TradingAgents Demo Mode")
    print("=" * 50)
    
    # Demo 1: Market Analysis
    print("\n📊 Demo 1: Market Analysis")
    symbols = ["AAPL", "TSLA", "GOOGL"]
    
    for symbol in symbols:
        print(f"\n🔍 Analyzing {symbol}...")
        try:
            result = await system.run_market_analysis(symbol)
            print(f"✅ {symbol} Analysis: {result.get('summary', 'Analysis completed')}")
        except Exception as e:
            print(f"❌ {symbol} Analysis failed: {e}")
    
    # Demo 2: Agent Debate
    print("\n🎭 Demo 2: Agent Debate")
    debate_topics = [
        "Should we invest in AI stocks?",
        "Is the market overvalued?",
        "Best strategy for current market conditions?"
    ]
    
    for topic in debate_topics:
        print(f"\n💬 Debating: {topic}")
        try:
            result = await system.run_agent_debate(topic)
            print(f"✅ Debate Result: {result.get('consensus', 'Debate completed')}")
        except Exception as e:
            print(f"❌ Debate failed: {e}")
    
    # Demo 3: Trading Workflow
    print("\n⚡ Demo 3: Trading Workflow")
    print(f"\n🔄 Executing trading workflow for AAPL...")
    try:
        result = await system.run_trading_workflow("AAPL", "momentum")
        print(f"✅ Trading Workflow: {result.get('summary', 'Workflow completed')}")
    except Exception as e:
        print(f"❌ Trading workflow failed: {e}")
    
    print("\n🎉 Demo completed!")


async def main():
    """Main application entry point"""
    print("🚀 Starting TradingAgents System...")
    
    # Initialize system
    system = TradingAgentsSystem()
    
    try:
        # Initialize all components
        await system.initialize()
        
        # Check command line arguments
        if len(sys.argv) > 1:
            mode = sys.argv[1].lower()
            
            if mode == "demo":
                await run_demo_mode(system)
            elif mode == "interactive":
                await run_interactive_mode(system)
            elif mode == "analyze" and len(sys.argv) > 2:
                symbol = sys.argv[2].upper()
                result = await system.run_market_analysis(symbol)
                print(f"Analysis Result: {result}")
            elif mode == "status":
                status = await system.get_system_status()
                print(f"System Status: {status}")
            else:
                print("Usage: python main.py [demo|interactive|analyze <symbol>|status]")
        else:
            # Default to interactive mode
            await run_interactive_mode(system)
    
    except KeyboardInterrupt:
        print("\n\n👋 Shutting down...")
    except Exception as e:
        print(f"❌ System error: {e}")
        logging.exception("System error")
    finally:
        # Cleanup
        await system.shutdown()


if __name__ == "__main__":
    # Run the main application
    asyncio.run(main())
