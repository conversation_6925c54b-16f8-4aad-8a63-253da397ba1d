#!/usr/bin/env python3
"""
蜡烛图形态验证演示 - 展示TradingAgents与蜡烛图形态识别的集成
"""

import asyncio
import random
import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from integrations.candlestick_integration import CandlestickPatternValidator


def generate_sample_candle_data(symbol: str, pattern_type: str = "doji") -> list:
    """生成示例蜡烛图数据"""
    base_price = 100.0 + random.uniform(-20, 20)
    candles = []
    
    # 生成基础蜡烛线数据
    for i in range(20):
        date = datetime.now() - timedelta(days=20-i)
        
        if i < 17:  # 前面的正常蜡烛线
            open_price = base_price + random.uniform(-2, 2)
            close_price = open_price + random.uniform(-3, 3)
            high_price = max(open_price, close_price) + random.uniform(0, 1)
            low_price = min(open_price, close_price) - random.uniform(0, 1)
            volume = random.randint(100000, 500000)
        else:  # 最后几根蜡烛线形成特定形态
            if pattern_type == "doji":
                open_price = base_price
                close_price = open_price + random.uniform(-0.1, 0.1)  # 几乎相等
                high_price = open_price + random.uniform(1, 2)
                low_price = open_price - random.uniform(1, 2)
                volume = random.randint(200000, 800000)
            
            elif pattern_type == "hammer":
                open_price = base_price + 1
                close_price = open_price + random.uniform(-0.5, 0.5)
                high_price = max(open_price, close_price) + 0.3
                low_price = min(open_price, close_price) - 3  # 长下影线
                volume = random.randint(300000, 900000)
            
            elif pattern_type == "engulfing_bullish":
                if i == 17:  # 第一根阴线
                    open_price = base_price + 2
                    close_price = base_price
                    high_price = open_price + 0.5
                    low_price = close_price - 0.5
                    volume = random.randint(200000, 600000)
                else:  # 第二根阳线（吞没）
                    open_price = base_price - 0.5
                    close_price = base_price + 3
                    high_price = close_price + 0.3
                    low_price = open_price - 0.3
                    volume = random.randint(400000, 1000000)
            
            else:  # 默认随机
                open_price = base_price + random.uniform(-2, 2)
                close_price = open_price + random.uniform(-3, 3)
                high_price = max(open_price, close_price) + random.uniform(0, 1)
                low_price = min(open_price, close_price) - random.uniform(0, 1)
                volume = random.randint(100000, 500000)
        
        candle = {
            'timestamp': date.isoformat(),
            'open': round(open_price, 2),
            'high': round(high_price, 2),
            'low': round(low_price, 2),
            'close': round(close_price, 2),
            'volume': volume
        }
        candles.append(candle)
        base_price = close_price  # 下一根蜡烛线的基准价格
    
    return candles


async def demo_single_pattern_validation():
    """演示单个形态验证"""
    print("🕯️ 蜡烛图形态验证演示 - 单个形态")
    print("=" * 60)
    
    # 创建验证器
    validator = CandlestickPatternValidator()
    
    try:
        # 演示不同形态的验证
        test_cases = [
            {
                'pattern_name': 'doji',
                'symbol': 'AAPL',
                'pattern_type': 'doji',
                'confidence': 0.85
            },
            {
                'pattern_name': 'hammer',
                'symbol': 'TSLA',
                'pattern_type': 'hammer',
                'confidence': 0.78
            },
            {
                'pattern_name': 'engulfing_bullish',
                'symbol': 'GOOGL',
                'pattern_type': 'engulfing_bullish',
                'confidence': 0.92
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n🎬 测试案例 {i}: {case['pattern_name']} ({case['symbol']})")
            print("-" * 50)
            
            # 生成示例数据
            candle_data = generate_sample_candle_data(case['symbol'], case['pattern_type'])
            
            print(f"📊 生成了{len(candle_data)}根蜡烛线数据")
            print(f"🎯 检测到形态: {case['pattern_name']} (置信度: {case['confidence']:.1%})")
            
            # 验证形态
            print(f"🤖 启动多智能体验证...")
            validation_result = await validator.validate_pattern(
                pattern_name=case['pattern_name'],
                symbol=case['symbol'],
                candle_data=candle_data,
                pattern_confidence=case['confidence']
            )
            
            # 显示验证结果
            if validation_result.get('validation_status') == 'completed':
                final_assessment = validation_result.get('final_assessment', {})
                
                print(f"✅ 验证完成!")
                print(f"   验证结论: {final_assessment.get('validation_conclusion', 'unknown')}")
                print(f"   可靠性等级: {final_assessment.get('reliability_level', 'unknown')}")
                print(f"   最终验证分数: {final_assessment.get('final_validation_score', 0):.1%}")
                print(f"   置信度提升: {final_assessment.get('validation_improvement', 0):+.1%}")
                
                # 显示建议
                recommendations = final_assessment.get('recommendations', [])
                if recommendations:
                    print(f"   💡 建议: {recommendations[0]}")
                
                # 显示风险提示
                risk_warnings = final_assessment.get('risk_warnings', [])
                if risk_warnings:
                    print(f"   ⚠️ 风险提示: {'; '.join(risk_warnings)}")
                
            else:
                print(f"❌ 验证失败: {validation_result.get('error', 'Unknown error')}")
            
            if i < len(test_cases):
                print(f"\n⏳ 准备下一个测试...")
                await asyncio.sleep(2)
    
    finally:
        await validator.shutdown()


async def demo_batch_validation():
    """演示批量形态验证"""
    print("\n\n📊 蜡烛图形态验证演示 - 批量验证")
    print("=" * 60)
    
    # 创建验证器
    validator = CandlestickPatternValidator()
    
    try:
        # 准备批量验证数据
        patterns_to_validate = [
            {
                'pattern_name': 'doji',
                'symbol': 'AAPL',
                'candle_data': generate_sample_candle_data('AAPL', 'doji'),
                'confidence': 0.82
            },
            {
                'pattern_name': 'hammer',
                'symbol': 'MSFT',
                'candle_data': generate_sample_candle_data('MSFT', 'hammer'),
                'confidence': 0.75
            },
            {
                'pattern_name': 'engulfing_bullish',
                'symbol': 'NVDA',
                'candle_data': generate_sample_candle_data('NVDA', 'engulfing_bullish'),
                'confidence': 0.88
            },
            {
                'pattern_name': 'shooting_star',
                'symbol': 'AMD',
                'candle_data': generate_sample_candle_data('AMD', 'random'),
                'confidence': 0.65
            }
        ]
        
        print(f"🔄 开始批量验证{len(patterns_to_validate)}个形态...")
        
        # 执行批量验证
        validation_results = await validator.batch_validate_patterns(patterns_to_validate)
        
        print(f"✅ 批量验证完成!")
        
        # 生成验证摘要
        summary = await validator.get_validation_summary(validation_results)
        
        print(f"\n📋 验证摘要:")
        print(f"   总形态数: {summary['total_patterns']}")
        print(f"   确认形态: {summary['confirmed_patterns']}")
        print(f"   可能形态: {summary['probable_patterns']}")
        print(f"   存疑形态: {summary['questionable_patterns']}")
        print(f"   验证失败: {summary['failed_validations']}")
        print(f"   平均验证分数: {summary['average_validation_score']:.1%}")
        
        # 显示高质量形态
        top_patterns = summary.get('top_patterns', [])
        if top_patterns:
            print(f"\n🌟 高质量形态:")
            for pattern in top_patterns[:3]:
                print(f"   - {pattern['symbol']}: {pattern['pattern']} (分数: {pattern['score']:.1%})")
        
        # 显示风险形态
        risk_patterns = summary.get('risk_patterns', [])
        if risk_patterns:
            print(f"\n⚠️ 风险形态:")
            for pattern in risk_patterns[:3]:
                print(f"   - {pattern['symbol']}: {pattern['pattern']} (分数: {pattern['score']:.1%})")
        
        # 详细结果
        print(f"\n📝 详细验证结果:")
        for result in validation_results:
            if result.get('validation_status') == 'completed':
                final_assessment = result.get('final_assessment', {})
                print(f"   {result['symbol']} {result['pattern_name']}: "
                      f"{final_assessment.get('validation_conclusion', 'unknown')} "
                      f"({final_assessment.get('final_validation_score', 0):.1%})")
            else:
                print(f"   {result.get('symbol', 'unknown')} {result.get('pattern_name', 'unknown')}: 验证失败")
    
    finally:
        await validator.shutdown()


async def demo_integration_workflow():
    """演示完整的集成工作流"""
    print("\n\n🔗 完整集成工作流演示")
    print("=" * 60)
    
    print("💡 这个演示展示了如何将TradingAgents集成到蜡烛图形态识别系统中:")
    print("   1. 🔍 形态识别系统检测到蜡烛图形态")
    print("   2. 🤖 TradingAgents多智能体系统验证形态")
    print("   3. 🎭 智能体进行结构化辩论")
    print("   4. 📊 生成综合验证结果和投资建议")
    print("   5. ✅ 为用户提供可靠的交易信号")
    
    # 模拟形态识别系统的输出
    detected_pattern = {
        'pattern_name': 'morning_star',
        'symbol': 'AAPL',
        'detection_time': datetime.now().isoformat(),
        'confidence': 0.87,
        'location': 'recent_candles',
        'candle_data': generate_sample_candle_data('AAPL', 'random')
    }
    
    print(f"\n🔍 模拟形态识别系统输出:")
    print(f"   检测到形态: {detected_pattern['pattern_name']}")
    print(f"   股票代码: {detected_pattern['symbol']}")
    print(f"   识别置信度: {detected_pattern['confidence']:.1%}")
    print(f"   检测时间: {detected_pattern['detection_time']}")
    
    # 调用TradingAgents验证
    print(f"\n🤖 调用TradingAgents多智能体验证系统...")
    
    validator = CandlestickPatternValidator()
    
    try:
        validation_result = await validator.validate_pattern(
            pattern_name=detected_pattern['pattern_name'],
            symbol=detected_pattern['symbol'],
            candle_data=detected_pattern['candle_data'],
            pattern_confidence=detected_pattern['confidence']
        )
        
        if validation_result.get('validation_status') == 'completed':
            final_assessment = validation_result.get('final_assessment', {})
            
            print(f"✅ 多智能体验证完成!")
            print(f"\n📊 验证结果:")
            print(f"   原始置信度: {detected_pattern['confidence']:.1%}")
            print(f"   验证后分数: {final_assessment.get('final_validation_score', 0):.1%}")
            print(f"   置信度变化: {final_assessment.get('validation_improvement', 0):+.1%}")
            print(f"   验证结论: {final_assessment.get('validation_conclusion', 'unknown')}")
            print(f"   可靠性等级: {final_assessment.get('reliability_level', 'unknown')}")
            
            # 显示各组件贡献
            components = final_assessment.get('validation_components', {})
            print(f"\n🔧 验证组件贡献:")
            print(f"   蜡烛图专家: {components.get('candlestick_expert', 0):.1%}")
            print(f"   多智能体共识: {components.get('multi_agent_consensus', 0):.1%}")
            print(f"   辩论结果: {components.get('debate_outcome', 0):.1%}")
            
            # 显示建议和风险提示
            recommendations = final_assessment.get('recommendations', [])
            risk_warnings = final_assessment.get('risk_warnings', [])
            
            if recommendations:
                print(f"\n💡 投资建议:")
                for rec in recommendations:
                    print(f"   - {rec}")
            
            if risk_warnings:
                print(f"\n⚠️ 风险提示:")
                for warning in risk_warnings:
                    print(f"   - {warning}")
            
            print(f"\n🎯 集成效果:")
            improvement = final_assessment.get('validation_improvement', 0)
            if improvement > 0.1:
                print(f"   ✅ 多智能体验证显著提升了形态可靠性 (+{improvement:.1%})")
            elif improvement > 0:
                print(f"   ✅ 多智能体验证提升了形态可靠性 (+{improvement:.1%})")
            elif improvement < -0.1:
                print(f"   ⚠️ 多智能体验证发现形态存在风险 ({improvement:.1%})")
            else:
                print(f"   📊 多智能体验证确认了原始判断")
        
        else:
            print(f"❌ 验证失败: {validation_result.get('error', 'Unknown error')}")
    
    finally:
        await validator.shutdown()


async def main():
    """主演示函数"""
    print("🚀 TradingAgents 蜡烛图形态验证集成演示")
    print("=" * 80)
    print("💡 展示如何将多智能体系统集成到蜡烛图形态识别中")
    print("🎯 为每个识别的形态提供AI智能体验证和投资建议")
    print()
    
    try:
        # 演示1: 单个形态验证
        await demo_single_pattern_validation()
        
        # 演示2: 批量验证
        await demo_batch_validation()
        
        # 演示3: 完整集成工作流
        await demo_integration_workflow()
        
        print(f"\n🎉 演示完成!")
        print(f"💡 集成优势:")
        print(f"   ✅ 多角度验证提高形态识别准确性")
        print(f"   ✅ AI智能体提供专业投资建议")
        print(f"   ✅ 结构化辩论减少决策偏见")
        print(f"   ✅ 风险评估和管理建议")
        print(f"   ✅ 可扩展的验证框架")
        
    except KeyboardInterrupt:
        print(f"\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
