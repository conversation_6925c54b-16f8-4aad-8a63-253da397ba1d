#!/usr/bin/env python3
"""
蜡烛图形态验证集成示例 - 简单易用的集成接口
"""

import asyncio
from datetime import datetime
from integrations.candlestick_integration import validate_candlestick_pattern


async def simple_pattern_validation_example():
    """简单的形态验证示例"""
    print("🕯️ 简单蜡烛图形态验证示例")
    print("=" * 50)
    
    # 示例蜡烛图数据（通常来自您的形态识别系统）
    sample_candle_data = [
        {
            'timestamp': '2024-01-15T09:30:00',
            'open': 150.00,
            'high': 152.50,
            'low': 149.00,
            'close': 151.20,
            'volume': 1000000
        },
        {
            'timestamp': '2024-01-15T10:30:00',
            'open': 151.20,
            'high': 151.50,
            'low': 148.80,
            'close': 149.50,
            'volume': 1200000
        },
        {
            'timestamp': '2024-01-15T11:30:00',
            'open': 149.50,
            'high': 150.00,
            'low': 149.40,
            'close': 149.60,
            'volume': 800000
        }
        # ... 更多蜡烛线数据
    ]
    
    print("📊 输入数据:")
    print(f"   股票代码: AAPL")
    print(f"   检测形态: doji")
    print(f"   原始置信度: 85%")
    print(f"   蜡烛线数量: {len(sample_candle_data)}")
    
    print("\n🤖 启动AI智能体验证...")
    
    # 调用验证函数
    result = await validate_candlestick_pattern(
        pattern_name="doji",
        symbol="AAPL", 
        candle_data=sample_candle_data,
        confidence=0.85
    )
    
    # 处理结果
    if result.get('validation_status') == 'completed':
        final_assessment = result.get('final_assessment', {})
        
        print("✅ 验证完成!")
        print(f"\n📋 验证结果:")
        print(f"   验证结论: {final_assessment.get('validation_conclusion', 'unknown')}")
        print(f"   可靠性等级: {final_assessment.get('reliability_level', 'unknown')}")
        print(f"   最终分数: {final_assessment.get('final_validation_score', 0):.1%}")
        print(f"   置信度提升: {final_assessment.get('validation_improvement', 0):+.1%}")
        
        # 显示建议
        recommendations = final_assessment.get('recommendations', [])
        if recommendations:
            print(f"\n💡 AI建议:")
            for rec in recommendations:
                print(f"   - {rec}")
        
        # 显示风险提示
        risk_warnings = final_assessment.get('risk_warnings', [])
        if risk_warnings:
            print(f"\n⚠️ 风险提示:")
            for warning in risk_warnings:
                print(f"   - {warning}")
    
    else:
        print(f"❌ 验证失败: {result.get('error', 'Unknown error')}")
    
    return result


def integration_guide():
    """集成指南"""
    print("\n\n📖 TradingAgents 蜡烛图集成指南")
    print("=" * 60)
    
    print("🔗 如何集成到您的蜡烛图形态识别系统:")
    print()
    
    print("1️⃣ 基本集成步骤:")
    print("   ```python")
    print("   from integrations.candlestick_integration import validate_candlestick_pattern")
    print()
    print("   # 当您的系统检测到形态时")
    print("   result = await validate_candlestick_pattern(")
    print("       pattern_name='detected_pattern',")
    print("       symbol='STOCK_SYMBOL',")
    print("       candle_data=your_candle_data,")
    print("       confidence=detection_confidence")
    print("   )")
    print("   ```")
    print()
    
    print("2️⃣ 数据格式要求:")
    print("   蜡烛图数据应包含以下字段:")
    print("   - timestamp: 时间戳")
    print("   - open: 开盘价")
    print("   - high: 最高价") 
    print("   - low: 最低价")
    print("   - close: 收盘价")
    print("   - volume: 成交量")
    print()
    
    print("3️⃣ 支持的形态类型:")
    print("   - 反转形态: doji, hammer, hanging_man, shooting_star")
    print("   - 吞没形态: engulfing_bullish, engulfing_bearish")
    print("   - 星形形态: morning_star, evening_star")
    print("   - 其他经典形态: harami, piercing_pattern, dark_cloud_cover")
    print()
    
    print("4️⃣ 返回结果解读:")
    print("   - validation_conclusion: confirmed/probable/questionable")
    print("   - reliability_level: high/medium/low")
    print("   - final_validation_score: 0.0-1.0 最终验证分数")
    print("   - recommendations: AI投资建议")
    print("   - risk_warnings: 风险提示")
    print()
    
    print("5️⃣ 集成优势:")
    print("   ✅ 多智能体验证提高准确性")
    print("   ✅ 专业投资建议和风险评估")
    print("   ✅ 结构化辩论减少偏见")
    print("   ✅ 基于《日本蜡烛图技术》理论")
    print("   ✅ 支持批量验证")
    print()
    
    print("6️⃣ 实际应用场景:")
    print("   📊 实时形态验证: 交易软件中的形态确认")
    print("   📈 历史回测: 验证形态的历史有效性")
    print("   🎯 投资决策: 为投资者提供AI增强的建议")
    print("   🔍 形态筛选: 从大量形态中筛选高质量信号")
    print("   📚 教育培训: 帮助学习者理解形态含义")


async def main():
    """主函数"""
    print("🚀 TradingAgents 蜡烛图形态验证集成")
    print("🎯 为蜡烛图形态识别提供AI智能体验证")
    print()
    
    try:
        # 运行简单示例
        await simple_pattern_validation_example()
        
        # 显示集成指南
        integration_guide()
        
        print("\n🎉 集成演示完成!")
        print("💡 现在您可以将TradingAgents集成到您的蜡烛图形态识别系统中")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
