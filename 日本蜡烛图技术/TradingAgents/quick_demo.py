#!/usr/bin/env python3
"""
Quick TradingAgents Demo
"""

print("🤖 TradingAgents - Multi-Agent Trading System")
print("=" * 50)

# Simulate agent initialization
agents = [
    {"name": "MarketAnalyst", "specialization": "technical_analysis"},
    {"name": "<PERSON>Researcher", "specialization": "bullish_analysis"},
    {"name": "BearResearcher", "specialization": "bearish_analysis"},
    {"name": "PortfolioManager", "specialization": "risk_management"},
    {"name": "DebateModerator", "specialization": "consensus_building"}
]

print(f"✅ System initialized with {len(agents)} agents:")
for agent in agents:
    print(f"   - {agent['name']} ({agent['specialization']})")

print("\n📊 Simulating market analysis for AAPL...")

# Simulate analysis results
analysis_results = [
    {"agent": "MarketAnalyst", "signal": "buy", "confidence": 0.75},
    {"agent": "BullResearcher", "signal": "buy", "confidence": 0.85},
    {"agent": "BearResearcher", "signal": "sell", "confidence": 0.65},
    {"agent": "PortfolioManager", "signal": "hold", "confidence": 0.70},
    {"agent": "DebateModerator", "signal": "buy", "confidence": 0.60}
]

for result in analysis_results:
    print(f"🤖 {result['agent']}: {result['signal'].upper()} (confidence: {result['confidence']:.0%})")

# Calculate consensus
buy_votes = sum(1 for r in analysis_results if r['signal'] == 'buy')
sell_votes = sum(1 for r in analysis_results if r['signal'] == 'sell')
hold_votes = sum(1 for r in analysis_results if r['signal'] == 'hold')

print(f"\n🎯 Consensus Analysis:")
print(f"   Buy votes: {buy_votes}")
print(f"   Sell votes: {sell_votes}")
print(f"   Hold votes: {hold_votes}")

if buy_votes > sell_votes and buy_votes > hold_votes:
    decision = "BUY"
elif sell_votes > buy_votes and sell_votes > hold_votes:
    decision = "SELL"
else:
    decision = "HOLD"

print(f"   Final Decision: {decision}")

print("\n🎭 Simulating agent debate...")
print("   Round 1: Agents present initial arguments")
print("   Round 2: Agents respond to counterarguments")
print("   Round 3: Consensus building")

print(f"\n✅ Demo completed successfully!")
print(f"\n💡 This demonstrates the TradingAgents system architecture:")
print(f"   - Multiple specialized AI agents")
print(f"   - Collaborative analysis and decision making")
print(f"   - Structured debate and consensus building")
print(f"   - Risk-aware portfolio management")

print(f"\n🚀 The full system includes:")
print(f"   - Real market data integration")
print(f"   - Advanced LLM-powered agents")
print(f"   - Comprehensive technical analysis")
print(f"   - Risk management and position sizing")
print(f"   - Trade execution capabilities")

print(f"\n📁 Project structure created successfully!")
print(f"   - Core agent framework ✅")
print(f"   - Multi-agent coordination ✅")
print(f"   - Debate and consensus system ✅")
print(f"   - Market data integration ✅")
print(f"   - Configuration management ✅")

print(f"\n🎉 TradingAgents system is ready for development!")
