# 🤖 TradingAgents - Multi-Agent Trading System

> A sophisticated multi-agent trading system inspired by TauricResearch/TradingAgents, implementing collaborative AI agents for financial market analysis and trading decisions.

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-009688.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

## 🎯 Project Overview

TradingAgents is a cutting-edge multi-agent system that simulates a professional trading team using AI agents. Each agent has specialized roles and capabilities, working together to analyze markets, debate investment decisions, and execute trades.

### 🌟 Core Features

- 🤖 **Multi-Agent Architecture** - Specialized AI agents with distinct roles
- 🎭 **Agent Debate System** - Collaborative decision-making through structured debates
- 📊 **Real-time Market Analysis** - Live data integration and technical analysis
- 🧠 **LLM Integration** - GPT-4 powered intelligent reasoning
- 🔄 **Workflow Management** - Structured trading processes and decision trees
- 📈 **Risk Management** - Automated risk assessment and position sizing
- 🎯 **Performance Tracking** - Comprehensive analytics and reporting

## 🏗️ System Architecture

### 🤖 Agent Roles

1. **📊 Market Analyst** - Technical and fundamental analysis
2. **🐂 Bull Researcher** - Identifies bullish opportunities and catalysts
3. **🐻 Bear Researcher** - Identifies risks and bearish signals
4. **💼 Portfolio Manager** - Position sizing and risk management
5. **🎭 Debate Moderator** - Facilitates agent discussions and consensus
6. **⚡ Execution Agent** - Trade execution and order management

### 🔄 Workflow Process

```
📊 Market Data → 🤖 Agent Analysis → 🎭 Debate Session → 💼 Risk Assessment → ⚡ Execution
```

## 🚀 Quick Start

### 📋 Prerequisites

- Python 3.8+
- OpenAI API Key (for GPT-4 integration)
- Market data provider access (Alpha Vantage, Yahoo Finance, etc.)

### ⚡ Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd TradingAgents
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your API keys
```

4. **Run the system**
```bash
python main.py
```

## 📁 Project Structure

```
TradingAgents/
├── 📁 agents/                    # AI Agent implementations
│   ├── base/                     # Base agent classes
│   ├── market_analyst.py         # Market analysis agent
│   ├── bull_researcher.py        # Bullish research agent
│   ├── bear_researcher.py        # Bearish research agent
│   ├── portfolio_manager.py      # Portfolio management agent
│   ├── debate_moderator.py       # Debate facilitation agent
│   └── execution_agent.py        # Trade execution agent
├── 📁 core/                      # Core system components
│   ├── agent_manager.py          # Agent lifecycle management
│   ├── debate_system.py          # Debate orchestration
│   ├── workflow_engine.py        # Workflow management
│   └── communication.py          # Inter-agent communication
├── 📁 data/                      # Data management
│   ├── market_data.py            # Market data providers
│   ├── portfolio_data.py         # Portfolio tracking
│   └── historical_data.py        # Historical analysis
├── 📁 strategies/                # Trading strategies
│   ├── momentum.py               # Momentum strategies
│   ├── mean_reversion.py         # Mean reversion strategies
│   └── arbitrage.py              # Arbitrage strategies
├── 📁 risk/                      # Risk management
│   ├── position_sizing.py        # Position size calculation
│   ├── risk_metrics.py           # Risk measurement
│   └── stop_loss.py              # Stop loss management
├── 📁 execution/                 # Trade execution
│   ├── order_manager.py          # Order management
│   ├── broker_interface.py       # Broker connectivity
│   └── slippage_model.py         # Execution cost modeling
├── 📁 utils/                     # Utility functions
│   ├── logging.py                # Logging configuration
│   ├── config.py                 # Configuration management
│   └── helpers.py                # Helper functions
├── 📁 tests/                     # Test suite
├── 📁 docs/                      # Documentation
├── 📄 main.py                    # Main application entry
├── 📄 requirements.txt           # Python dependencies
├── 📄 .env.example               # Environment template
└── 📄 config.yaml                # System configuration
```

## 🤖 Agent System

### 📊 Market Analyst Agent

```python
class MarketAnalyst(BaseAgent):
    """
    Performs comprehensive market analysis including:
    - Technical indicator analysis
    - Chart pattern recognition
    - Market sentiment analysis
    - Economic data interpretation
    """
    
    async def analyze_market(self, symbol: str) -> AnalysisResult:
        # Implementation details
        pass
```

### 🎭 Debate System

```python
class DebateSystem:
    """
    Orchestrates structured debates between agents:
    - Presents market scenarios
    - Facilitates agent arguments
    - Reaches consensus decisions
    - Documents reasoning process
    """
    
    async def conduct_debate(self, topic: str, participants: List[Agent]) -> DebateResult:
        # Implementation details
        pass
```

## 🔧 Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4

# Market Data
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
YAHOO_FINANCE_ENABLED=true

# Trading Configuration
RISK_TOLERANCE=0.02
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.05

# Logging
LOG_LEVEL=INFO
LOG_FILE=trading_agents.log
```

## 🎯 Usage Examples

### Basic Market Analysis

```python
from agents.market_analyst import MarketAnalyst
from core.agent_manager import AgentManager

# Initialize agent manager
agent_manager = AgentManager()

# Create market analyst
analyst = MarketAnalyst(name="TechAnalyst", model="gpt-4")

# Perform analysis
result = await analyst.analyze_market("AAPL")
print(f"Analysis: {result.summary}")
print(f"Recommendation: {result.recommendation}")
```

### Multi-Agent Debate

```python
from core.debate_system import DebateSystem
from agents import BullResearcher, BearResearcher

# Initialize debate system
debate_system = DebateSystem()

# Create agents
bull_agent = BullResearcher(name="BullAnalyst")
bear_agent = BearResearcher(name="BearAnalyst")

# Conduct debate
debate_result = await debate_system.conduct_debate(
    topic="Should we buy TSLA?",
    participants=[bull_agent, bear_agent]
)

print(f"Consensus: {debate_result.consensus}")
print(f"Final Decision: {debate_result.decision}")
```

## 📊 Performance Metrics

The system tracks comprehensive performance metrics:

- **Return Metrics**: Total return, Sharpe ratio, Sortino ratio
- **Risk Metrics**: Maximum drawdown, VaR, volatility
- **Trade Metrics**: Win rate, average trade duration, profit factor
- **Agent Metrics**: Decision accuracy, debate participation, consensus rate

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
python -m pytest tests/

# Run specific test categories
python -m pytest tests/agents/
python -m pytest tests/core/
python -m pytest tests/strategies/

# Run with coverage
python -m pytest tests/ --cov=.
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Inspired by TauricResearch/TradingAgents
- Built with OpenAI GPT-4
- Market data provided by Alpha Vantage and Yahoo Finance

---

<div align="center">

**🚀 Ready to revolutionize your trading with AI agents?**

</div>
