#!/usr/bin/env python3
"""
TradingAgents LLM Demo - Test with real DeepSeek-V3 API
"""

import asyncio
import os
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.llm_client import LLMClient


class LLMTradingDemo:
    """Demo using real LLM for trading analysis"""
    
    def __init__(self):
        """Initialize demo with LLM client"""
        # Configure LLM
        llm_config = {
            'provider': 'deepseek',
            'model': 'deepseek-v3',
            'temperature': 0.3,
            'max_tokens': 1500,
            'timeout': 30,
            'retry_attempts': 3
        }
        
        self.llm_client = LLMClient(llm_config)
        print("🤖 TradingAgents LLM Demo - DeepSeek-V3 Integration")
        print("=" * 60)
    
    async def test_llm_connection(self):
        """Test LLM connection"""
        print("🔗 Testing LLM connection...")
        
        health = await self.llm_client.health_check()
        
        if health['status'] == 'healthy':
            print(f"✅ LLM connection successful!")
            print(f"   Provider: {health['provider']}")
            print(f"   Model: {health['model']}")
            print(f"   Test response: {health.get('test_successful', False)}")
        else:
            print(f"❌ LLM connection failed: {health.get('error', 'Unknown error')}")
            return False
        
        return True
    
    async def analyze_stock_with_llm(self, symbol: str):
        """Analyze stock using LLM"""
        print(f"\n📊 LLM Analysis for {symbol}")
        print("-" * 40)
        
        # Market Analyst perspective
        market_prompt = f"""
        As a professional market analyst, analyze {symbol} stock. Consider:
        1. Technical indicators and chart patterns
        2. Market sentiment and trends
        3. Risk factors and opportunities
        4. Investment recommendation (BUY/SELL/HOLD)
        
        Provide a concise analysis with your recommendation and confidence level.
        """
        
        system_prompt = "You are a professional market analyst with expertise in technical analysis and market trends."
        
        try:
            print("🤖 MarketAnalyst (DeepSeek-V3) analyzing...")
            response = await self.llm_client.generate_response(market_prompt, system_prompt)
            
            print(f"📈 Analysis Result:")
            print(f"   {response['content']}")
            print(f"   Model: {response['model']}")
            print(f"   Provider: {response['provider']}")
            
            return response
            
        except Exception as e:
            print(f"❌ Analysis failed: {e}")
            return None
    
    async def conduct_llm_debate(self, topic: str):
        """Conduct debate using LLM with different perspectives"""
        print(f"\n🎭 LLM Agent Debate: {topic}")
        print("-" * 50)
        
        # Define different agent perspectives
        agents = [
            {
                'name': 'BullResearcher',
                'system_prompt': 'You are a bullish research analyst who identifies growth opportunities and positive catalysts.',
                'prompt': f'Argue for a bullish position on {topic}. Focus on growth potential and positive factors.'
            },
            {
                'name': 'BearResearcher', 
                'system_prompt': 'You are a bearish research analyst who identifies risks and potential downsides.',
                'prompt': f'Argue for a bearish position on {topic}. Focus on risks and potential negative factors.'
            },
            {
                'name': 'PortfolioManager',
                'system_prompt': 'You are a portfolio manager focused on risk management and balanced decisions.',
                'prompt': f'Provide a balanced risk management perspective on {topic}. Consider portfolio allocation and risk factors.'
            }
        ]
        
        debate_results = []
        
        for agent in agents:
            try:
                print(f"\n🗣️ {agent['name']} (DeepSeek-V3):")
                response = await self.llm_client.generate_response(
                    agent['prompt'], 
                    agent['system_prompt']
                )
                
                print(f"   {response['content'][:200]}...")
                debate_results.append({
                    'agent': agent['name'],
                    'response': response['content'],
                    'success': True
                })
                
            except Exception as e:
                print(f"   ❌ {agent['name']} failed: {e}")
                debate_results.append({
                    'agent': agent['name'],
                    'error': str(e),
                    'success': False
                })
        
        return debate_results
    
    async def generate_investment_decision(self, symbol: str, debate_results: list):
        """Generate final investment decision based on debate"""
        print(f"\n🎯 Final Investment Decision for {symbol}")
        print("-" * 40)
        
        # Summarize debate results
        successful_analyses = [r for r in debate_results if r.get('success', False)]
        
        if not successful_analyses:
            print("❌ No successful analyses available for decision making")
            return None
        
        # Create synthesis prompt
        synthesis_prompt = f"""
        Based on the following expert analyses for {symbol}, provide a final investment recommendation:
        
        """
        
        for result in successful_analyses:
            synthesis_prompt += f"\n{result['agent']}: {result['response'][:300]}...\n"
        
        synthesis_prompt += """
        
        Synthesize these viewpoints and provide:
        1. Final recommendation (BUY/SELL/HOLD)
        2. Confidence level (1-10)
        3. Key reasoning
        4. Risk assessment
        5. Suggested position size (% of portfolio)
        """
        
        system_prompt = "You are a senior investment advisor who synthesizes multiple expert opinions to make final investment decisions."
        
        try:
            print("🤖 Investment Advisor (DeepSeek-V3) synthesizing...")
            response = await self.llm_client.generate_response(synthesis_prompt, system_prompt)
            
            print(f"📋 Final Decision:")
            print(f"   {response['content']}")
            
            return response
            
        except Exception as e:
            print(f"❌ Decision synthesis failed: {e}")
            return None
    
    async def run_complete_demo(self):
        """Run complete LLM trading demo"""
        print("🚀 Starting Complete LLM Trading Demo")
        print("=" * 60)
        
        # Test connection
        if not await self.test_llm_connection():
            print("❌ Cannot proceed without LLM connection")
            return
        
        # Demo symbols
        symbols = ["AAPL", "TSLA"]
        
        for symbol in symbols:
            print(f"\n🎬 Demo Scenario: {symbol} Investment Analysis")
            print("=" * 60)
            
            try:
                # Step 1: Individual analysis
                analysis = await self.analyze_stock_with_llm(symbol)
                
                # Step 2: Multi-agent debate
                debate_results = await self.conduct_llm_debate(f"investing in {symbol}")
                
                # Step 3: Final decision
                decision = await self.generate_investment_decision(symbol, debate_results)
                
                print(f"\n✅ {symbol} analysis completed successfully!")
                
            except Exception as e:
                print(f"❌ {symbol} analysis failed: {e}")
            
            if symbol != symbols[-1]:
                print(f"\n⏳ Preparing next analysis...")
                await asyncio.sleep(2)
        
        print(f"\n🎉 LLM Trading Demo completed!")
        print(f"💡 This demonstrates real AI-powered investment analysis using DeepSeek-V3")


async def main():
    """Main demo function"""
    try:
        demo = LLMTradingDemo()
        await demo.run_complete_demo()
    except KeyboardInterrupt:
        print(f"\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
