#!/bin/bash

# TradingAgents Startup Script

echo "🤖 TradingAgents - Multi-Agent Trading System"
echo "=============================================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed"
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "📥 Installing dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs
mkdir -p data
mkdir -p backups

# Copy environment template if .env doesn't exist
if [ ! -f ".env" ]; then
    echo "⚙️ Creating environment configuration..."
    cp .env.example .env
    echo "📝 Please edit .env file with your API keys and configuration"
fi

# Run the application based on argument
case "$1" in
    "test")
        echo "🧪 Running system tests..."
        python3 test_system.py
        ;;
    "quick-test")
        echo "⚡ Running quick test..."
        python3 test_system.py quick
        ;;
    "demo")
        echo "🎭 Running demo mode..."
        python3 main.py demo
        ;;
    "interactive")
        echo "💬 Starting interactive mode..."
        python3 main.py interactive
        ;;
    "analyze")
        if [ -z "$2" ]; then
            echo "❌ Please provide a symbol to analyze"
            echo "Usage: ./run.sh analyze AAPL"
            exit 1
        fi
        echo "📊 Analyzing $2..."
        python3 main.py analyze "$2"
        ;;
    "status")
        echo "📋 Checking system status..."
        python3 main.py status
        ;;
    *)
        echo "🚀 Available commands:"
        echo "  ./run.sh test          - Run comprehensive tests"
        echo "  ./run.sh quick-test    - Run quick system test"
        echo "  ./run.sh demo          - Run demonstration mode"
        echo "  ./run.sh interactive   - Start interactive mode"
        echo "  ./run.sh analyze SYMBOL - Analyze specific symbol"
        echo "  ./run.sh status        - Check system status"
        echo ""
        echo "💡 Starting interactive mode by default..."
        python3 main.py interactive
        ;;
esac

echo "👋 TradingAgents session ended"
