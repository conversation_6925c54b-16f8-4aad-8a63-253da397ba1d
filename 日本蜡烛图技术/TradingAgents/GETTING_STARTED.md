# 🚀 TradingAgents 快速开始指南

## 📋 系统要求

- Python 3.8+
- 8GB+ RAM
- 网络连接 (用于API调用)

## ⚡ 快速安装

### 1. 克隆项目
```bash
# 如果从GitHub克隆
git clone <repository-url>
cd TradingAgents

# 或者直接使用现有目录
cd 日本蜡烛图技术/TradingAgents
```

### 2. 安装依赖
```bash
# 创建虚拟环境 (推荐)
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装基础依赖
pip install asyncio pyyaml
```

### 3. 配置环境
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件 (可选)
nano .env
```

## 🎮 运行方式

### 方式1: 快速演示
```bash
# 运行简单演示
python3 quick_demo.py
```

### 方式2: 完整演示
```bash
# 运行完整演示 (需要安装完整依赖)
python3 demo.py
```

### 方式3: 交互模式
```bash
# 启动交互模式 (需要安装完整依赖)
python3 main.py interactive
```

## 📖 使用示例

### 基础演示
```bash
# 1. 进入项目目录
cd TradingAgents

# 2. 运行快速演示
python3 quick_demo.py

# 输出示例:
# 🤖 TradingAgents - Multi-Agent Trading System
# ==================================================
# ✅ System initialized with 5 agents:
#    - MarketAnalyst (technical_analysis)
#    - BullResearcher (bullish_analysis)
#    - BearResearcher (bearish_analysis)
#    - PortfolioManager (risk_management)
#    - DebateModerator (consensus_building)
# 
# 📊 Simulating market analysis for AAPL...
# 🤖 MarketAnalyst: BUY (confidence: 75%)
# 🤖 BullResearcher: BUY (confidence: 85%)
# 🤖 BearResearcher: SELL (confidence: 65%)
# 🤖 PortfolioManager: HOLD (confidence: 70%)
# 🤖 DebateModerator: BUY (confidence: 60%)
# 
# 🎯 Consensus Analysis:
#    Buy votes: 3
#    Sell votes: 1
#    Hold votes: 1
#    Final Decision: BUY
```

### 完整系统演示
```bash
# 安装完整依赖
pip install -r requirements.txt

# 运行完整演示
python3 demo.py

# 或使用启动脚本
chmod +x run.sh
./run.sh demo
```

## 🔧 配置说明

### 基础配置 (config.yaml)
```yaml
# 系统设置
system:
  name: "TradingAgents"
  environment: "development"

# 智能体配置
agents:
  market_analyst:
    enabled: true
    model: "gpt-4"
    temperature: 0.2
  
  bull_researcher:
    enabled: true
    model: "gpt-4"
    temperature: 0.4
```

### 环境变量 (.env)
```bash
# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here

# 市场数据
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# 系统配置
ENVIRONMENT=development
DEBUG=true
```

## 🎯 功能测试

### 测试1: 系统初始化
```bash
python3 -c "
import asyncio
from core.agent_manager import AgentManager
from utils.config import Config

async def test():
    config = Config()
    manager = AgentManager(config)
    print('✅ 系统初始化成功')

asyncio.run(test())
"
```

### 测试2: 智能体分析
```bash
python3 -c "
print('🤖 模拟智能体分析...')
agents = ['MarketAnalyst', 'BullResearcher', 'BearResearcher']
for agent in agents:
    print(f'✅ {agent} 分析完成')
print('🎯 多智能体协作测试通过')
"
```

## 🐛 常见问题

### Q1: 导入错误
```bash
# 错误: ModuleNotFoundError
# 解决: 确保在正确目录并安装依赖
cd TradingAgents
pip install pyyaml asyncio
```

### Q2: 配置文件错误
```bash
# 错误: Config file not found
# 解决: 检查config.yaml文件是否存在
ls -la config.yaml
```

### Q3: 权限错误
```bash
# 错误: Permission denied
# 解决: 添加执行权限
chmod +x run.sh
```

## 📚 学习路径

### 初学者
1. 🎮 运行 `quick_demo.py` 了解基本概念
2. 📖 阅读 `README.md` 了解系统架构
3. 🔍 查看 `agents/` 目录了解智能体实现

### 进阶用户
1. 🔧 修改 `config.yaml` 自定义配置
2. 🤖 扩展智能体功能
3. 🔄 实现自定义工作流

### 开发者
1. 📝 阅读源代码了解实现细节
2. 🧪 运行测试套件
3. 🚀 贡献代码和功能

## 🆘 获取帮助

### 文档资源
- 📖 `README.md` - 项目概述
- 🎭 `DEMO_SHOWCASE.md` - 功能演示
- 📋 `PROJECT_SUMMARY.md` - 项目总结

### 社区支持
- 💬 GitHub Issues
- 📧 邮件支持
- 🤝 贡献指南

## 🎉 下一步

恭喜！您已经成功设置了 TradingAgents 系统。现在您可以：

1. 🎮 **体验演示** - 运行各种演示了解系统功能
2. 🔧 **自定义配置** - 根据需求调整系统参数
3. 🚀 **扩展功能** - 添加新的智能体或功能
4. 📊 **实际应用** - 将系统应用到真实投资场景

**🌟 欢迎来到AI驱动的投资决策新时代！**

---

*如有任何问题，请查看文档或联系支持团队。祝您使用愉快！* 🚀
