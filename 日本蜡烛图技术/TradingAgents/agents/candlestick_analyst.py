"""
Candlestick Analyst Agent - 专门分析蜡烛图形态的智能体
"""

import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime

from agents.base.base_agent import BaseAgent


class CandlestickAnalyst(BaseAgent):
    """
    蜡烛图分析师智能体 - 专门验证和分析蜡烛图形态
    """
    
    async def _initialize_agent(self):
        """初始化蜡烛图分析师能力"""
        self.logger.info("初始化蜡烛图分析师能力...")
        
        # 支持的蜡烛图形态
        self.supported_patterns = [
            # 反转形态
            'doji', 'hammer', 'hanging_man', 'shooting_star', 'inverted_hammer',
            'engulfing_bullish', 'engulfing_bearish', 'harami_bullish', 'harami_bearish',
            'dark_cloud_cover', 'piercing_pattern', 'morning_star', 'evening_star',
            'three_white_soldiers', 'three_black_crows',
            
            # 持续形态
            'rising_three_methods', 'falling_three_methods', 'upside_gap_three_methods',
            'downside_gap_three_methods', 'spinning_top', 'marubozu_bullish', 'marubozu_bearish',
            
            # 特殊形态
            'abandoned_baby', 'belt_hold', 'breakaway', 'concealing_baby_swallow',
            'counterattack', 'dragonfly_doji', 'gravestone_doji'
        ]
        
        # 形态可靠性等级
        self.pattern_reliability = {
            'high': ['engulfing_bullish', 'engulfing_bearish', 'morning_star', 'evening_star', 
                    'three_white_soldiers', 'three_black_crows'],
            'medium': ['hammer', 'hanging_man', 'shooting_star', 'piercing_pattern', 
                      'dark_cloud_cover', 'harami_bullish', 'harami_bearish'],
            'low': ['doji', 'spinning_top', 'inverted_hammer', 'belt_hold']
        }
        
        self.logger.info(f"✅ 蜡烛图分析师初始化完成，支持{len(self.supported_patterns)}种形态")
    
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析蜡烛图形态
        
        Args:
            symbol: 股票代码
            market_data: 包含蜡烛图数据的市场数据
            
        Returns:
            蜡烛图形态分析结果
        """
        self.logger.info(f"🕯️ 开始蜡烛图形态分析: {symbol}")
        
        try:
            analysis_result = {
                'symbol': symbol,
                'analyst': self.name,
                'analysis_type': 'candlestick_pattern',
                'timestamp': datetime.now().isoformat()
            }
            
            # 获取蜡烛图数据
            candles = market_data.get('candles', [])
            if not candles:
                return {'error': '没有蜡烛图数据可供分析'}
            
            # 检测形态
            detected_patterns = await self._detect_patterns(candles)
            analysis_result['detected_patterns'] = detected_patterns
            
            # 验证形态
            pattern_validation = await self._validate_patterns(detected_patterns, candles)
            analysis_result['pattern_validation'] = pattern_validation
            
            # 生成交易信号
            trading_signals = await self._generate_trading_signals(detected_patterns, pattern_validation)
            analysis_result['trading_signals'] = trading_signals
            
            # AI增强分析
            if self.llm_client:
                ai_analysis = await self._ai_enhanced_analysis(symbol, detected_patterns, candles)
                analysis_result['ai_analysis'] = ai_analysis
            
            # 生成总体评估
            overall_assessment = await self._generate_overall_assessment(analysis_result)
            analysis_result['overall_assessment'] = overall_assessment
            
            self.logger.info(f"✅ 蜡烛图形态分析完成: {symbol}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"❌ 蜡烛图形态分析失败: {symbol}: {e}")
            raise
    
    async def _detect_patterns(self, candles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """检测蜡烛图形态"""
        detected_patterns = []
        
        if len(candles) < 3:
            return detected_patterns
        
        # 获取最近的蜡烛线
        recent_candles = candles[-10:]  # 分析最近10根蜡烛线
        
        # 检测各种形态
        patterns_found = []
        
        # 检测Doji形态
        if self._is_doji(recent_candles[-1]):
            patterns_found.append({
                'pattern': 'doji',
                'type': 'reversal',
                'position': len(candles) - 1,
                'confidence': 0.7,
                'description': 'Doji - 市场犹豫不决的信号'
            })
        
        # 检测锤子线
        if self._is_hammer(recent_candles[-1]):
            patterns_found.append({
                'pattern': 'hammer',
                'type': 'bullish_reversal',
                'position': len(candles) - 1,
                'confidence': 0.8,
                'description': 'Hammer - 潜在的看涨反转信号'
            })
        
        # 检测上吊线
        if self._is_hanging_man(recent_candles[-1]):
            patterns_found.append({
                'pattern': 'hanging_man',
                'type': 'bearish_reversal',
                'position': len(candles) - 1,
                'confidence': 0.75,
                'description': 'Hanging Man - 潜在的看跌反转信号'
            })
        
        # 检测吞没形态
        if len(recent_candles) >= 2:
            engulfing = self._is_engulfing_pattern(recent_candles[-2], recent_candles[-1])
            if engulfing:
                patterns_found.append(engulfing)
        
        # 检测晨星形态
        if len(recent_candles) >= 3:
            morning_star = self._is_morning_star(recent_candles[-3:])
            if morning_star:
                patterns_found.append(morning_star)
        
        return patterns_found
    
    def _is_doji(self, candle: Dict[str, Any]) -> bool:
        """检测Doji形态"""
        open_price = candle['open']
        close_price = candle['close']
        high_price = candle['high']
        low_price = candle['low']
        
        body_size = abs(close_price - open_price)
        total_range = high_price - low_price
        
        # Doji的实体很小，通常小于总范围的5%
        return body_size <= total_range * 0.05 if total_range > 0 else False
    
    def _is_hammer(self, candle: Dict[str, Any]) -> bool:
        """检测锤子线形态"""
        open_price = candle['open']
        close_price = candle['close']
        high_price = candle['high']
        low_price = candle['low']
        
        body_size = abs(close_price - open_price)
        lower_shadow = min(open_price, close_price) - low_price
        upper_shadow = high_price - max(open_price, close_price)
        
        # 锤子线特征：下影线长，上影线短，实体小
        return (lower_shadow >= body_size * 2 and 
                upper_shadow <= body_size * 0.5 and
                body_size > 0)
    
    def _is_hanging_man(self, candle: Dict[str, Any]) -> bool:
        """检测上吊线形态（形状类似锤子线，但出现在上升趋势中）"""
        return self._is_hammer(candle)  # 形状相同，但含义不同
    
    def _is_engulfing_pattern(self, prev_candle: Dict[str, Any], curr_candle: Dict[str, Any]) -> Dict[str, Any]:
        """检测吞没形态"""
        prev_open = prev_candle['open']
        prev_close = prev_candle['close']
        curr_open = curr_candle['open']
        curr_close = curr_candle['close']
        
        # 看涨吞没
        if (prev_close < prev_open and  # 前一根是阴线
            curr_close > curr_open and  # 当前是阳线
            curr_open < prev_close and  # 当前开盘价低于前一根收盘价
            curr_close > prev_open):    # 当前收盘价高于前一根开盘价
            
            return {
                'pattern': 'engulfing_bullish',
                'type': 'bullish_reversal',
                'position': 'last_two_candles',
                'confidence': 0.85,
                'description': 'Bullish Engulfing - 强烈的看涨反转信号'
            }
        
        # 看跌吞没
        elif (prev_close > prev_open and  # 前一根是阳线
              curr_close < curr_open and  # 当前是阴线
              curr_open > prev_close and  # 当前开盘价高于前一根收盘价
              curr_close < prev_open):    # 当前收盘价低于前一根开盘价
            
            return {
                'pattern': 'engulfing_bearish',
                'type': 'bearish_reversal',
                'position': 'last_two_candles',
                'confidence': 0.85,
                'description': 'Bearish Engulfing - 强烈的看跌反转信号'
            }
        
        return None
    
    def _is_morning_star(self, three_candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """检测晨星形态"""
        if len(three_candles) != 3:
            return None
        
        first, second, third = three_candles
        
        # 晨星特征：
        # 1. 第一根是长阴线
        # 2. 第二根是小实体（星线）
        # 3. 第三根是长阳线
        
        first_body = abs(first['close'] - first['open'])
        second_body = abs(second['close'] - second['open'])
        third_body = abs(third['close'] - third['open'])
        
        first_range = first['high'] - first['low']
        second_range = second['high'] - second['low']
        third_range = third['high'] - third['low']
        
        if (first['close'] < first['open'] and  # 第一根是阴线
            first_body >= first_range * 0.6 and  # 第一根是长实体
            second_body <= second_range * 0.3 and  # 第二根是小实体
            third['close'] > third['open'] and  # 第三根是阳线
            third_body >= third_range * 0.6 and  # 第三根是长实体
            third['close'] > (first['open'] + first['close']) / 2):  # 第三根收盘价超过第一根实体中点
            
            return {
                'pattern': 'morning_star',
                'type': 'bullish_reversal',
                'position': 'last_three_candles',
                'confidence': 0.9,
                'description': 'Morning Star - 非常强烈的看涨反转信号'
            }
        
        return None
    
    async def _validate_patterns(self, patterns: List[Dict[str, Any]], candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证检测到的形态"""
        validation_result = {
            'total_patterns': len(patterns),
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'pattern_details': []
        }
        
        for pattern in patterns:
            confidence = pattern.get('confidence', 0.5)
            pattern_name = pattern.get('pattern', '')
            
            # 根据置信度分类
            if confidence >= 0.8:
                validation_result['high_confidence'] += 1
                reliability = 'high'
            elif confidence >= 0.6:
                validation_result['medium_confidence'] += 1
                reliability = 'medium'
            else:
                validation_result['low_confidence'] += 1
                reliability = 'low'
            
            # 添加验证详情
            validation_result['pattern_details'].append({
                'pattern': pattern_name,
                'confidence': confidence,
                'reliability': reliability,
                'type': pattern.get('type', 'unknown'),
                'description': pattern.get('description', ''),
                'validation_score': self._calculate_validation_score(pattern, candles)
            })
        
        return validation_result
    
    def _calculate_validation_score(self, pattern: Dict[str, Any], candles: List[Dict[str, Any]]) -> float:
        """计算形态验证分数"""
        base_score = pattern.get('confidence', 0.5)
        
        # 根据成交量验证
        if len(candles) >= 2:
            recent_volume = candles[-1].get('volume', 0)
            avg_volume = sum(c.get('volume', 0) for c in candles[-10:]) / min(10, len(candles))
            
            if recent_volume > avg_volume * 1.5:  # 成交量放大
                base_score += 0.1
            elif recent_volume < avg_volume * 0.5:  # 成交量萎缩
                base_score -= 0.1
        
        # 根据形态类型调整
        pattern_name = pattern.get('pattern', '')
        if pattern_name in self.pattern_reliability.get('high', []):
            base_score += 0.05
        elif pattern_name in self.pattern_reliability.get('low', []):
            base_score -= 0.05
        
        return min(1.0, max(0.0, base_score))
    
    async def _generate_trading_signals(self, patterns: List[Dict[str, Any]], validation: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易信号"""
        signals = {
            'primary_signal': 'hold',
            'signal_strength': 0.5,
            'bullish_signals': 0,
            'bearish_signals': 0,
            'reversal_signals': 0,
            'continuation_signals': 0,
            'recommendations': []
        }
        
        for pattern in patterns:
            pattern_type = pattern.get('type', '')
            confidence = pattern.get('confidence', 0.5)
            
            if 'bullish' in pattern_type:
                signals['bullish_signals'] += 1
                signals['signal_strength'] += confidence * 0.3
            elif 'bearish' in pattern_type:
                signals['bearish_signals'] += 1
                signals['signal_strength'] -= confidence * 0.3
            
            if 'reversal' in pattern_type:
                signals['reversal_signals'] += 1
            elif 'continuation' in pattern_type:
                signals['continuation_signals'] += 1
        
        # 确定主要信号
        if signals['bullish_signals'] > signals['bearish_signals']:
            signals['primary_signal'] = 'buy'
        elif signals['bearish_signals'] > signals['bullish_signals']:
            signals['primary_signal'] = 'sell'
        
        # 标准化信号强度
        signals['signal_strength'] = max(0.0, min(1.0, signals['signal_strength']))
        
        # 生成建议
        if signals['signal_strength'] > 0.7:
            signals['recommendations'].append('强烈建议关注该形态信号')
        elif signals['signal_strength'] > 0.5:
            signals['recommendations'].append('建议结合其他指标确认')
        else:
            signals['recommendations'].append('形态信号较弱，谨慎操作')
        
        return signals
    
    async def _ai_enhanced_analysis(self, symbol: str, patterns: List[Dict[str, Any]], candles: List[Dict[str, Any]]) -> str:
        """AI增强分析"""
        if not patterns:
            return "未检测到明显的蜡烛图形态"
        
        # 构建分析提示
        pattern_descriptions = []
        for pattern in patterns:
            pattern_descriptions.append(f"- {pattern['pattern']}: {pattern['description']} (置信度: {pattern['confidence']:.1%})")
        
        prompt = f"""
        作为专业的蜡烛图技术分析师，请分析{symbol}的以下蜡烛图形态：

        检测到的形态：
        {chr(10).join(pattern_descriptions)}

        当前价格信息：
        - 最新收盘价: {candles[-1]['close']:.2f}
        - 最新成交量: {candles[-1].get('volume', 0):,}
        - 价格区间: {candles[-1]['low']:.2f} - {candles[-1]['high']:.2f}

        请提供：
        1. 形态的技术含义和市场心理
        2. 交易建议和风险提示
        3. 需要关注的确认信号
        4. 止损和目标位建议

        请用专业但易懂的语言回答。
        """
        
        system_prompt = "你是一位资深的蜡烛图技术分析专家，精通《日本蜡烛图技术》理论，能够准确解读各种蜡烛图形态的含义。"
        
        try:
            response = await self._generate_llm_response(prompt, system_prompt)
            return response
        except Exception as e:
            self.logger.error(f"AI增强分析失败: {e}")
            return f"AI分析暂时不可用。基于检测到的{len(patterns)}个形态，建议结合其他技术指标进行综合分析。"
    
    async def _generate_overall_assessment(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成总体评估"""
        patterns = analysis_result.get('detected_patterns', [])
        validation = analysis_result.get('pattern_validation', {})
        signals = analysis_result.get('trading_signals', {})
        
        assessment = {
            'pattern_count': len(patterns),
            'overall_reliability': 'medium',
            'market_outlook': 'neutral',
            'action_recommendation': 'hold',
            'confidence_level': 0.5,
            'key_insights': []
        }
        
        # 计算总体可靠性
        high_conf = validation.get('high_confidence', 0)
        total_patterns = validation.get('total_patterns', 0)
        
        if total_patterns > 0:
            high_conf_ratio = high_conf / total_patterns
            if high_conf_ratio >= 0.6:
                assessment['overall_reliability'] = 'high'
            elif high_conf_ratio >= 0.3:
                assessment['overall_reliability'] = 'medium'
            else:
                assessment['overall_reliability'] = 'low'
        
        # 确定市场展望
        primary_signal = signals.get('primary_signal', 'hold')
        signal_strength = signals.get('signal_strength', 0.5)
        
        if primary_signal == 'buy' and signal_strength > 0.6:
            assessment['market_outlook'] = 'bullish'
            assessment['action_recommendation'] = 'buy'
        elif primary_signal == 'sell' and signal_strength > 0.6:
            assessment['market_outlook'] = 'bearish'
            assessment['action_recommendation'] = 'sell'
        
        assessment['confidence_level'] = signal_strength
        
        # 生成关键洞察
        if patterns:
            assessment['key_insights'].append(f"检测到{len(patterns)}个蜡烛图形态")
            
            reversal_count = signals.get('reversal_signals', 0)
            if reversal_count > 0:
                assessment['key_insights'].append(f"发现{reversal_count}个反转信号，市场可能出现转折")
            
            if assessment['overall_reliability'] == 'high':
                assessment['key_insights'].append("形态可靠性较高，值得重点关注")
        else:
            assessment['key_insights'].append("当前未发现明显的蜡烛图形态")
        
        return assessment
    
    async def _generate_debate_argument(self, topic: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """为辩论生成蜡烛图技术分析观点"""
        return {
            'position': 'technical_analysis',
            'argument': f"从蜡烛图技术分析角度看，{topic}需要重点关注价格形态和市场心理变化",
            'evidence': [
                "蜡烛图形态反映市场参与者的心理状态",
                "形态的确认需要成交量配合",
                "技术分析应结合趋势和支撑阻力位"
            ],
            'confidence': 0.8,
            'methodology': 'candlestick_technical_analysis'
        }
