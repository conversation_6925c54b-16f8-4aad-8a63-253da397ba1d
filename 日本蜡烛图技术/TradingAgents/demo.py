#!/usr/bin/env python3
"""
TradingAgents Demo - Simple demonstration of the multi-agent trading system
"""

import asyncio
import random
from datetime import datetime
from typing import Dict, Any, List


class MockAgent:
    """Mock agent for demonstration"""
    
    def __init__(self, name: str, specialization: str):
        self.name = name
        self.specialization = specialization
    
    async def analyze(self, symbol: str) -> Dict[str, Any]:
        """Mock analysis"""
        await asyncio.sleep(0.1)  # Simulate processing time
        
        # Generate mock analysis based on specialization
        if self.specialization == "bullish":
            signal = "buy"
            sentiment = "bullish"
            confidence = random.uniform(0.6, 0.9)
        elif self.specialization == "bearish":
            signal = "sell"
            sentiment = "bearish"
            confidence = random.uniform(0.6, 0.9)
        else:
            signal = random.choice(["buy", "sell", "hold"])
            sentiment = random.choice(["bullish", "bearish", "neutral"])
            confidence = random.uniform(0.5, 0.8)
        
        return {
            'agent': self.name,
            'specialization': self.specialization,
            'symbol': symbol,
            'signal': signal,
            'sentiment': sentiment,
            'confidence': confidence,
            'analysis': f"{self.name} analysis for {symbol}",
            'timestamp': datetime.now().isoformat()
        }


class TradingAgentsDemo:
    """Demo version of TradingAgents system"""
    
    def __init__(self):
        self.agents = [
            MockAgent("MarketAnalyst", "technical_analysis"),
            MockAgent("BullResearcher", "bullish"),
            MockAgent("BearResearcher", "bearish"),
            MockAgent("PortfolioManager", "risk_management"),
            MockAgent("DebateModerator", "consensus")
        ]
        
        print("🤖 TradingAgents Demo System Initialized")
        print(f"   Active Agents: {len(self.agents)}")
        for agent in self.agents:
            print(f"   - {agent.name} ({agent.specialization})")
    
    async def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """Analyze a symbol with all agents"""
        print(f"\n🔍 Starting multi-agent analysis for {symbol}")
        print("-" * 50)
        
        # Run analysis with all agents
        analysis_tasks = [agent.analyze(symbol) for agent in self.agents]
        agent_results = await asyncio.gather(*analysis_tasks)
        
        # Display individual agent results
        for result in agent_results:
            print(f"🤖 {result['agent']}: {result['signal'].upper()} "
                  f"({result['sentiment']}, confidence: {result['confidence']:.1%})")
        
        # Generate consensus
        signals = [r['signal'] for r in agent_results]
        buy_votes = signals.count('buy')
        sell_votes = signals.count('sell')
        hold_votes = signals.count('hold')
        
        if buy_votes > sell_votes and buy_votes > hold_votes:
            consensus = 'BUY'
            consensus_strength = buy_votes / len(signals)
        elif sell_votes > buy_votes and sell_votes > hold_votes:
            consensus = 'SELL'
            consensus_strength = sell_votes / len(signals)
        else:
            consensus = 'HOLD'
            consensus_strength = hold_votes / len(signals)
        
        print(f"\n📊 Consensus Analysis:")
        print(f"   Buy votes: {buy_votes}")
        print(f"   Sell votes: {sell_votes}")
        print(f"   Hold votes: {hold_votes}")
        print(f"   🎯 Final Decision: {consensus} (strength: {consensus_strength:.1%})")
        
        return {
            'symbol': symbol,
            'agent_results': agent_results,
            'consensus': consensus,
            'consensus_strength': consensus_strength,
            'vote_breakdown': {
                'buy': buy_votes,
                'sell': sell_votes,
                'hold': hold_votes
            }
        }
    
    async def conduct_debate(self, topic: str) -> Dict[str, Any]:
        """Simulate agent debate"""
        print(f"\n🎭 Agent Debate: {topic}")
        print("-" * 50)
        
        # Simulate debate rounds
        debate_rounds = []
        
        for round_num in range(1, 4):  # 3 rounds
            print(f"\n🗣️  Round {round_num}:")
            
            round_arguments = []
            for agent in self.agents:
                await asyncio.sleep(0.1)  # Simulate thinking time
                
                # Generate argument based on agent specialization
                if agent.specialization == "bullish":
                    argument = f"I see strong bullish indicators for {topic}"
                    position = "FOR"
                elif agent.specialization == "bearish":
                    argument = f"There are significant risks regarding {topic}"
                    position = "AGAINST"
                else:
                    argument = f"From a {agent.specialization} perspective, {topic} requires careful consideration"
                    position = "NEUTRAL"
                
                round_arguments.append({
                    'agent': agent.name,
                    'position': position,
                    'argument': argument
                })
                
                print(f"   {agent.name}: {argument}")
            
            debate_rounds.append({
                'round': round_num,
                'arguments': round_arguments
            })
        
        # Determine debate outcome
        all_positions = []
        for round_data in debate_rounds:
            for arg in round_data['arguments']:
                all_positions.append(arg['position'])
        
        for_count = all_positions.count('FOR')
        against_count = all_positions.count('AGAINST')
        neutral_count = all_positions.count('NEUTRAL')
        
        if for_count > against_count:
            outcome = "APPROVED"
        elif against_count > for_count:
            outcome = "REJECTED"
        else:
            outcome = "NO CONSENSUS"
        
        print(f"\n🏆 Debate Outcome: {outcome}")
        print(f"   For: {for_count}, Against: {against_count}, Neutral: {neutral_count}")
        
        return {
            'topic': topic,
            'rounds': debate_rounds,
            'outcome': outcome,
            'vote_summary': {
                'for': for_count,
                'against': against_count,
                'neutral': neutral_count
            }
        }
    
    async def run_trading_workflow(self, symbol: str) -> Dict[str, Any]:
        """Simulate complete trading workflow"""
        print(f"\n⚡ Trading Workflow for {symbol}")
        print("=" * 50)
        
        # Step 1: Market Analysis
        print("📊 Step 1: Market Analysis")
        analysis_result = await self.analyze_symbol(symbol)
        
        # Step 2: Agent Debate
        print(f"\n🎭 Step 2: Agent Debate")
        debate_result = await self.conduct_debate(f"investing in {symbol}")
        
        # Step 3: Risk Assessment
        print(f"\n⚖️  Step 3: Risk Assessment")
        await asyncio.sleep(0.2)
        risk_level = random.choice(["LOW", "MEDIUM", "HIGH"])
        position_size = random.uniform(0.01, 0.10)  # 1-10% of portfolio
        
        print(f"   Risk Level: {risk_level}")
        print(f"   Recommended Position Size: {position_size:.1%}")
        
        # Step 4: Final Decision
        print(f"\n🎯 Step 4: Final Decision")
        
        # Combine analysis and debate results
        analysis_consensus = analysis_result['consensus']
        debate_outcome = debate_result['outcome']
        
        if analysis_consensus == 'BUY' and debate_outcome == 'APPROVED':
            final_decision = 'EXECUTE BUY ORDER'
            confidence = 0.8
        elif analysis_consensus == 'SELL' and debate_outcome == 'REJECTED':
            final_decision = 'EXECUTE SELL ORDER'
            confidence = 0.8
        else:
            final_decision = 'HOLD POSITION'
            confidence = 0.6
        
        print(f"   Final Decision: {final_decision}")
        print(f"   Confidence: {confidence:.1%}")
        
        return {
            'symbol': symbol,
            'workflow_steps': {
                'analysis': analysis_result,
                'debate': debate_result,
                'risk_assessment': {
                    'risk_level': risk_level,
                    'position_size': position_size
                }
            },
            'final_decision': final_decision,
            'confidence': confidence,
            'timestamp': datetime.now().isoformat()
        }


async def run_demo():
    """Run the TradingAgents demo"""
    print("🚀 TradingAgents Multi-Agent Trading System Demo")
    print("=" * 60)
    print("🎯 Simulating AI agents working together to make investment decisions")
    print()
    
    # Initialize demo system
    demo_system = TradingAgentsDemo()
    
    # Demo scenarios
    symbols = ["AAPL", "TSLA", "GOOGL"]
    
    for i, symbol in enumerate(symbols, 1):
        print(f"\n🎬 Demo Scenario {i}: {symbol}")
        print("=" * 60)
        
        try:
            # Run complete trading workflow
            workflow_result = await demo_system.run_trading_workflow(symbol)
            
            print(f"\n✅ Workflow completed for {symbol}")
            print(f"   Decision: {workflow_result['final_decision']}")
            print(f"   Confidence: {workflow_result['confidence']:.1%}")
            
        except Exception as e:
            print(f"❌ Error in workflow for {symbol}: {e}")
        
        if i < len(symbols):
            print(f"\n⏳ Preparing next scenario...")
            await asyncio.sleep(1)
    
    print(f"\n🎉 Demo completed!")
    print(f"💡 This demonstrates how multiple AI agents can:")
    print(f"   - Analyze markets from different perspectives")
    print(f"   - Debate investment decisions collaboratively")
    print(f"   - Reach consensus through structured workflows")
    print(f"   - Make informed trading decisions")
    print(f"\n🔧 To run the full system:")
    print(f"   python3 main.py interactive")


async def main():
    """Main demo function"""
    try:
        await run_demo()
    except KeyboardInterrupt:
        print(f"\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
