#!/usr/bin/env python3
"""
TradingAgents System Test Script
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import TradingAgentsSystem


async def test_system_initialization():
    """Test system initialization"""
    print("🔧 Testing system initialization...")
    
    try:
        system = TradingAgentsSystem()
        await system.initialize()
        print("✅ System initialization successful")
        return system
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        return None


async def test_market_analysis(system: TradingAgentsSystem):
    """Test market analysis functionality"""
    print("\n📊 Testing market analysis...")
    
    try:
        symbols = ["AAPL", "TSLA", "GOOGL"]
        
        for symbol in symbols:
            print(f"\n🔍 Analyzing {symbol}...")
            result = await system.run_market_analysis(symbol)
            
            if result:
                print(f"✅ {symbol} analysis completed")
                print(f"   Agents analyzed: {result.get('summary', {}).get('total_agents', 0)}")
                print(f"   Overall sentiment: {result.get('summary', {}).get('overall_sentiment', 'unknown')}")
                print(f"   Overall signal: {result.get('summary', {}).get('overall_signal', 'unknown')}")
            else:
                print(f"❌ {symbol} analysis failed")
        
        print("✅ Market analysis test completed")
        
    except Exception as e:
        print(f"❌ Market analysis test failed: {e}")


async def test_agent_debate(system: TradingAgentsSystem):
    """Test agent debate functionality"""
    print("\n🎭 Testing agent debate...")
    
    try:
        topics = [
            "Should we invest in AAPL?",
            "Is the current market overvalued?",
            "Best strategy for tech stocks?"
        ]
        
        for topic in topics:
            print(f"\n💬 Debating: {topic}")
            result = await system.run_agent_debate(topic)
            
            if result:
                print(f"✅ Debate completed")
                print(f"   Rounds: {len(result.get('rounds', []))}")
                print(f"   Consensus: {result.get('consensus_reached', False)}")
                if result.get('summary'):
                    print(f"   Decision: {result['summary'].get('decision', 'unknown')}")
                    print(f"   Confidence: {result['summary'].get('confidence', 0):.1%}")
            else:
                print(f"❌ Debate failed")
        
        print("✅ Agent debate test completed")
        
    except Exception as e:
        print(f"❌ Agent debate test failed: {e}")


async def test_trading_workflow(system: TradingAgentsSystem):
    """Test trading workflow functionality"""
    print("\n⚡ Testing trading workflow...")
    
    try:
        symbols = ["AAPL", "TSLA"]
        strategies = ["momentum", "value"]
        
        for symbol in symbols:
            for strategy in strategies:
                print(f"\n🔄 Testing {symbol} with {strategy} strategy...")
                result = await system.run_trading_workflow(symbol, strategy)
                
                if result:
                    print(f"✅ Workflow completed")
                    print(f"   Status: {result.get('status', 'unknown')}")
                    if result.get('result'):
                        print(f"   Decision: {result['result'].get('decision', 'unknown')}")
                        print(f"   Confidence: {result['result'].get('confidence', 0):.1%}")
                else:
                    print(f"❌ Workflow failed")
        
        print("✅ Trading workflow test completed")
        
    except Exception as e:
        print(f"❌ Trading workflow test failed: {e}")


async def test_system_status(system: TradingAgentsSystem):
    """Test system status functionality"""
    print("\n📋 Testing system status...")
    
    try:
        status = await system.get_system_status()
        
        print("✅ System status retrieved")
        print(f"   System: {status.get('system', 'unknown')}")
        print(f"   Status: {status.get('status', 'unknown')}")
        
        agents = status.get('agents', {})
        print(f"   Active agents: {agents.get('active_agents', 0)}")
        print(f"   Total agents: {agents.get('total_agents', 0)}")
        
        market_data = status.get('market_data', {})
        print(f"   Market data provider: {market_data.get('provider', 'unknown')}")
        print(f"   Market data status: {market_data.get('status', 'unknown')}")
        
        print("✅ System status test completed")
        
    except Exception as e:
        print(f"❌ System status test failed: {e}")


async def run_comprehensive_test():
    """Run comprehensive system test"""
    print("🚀 Starting TradingAgents Comprehensive Test")
    print("=" * 60)
    
    # Initialize system
    system = await test_system_initialization()
    if not system:
        print("❌ Cannot proceed with tests - system initialization failed")
        return
    
    try:
        # Run all tests
        await test_system_status(system)
        await test_market_analysis(system)
        await test_agent_debate(system)
        await test_trading_workflow(system)
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed successfully!")
        print("\n💡 System is ready for use. Try running:")
        print("   python main.py interactive    # Interactive mode")
        print("   python main.py demo          # Demo mode")
        print("   python main.py analyze AAPL # Analyze specific symbol")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
    
    finally:
        # Cleanup
        await system.shutdown()
        print("\n👋 Test cleanup completed")


async def run_quick_test():
    """Run quick system test"""
    print("⚡ Quick TradingAgents Test")
    print("=" * 30)
    
    system = await test_system_initialization()
    if system:
        await test_system_status(system)
        print("\n✅ Quick test passed!")
        await system.shutdown()
    else:
        print("❌ Quick test failed!")


async def main():
    """Main test function"""
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        await run_quick_test()
    else:
        await run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
