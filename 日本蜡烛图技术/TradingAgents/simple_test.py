#!/usr/bin/env python3
"""
Simple TradingAgents Test - No external dependencies
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class SimpleConfig:
    """Simple configuration without YAML dependency"""
    
    def __init__(self):
        self.config_data = {
            'system': {
                'name': 'TradingAgents',
                'version': '1.0.0',
                'environment': 'development'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'console_output': True
            },
            'agents': {
                'market_analyst': {'enabled': True, 'name': 'MarketAnalyst', 'model': 'gpt-4', 'temperature': 0.2},
                'bull_researcher': {'enabled': True, 'name': 'BullResearcher', 'model': 'gpt-4', 'temperature': 0.4},
                'bear_researcher': {'enabled': True, 'name': 'BearResearcher', 'model': 'gpt-4', 'temperature': 0.4},
                'portfolio_manager': {'enabled': True, 'name': 'PortfolioManager', 'model': 'gpt-4', 'temperature': 0.1},
                'debate_moderator': {'enabled': True, 'name': 'DebateModerator', 'model': 'gpt-4', 'temperature': 0.2},
                'execution_agent': {'enabled': True, 'name': 'ExecutionAgent', 'model': 'gpt-3.5-turbo', 'temperature': 0.1}
            },
            'debate_system': {
                'enabled': True,
                'max_rounds': 5,
                'consensus_threshold': 0.7
            },
            'workflow': {
                'enabled': True,
                'max_concurrent_workflows': 10
            },
            'market_data': {
                'providers': {
                    'primary': 'mock_data'
                },
                'cache': {
                    'ttl': 300
                }
            }
        }
    
    def get(self, key: str, default=None):
        """Get configuration value"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default


async def test_simple_system():
    """Test the TradingAgents system with simple configuration"""
    print("🤖 TradingAgents Simple Test")
    print("=" * 40)
    
    try:
        # Import with simple config
        from core.agent_manager import AgentManager
        from core.debate_system import DebateSystem
        from core.workflow_engine import WorkflowEngine
        from data.market_data import MarketDataProvider
        
        # Create simple config
        config = SimpleConfig()
        
        print("📋 Testing component initialization...")
        
        # Test Market Data Provider
        print("\n📊 Testing Market Data Provider...")
        market_data = MarketDataProvider(config)
        await market_data.initialize()
        
        # Test getting market data
        data = await market_data.get_symbol_data("AAPL")
        print(f"✅ Market data retrieved for AAPL: {data['current_price']}")
        
        # Test Agent Manager
        print("\n🤖 Testing Agent Manager...")
        agent_manager = AgentManager(config)
        await agent_manager.initialize_agents()
        
        status = await agent_manager.get_agent_status()
        print(f"✅ Agent Manager initialized: {status['active_agents']} active agents")
        
        # Test analysis
        print("\n🔍 Testing market analysis...")
        analysis_result = await agent_manager.analyze_symbol("AAPL", data)
        print(f"✅ Analysis completed: {analysis_result['summary']['total_agents']} agents analyzed")
        
        # Test Debate System
        print("\n🎭 Testing Debate System...")
        debate_system = DebateSystem(config)
        await debate_system.initialize()
        
        debate_result = await debate_system.conduct_debate(
            topic="Should we invest in AAPL?",
            analysis_results=analysis_result
        )
        print(f"✅ Debate completed: {len(debate_result['rounds'])} rounds")
        
        # Test Workflow Engine
        print("\n⚡ Testing Workflow Engine...")
        workflow_engine = WorkflowEngine(config)
        await workflow_engine.initialize()
        
        workflow_result = await workflow_engine.execute_workflow(
            workflow_type="trading_decision",
            symbol="AAPL",
            strategy="momentum"
        )
        print(f"✅ Workflow completed: {workflow_result['status']}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        await workflow_engine.shutdown()
        await debate_system.shutdown()
        await agent_manager.shutdown()
        await market_data.shutdown()
        
        print("\n🎉 All tests passed successfully!")
        print("\n💡 System is working correctly. You can now run:")
        print("   python3 main.py demo          # Demo mode")
        print("   python3 main.py interactive   # Interactive mode")
        print("   python3 main.py analyze AAPL # Analyze specific symbol")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    success = await test_simple_system()
    
    if success:
        print("\n✅ TradingAgents system is ready!")
    else:
        print("\n❌ TradingAgents system has issues")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
