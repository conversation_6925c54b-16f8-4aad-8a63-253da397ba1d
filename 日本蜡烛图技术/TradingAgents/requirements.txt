# Core Dependencies
asyncio-mqtt==0.16.1
aiohttp==3.9.1
aiofiles==23.2.1
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0

# AI/ML Dependencies
openai==1.6.1
anthropic==0.8.1
langchain==0.1.0
langchain-openai==0.0.2
tiktoken==0.5.2

# Data Processing
pandas==2.1.4
numpy==1.24.4
scipy==1.11.4
scikit-learn==1.3.2

# Financial Data
yfinance==0.2.28
alpha-vantage==2.3.1
ccxt==4.1.77
pandas-datareader==0.10.0

# Technical Analysis
TA-Lib==0.4.28
talib-binary==0.4.19
mplfinance==0.12.10a0

# Web Framework
fastapi==0.108.0
uvicorn[standard]==0.25.0
websockets==12.0
starlette==0.32.0

# Database
sqlalchemy==2.0.25
alembic==1.13.1
asyncpg==0.29.0
redis==5.0.1

# Monitoring & Logging
structlog==23.2.0
prometheus-client==0.19.0
sentry-sdk==1.39.2

# Configuration
PyYAML==6.0.1
toml==0.10.2
configparser==6.0.0

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0
httpx==0.26.0

# Testing
pytest==7.4.4
pytest-asyncio==0.23.2
pytest-cov==4.1.0
pytest-mock==3.12.0
hypothesis==6.92.1

# Development
black==23.12.1
isort==5.13.2
flake8==7.0.0
mypy==1.8.0
pre-commit==3.6.0

# Jupyter (for analysis)
jupyter==1.0.0
ipykernel==6.27.1
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# Async utilities
asyncio-throttle==1.0.2
aioredis==2.0.1
aiocache==0.12.2

# Crypto/Security
cryptography==41.0.8
bcrypt==4.1.2
passlib==1.7.4

# Time handling
pytz==2023.3
python-dateutil==2.8.2
arrow==1.3.0

# Networking
requests==2.31.0
urllib3==2.1.0

# Data validation
marshmallow==3.20.2
cerberus==1.3.5

# Message queues
celery==5.3.4
kombu==5.3.4

# Caching
diskcache==5.6.3

# Environment
python-decouple==3.8
