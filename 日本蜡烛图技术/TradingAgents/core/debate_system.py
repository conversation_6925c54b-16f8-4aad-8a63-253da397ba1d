"""
Debate System - Orchestrates structured debates between agents
"""

import asyncio
import logging
from typing import Dict, Any, List
from datetime import datetime


class DebateSystem:
    """Manages structured debates between trading agents"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger("DebateSystem")
        self.debate_config = config.get('debate_system', {})
        
        self.max_rounds = self.debate_config.get('max_rounds', 5)
        self.consensus_threshold = self.debate_config.get('consensus_threshold', 0.7)
        self.round_timeout = self.debate_config.get('round_timeout', 300)
        
        self.active_debates = {}
        self.debate_history = []
        
        self.logger.info("DebateSystem initialized")
    
    async def initialize(self):
        """Initialize the debate system"""
        self.logger.info("Initializing debate system...")
        self.logger.info("✅ Debate system ready")
    
    async def conduct_debate(self, topic: str, participants: List = None, 
                           analysis_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """Conduct a structured debate"""
        debate_id = f"debate_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.logger.info(f"🎭 Starting debate: {topic} (ID: {debate_id})")
        
        debate_result = {
            'debate_id': debate_id,
            'topic': topic,
            'participants': [p.name if hasattr(p, 'name') else str(p) for p in participants] if participants else [],
            'start_time': datetime.now().isoformat(),
            'rounds': [],
            'consensus_reached': False,
            'final_decision': None
        }
        
        try:
            # If we have analysis results, use them as the basis for debate
            if analysis_results:
                debate_result['analysis_basis'] = analysis_results
                
                # Extract different viewpoints from analysis results
                viewpoints = self._extract_viewpoints(analysis_results)
                debate_result['initial_viewpoints'] = viewpoints
                
                # Simulate debate rounds
                for round_num in range(1, self.max_rounds + 1):
                    round_result = await self._conduct_debate_round(
                        round_num, topic, viewpoints, participants
                    )
                    debate_result['rounds'].append(round_result)
                    
                    # Check for consensus
                    consensus = self._check_consensus(debate_result['rounds'])
                    if consensus['reached']:
                        debate_result['consensus_reached'] = True
                        debate_result['final_decision'] = consensus['decision']
                        break
                
                # Generate final summary
                debate_result['summary'] = self._generate_debate_summary(debate_result)
                
            else:
                # Fallback for debates without analysis results
                debate_result['summary'] = {
                    'decision': 'hold',
                    'confidence': 0.5,
                    'reasoning': 'Insufficient data for debate'
                }
            
            debate_result['end_time'] = datetime.now().isoformat()
            debate_result['duration'] = self._calculate_duration(
                debate_result['start_time'], debate_result['end_time']
            )
            
            # Store debate history
            self.debate_history.append(debate_result)
            
            self.logger.info(f"✅ Debate completed: {topic}")
            return debate_result
            
        except Exception as e:
            self.logger.error(f"❌ Debate failed: {e}")
            debate_result['error'] = str(e)
            debate_result['status'] = 'failed'
            return debate_result
    
    def _extract_viewpoints(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Extract different viewpoints from analysis results"""
        viewpoints = {
            'bullish': [],
            'bearish': [],
            'neutral': []
        }
        
        agent_analyses = analysis_results.get('agent_analyses', {})
        
        for agent_name, analysis in agent_analyses.items():
            if analysis.get('status') == 'success':
                agent_analysis = analysis.get('analysis', {})
                sentiment = agent_analysis.get('sentiment', 'neutral')
                signal = agent_analysis.get('signal', 'hold')
                
                viewpoint = {
                    'agent': agent_name,
                    'sentiment': sentiment,
                    'signal': signal,
                    'confidence': agent_analysis.get('confidence', 0.5),
                    'perspective': agent_analysis.get('perspective', 'general')
                }
                
                if sentiment == 'bullish' or signal == 'buy':
                    viewpoints['bullish'].append(viewpoint)
                elif sentiment == 'bearish' or signal == 'sell':
                    viewpoints['bearish'].append(viewpoint)
                else:
                    viewpoints['neutral'].append(viewpoint)
        
        return viewpoints
    
    async def _conduct_debate_round(self, round_num: int, topic: str, 
                                  viewpoints: Dict[str, Any], participants: List) -> Dict[str, Any]:
        """Conduct a single debate round"""
        self.logger.info(f"🗣️ Debate round {round_num}")
        
        round_result = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            'arguments': [],
            'round_summary': {}
        }
        
        # Simulate arguments from different perspectives
        if viewpoints.get('bullish'):
            for viewpoint in viewpoints['bullish']:
                argument = {
                    'agent': viewpoint['agent'],
                    'position': 'bullish',
                    'argument': f"Based on {viewpoint['perspective']} analysis, there are strong bullish indicators",
                    'evidence': ["Positive technical signals", "Strong fundamentals", "Favorable sentiment"],
                    'confidence': viewpoint['confidence']
                }
                round_result['arguments'].append(argument)
        
        if viewpoints.get('bearish'):
            for viewpoint in viewpoints['bearish']:
                argument = {
                    'agent': viewpoint['agent'],
                    'position': 'bearish',
                    'argument': f"From a {viewpoint['perspective']} standpoint, there are significant risks",
                    'evidence': ["Technical weakness", "Fundamental concerns", "Market headwinds"],
                    'confidence': viewpoint['confidence']
                }
                round_result['arguments'].append(argument)
        
        # Generate round summary
        round_result['round_summary'] = self._summarize_round(round_result['arguments'])
        
        return round_result
    
    def _summarize_round(self, arguments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize a debate round"""
        bullish_args = [arg for arg in arguments if arg['position'] == 'bullish']
        bearish_args = [arg for arg in arguments if arg['position'] == 'bearish']
        
        return {
            'bullish_arguments': len(bullish_args),
            'bearish_arguments': len(bearish_args),
            'dominant_position': 'bullish' if len(bullish_args) > len(bearish_args) else 'bearish' if len(bearish_args) > len(bullish_args) else 'balanced',
            'argument_quality': 'good',  # Simplified
            'consensus_emerging': len(bullish_args) > 0 and len(bearish_args) > 0
        }
    
    def _check_consensus(self, rounds: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if consensus has been reached"""
        if not rounds:
            return {'reached': False, 'decision': None}
        
        # Simple consensus logic - if last round has dominant position
        last_round = rounds[-1]
        round_summary = last_round.get('round_summary', {})
        dominant_position = round_summary.get('dominant_position', 'balanced')
        
        if dominant_position in ['bullish', 'bearish']:
            return {
                'reached': True,
                'decision': 'buy' if dominant_position == 'bullish' else 'sell',
                'confidence': 0.7
            }
        
        return {'reached': False, 'decision': None}
    
    def _generate_debate_summary(self, debate_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final debate summary"""
        rounds = debate_result.get('rounds', [])
        
        if not rounds:
            return {
                'decision': 'hold',
                'confidence': 0.5,
                'reasoning': 'No debate rounds conducted'
            }
        
        # Count arguments by position
        total_bullish = 0
        total_bearish = 0
        
        for round_data in rounds:
            for arg in round_data.get('arguments', []):
                if arg['position'] == 'bullish':
                    total_bullish += 1
                elif arg['position'] == 'bearish':
                    total_bearish += 1
        
        # Determine final decision
        if total_bullish > total_bearish:
            decision = 'buy'
            confidence = min(0.6 + (total_bullish - total_bearish) * 0.1, 0.9)
        elif total_bearish > total_bullish:
            decision = 'sell'
            confidence = min(0.6 + (total_bearish - total_bullish) * 0.1, 0.9)
        else:
            decision = 'hold'
            confidence = 0.5
        
        return {
            'decision': decision,
            'confidence': confidence,
            'reasoning': f"Debate concluded with {total_bullish} bullish and {total_bearish} bearish arguments",
            'total_rounds': len(rounds),
            'consensus_reached': debate_result.get('consensus_reached', False),
            'debate_quality': 'good'  # Simplified
        }
    
    def _calculate_duration(self, start_time: str, end_time: str) -> float:
        """Calculate debate duration in seconds"""
        try:
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            return (end - start).total_seconds()
        except:
            return 0.0
    
    def get_status(self) -> Dict[str, Any]:
        """Get debate system status"""
        return {
            'active_debates': len(self.active_debates),
            'total_debates': len(self.debate_history),
            'max_rounds': self.max_rounds,
            'consensus_threshold': self.consensus_threshold,
            'status': 'active'
        }
    
    async def shutdown(self):
        """Shutdown the debate system"""
        self.logger.info("Shutting down debate system...")
        self.active_debates.clear()
        self.logger.info("✅ Debate system shutdown complete")
