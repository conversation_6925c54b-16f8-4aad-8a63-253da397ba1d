"""
基础智能体类 - 多智能体交易系统核心
基于TradingAgents框架设计
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import json
import logging
from enum import Enum

class AgentRole(Enum):
    """智能体角色枚举"""
    TECHNICAL_ANALYST = "technical_analyst"
    FUNDAMENTAL_ANALYST = "fundamental_analyst"
    SENTIMENT_ANALYST = "sentiment_analyst"
    NEWS_ANALYST = "news_analyst"
    BULLISH_RESEARCHER = "bullish_researcher"
    BEARISH_RESEARCHER = "bearish_researcher"
    TRADER = "trader"
    RISK_MANAGER = "risk_manager"
    PORTFOLIO_MANAGER = "portfolio_manager"

class MessageType(Enum):
    """消息类型枚举"""
    ANALYSIS_REPORT = "analysis_report"
    RESEARCH_OPINION = "research_opinion"
    DEBATE_ARGUMENT = "debate_argument"
    TRADING_SIGNAL = "trading_signal"
    RISK_ASSESSMENT = "risk_assessment"
    DECISION_REQUEST = "decision_request"
    DECISION_RESPONSE = "decision_response"

@dataclass
class Message:
    """智能体间通信消息"""
    message_type: MessageType
    from_agent: str
    to_agents: List[str]
    content: Dict[str, Any]
    timestamp: datetime
    message_id: str
    priority: int = 1  # 1-5, 5为最高优先级

@dataclass
class AnalysisResult:
    """分析结果基础结构"""
    agent_id: str
    analysis_type: str
    confidence: float  # 0-1
    signals: List[Dict[str, Any]]
    reasoning: str
    timestamp: datetime
    metadata: Dict[str, Any]

class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(
        self,
        agent_id: str,
        role: AgentRole,
        llm_config: Dict[str, Any],
        communication_hub: Optional[Any] = None
    ):
        self.agent_id = agent_id
        self.role = role
        self.llm_config = llm_config
        self.communication_hub = communication_hub
        self.logger = logging.getLogger(f"Agent.{agent_id}")
        
        # 智能体状态
        self.is_active = False
        self.current_task = None
        self.memory = []  # 存储历史交互
        self.context = {}  # 当前上下文
        
        # 性能指标
        self.analysis_count = 0
        self.accuracy_score = 0.0
        self.response_time = 0.0
        
        self.logger.info(f"Agent {agent_id} ({role.value}) initialized")
    
    @abstractmethod
    async def analyze(self, market_data: Dict[str, Any]) -> AnalysisResult:
        """执行分析任务"""
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """获取智能体的系统提示词"""
        pass
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理接收到的消息"""
        self.logger.info(f"Processing message {message.message_id} from {message.from_agent}")
        
        try:
            # 更新上下文
            self._update_context(message)
            
            # 根据消息类型处理
            if message.message_type == MessageType.ANALYSIS_REPORT:
                return await self._handle_analysis_report(message)
            elif message.message_type == MessageType.DEBATE_ARGUMENT:
                return await self._handle_debate_argument(message)
            elif message.message_type == MessageType.DECISION_REQUEST:
                return await self._handle_decision_request(message)
            else:
                self.logger.warning(f"Unknown message type: {message.message_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return None
    
    async def send_message(
        self,
        message_type: MessageType,
        to_agents: List[str],
        content: Dict[str, Any],
        priority: int = 1
    ) -> bool:
        """发送消息给其他智能体"""
        if not self.communication_hub:
            self.logger.error("No communication hub configured")
            return False
        
        message = Message(
            message_type=message_type,
            from_agent=self.agent_id,
            to_agents=to_agents,
            content=content,
            timestamp=datetime.now(),
            message_id=f"{self.agent_id}_{datetime.now().timestamp()}",
            priority=priority
        )
        
        return await self.communication_hub.send_message(message)
    
    def _update_context(self, message: Message):
        """更新智能体上下文"""
        self.context[f"last_message_from_{message.from_agent}"] = message
        self.memory.append({
            'timestamp': message.timestamp,
            'from': message.from_agent,
            'type': message.message_type.value,
            'content_summary': self._summarize_content(message.content)
        })
        
        # 保持内存大小限制
        if len(self.memory) > 100:
            self.memory = self.memory[-50:]
    
    def _summarize_content(self, content: Dict[str, Any]) -> str:
        """总结消息内容"""
        # 简单的内容总结，实际实现可以更复杂
        if 'signals' in content:
            return f"Signals: {len(content['signals'])} items"
        elif 'confidence' in content:
            return f"Confidence: {content['confidence']}"
        else:
            return f"Content keys: {list(content.keys())}"
    
    async def _handle_analysis_report(self, message: Message) -> Optional[Message]:
        """处理分析报告"""
        # 基础实现，子类可以重写
        self.logger.info(f"Received analysis report from {message.from_agent}")
        return None
    
    async def _handle_debate_argument(self, message: Message) -> Optional[Message]:
        """处理辩论论点"""
        # 基础实现，子类可以重写
        self.logger.info(f"Received debate argument from {message.from_agent}")
        return None
    
    async def _handle_decision_request(self, message: Message) -> Optional[Message]:
        """处理决策请求"""
        # 基础实现，子类可以重写
        self.logger.info(f"Received decision request from {message.from_agent}")
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            'agent_id': self.agent_id,
            'role': self.role.value,
            'is_active': self.is_active,
            'current_task': self.current_task,
            'analysis_count': self.analysis_count,
            'accuracy_score': self.accuracy_score,
            'response_time': self.response_time,
            'memory_size': len(self.memory),
            'last_activity': self.memory[-1]['timestamp'] if self.memory else None
        }
    
    def update_performance_metrics(self, accuracy: float, response_time: float):
        """更新性能指标"""
        self.analysis_count += 1
        
        # 计算移动平均
        alpha = 0.1  # 学习率
        self.accuracy_score = (1 - alpha) * self.accuracy_score + alpha * accuracy
        self.response_time = (1 - alpha) * self.response_time + alpha * response_time
        
        self.logger.info(f"Performance updated: accuracy={self.accuracy_score:.3f}, response_time={self.response_time:.3f}")

class AnalystAgent(BaseAgent):
    """分析师智能体基类"""
    
    def __init__(self, agent_id: str, role: AgentRole, llm_config: Dict[str, Any], **kwargs):
        super().__init__(agent_id, role, llm_config, **kwargs)
        self.analysis_history = []
        self.specialization = self._get_specialization()
    
    @abstractmethod
    def _get_specialization(self) -> str:
        """获取专业领域"""
        pass
    
    async def analyze(self, market_data: Dict[str, Any]) -> AnalysisResult:
        """执行分析"""
        start_time = datetime.now()
        
        try:
            # 执行具体分析
            analysis = await self._perform_analysis(market_data)
            
            # 计算响应时间
            response_time = (datetime.now() - start_time).total_seconds()
            
            # 创建分析结果
            result = AnalysisResult(
                agent_id=self.agent_id,
                analysis_type=self.specialization,
                confidence=analysis.get('confidence', 0.5),
                signals=analysis.get('signals', []),
                reasoning=analysis.get('reasoning', ''),
                timestamp=datetime.now(),
                metadata=analysis.get('metadata', {})
            )
            
            # 更新性能指标
            self.update_performance_metrics(result.confidence, response_time)
            
            # 保存到历史
            self.analysis_history.append(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            raise
    
    @abstractmethod
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行具体的分析逻辑"""
        pass

class ResearchAgent(BaseAgent):
    """研究员智能体基类"""
    
    def __init__(self, agent_id: str, role: AgentRole, llm_config: Dict[str, Any], **kwargs):
        super().__init__(agent_id, role, llm_config, **kwargs)
        self.research_bias = self._get_research_bias()
        self.debate_history = []
    
    @abstractmethod
    def _get_research_bias(self) -> str:
        """获取研究偏向（看涨/看跌）"""
        pass
    
    async def evaluate_analysis(self, analyst_reports: List[AnalysisResult]) -> Dict[str, Any]:
        """评估分析师报告"""
        try:
            # 执行研究评估
            evaluation = await self._perform_evaluation(analyst_reports)
            
            # 生成研究观点
            opinion = await self._generate_opinion(evaluation)
            
            return {
                'evaluation': evaluation,
                'opinion': opinion,
                'bias': self.research_bias,
                'confidence': evaluation.get('confidence', 0.5)
            }
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {e}")
            raise
    
    @abstractmethod
    async def _perform_evaluation(self, analyst_reports: List[AnalysisResult]) -> Dict[str, Any]:
        """执行具体的评估逻辑"""
        pass
    
    @abstractmethod
    async def _generate_opinion(self, evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """生成研究观点"""
        pass

class TradingAgent(BaseAgent):
    """交易智能体基类"""
    
    def __init__(self, agent_id: str, role: AgentRole, llm_config: Dict[str, Any], **kwargs):
        super().__init__(agent_id, role, llm_config, **kwargs)
        self.trading_history = []
        self.current_positions = {}
    
    async def make_decision(
        self,
        analyst_reports: List[AnalysisResult],
        research_opinions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """制定交易决策"""
        try:
            # 综合分析所有信息
            synthesis = await self._synthesize_information(analyst_reports, research_opinions)
            
            # 制定交易策略
            strategy = await self._develop_strategy(synthesis)
            
            # 生成具体交易参数
            trade_params = await self._generate_trade_parameters(strategy)
            
            decision = {
                'action': trade_params.get('action', 'HOLD'),
                'quantity': trade_params.get('quantity', 0),
                'entry_price': trade_params.get('entry_price'),
                'stop_loss': trade_params.get('stop_loss'),
                'take_profit': trade_params.get('take_profit'),
                'reasoning': synthesis.get('reasoning', ''),
                'confidence': synthesis.get('confidence', 0.5),
                'risk_level': trade_params.get('risk_level', 'MEDIUM')
            }
            
            # 记录决策历史
            self.trading_history.append({
                'timestamp': datetime.now(),
                'decision': decision,
                'inputs': {
                    'analyst_reports': len(analyst_reports),
                    'research_opinions': len(research_opinions)
                }
            })
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Decision making failed: {e}")
            raise
    
    @abstractmethod
    async def _synthesize_information(
        self,
        analyst_reports: List[AnalysisResult],
        research_opinions: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """综合分析信息"""
        pass
    
    @abstractmethod
    async def _develop_strategy(self, synthesis: Dict[str, Any]) -> Dict[str, Any]:
        """制定交易策略"""
        pass
    
    @abstractmethod
    async def _generate_trade_parameters(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """生成交易参数"""
        pass
