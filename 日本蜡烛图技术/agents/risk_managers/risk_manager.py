"""
风险管理智能体
负责评估和控制交易风险
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from agents.base.agent import AnalystAgent, AgentRole, AnalysisResult, Message, MessageType
from llm_integration.llm_manager import llm_manager
from llm_integration.prompts.candlestick_prompts import RISK_MANAGER_PROMPT


class RiskManager(AnalystAgent):
    """风险管理智能体"""
    
    def __init__(self, agent_id: str, llm_config: Optional[Dict[str, Any]] = None):
        super().__init__(agent_id, AgentRole.RISK_MANAGER, llm_config)
        self.max_portfolio_risk = 0.10  # 最大组合风险10%
        self.max_single_position = 0.05  # 单个仓位最大5%
        self.risk_tolerance = "conservative"  # conservative, moderate, aggressive
        
    def _get_specialization(self) -> str:
        """获取专业化领域"""
        return "risk_management"

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
        你是一位资深的风险管理专家，拥有20年以上的金融风险管理经验。
        
        你的核心职责：
        - 保护投资组合免受重大损失
        - 确保风险敞口在可控范围内
        - 提供专业的风险评估和建议
        - 制定有效的风险缓解策略
        
        你的专业技能：
        - 精通各种风险度量方法
        - 熟悉市场风险、信用风险、流动性风险
        - 擅长压力测试和情景分析
        - 具备丰富的危机处理经验
        
        你的风险管理原则：
        1. 风险控制优先于收益追求
        2. 分散投资，避免过度集中
        3. 设置多层次的风险防护
        4. 持续监控和动态调整
        5. 保持充足的安全边际
        
        你的决策标准：
        - 单笔交易风险不超过总资金的2%
        - 组合总风险不超过10%
        - 风险收益比至少1:2
        - 必须有明确的止损计划
        
        请始终保持谨慎、专业的风险管理态度。
        """
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行风险评估分析"""
        try:
            # 提取交易决策信息
            trading_decision = market_data.get('trading_decision', {})
            market_context = market_data.get('market_context', {})
            symbol = market_data.get('symbol', 'Unknown')
            
            # 构建风险评估提示词
            prompt = RISK_MANAGER_PROMPT.format(
                trading_decision=json.dumps(trading_decision, indent=2, ensure_ascii=False),
                market_context=json.dumps(market_context, indent=2, ensure_ascii=False)
            )
            
            # 调用LLM进行风险评估
            response = await llm_manager.generate_response(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                temperature=0.1,  # 低温度确保风险评估的一致性
                max_tokens=2000
            )
            
            # 解析响应
            risk_assessment = self._parse_llm_response(response.content)
            
            # 验证和调整风险评估
            risk_assessment = self._validate_risk_assessment(risk_assessment, trading_decision)
            
            # 添加元数据
            risk_assessment.update({
                'agent_id': self.agent_id,
                'agent_role': self.role.value,
                'risk_tolerance': self.risk_tolerance,
                'assessment_timestamp': datetime.now().isoformat(),
                'llm_tokens_used': response.tokens_used,
                'llm_cost': response.cost
            })
            
            self.logger.info(f"Risk assessment completed for {symbol}: {risk_assessment.get('risk_level')}")
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {e}")
            return self._get_fallback_assessment(market_data)
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，创建默认评估
                return self._get_default_assessment()
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse LLM response as JSON: {e}")
            return self._get_default_assessment()
    
    def _validate_risk_assessment(self, assessment: Dict[str, Any], trading_decision: Dict[str, Any]) -> Dict[str, Any]:
        """验证和调整风险评估"""
        # 确保风险等级有效
        if assessment.get('risk_level') not in ['LOW', 'MEDIUM', 'HIGH']:
            assessment['risk_level'] = 'MEDIUM'
        
        # 基于交易决策调整风险等级
        decision = trading_decision.get('decision', 'HOLD')
        confidence = trading_decision.get('confidence', 0.5)
        
        # 如果交易决策置信度低，提高风险等级
        if confidence < 0.6:
            if assessment['risk_level'] == 'LOW':
                assessment['risk_level'] = 'MEDIUM'
            elif assessment['risk_level'] == 'MEDIUM':
                assessment['risk_level'] = 'HIGH'
        
        # 确保仓位建议字段
        if 'position_recommendation' not in assessment:
            assessment['position_recommendation'] = {
                'approved': decision != 'HOLD' and confidence > 0.6,
                'max_position_size': self.max_single_position,
                'conditions': ['确认技术信号', '控制风险敞口']
            }
        
        # 调整仓位大小基于风险等级
        risk_level = assessment['risk_level']
        if risk_level == 'HIGH':
            assessment['position_recommendation']['max_position_size'] = min(
                assessment['position_recommendation'].get('max_position_size', self.max_single_position),
                0.02  # 高风险时最大2%
            )
        elif risk_level == 'MEDIUM':
            assessment['position_recommendation']['max_position_size'] = min(
                assessment['position_recommendation'].get('max_position_size', self.max_single_position),
                0.03  # 中等风险时最大3%
            )
        
        return assessment
    
    def _get_default_assessment(self) -> Dict[str, Any]:
        """获取默认风险评估"""
        return {
            'risk_level': 'MEDIUM',
            'overall_assessment': '需要进一步评估风险因素',
            'risk_factors': [
                {
                    'factor': '市场波动性',
                    'impact': 'MEDIUM',
                    'probability': 0.6,
                    'mitigation': '控制仓位大小'
                }
            ],
            'position_recommendation': {
                'approved': False,
                'max_position_size': self.max_single_position,
                'conditions': ['等待更明确的信号']
            },
            'monitoring_plan': {
                'key_metrics': ['价格变动', '成交量', '技术指标'],
                'alert_levels': ['2%止损', '5%获利'],
                'review_frequency': '每日'
            },
            'recommendations': [
                '保持谨慎的仓位管理',
                '设置明确的止损点',
                '密切监控市场变化'
            ]
        }
    
    def _get_fallback_assessment(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取备用风险评估"""
        trading_decision = market_data.get('trading_decision', {})
        decision = trading_decision.get('decision', 'HOLD')
        confidence = trading_decision.get('confidence', 0.5)
        
        # 基于简单规则的风险评估
        if decision == 'HOLD':
            risk_level = 'LOW'
            approved = False
        elif confidence > 0.8:
            risk_level = 'LOW'
            approved = True
        elif confidence > 0.6:
            risk_level = 'MEDIUM'
            approved = True
        else:
            risk_level = 'HIGH'
            approved = False
        
        fallback_assessment = self._get_default_assessment()
        fallback_assessment.update({
            'risk_level': risk_level,
            'overall_assessment': f'基于交易决策的风险评估：{decision}，置信度{confidence:.2f}',
            'position_recommendation': {
                'approved': approved,
                'max_position_size': self.max_single_position if approved else 0,
                'conditions': ['确认信号强度', '控制风险敞口'] if approved else ['等待更好机会']
            },
            'agent_id': self.agent_id,
            'agent_role': self.role.value,
            'risk_tolerance': self.risk_tolerance,
            'assessment_timestamp': datetime.now().isoformat(),
            'fallback_used': True
        })
        
        return fallback_assessment
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理消息"""
        if message.message_type == MessageType.TRADING_SIGNAL:
            # 处理交易信号，进行风险评估
            market_data = {
                'trading_decision': message.content,
                'market_context': message.content.get('market_context', {}),
                'symbol': message.content.get('symbol', 'Unknown')
            }
            
            risk_assessment = await self._perform_analysis(market_data)
            
            # 创建风险评估消息
            response_message = Message(
                message_type=MessageType.RISK_ASSESSMENT,
                from_agent=self.agent_id,
                to_agents=['portfolio_manager_001'],  # 发送给组合管理
                content=risk_assessment,
                timestamp=datetime.now(),
                message_id=f"risk_assessment_{datetime.now().timestamp()}",
                priority=1  # 高优先级
            )
            
            return response_message
        
        elif message.message_type == MessageType.DECISION_REQUEST:
            # 处理工作流决策请求
            workflow_id = message.content.get('workflow_id')
            stage = message.content.get('stage')
            
            if stage == 'risk_management':
                risk_assessment = await self._perform_analysis(message.content)
                
                # 通知工作流完成
                if self.communication_hub:
                    await self.communication_hub.complete_workflow_stage(
                        workflow_id=workflow_id,
                        stage_name=stage,
                        result=risk_assessment
                    )
        
        return None
    
    def get_portfolio_risk_metrics(self) -> Dict[str, Any]:
        """获取组合风险指标"""
        return {
            'max_portfolio_risk': self.max_portfolio_risk,
            'max_single_position': self.max_single_position,
            'risk_tolerance': self.risk_tolerance,
            'current_risk_utilization': 0.0,  # 当前风险使用率
            'available_risk_capacity': self.max_portfolio_risk  # 可用风险容量
        }
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        base_status = super().get_status()
        base_status.update({
            'risk_tolerance': self.risk_tolerance,
            'max_portfolio_risk': self.max_portfolio_risk,
            'max_single_position': self.max_single_position,
            'specialization': '风险评估与控制'
        })
        return base_status
