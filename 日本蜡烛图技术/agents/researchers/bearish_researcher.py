"""
看跌研究员智能体
专门识别市场风险和下跌信号
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from agents.base.agent import AnalystAgent, AgentRole, AnalysisResult, Message, MessageType
from llm_integration.llm_manager import llm_manager
from llm_integration.prompts.candlestick_prompts import BEARISH_RESEARCHER_PROMPT


class BearishResearcher(AnalystAgent):
    """看跌研究员智能体"""
    
    def __init__(self, agent_id: str, llm_config: Optional[Dict[str, Any]] = None):
        super().__init__(agent_id, AgentRole.BEARISH_RESEARCHER, llm_config)
        self.bias = "bearish"
        self.research_focus = [
            "风险信号识别",
            "下跌风险分析", 
            "卖出时机评估",
            "风险控制建议"
        ]
    
    def _get_specialization(self) -> str:
        """获取专业化领域"""
        return "bearish_research"

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
        你是一位谨慎的看跌研究员，以风险控制为核心理念，专门识别市场中的风险因素。
        
        你的专长：
        - 识别市场中的风险信号和看跌形态
        - 分析可能导致价格下跌的因素
        - 评估投资风险和下行压力
        - 提供风险防护和对冲策略
        
        你的分析风格：
        - 谨慎保守，风险优先
        - 基于数据和历史经验
        - 关注潜在的负面因素
        - 提供具体的风险防护建议
        
        你的目标不是唱空市场，而是帮助投资者识别和管理风险，做出更明智的决策。
        """
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行看跌研究分析"""
        try:
            # 提取关键信息
            symbol = market_data.get('symbol', 'Unknown')
            patterns = market_data.get('patterns', [])
            technical_analysis = market_data.get('technical_analysis', {})
            
            # 构建分析提示词
            analysis_data = {
                'symbol': symbol,
                'patterns': patterns,
                'technical_analysis': technical_analysis,
                'timestamp': datetime.now().isoformat()
            }
            
            prompt = BEARISH_RESEARCHER_PROMPT.format(
                analysis_data=json.dumps(analysis_data, indent=2, ensure_ascii=False)
            )
            
            # 调用LLM进行分析
            response = await llm_manager.generate_response(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                temperature=0.2,  # 较低的温度保持一致性
                max_tokens=1500
            )
            
            # 解析响应
            analysis_result = self._parse_llm_response(response.content)
            
            # 添加元数据
            analysis_result.update({
                'agent_id': self.agent_id,
                'agent_role': self.role.value,
                'bias': self.bias,
                'analysis_timestamp': datetime.now().isoformat(),
                'llm_tokens_used': response.tokens_used,
                'llm_cost': response.cost
            })
            
            self.logger.info(f"Bearish research completed for {symbol}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Bearish research analysis failed: {e}")
            return self._get_fallback_analysis(market_data)
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，创建结构化响应
                return {
                    'opinion': 'BEARISH',
                    'confidence': 0.6,
                    'reasoning': response_content[:200] + '...' if len(response_content) > 200 else response_content,
                    'key_factors': ['技术面显示风险信号'],
                    'target_price': '待确定',
                    'time_horizon': '中短期',
                    'risk_factors': ['技术面风险'],
                    'protection_strategies': ['风险控制']
                }
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse LLM response as JSON: {e}")
            return self._get_fallback_analysis({})
    
    def _get_fallback_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取备用分析结果"""
        symbol = market_data.get('symbol', 'Unknown')
        patterns = market_data.get('patterns', [])
        
        # 简单的规则基础分析
        bearish_patterns = [p for p in patterns if p.get('signal') == 'BEARISH']
        neutral_patterns = [p for p in patterns if p.get('signal') == 'NEUTRAL']
        
        # 看跌研究员更关注风险，即使没有明显看跌信号也会保持谨慎
        confidence = min(0.8, len(bearish_patterns) * 0.3 + len(neutral_patterns) * 0.1 + 0.4)
        
        return {
            'opinion': 'BEARISH',
            'confidence': confidence,
            'reasoning': f'发现{len(bearish_patterns)}个看跌形态，需要关注下行风险',
            'key_factors': [
                f'识别到{len(bearish_patterns)}个看跌形态',
                '市场存在不确定性',
                '需要谨慎评估风险'
            ],
            'target_price': '基于风险评估确定',
            'time_horizon': '短中期',
            'risk_factors': [
                '技术面显示弱势',
                '市场波动性较高',
                '需要关注宏观环境'
            ],
            'protection_strategies': [
                '适当降低仓位',
                '设置止损保护',
                '分散投资风险'
            ],
            'agent_id': self.agent_id,
            'agent_role': self.role.value,
            'bias': self.bias,
            'analysis_timestamp': datetime.now().isoformat(),
            'fallback_used': True
        }
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理消息"""
        if message.message_type == MessageType.ANALYSIS_REPORT:
            # 处理技术分析报告
            analysis_result = await self._perform_analysis(message.content)
            
            # 创建研究观点消息
            response_message = Message(
                message_type=MessageType.RESEARCH_OPINION,
                from_agent=self.agent_id,
                to_agents=['trader_001', 'risk_manager_001'],  # 发送给交易员和风险管理
                content=analysis_result,
                timestamp=datetime.now(),
                message_id=f"bearish_opinion_{datetime.now().timestamp()}",
                priority=2
            )
            
            return response_message
        
        elif message.message_type == MessageType.DECISION_REQUEST:
            # 处理决策请求
            workflow_id = message.content.get('workflow_id')
            stage = message.content.get('stage')
            
            if stage == 'research':
                analysis_result = await self._perform_analysis(message.content)
                
                # 通知工作流完成
                if self.communication_hub:
                    await self.communication_hub.complete_workflow_stage(
                        workflow_id=workflow_id,
                        stage_name=stage,
                        result=analysis_result
                    )
        
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        base_status = super().get_status()
        base_status.update({
            'bias': self.bias,
            'research_focus': self.research_focus,
            'specialization': '风险识别与控制'
        })
        return base_status
