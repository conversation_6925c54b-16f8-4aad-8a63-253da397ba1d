"""
看涨研究员智能体
专门寻找和分析市场中的买入机会
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from agents.base.agent import AnalystAgent, AgentRole, AnalysisResult, Message, MessageType
from llm_integration.llm_manager import llm_manager
from llm_integration.prompts.candlestick_prompts import BULLISH_RESEARCHER_PROMPT


class BullishResearcher(AnalystAgent):
    """看涨研究员智能体"""
    
    def __init__(self, agent_id: str, llm_config: Optional[Dict[str, Any]] = None):
        super().__init__(agent_id, AgentRole.BULLISH_RESEARCHER, llm_config)
        self.bias = "bullish"
        self.research_focus = [
            "看涨形态识别",
            "上涨催化剂分析", 
            "买入时机评估",
            "目标价位预测"
        ]
    
    def _get_specialization(self) -> str:
        """获取专业化领域"""
        return "bullish_research"

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
        你是一位专业的看涨研究员，拥有敏锐的市场洞察力和丰富的投资经验。
        
        你的专长：
        - 识别市场中的买入机会和看涨信号
        - 分析技术形态的上涨潜力
        - 寻找推动价格上涨的催化剂
        - 评估风险收益比和投资价值
        
        你的分析风格：
        - 积极寻找机会，但保持理性
        - 基于数据和技术分析
        - 关注长期价值和短期机会
        - 提供具体的目标价位和时间框架
        
        请始终保持专业、客观的分析态度，即使在看涨的同时也要考虑风险因素。
        """
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行看涨研究分析"""
        try:
            # 提取关键信息
            symbol = market_data.get('symbol', 'Unknown')
            patterns = market_data.get('patterns', [])
            technical_analysis = market_data.get('technical_analysis', {})
            
            # 构建分析提示词
            analysis_data = {
                'symbol': symbol,
                'patterns': patterns,
                'technical_analysis': technical_analysis,
                'timestamp': datetime.now().isoformat()
            }
            
            prompt = BULLISH_RESEARCHER_PROMPT.format(
                analysis_data=json.dumps(analysis_data, indent=2, ensure_ascii=False)
            )
            
            # 调用LLM进行分析
            response = await llm_manager.generate_response(
                prompt=prompt,
                system_prompt=self.get_system_prompt(),
                temperature=0.2,  # 较低的温度保持一致性
                max_tokens=1500
            )
            
            # 解析响应
            analysis_result = self._parse_llm_response(response.content)
            
            # 添加元数据
            analysis_result.update({
                'agent_id': self.agent_id,
                'agent_role': self.role.value,
                'bias': self.bias,
                'analysis_timestamp': datetime.now().isoformat(),
                'llm_tokens_used': response.tokens_used,
                'llm_cost': response.cost
            })
            
            self.logger.info(f"Bullish research completed for {symbol}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Bullish research analysis failed: {e}")
            return self._get_fallback_analysis(market_data)
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_content[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果没有找到JSON，创建结构化响应
                return {
                    'opinion': 'BULLISH',
                    'confidence': 0.7,
                    'reasoning': response_content[:200] + '...' if len(response_content) > 200 else response_content,
                    'key_factors': ['技术面显示积极信号'],
                    'target_price': '待确定',
                    'time_horizon': '中短期',
                    'catalysts': ['技术突破'],
                    'risk_assessment': '需要关注市场整体环境'
                }
                
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse LLM response as JSON: {e}")
            return self._get_fallback_analysis({})
    
    def _get_fallback_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取备用分析结果"""
        symbol = market_data.get('symbol', 'Unknown')
        patterns = market_data.get('patterns', [])
        
        # 简单的规则基础分析
        bullish_patterns = [p for p in patterns if p.get('signal') == 'BULLISH']
        confidence = min(0.8, len(bullish_patterns) * 0.2 + 0.4)
        
        return {
            'opinion': 'BULLISH',
            'confidence': confidence,
            'reasoning': f'发现{len(bullish_patterns)}个看涨形态，技术面显示积极信号',
            'key_factors': [
                f'识别到{len(bullish_patterns)}个看涨形态',
                '技术指标支持上涨',
                '市场情绪积极'
            ],
            'target_price': '基于技术分析确定',
            'time_horizon': '短中期',
            'catalysts': [
                '技术形态突破',
                '成交量配合'
            ],
            'risk_assessment': '需要关注整体市场风险',
            'agent_id': self.agent_id,
            'agent_role': self.role.value,
            'bias': self.bias,
            'analysis_timestamp': datetime.now().isoformat(),
            'fallback_used': True
        }
    
    async def process_message(self, message: Message) -> Optional[Message]:
        """处理消息"""
        if message.message_type == MessageType.ANALYSIS_REPORT:
            # 处理技术分析报告
            analysis_result = await self._perform_analysis(message.content)
            
            # 创建研究观点消息
            response_message = Message(
                message_type=MessageType.RESEARCH_OPINION,
                from_agent=self.agent_id,
                to_agents=['trader_001', 'risk_manager_001'],  # 发送给交易员和风险管理
                content=analysis_result,
                timestamp=datetime.now(),
                message_id=f"bullish_opinion_{datetime.now().timestamp()}",
                priority=2
            )
            
            return response_message
        
        elif message.message_type == MessageType.DECISION_REQUEST:
            # 处理决策请求
            workflow_id = message.content.get('workflow_id')
            stage = message.content.get('stage')
            
            if stage == 'research':
                analysis_result = await self._perform_analysis(message.content)
                
                # 通知工作流完成
                if self.communication_hub:
                    await self.communication_hub.complete_workflow_stage(
                        workflow_id=workflow_id,
                        stage_name=stage,
                        result=analysis_result
                    )
        
        return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        base_status = super().get_status()
        base_status.update({
            'bias': self.bias,
            'research_focus': self.research_focus,
            'specialization': '看涨机会识别'
        })
        return base_status
