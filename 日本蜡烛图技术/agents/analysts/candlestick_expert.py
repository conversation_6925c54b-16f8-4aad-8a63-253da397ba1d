"""
蜡烛图形态专家智能体
基于现有的蜡烛图形态识别系统，增加LLM智能解读能力
"""

import asyncio
from typing import Dict, List, Any
from datetime import datetime
import json

from ..base.agent import AnalystAgent, AgentRole, AnalysisResult, MessageType
from ...src.patterns.pattern_analyzer import PatternAnalyzer
from ...src.models.pattern import PatternResult, PatternName, PatternSignal


class CandlestickPatternExpert(AnalystAgent):
    """蜡烛图形态专家智能体"""
    
    def __init__(self, agent_id: str, llm_config: Dict[str, Any], **kwargs):
        super().__init__(
            agent_id=agent_id,
            role=AgentRole.TECHNICAL_ANALYST,
            llm_config=llm_config,
            **kwargs
        )
        
        # 初始化现有的形态识别系统
        self.pattern_analyzer = PatternAnalyzer()
        
        # 形态权重配置（可根据历史表现调整）
        self.pattern_weights = {
            PatternName.MORNING_STAR: 0.95,
            PatternName.EVENING_STAR: 0.95,
            PatternName.BULLISH_ENGULFING: 0.85,
            PatternName.BEARISH_ENGULFING: 0.85,
            PatternName.HAMMER: 0.75,
            PatternName.HANGING_MAN: 0.75,
            PatternName.DOJI: 0.60,
            PatternName.THREE_WHITE_SOLDIERS: 0.90,
            PatternName.THREE_BLACK_CROWS: 0.90,
            # 可以继续添加更多形态权重
        }
        
        # 市场环境因子
        self.market_context_factors = {
            'trend_strength': 1.0,
            'volume_confirmation': 1.0,
            'support_resistance': 1.0,
            'market_volatility': 1.0
        }
    
    def _get_specialization(self) -> str:
        return "candlestick_pattern_analysis"
    
    def get_system_prompt(self) -> str:
        return """你是一位资深的蜡烛图形态分析专家，专门研究日本蜡烛图技术。

你的专业能力包括：
1. 识别和解读各种经典蜡烛图形态
2. 分析形态的市场含义和交易机会
3. 评估形态的可靠性和成功概率
4. 结合市场环境提供交易建议

分析时请考虑：
- 形态的完整性和标准性
- 当前市场趋势和环境
- 成交量确认情况
- 支撑阻力位置关系
- 形态出现的时机和背景

请用专业、客观的语言提供分析，并给出明确的交易信号和风险提示。"""
    
    async def _perform_analysis(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行蜡烛图形态分析"""
        try:
            # 1. 使用现有系统识别形态
            candles = market_data.get('candles', [])
            if not candles:
                raise ValueError("No candle data provided")
            
            # 转换数据格式
            candle_objects = self._convert_to_candle_objects(candles)
            
            # 执行形态识别
            patterns = await self._identify_patterns(candle_objects)
            
            # 2. LLM智能解读
            interpretation = await self._llm_interpret_patterns(patterns, market_data)
            
            # 3. 生成交易信号
            signals = self._generate_trading_signals(patterns, interpretation)
            
            # 4. 计算综合置信度
            confidence = self._calculate_overall_confidence(patterns, interpretation)
            
            # 5. 生成详细推理
            reasoning = await self._generate_reasoning(patterns, interpretation, signals)
            
            return {
                'patterns': [self._pattern_to_dict(p) for p in patterns],
                'interpretation': interpretation,
                'signals': signals,
                'confidence': confidence,
                'reasoning': reasoning,
                'metadata': {
                    'total_patterns': len(patterns),
                    'bullish_patterns': len([p for p in patterns if p.signal == PatternSignal.BULLISH]),
                    'bearish_patterns': len([p for p in patterns if p.signal == PatternSignal.BEARISH]),
                    'analysis_timestamp': datetime.now().isoformat(),
                    'market_context': self._assess_market_context(market_data)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
            raise
    
    def _convert_to_candle_objects(self, candles: List[Dict]) -> List[Any]:
        """转换蜡烛数据格式"""
        # 这里需要根据你的Candle类实现具体转换
        # 假设你有一个Candle类
        from ...src.models.candle import Candle
        
        candle_objects = []
        for candle_data in candles:
            candle = Candle(
                open=float(candle_data['open']),
                high=float(candle_data['high']),
                low=float(candle_data['low']),
                close=float(candle_data['close']),
                volume=int(candle_data.get('volume', 0)),
                timestamp=candle_data['timestamp']
            )
            candle_objects.append(candle)
        
        return candle_objects
    
    async def _identify_patterns(self, candles: List[Any]) -> List[PatternResult]:
        """识别蜡烛图形态"""
        # 使用现有的形态识别系统
        patterns = self.pattern_analyzer.analyze_patterns(candles)
        
        # 过滤和排序
        filtered_patterns = []
        for pattern in patterns:
            # 只保留置信度较高的形态
            if pattern.confidence >= 0.6:
                filtered_patterns.append(pattern)
        
        # 按置信度排序
        filtered_patterns.sort(key=lambda x: x.confidence, reverse=True)
        
        return filtered_patterns
    
    async def _llm_interpret_patterns(
        self,
        patterns: List[PatternResult],
        market_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """使用LLM智能解读形态"""
        if not patterns:
            return {
                'summary': '未发现明显的蜡烛图形态',
                'market_outlook': 'NEUTRAL',
                'key_insights': [],
                'risk_factors': []
            }
        
        # 构建LLM输入
        pattern_descriptions = []
        for pattern in patterns[:5]:  # 只分析前5个最重要的形态
            desc = {
                'name': pattern.pattern_name.value,
                'chinese_name': self._get_chinese_name(pattern.pattern_name),
                'signal': pattern.signal.value,
                'confidence': pattern.confidence,
                'description': pattern.description,
                'position': f"第{pattern.start_index}-{pattern.end_index}根蜡烛"
            }
            pattern_descriptions.append(desc)
        
        # 市场环境信息
        market_context = self._assess_market_context(market_data)
        
        # LLM提示词
        prompt = f"""
作为蜡烛图形态专家，请分析以下识别出的形态：

识别到的形态：
{json.dumps(pattern_descriptions, ensure_ascii=False, indent=2)}

市场环境：
{json.dumps(market_context, ensure_ascii=False, indent=2)}

请提供：
1. 形态组合的整体解读
2. 市场前景判断（BULLISH/BEARISH/NEUTRAL）
3. 关键洞察和交易机会
4. 风险因素和注意事项

请用专业但易懂的语言回答。
"""
        
        # 调用LLM（这里需要实现具体的LLM调用）
        llm_response = await self._call_llm(prompt)
        
        return self._parse_llm_response(llm_response)
    
    def _generate_trading_signals(
        self,
        patterns: List[PatternResult],
        interpretation: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成交易信号"""
        signals = []
        
        for pattern in patterns:
            # 基础信号强度
            signal_strength = pattern.confidence * self.pattern_weights.get(pattern.pattern_name, 0.5)
            
            # 根据市场环境调整
            market_outlook = interpretation.get('market_outlook', 'NEUTRAL')
            if pattern.signal == PatternSignal.BULLISH and market_outlook == 'BULLISH':
                signal_strength *= 1.2
            elif pattern.signal == PatternSignal.BEARISH and market_outlook == 'BEARISH':
                signal_strength *= 1.2
            elif pattern.signal.value != market_outlook and market_outlook != 'NEUTRAL':
                signal_strength *= 0.8
            
            # 生成具体信号
            signal = {
                'pattern_name': pattern.pattern_name.value,
                'signal_type': pattern.signal.value,
                'strength': min(signal_strength, 1.0),  # 限制在1.0以内
                'entry_suggestion': self._get_entry_suggestion(pattern),
                'stop_loss_suggestion': self._get_stop_loss_suggestion(pattern),
                'take_profit_suggestion': self._get_take_profit_suggestion(pattern),
                'time_horizon': self._get_time_horizon(pattern),
                'risk_level': self._assess_risk_level(pattern, signal_strength)
            }
            
            signals.append(signal)
        
        return signals
    
    def _calculate_overall_confidence(
        self,
        patterns: List[PatternResult],
        interpretation: Dict[str, Any]
    ) -> float:
        """计算综合置信度"""
        if not patterns:
            return 0.0
        
        # 加权平均置信度
        total_weight = 0
        weighted_confidence = 0
        
        for pattern in patterns:
            weight = self.pattern_weights.get(pattern.pattern_name, 0.5)
            weighted_confidence += pattern.confidence * weight
            total_weight += weight
        
        base_confidence = weighted_confidence / total_weight if total_weight > 0 else 0
        
        # 根据形态一致性调整
        bullish_count = len([p for p in patterns if p.signal == PatternSignal.BULLISH])
        bearish_count = len([p for p in patterns if p.signal == PatternSignal.BEARISH])
        
        if bullish_count > 0 and bearish_count > 0:
            # 信号冲突，降低置信度
            base_confidence *= 0.8
        elif len(patterns) > 1 and (bullish_count == len(patterns) or bearish_count == len(patterns)):
            # 信号一致，提高置信度
            base_confidence *= 1.1
        
        return min(base_confidence, 1.0)
    
    async def _generate_reasoning(
        self,
        patterns: List[PatternResult],
        interpretation: Dict[str, Any],
        signals: List[Dict[str, Any]]
    ) -> str:
        """生成详细推理"""
        reasoning_parts = []
        
        # 形态识别总结
        if patterns:
            pattern_names = [self._get_chinese_name(p.pattern_name) for p in patterns[:3]]
            reasoning_parts.append(f"识别到{len(patterns)}个蜡烛图形态，主要包括：{', '.join(pattern_names)}")
        
        # 信号方向分析
        bullish_signals = [s for s in signals if s['signal_type'] == 'BULLISH']
        bearish_signals = [s for s in signals if s['signal_type'] == 'BEARISH']
        
        if bullish_signals and not bearish_signals:
            reasoning_parts.append("所有形态均指向看涨方向，建议关注买入机会")
        elif bearish_signals and not bullish_signals:
            reasoning_parts.append("所有形态均指向看跌方向，建议关注卖出机会")
        elif bullish_signals and bearish_signals:
            reasoning_parts.append("形态信号存在分歧，建议谨慎观望，等待更明确的方向")
        
        # 市场环境考虑
        market_outlook = interpretation.get('market_outlook', 'NEUTRAL')
        if market_outlook != 'NEUTRAL':
            reasoning_parts.append(f"结合当前市场环境，整体倾向{market_outlook.lower()}")
        
        # 风险提示
        high_risk_signals = [s for s in signals if s.get('risk_level') == 'HIGH']
        if high_risk_signals:
            reasoning_parts.append("部分信号风险较高，建议控制仓位")
        
        return "。".join(reasoning_parts) + "。"
    
    def _assess_market_context(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估市场环境"""
        # 这里可以添加更复杂的市场环境分析
        candles = market_data.get('candles', [])
        if len(candles) < 10:
            return {'trend': 'UNKNOWN', 'volatility': 'UNKNOWN'}
        
        # 简单的趋势分析
        recent_closes = [float(c['close']) for c in candles[-10:]]
        trend = 'UPTREND' if recent_closes[-1] > recent_closes[0] else 'DOWNTREND'
        
        # 简单的波动率分析
        price_changes = [abs(recent_closes[i] - recent_closes[i-1]) for i in range(1, len(recent_closes))]
        avg_change = sum(price_changes) / len(price_changes)
        volatility = 'HIGH' if avg_change > recent_closes[-1] * 0.02 else 'NORMAL'
        
        return {
            'trend': trend,
            'volatility': volatility,
            'price_level': recent_closes[-1],
            'volume_trend': 'UNKNOWN'  # 需要更多数据分析
        }
    
    def _get_chinese_name(self, pattern_name: PatternName) -> str:
        """获取形态中文名称"""
        # 使用现有的中文名称映射
        from ...src.models.pattern import PatternName
        chinese_names = {
            PatternName.MORNING_STAR: "启明星",
            PatternName.EVENING_STAR: "黄昏星",
            PatternName.BULLISH_ENGULFING: "看涨吞没",
            PatternName.BEARISH_ENGULFING: "看跌吞没",
            PatternName.HAMMER: "锤子线",
            PatternName.HANGING_MAN: "上吊线",
            PatternName.DOJI: "十字线",
            PatternName.THREE_WHITE_SOLDIERS: "前进白色三兵",
            PatternName.THREE_BLACK_CROWS: "三只乌鸦",
            # 添加更多映射
        }
        return chinese_names.get(pattern_name, pattern_name.value)
    
    def _pattern_to_dict(self, pattern: PatternResult) -> Dict[str, Any]:
        """将形态结果转换为字典"""
        return {
            'name': pattern.pattern_name.value,
            'chinese_name': self._get_chinese_name(pattern.pattern_name),
            'signal': pattern.signal.value,
            'confidence': pattern.confidence,
            'start_index': pattern.start_index,
            'end_index': pattern.end_index,
            'description': pattern.description,
            'start_time': pattern.start_time.isoformat() if pattern.start_time else None,
            'end_time': pattern.end_time.isoformat() if pattern.end_time else None
        }
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM（需要实现具体的LLM接口）"""
        # 这里需要实现具体的LLM调用逻辑
        # 可以使用OpenAI API、Claude API或本地模型
        # 暂时返回模拟响应
        return """
        {
            "summary": "发现多个重要的反转形态，显示市场可能出现转折",
            "market_outlook": "BULLISH",
            "key_insights": ["启明星形态确认底部反转", "成交量配合良好"],
            "risk_factors": ["需要关注突破确认", "整体市场环境仍需观察"]
        }
        """
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON响应
            return json.loads(response.strip())
        except json.JSONDecodeError:
            # 如果不是JSON，返回默认结构
            return {
                'summary': response.strip(),
                'market_outlook': 'NEUTRAL',
                'key_insights': [],
                'risk_factors': []
            }
    
    def _get_entry_suggestion(self, pattern: PatternResult) -> str:
        """获取入场建议"""
        if pattern.signal == PatternSignal.BULLISH:
            return "突破形态高点时买入"
        elif pattern.signal == PatternSignal.BEARISH:
            return "跌破形态低点时卖出"
        else:
            return "等待方向明确"
    
    def _get_stop_loss_suggestion(self, pattern: PatternResult) -> str:
        """获取止损建议"""
        if pattern.signal == PatternSignal.BULLISH:
            return "形态低点下方"
        elif pattern.signal == PatternSignal.BEARISH:
            return "形态高点上方"
        else:
            return "根据支撑阻力位设置"
    
    def _get_take_profit_suggestion(self, pattern: PatternResult) -> str:
        """获取止盈建议"""
        return "形态高度的1-2倍"
    
    def _get_time_horizon(self, pattern: PatternResult) -> str:
        """获取时间周期"""
        return "短期到中期（1-4周）"
    
    def _assess_risk_level(self, pattern: PatternResult, signal_strength: float) -> str:
        """评估风险等级"""
        if signal_strength >= 0.8:
            return "LOW"
        elif signal_strength >= 0.6:
            return "MEDIUM"
        else:
            return "HIGH"
