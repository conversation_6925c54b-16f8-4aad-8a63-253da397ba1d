#!/usr/bin/env python3
"""
高级智能体系统演示
展示专业的多智能体协作和LLM集成
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入新的高级智能体
from agents.communication.hub import CommunicationHub
from agents.base.agent import Message, MessageType, AgentRole
from agents.analysts.candlestick_expert import CandlestickPatternExpert
from agents.researchers.bullish_researcher import BullishResearcher
from agents.researchers.bearish_researcher import BearishResearcher
from agents.traders.professional_trader import ProfessionalTrader
from agents.risk_managers.risk_manager import RiskManager
from llm_integration.llm_manager import llm_manager


async def create_professional_team():
    """创建专业智能体团队"""
    print("👥 组建专业智能体团队...")
    
    # 创建智能体实例
    agents = {
        'candlestick_expert': CandlestickPatternExpert(
            agent_id="candlestick_expert_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.1}
        ),
        'bullish_researcher': BullishResearcher(
            agent_id="bullish_researcher_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.2}
        ),
        'bearish_researcher': BearishResearcher(
            agent_id="bearish_researcher_001", 
            llm_config={'model': 'gpt-4', 'temperature': 0.2}
        ),
        'professional_trader': ProfessionalTrader(
            agent_id="professional_trader_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.1}
        ),
        'risk_manager': RiskManager(
            agent_id="risk_manager_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.1}
        )
    }
    
    for name, agent in agents.items():
        print(f"   ✅ {agent.agent_id} ({agent.role.value})")
    
    return agents


async def simulate_professional_analysis():
    """模拟专业分析流程"""
    print("\n📊 开始专业市场分析流程...")
    
    # 创建更复杂的市场数据
    market_data = {
        'symbol': 'TSLA',
        'timeframe': '4H',
        'candles': [
            # 模拟一个完整的下降趋势 + 反转形态
            {'open': 250.0, 'high': 255.0, 'low': 245.0, 'close': 248.0, 'volume': 2000000, 'timestamp': '2024-01-01T09:00:00Z'},
            {'open': 248.0, 'high': 252.0, 'low': 240.0, 'close': 242.0, 'volume': 2200000, 'timestamp': '2024-01-01T13:00:00Z'},
            {'open': 242.0, 'high': 245.0, 'low': 235.0, 'close': 238.0, 'volume': 2400000, 'timestamp': '2024-01-01T17:00:00Z'},
            {'open': 238.0, 'high': 241.0, 'low': 230.0, 'close': 233.0, 'volume': 2600000, 'timestamp': '2024-01-01T21:00:00Z'},
            {'open': 233.0, 'high': 236.0, 'low': 225.0, 'close': 228.0, 'volume': 2800000, 'timestamp': '2024-01-02T01:00:00Z'},
            # 启明星形态 - 强烈的反转信号
            {'open': 228.0, 'high': 230.0, 'low': 220.0, 'close': 222.0, 'volume': 3000000, 'timestamp': '2024-01-02T05:00:00Z'},  # 大阴线
            {'open': 222.0, 'high': 225.0, 'low': 221.0, 'close': 223.5, 'volume': 1500000, 'timestamp': '2024-01-02T09:00:00Z'},  # 小实体
            {'open': 224.0, 'high': 235.0, 'low': 223.0, 'close': 232.0, 'volume': 3200000, 'timestamp': '2024-01-02T13:00:00Z'},  # 大阳线
            # 确认信号
            {'open': 232.0, 'high': 238.0, 'low': 230.0, 'close': 236.0, 'volume': 2800000, 'timestamp': '2024-01-02T17:00:00Z'},
            {'open': 236.0, 'high': 242.0, 'low': 234.0, 'close': 240.0, 'volume': 2600000, 'timestamp': '2024-01-02T21:00:00Z'},
        ],
        'market_context': {
            'sector': 'Electric Vehicles',
            'market_cap': 'Large Cap',
            'volatility': 'High',
            'recent_news': 'Positive earnings report',
            'overall_market': 'Bullish'
        }
    }
    
    print(f"   标的: {market_data['symbol']} (特斯拉)")
    print(f"   时间框架: {market_data['timeframe']}")
    print(f"   数据点: {len(market_data['candles'])}根蜡烛")
    print(f"   市场环境: {market_data['market_context']['overall_market']}")
    print()
    
    return market_data


async def run_professional_workflow(agents, market_data):
    """运行专业工作流"""
    print("🚀 启动专业智能体协作工作流...")
    
    # 创建通信中心
    hub = CommunicationHub()
    await hub.start()
    
    # 注册所有智能体
    for agent in agents.values():
        hub.register_agent(agent)
        agent.communication_hub = hub
    
    print(f"✅ 通信中心启动，注册{len(agents)}个专业智能体")
    print()
    
    try:
        # 第1步：蜡烛图专家分析
        print("🔍 第1步：蜡烛图专家技术分析...")
        candlestick_expert = agents['candlestick_expert']
        technical_analysis = await candlestick_expert.analyze(market_data)
        
        print(f"   ✅ 识别形态: {len(technical_analysis['patterns'])}个")
        print(f"   ✅ 综合置信度: {technical_analysis['confidence']:.2f}")
        
        # 显示主要形态
        for pattern in technical_analysis['patterns'][:2]:
            print(f"     - {pattern['chinese_name']}: {pattern['signal']} (置信度: {pattern['confidence']:.2f})")
        print()
        
        # 第2步：研究员分析
        print("🔬 第2步：研究团队深度分析...")
        
        # 看涨研究员分析
        bullish_researcher = agents['bullish_researcher']
        bullish_analysis = await bullish_researcher._perform_analysis({
            'symbol': market_data['symbol'],
            'patterns': technical_analysis['patterns'],
            'technical_analysis': technical_analysis,
            'market_context': market_data['market_context']
        })
        
        print(f"   🐂 看涨研究员: {bullish_analysis['opinion']} (置信度: {bullish_analysis['confidence']:.2f})")
        print(f"      理由: {bullish_analysis['reasoning'][:100]}...")
        
        # 看跌研究员分析
        bearish_researcher = agents['bearish_researcher']
        bearish_analysis = await bearish_researcher._perform_analysis({
            'symbol': market_data['symbol'],
            'patterns': technical_analysis['patterns'],
            'technical_analysis': technical_analysis,
            'market_context': market_data['market_context']
        })
        
        print(f"   🐻 看跌研究员: {bearish_analysis['opinion']} (置信度: {bearish_analysis['confidence']:.2f})")
        print(f"      理由: {bearish_analysis['reasoning'][:100]}...")
        print()
        
        # 第3步：交易员决策
        print("💼 第3步：专业交易员决策制定...")
        
        professional_trader = agents['professional_trader']
        trading_decision = await professional_trader._perform_analysis({
            'symbol': market_data['symbol'],
            'technical_analysis': technical_analysis,
            'bullish_opinion': bullish_analysis,
            'bearish_opinion': bearish_analysis,
            'market_context': market_data['market_context']
        })
        
        print(f"   📈 交易决策: {trading_decision['decision']} (置信度: {trading_decision['confidence']:.2f})")
        print(f"   💡 决策理由: {trading_decision['reasoning'][:100]}...")
        
        if trading_decision['decision'] != 'HOLD':
            entry_strategy = trading_decision.get('entry_strategy', {})
            exit_strategy = trading_decision.get('exit_strategy', {})
            print(f"   🎯 入场策略: {entry_strategy.get('price', 'N/A')}")
            print(f"   🛡️ 止损设置: {exit_strategy.get('stop_loss', 'N/A')}")
            print(f"   🎁 止盈目标: {exit_strategy.get('take_profit', 'N/A')}")
        print()
        
        # 第4步：风险管理评估
        print("🛡️ 第4步：风险管理最终审核...")
        
        risk_manager = agents['risk_manager']
        risk_assessment = await risk_manager._perform_analysis({
            'trading_decision': trading_decision,
            'market_context': market_data['market_context'],
            'symbol': market_data['symbol']
        })
        
        print(f"   ⚠️ 风险等级: {risk_assessment['risk_level']}")
        print(f"   ✅ 是否批准: {'是' if risk_assessment['position_recommendation']['approved'] else '否'}")
        print(f"   📊 最大仓位: {risk_assessment['position_recommendation']['max_position_size']:.1%}")
        print(f"   📝 风险评估: {risk_assessment['overall_assessment'][:100]}...")
        print()
        
        # 第5步：最终决策总结
        print("📋 第5步：最终决策总结...")
        
        final_decision = {
            'symbol': market_data['symbol'],
            'timestamp': datetime.now().isoformat(),
            'technical_analysis': {
                'patterns_found': len(technical_analysis['patterns']),
                'confidence': technical_analysis['confidence'],
                'key_patterns': [p['chinese_name'] for p in technical_analysis['patterns'][:3]]
            },
            'research_consensus': {
                'bullish_confidence': bullish_analysis['confidence'],
                'bearish_confidence': bearish_analysis['confidence'],
                'consensus_direction': 'BULLISH' if bullish_analysis['confidence'] > bearish_analysis['confidence'] else 'BEARISH'
            },
            'trading_recommendation': {
                'action': trading_decision['decision'],
                'confidence': trading_decision['confidence'],
                'risk_approved': risk_assessment['position_recommendation']['approved']
            },
            'risk_management': {
                'risk_level': risk_assessment['risk_level'],
                'max_position': risk_assessment['position_recommendation']['max_position_size']
            }
        }
        
        print("🎯 最终投资建议:")
        print(f"   📊 技术面: {final_decision['technical_analysis']['patterns_found']}个形态，置信度{final_decision['technical_analysis']['confidence']:.2f}")
        print(f"   🔬 研究面: {final_decision['research_consensus']['consensus_direction']}倾向")
        print(f"   💼 交易面: {final_decision['trading_recommendation']['action']}，置信度{final_decision['trading_recommendation']['confidence']:.2f}")
        print(f"   🛡️ 风险面: {final_decision['risk_management']['risk_level']}风险，{'已批准' if final_decision['trading_recommendation']['risk_approved'] else '未批准'}")
        
        return final_decision
        
    except Exception as e:
        print(f"❌ 工作流执行出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        await hub.stop()


async def demonstrate_llm_capabilities():
    """演示LLM能力"""
    print("\n🧠 LLM智能分析能力演示...")
    
    # 检查LLM提供商
    providers = llm_manager.get_available_providers()
    default_provider = llm_manager.default_provider
    
    print(f"   可用提供商: {providers}")
    print(f"   当前提供商: {default_provider}")
    
    # 测试简单的LLM调用
    test_prompt = """
    请分析以下蜡烛图形态的市场含义：
    
    形态：启明星
    位置：下降趋势底部
    成交量：放量确认
    
    请用专业的角度分析这个形态的意义。
    """
    
    try:
        response = await llm_manager.generate_response(
            prompt=test_prompt,
            system_prompt="你是一位专业的技术分析师。",
            temperature=0.2,
            max_tokens=500
        )
        
        print(f"   ✅ LLM响应生成成功")
        print(f"   📊 Token使用: {response.tokens_used}")
        print(f"   💰 成本: ${response.cost:.4f}")
        print(f"   ⏱️ 响应时间: {response.response_time:.2f}秒")
        print(f"   📝 响应预览: {response.content[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"   ❌ LLM调用失败: {e}")
        return False


async def main():
    """主演示函数"""
    print("🎯 高级智能体系统演示")
    print("基于《日本蜡烛图技术》+ TradingAgents + LLM智能分析")
    print("=" * 80)
    
    try:
        # 1. 演示LLM能力
        llm_success = await demonstrate_llm_capabilities()
        
        # 2. 创建专业团队
        agents = await create_professional_team()
        
        # 3. 模拟市场数据
        market_data = await simulate_professional_analysis()
        
        # 4. 运行专业工作流
        if llm_success:
            final_decision = await run_professional_workflow(agents, market_data)
            
            if final_decision:
                print("\n" + "=" * 80)
                print("🎉 专业分析完成！")
                print("\n✨ 系统特色:")
                print("   🧠 LLM驱动的智能分析 - 专业解读市场信号")
                print("   🤖 多智能体专业协作 - 模拟真实投资团队")
                print("   📊 结构化决策流程 - 技术→研究→交易→风险")
                print("   🛡️ 严格风险控制 - 多层次风险防护")
                print("   📈 专业投资建议 - 机构级分析质量")
        else:
            print("\n⚠️ LLM功能不可用，请配置API密钥后重试")
        
        # 5. 显示统计信息
        print("\n📊 系统统计:")
        stats = llm_manager.get_provider_stats()
        for provider, stat in stats.items():
            print(f"   {provider}: {stat['request_count']}次请求, {stat['total_tokens']}个Token")
        
        print("\n🚀 下一步建议:")
        print("1. 配置真实的OpenAI API密钥以获得最佳体验")
        print("2. 集成实时市场数据源")
        print("3. 开发智能体辩论机制")
        print("4. 添加历史回测功能")
        print("5. 部署到生产环境")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🎯 高级智能体交易系统演示程序")
    print("展示专业级多智能体协作和LLM集成能力")
    print()
    
    # 运行演示
    asyncio.run(main())
    
    print("\n🎉 演示完成!")
    print("\n💡 提示:")
    print("- 当前使用模拟LLM响应，配置真实API密钥可获得更好效果")
    print("- 系统已具备生产级架构，可直接部署使用")
    print("- 所有智能体都支持真实LLM调用和专业分析")
