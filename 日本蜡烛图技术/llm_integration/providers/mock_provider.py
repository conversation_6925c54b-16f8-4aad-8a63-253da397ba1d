"""
模拟LLM提供商
用于开发和测试环境，避免真实API调用
"""

import asyncio
import json
import random
from typing import Dict, Any, Optional, List

from .base import BaseLLMProvider, LLMResponse, LLMConfig


class MockLLMProvider(BaseLLMProvider):
    """模拟LLM提供商"""
    
    def __init__(self, config: LLMConfig, api_key: Optional[str] = None):
        super().__init__(config, api_key)
        self.mock_responses = self._load_mock_responses()
    
    def _load_mock_responses(self) -> Dict[str, str]:
        """加载模拟响应模板"""
        return {
            'pattern_analysis': '''
            {
                "summary": "发现多个重要的蜡烛图形态，显示市场可能出现转折点",
                "market_outlook": "BULLISH",
                "key_insights": [
                    "启明星形态确认底部反转信号",
                    "成交量配合良好，增强信号可靠性",
                    "价格突破前期阻力位，显示买盘积极"
                ],
                "risk_factors": [
                    "需要关注突破后的确认",
                    "整体市场环境仍需观察",
                    "建议控制仓位，分批建仓"
                ]
            }
            ''',
            
            'trend_analysis': '''
            {
                "trend_direction": "UPTREND",
                "trend_strength": "STRONG",
                "support_levels": [145.50, 142.30, 139.80],
                "resistance_levels": [152.20, 155.60, 158.90],
                "analysis": "当前处于强势上升趋势中，价格持续创新高，成交量配合良好。建议在回调至支撑位时寻找买入机会。"
            }
            ''',
            
            'bullish_research': '''
            {
                "opinion": "BULLISH",
                "confidence": 0.85,
                "reasoning": "技术面多个指标显示看涨信号，基本面支撑良好，建议积极关注买入机会。",
                "target_price": 165.00,
                "time_horizon": "1-3个月"
            }
            ''',
            
            'bearish_research': '''
            {
                "opinion": "BEARISH",
                "confidence": 0.35,
                "reasoning": "虽然短期存在一些风险因素，但整体趋势仍然向上，建议谨慎但不必过度悲观。",
                "target_price": 140.00,
                "time_horizon": "短期调整"
            }
            ''',
            
            'trading_decision': '''
            {
                "action": "BUY",
                "confidence": 0.78,
                "entry_price": 148.50,
                "stop_loss": 145.00,
                "take_profit": 158.00,
                "position_size": "中等",
                "reasoning": "综合技术分析和研究观点，当前是较好的买入时机。建议分批建仓，严格止损。"
            }
            ''',
            
            'risk_assessment': '''
            {
                "risk_level": "MEDIUM",
                "max_drawdown": 0.08,
                "risk_reward_ratio": 2.5,
                "position_recommendation": "适中仓位，建议不超过总资金的15%",
                "risk_factors": [
                    "市场波动性较高",
                    "宏观经济不确定性",
                    "技术面存在假突破风险"
                ]
            }
            '''
        }
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成模拟响应"""
        # 模拟网络延迟
        await asyncio.sleep(random.uniform(0.5, 2.0))
        
        # 根据提示词内容选择合适的模拟响应
        response_content = self._select_mock_response(prompt, system_prompt)
        
        # 模拟token使用量
        tokens_used = len(response_content.split()) * 1.3  # 粗略估算
        
        return LLMResponse(
            content=response_content,
            model=f"mock-{self.config.model}",
            tokens_used=int(tokens_used),
            response_time=0,  # 将在retry_request中设置
            cost=0.0,  # 模拟环境无成本
            metadata={
                'mock': True,
                'prompt_length': len(prompt),
                'system_prompt_length': len(system_prompt) if system_prompt else 0
            }
        )
    
    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> LLMResponse:
        """生成模拟对话响应"""
        # 合并所有消息内容
        combined_prompt = " ".join([msg.get('content', '') for msg in messages])
        
        # 查找系统消息
        system_prompt = None
        for msg in messages:
            if msg.get('role') == 'system':
                system_prompt = msg.get('content')
                break
        
        return await self.generate_response(combined_prompt, system_prompt, **kwargs)
    
    def _select_mock_response(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """根据提示词选择合适的模拟响应"""
        prompt_lower = prompt.lower()
        system_lower = (system_prompt or "").lower()
        
        # 蜡烛图形态分析
        if any(keyword in prompt_lower for keyword in ['蜡烛图', 'candlestick', '形态', 'pattern']):
            return self.mock_responses['pattern_analysis']
        
        # 趋势分析
        elif any(keyword in prompt_lower for keyword in ['趋势', 'trend', '支撑', 'support', '阻力', 'resistance']):
            return self.mock_responses['trend_analysis']
        
        # 看涨研究
        elif any(keyword in system_lower for keyword in ['bullish', '看涨', '买入']):
            return self.mock_responses['bullish_research']
        
        # 看跌研究
        elif any(keyword in system_lower for keyword in ['bearish', '看跌', '卖出']):
            return self.mock_responses['bearish_research']
        
        # 交易决策
        elif any(keyword in prompt_lower for keyword in ['交易', 'trading', '决策', 'decision']):
            return self.mock_responses['trading_decision']
        
        # 风险评估
        elif any(keyword in prompt_lower for keyword in ['风险', 'risk', '评估', 'assessment']):
            return self.mock_responses['risk_assessment']
        
        # 默认响应
        else:
            return '''
            {
                "analysis": "基于当前市场数据的综合分析",
                "recommendation": "建议继续观察市场动态，等待更明确的信号",
                "confidence": 0.6,
                "next_steps": ["监控关键技术位", "关注成交量变化", "等待确认信号"]
            }
            '''
    
    def estimate_cost(self, tokens: int) -> float:
        """模拟环境无成本"""
        return 0.0
    
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        return "Mock"
