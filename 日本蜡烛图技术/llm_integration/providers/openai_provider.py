"""
OpenAI LLM提供商实现
"""

import asyncio
from typing import Dict, Any, Optional, List
import openai
from openai import Async<PERSON>penA<PERSON>

from .base import BaseLLMProvider, LLMResponse, LLMConfig


class OpenAIProvider(BaseLLMProvider):
    """OpenAI提供商"""
    
    # OpenAI定价 (每1K tokens的价格，美元)
    PRICING = {
        'gpt-4': {'input': 0.03, 'output': 0.06},
        'gpt-4-turbo': {'input': 0.01, 'output': 0.03},
        'gpt-3.5-turbo': {'input': 0.001, 'output': 0.002},
        'gpt-3.5-turbo-16k': {'input': 0.003, 'output': 0.004}
    }
    
    def __init__(self, config: LLMConfig, api_key: str):
        super().__init__(config, api_key)
        self.client = AsyncOpenAI(api_key=api_key)
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成响应"""
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        return await self._retry_request(self._make_chat_request, messages, **kwargs)
    
    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> LLMResponse:
        """生成对话响应"""
        return await self._retry_request(self._make_chat_request, messages, **kwargs)
    
    async def _make_chat_request(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> LLMResponse:
        """发起聊天请求"""
        # 合并配置和kwargs
        request_config = {
            'model': self.config.model,
            'temperature': self.config.temperature,
            'max_tokens': self.config.max_tokens,
            'top_p': self.config.top_p,
            'frequency_penalty': self.config.frequency_penalty,
            'presence_penalty': self.config.presence_penalty,
            **kwargs
        }
        
        try:
            response = await self.client.chat.completions.create(
                messages=messages,
                **request_config
            )
            
            # 提取响应信息
            content = response.choices[0].message.content
            tokens_used = response.usage.total_tokens
            
            # 计算成本
            cost = self._calculate_cost(
                input_tokens=response.usage.prompt_tokens,
                output_tokens=response.usage.completion_tokens
            )
            
            return LLMResponse(
                content=content,
                model=response.model,
                tokens_used=tokens_used,
                response_time=0,  # 将在retry_request中设置
                cost=cost,
                metadata={
                    'finish_reason': response.choices[0].finish_reason,
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens
                }
            )
            
        except Exception as e:
            raise Exception(f"OpenAI API request failed: {str(e)}")
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算请求成本"""
        model_pricing = self.PRICING.get(self.config.model, {'input': 0.01, 'output': 0.03})
        
        input_cost = (input_tokens / 1000) * model_pricing['input']
        output_cost = (output_tokens / 1000) * model_pricing['output']
        
        return input_cost + output_cost
    
    def estimate_cost(self, tokens: int) -> float:
        """估算成本（假设输入输出各占一半）"""
        model_pricing = self.PRICING.get(self.config.model, {'input': 0.01, 'output': 0.03})
        avg_price = (model_pricing['input'] + model_pricing['output']) / 2
        return (tokens / 1000) * avg_price
    
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        return "OpenAI"
