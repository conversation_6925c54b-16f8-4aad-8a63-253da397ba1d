"""
LLM提供商基础接口
定义所有LLM提供商必须实现的接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import time
import asyncio


@dataclass
class LLMResponse:
    """LLM响应结果"""
    content: str
    model: str
    tokens_used: int
    response_time: float
    cost: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMConfig:
    """LLM配置"""
    model: str
    temperature: float = 0.1
    max_tokens: int = 2000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    retry_attempts: int = 3


class BaseLLMProvider(ABC):
    """LLM提供商基础类"""
    
    def __init__(self, config: LLMConfig, api_key: Optional[str] = None):
        self.config = config
        self.api_key = api_key
        self.request_count = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        
    @abstractmethod
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """生成响应"""
        pass
    
    @abstractmethod
    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        **kwargs
    ) -> LLMResponse:
        """生成对话响应"""
        pass
    
    @abstractmethod
    def estimate_cost(self, tokens: int) -> float:
        """估算成本"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """获取使用统计"""
        return {
            'provider': self.get_provider_name(),
            'request_count': self.request_count,
            'total_tokens': self.total_tokens,
            'total_cost': self.total_cost,
            'avg_tokens_per_request': self.total_tokens / max(self.request_count, 1)
        }
    
    def _update_stats(self, response: LLMResponse):
        """更新统计信息"""
        self.request_count += 1
        self.total_tokens += response.tokens_used
        if response.cost:
            self.total_cost += response.cost
    
    async def _retry_request(self, func, *args, **kwargs) -> LLMResponse:
        """重试机制"""
        last_exception = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                start_time = time.time()
                response = await func(*args, **kwargs)
                response.response_time = time.time() - start_time
                
                self._update_stats(response)
                return response
                
            except Exception as e:
                last_exception = e
                if attempt < self.config.retry_attempts - 1:
                    # 指数退避
                    wait_time = 2 ** attempt
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    break
        
        raise last_exception or Exception("All retry attempts failed")
