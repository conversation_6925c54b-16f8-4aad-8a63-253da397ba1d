#!/usr/bin/env python3
"""
智能体辩论系统演示
展示看涨vs看跌研究员的实时辩论
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.communication.hub import CommunicationHub
from agents.analysts.candlestick_expert import CandlestickPatternExpert
from agents.researchers.bullish_researcher import BullishResearcher
from agents.researchers.bearish_researcher import BearishResearcher
from agents.debate.debate_moderator import DebateModerator
from market_data.yahoo_finance_provider import market_data_manager


async def create_debate_team():
    """创建辩论团队"""
    print("👥 组建智能体辩论团队...")
    
    agents = {
        'candlestick_expert': CandlestickPatternExpert(
            agent_id="candlestick_expert_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.1}
        ),
        'bullish_researcher': Bullish<PERSON>esearcher(
            agent_id="bullish_researcher_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.2}
        ),
        'bearish_researcher': <PERSON><PERSON><PERSON><PERSON>archer(
            agent_id="bearish_researcher_001", 
            llm_config={'model': 'gpt-4', 'temperature': 0.2}
        ),
        'debate_moderator': DebateModerator(
            agent_id="debate_moderator_001",
            llm_config={'model': 'gpt-4', 'temperature': 0.3}
        )
    }
    
    for name, agent in agents.items():
        print(f"   ✅ {agent.agent_id} ({agent.role.value})")
    
    return agents


async def get_real_market_data():
    """获取真实市场数据"""
    print("📊 获取实时市场数据...")
    
    # 热门股票列表
    symbols = ['AAPL', 'TSLA', 'NVDA', 'MSFT']
    
    try:
        # 获取市场概况
        market_overview = await market_data_manager.get_market_overview()
        print(f"   📈 市场状态: {market_overview['market_status']}")
        
        for index_name, data in market_overview['indices'].items():
            change_symbol = "📈" if data['change'] > 0 else "📉"
            print(f"   {change_symbol} {index_name}: {data['current']} ({data['change']:+.2f}, {data['change_percent']:+.2f}%)")
        
        # 选择一个股票进行详细分析
        selected_symbol = 'TSLA'
        stock_data = await market_data_manager.get_stock_data(selected_symbol, '1h')
        
        print(f"\n🎯 选择分析标的: {selected_symbol}")
        print(f"   💰 当前价格: ${stock_data['current_price']:.2f}")
        print(f"   📊 数据点数: {len(stock_data['candles'])}根蜡烛")
        print(f"   🕐 最后更新: {stock_data['last_updated']}")
        print(f"   📡 数据源: {stock_data['data_source']}")
        
        return stock_data
        
    except Exception as e:
        print(f"   ⚠️ 获取实时数据失败，使用模拟数据: {e}")
        return await market_data_manager.get_stock_data('TSLA', '1h')


async def run_debate_simulation(agents, market_data):
    """运行辩论模拟"""
    print("\n🎭 启动智能体辩论模拟...")
    
    # 创建通信中心
    hub = CommunicationHub()
    await hub.start()
    
    # 注册所有智能体
    for agent in agents.values():
        hub.register_agent(agent)
        agent.communication_hub = hub
    
    print(f"✅ 通信中心启动，注册{len(agents)}个智能体")
    
    try:
        # 第1步：技术分析
        print("\n🔍 第1步：蜡烛图专家技术分析...")
        candlestick_expert = agents['candlestick_expert']
        technical_analysis = await candlestick_expert.analyze(market_data)
        
        print(f"   ✅ 识别形态: {len(technical_analysis['patterns'])}个")
        print(f"   ✅ 综合置信度: {technical_analysis['confidence']:.2f}")
        
        # 显示主要形态
        for pattern in technical_analysis['patterns'][:3]:
            print(f"     - {pattern['chinese_name']}: {pattern['signal']} (置信度: {pattern['confidence']:.2f})")
        
        # 第2步：研究员初步分析
        print("\n🔬 第2步：研究员初步观点形成...")
        
        analysis_data = {
            'symbol': market_data['symbol'],
            'patterns': technical_analysis['patterns'],
            'technical_analysis': technical_analysis,
            'market_context': {
                'current_price': market_data['current_price'],
                'previous_close': market_data['previous_close'],
                'data_source': market_data['data_source']
            }
        }
        
        # 看涨研究员分析
        bullish_researcher = agents['bullish_researcher']
        bullish_opinion = await bullish_researcher._perform_analysis(analysis_data)
        
        print(f"   🐂 看涨研究员观点:")
        print(f"      立场: {bullish_opinion['opinion']} (置信度: {bullish_opinion['confidence']:.2f})")
        print(f"      理由: {bullish_opinion['reasoning'][:80]}...")
        
        # 看跌研究员分析
        bearish_researcher = agents['bearish_researcher']
        bearish_opinion = await bearish_researcher._perform_analysis(analysis_data)
        
        print(f"   🐻 看跌研究员观点:")
        print(f"      立场: {bearish_opinion['opinion']} (置信度: {bearish_opinion['confidence']:.2f})")
        print(f"      理由: {bearish_opinion['reasoning'][:80]}...")
        
        # 第3步：启动正式辩论
        print(f"\n🎭 第3步：启动正式辩论 - {market_data['symbol']}投资决策")
        
        debate_moderator = agents['debate_moderator']
        
        # 启动辩论会话
        debate_topic = f"{market_data['symbol']}股票投资决策：当前是否应该买入？"
        participants = ['bullish_researcher_001', 'bearish_researcher_001']
        
        session_id = await debate_moderator.start_debate(
            topic=debate_topic,
            participants=participants,
            initial_data=analysis_data
        )
        
        print(f"   🎯 辩论主题: {debate_topic}")
        print(f"   👥 参与者: {', '.join(participants)}")
        print(f"   🆔 会话ID: {session_id}")
        
        # 模拟多轮辩论
        for round_num in range(1, 4):  # 3轮辩论
            print(f"\n🔄 第{round_num}轮辩论:")
            
            # 构建本轮论点
            if round_num == 1:
                bullish_arg = f"基于技术分析，{bullish_opinion['reasoning'][:100]}..."
                bearish_arg = f"从风险角度看，{bearish_opinion['reasoning'][:100]}..."
            elif round_num == 2:
                bullish_arg = "进一步分析显示，市场情绪正在改善，技术指标支持上涨趋势。"
                bearish_arg = "但是宏观经济环境存在不确定性，需要谨慎对待当前的技术信号。"
            else:
                bullish_arg = "综合考虑风险收益比，当前价位提供了良好的投资机会。"
                bearish_arg = "虽然有上涨潜力，但下行风险同样不容忽视，建议等待更明确的信号。"
            
            print(f"   🐂 看涨论点: {bullish_arg}")
            print(f"   🐻 看跌论点: {bearish_arg}")
            
            # 主持人分析
            moderation_result = await debate_moderator.moderate_round(
                session_id=session_id,
                round_number=round_num,
                bullish_argument=bullish_arg,
                bearish_argument=bearish_arg
            )
            
            print(f"   🎯 主持人总结: {moderation_result['debate_summary'][:100]}...")
            print(f"   🤝 共识点: {', '.join(moderation_result['consensus_points'][:2])}")
            print(f"   ⚡ 分歧点: {', '.join(moderation_result['key_disagreements'][:2])}")
            
            # 检查是否达成共识
            confidence = moderation_result.get('recommendation', {}).get('confidence_level', 0)
            if confidence > 0.8:
                print(f"   ✅ 达成高度共识 (置信度: {confidence:.2f})")
                break
            
            await asyncio.sleep(1)  # 模拟思考时间
        
        # 第4步：辩论总结
        print(f"\n📋 第4步：辩论总结与最终建议")
        
        debate_status = debate_moderator.get_debate_status(session_id)
        if debate_status:
            print(f"   ⏱️ 辩论时长: {debate_status['duration']:.1f}秒")
            print(f"   🔄 完成轮数: {debate_status['rounds_completed']}/{debate_status['max_rounds']}")
            print(f"   🤝 是否达成共识: {'是' if debate_status['consensus_reached'] else '否'}")
        
        # 最终建议
        final_recommendation = moderation_result.get('recommendation', {})
        print(f"\n🎯 最终投资建议:")
        print(f"   📊 建议方案: {final_recommendation.get('suggested_approach', '综合考虑')}")
        print(f"   📈 置信度: {final_recommendation.get('confidence_level', 0.5):.2f}")
        print(f"   📝 下一步行动: {', '.join(final_recommendation.get('next_steps', ['继续观察']))}")
        
        return {
            'symbol': market_data['symbol'],
            'technical_analysis': technical_analysis,
            'bullish_opinion': bullish_opinion,
            'bearish_opinion': bearish_opinion,
            'debate_result': moderation_result,
            'final_recommendation': final_recommendation
        }
        
    except Exception as e:
        print(f"❌ 辩论模拟出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        await hub.stop()


async def main():
    """主演示函数"""
    print("🎭 智能体辩论系统演示")
    print("看涨 vs 看跌研究员实时辩论")
    print("=" * 60)
    
    try:
        # 1. 创建辩论团队
        agents = await create_debate_team()
        
        # 2. 获取实时市场数据
        market_data = await get_real_market_data()
        
        # 3. 运行辩论模拟
        result = await run_debate_simulation(agents, market_data)
        
        if result:
            print("\n" + "=" * 60)
            print("🎉 辩论系统演示完成！")
            
            print("\n✨ 系统特色展示:")
            print("   🧠 AI驱动的智能辩论 - GPT-4级别的观点碰撞")
            print("   🤖 专业研究员对抗 - 看涨vs看跌深度分析")
            print("   🎯 专业主持人引导 - 结构化辩论流程")
            print("   📊 实时市场数据 - 基于真实数据的分析")
            print("   🔄 多轮辩论机制 - 逐步深入核心问题")
            print("   🤝 共识达成算法 - 智能化决策收敛")
            
            print(f"\n📈 {result['symbol']}分析结果:")
            print(f"   🔍 技术面: {len(result['technical_analysis']['patterns'])}个形态")
            print(f"   🐂 看涨置信度: {result['bullish_opinion']['confidence']:.2f}")
            print(f"   🐻 看跌置信度: {result['bearish_opinion']['confidence']:.2f}")
            print(f"   🎯 最终建议: {result['final_recommendation'].get('suggested_approach', 'N/A')}")
        
        print("\n🚀 下一步可以:")
        print("1. 配置真实OpenAI API获得更好的辩论效果")
        print("2. 添加更多辩论参与者（基本面分析师等）")
        print("3. 实现WebSocket实时辩论界面")
        print("4. 集成更多市场数据源")
        print("5. 开发辩论历史记录和回放功能")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🎭 智能体辩论系统启动")
    print("展示AI驱动的投资决策辩论")
    print()
    
    # 运行演示
    asyncio.run(main())
    
    print("\n🎉 辩论演示完成!")
    print("\n💡 这展示了:")
    print("- 🧠 AI智能体的观点碰撞能力")
    print("- 🤝 结构化的决策讨论流程") 
    print("- 📊 基于真实数据的专业分析")
    print("- 🎯 从分歧到共识的智能收敛")
