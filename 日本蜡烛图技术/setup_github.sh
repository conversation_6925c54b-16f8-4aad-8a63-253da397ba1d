#!/bin/bash

# 🚀 AI智能体投资分析平台 - GitHub设置脚本
# 自动化项目清理和GitHub上传流程

echo "🚀 AI智能体投资分析平台 - GitHub设置开始"
echo "=================================================="

# 检查是否在正确的目录
if [ ! -f "README.md" ] || [ ! -d "agents" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

echo "📁 当前目录：$(pwd)"
echo "✅ 项目结构验证通过"

# 1. 清理临时文件
echo ""
echo "🧹 清理临时文件..."

# 删除Python缓存
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "*.pyo" -delete 2>/dev/null || true

# 删除系统文件
find . -name ".DS_Store" -delete 2>/dev/null || true
find . -name "Thumbs.db" -delete 2>/dev/null || true

echo "✅ 临时文件清理完成"

# 2. 检查必要文件
echo ""
echo "📋 检查必要文件..."

required_files=("README.md" "LICENSE" ".gitignore" "demo.html")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ 缺少文件: $file"
        exit 1
    fi
done

required_dirs=("agents" "backend" "frontend" "llm_integration" "market_data")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/"
    else
        echo "❌ 缺少目录: $dir"
        exit 1
    fi
done

echo "✅ 所有必要文件和目录都存在"

# 3. 检查Git状态
echo ""
echo "🔧 检查Git状态..."

if [ ! -d ".git" ]; then
    echo "📦 初始化Git仓库..."
    git init
    echo "✅ Git仓库初始化完成"
else
    echo "✅ Git仓库已存在"
fi

# 4. 配置Git（如果需要）
echo ""
echo "👤 检查Git配置..."

if [ -z "$(git config user.name)" ]; then
    echo "⚠️  请配置Git用户名："
    read -p "输入你的姓名: " git_name
    git config --global user.name "$git_name"
fi

if [ -z "$(git config user.email)" ]; then
    echo "⚠️  请配置Git邮箱："
    read -p "输入你的邮箱: " git_email
    git config --global user.email "$git_email"
fi

echo "✅ Git配置完成"
echo "   用户名: $(git config user.name)"
echo "   邮箱: $(git config user.email)"

# 5. 添加文件到Git
echo ""
echo "📦 添加文件到Git..."

git add .
echo "✅ 文件添加完成"

# 6. 检查Git状态
echo ""
echo "📊 Git状态："
git status --short

# 7. 创建提交
echo ""
echo "💾 创建Git提交..."

commit_message="🎉 Initial commit: AI Investment Analysis Platform

✨ Features:
- 🤖 Multi-agent investment analysis system
- 🎭 AI agent debate mechanism
- 🧠 LLM-driven market analysis (GPT-4 support)
- 📊 Candlestick pattern recognition (20+ patterns)
- 🔄 Structured decision workflow
- 🎨 Professional web interface (React + Ant Design)

🏗️ Architecture:
- Backend: FastAPI + Python
- Frontend: React + Ant Design  
- AI Agents: 5 specialized agents
- LLM Integration: OpenAI GPT-4
- Real-time Data: Yahoo Finance API

🌟 Innovation:
- World's first AI agent investment debate system
- Combines traditional technical analysis with modern AI
- Simulates real investment committee processes
- Quantified debate quality assessment

🎯 Components:
- Candlestick Expert: Pattern recognition specialist
- Bullish Researcher: Opportunity identification
- Bearish Researcher: Risk assessment
- Professional Trader: Decision making
- Risk Manager: Risk control
- Debate Moderator: Consensus facilitation

📊 Supported Patterns:
- Reversal: Morning Star, Evening Star, Hammer, Engulfing
- Continuation: Three White Soldiers, Three Black Crows
- Doji Series: Standard Doji, Gravestone, Dragonfly

🚀 Ready for production deployment and further development!"

git commit -m "$commit_message"
echo "✅ Git提交完成"

# 8. 获取GitHub仓库信息
echo ""
echo "🌐 GitHub仓库设置..."
echo ""
echo "请在GitHub上创建新仓库："
echo "1. 访问 https://github.com/new"
echo "2. 仓库名称建议: AI-Investment-Analysis-Platform"
echo "3. 描述: 🕯️ AI智能体投资分析平台 - 基于《日本蜡烛图技术》+ TradingAgents + GPT-4的革命性投资决策系统"
echo "4. 选择Public（推荐）"
echo "5. 不要勾选'Initialize this repository with a README'"
echo ""

read -p "请输入你的GitHub用户名: " github_username
read -p "请输入仓库名称 (默认: AI-Investment-Analysis-Platform): " repo_name

if [ -z "$repo_name" ]; then
    repo_name="AI-Investment-Analysis-Platform"
fi

github_url="https://github.com/$github_username/$repo_name.git"

echo ""
echo "🔗 GitHub仓库URL: $github_url"

# 9. 添加远程仓库
echo ""
echo "🔗 添加远程仓库..."

# 检查是否已有远程仓库
if git remote get-url origin >/dev/null 2>&1; then
    echo "⚠️  远程仓库已存在，更新URL..."
    git remote set-url origin "$github_url"
else
    git remote add origin "$github_url"
fi

echo "✅ 远程仓库设置完成"

# 10. 推送到GitHub
echo ""
echo "🚀 推送到GitHub..."
echo "⚠️  请确保你已在GitHub上创建了仓库！"
echo ""

read -p "是否现在推送到GitHub? (y/N): " push_confirm

if [[ $push_confirm =~ ^[Yy]$ ]]; then
    echo "📤 推送中..."
    
    git branch -M main
    
    if git push -u origin main; then
        echo ""
        echo "🎉 成功推送到GitHub!"
        echo ""
        echo "🌐 你的项目现在可以在以下地址访问:"
        echo "   📱 仓库: https://github.com/$github_username/$repo_name"
        echo "   📖 README: https://github.com/$github_username/$repo_name#readme"
        echo "   🎭 演示页面: https://$github_username.github.io/$repo_name/demo.html (需要启用GitHub Pages)"
        echo ""
        echo "📋 后续建议:"
        echo "   1. 在GitHub上设置仓库描述和标签"
        echo "   2. 启用GitHub Pages展示演示页面"
        echo "   3. 创建Release版本"
        echo "   4. 添加项目截图到README"
        echo "   5. 在社交媒体分享你的创新项目"
        echo ""
        echo "🎯 这是全球首个AI智能体投资辩论系统！"
        echo "🌟 记得给项目加上合适的标签: ai, machine-learning, trading, investment, multi-agent-system"
    else
        echo ""
        echo "❌ 推送失败！"
        echo "请检查："
        echo "1. GitHub仓库是否已创建"
        echo "2. 用户名和仓库名是否正确"
        echo "3. 是否有推送权限"
        echo ""
        echo "你可以稍后手动推送："
        echo "   git push -u origin main"
    fi
else
    echo ""
    echo "⏸️  跳过推送步骤"
    echo ""
    echo "📝 手动推送命令："
    echo "   git branch -M main"
    echo "   git push -u origin main"
fi

echo ""
echo "✅ GitHub设置脚本完成！"
echo ""
echo "📚 更多信息请查看:"
echo "   - README.md: 项目说明"
echo "   - GITHUB_SETUP.md: 详细GitHub设置指南"
echo "   - PROJECT_STRUCTURE.md: 项目结构说明"
echo ""
echo "🚀 祝你的AI智能体投资分析平台获得成功！"
