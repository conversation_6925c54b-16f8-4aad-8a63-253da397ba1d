# 🔑 多智能体交易系统环境配置

# ===========================================
# LLM API 配置
# ===========================================

# OpenAI API
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=2000

# Anthropic Claude API
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_TEMPERATURE=0.1
ANTHROPIC_MAX_TOKENS=2000

# ===========================================
# 数据库配置
# ===========================================

# PostgreSQL (生产环境)
DATABASE_URL=postgresql://user:password@localhost:5432/trading_agents
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_agents
DB_USER=trading_user
DB_PASSWORD=your_db_password

# SQLite (开发环境)
SQLITE_DB_PATH=./data/trading_agents.db

# ===========================================
# Redis 配置 (消息队列和缓存)
# ===========================================

REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# ===========================================
# 应用配置
# ===========================================

# 环境模式
ENVIRONMENT=development  # development, staging, production

# 服务端口
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 日志级别
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR

# 安全配置
SECRET_KEY=your_super_secret_key_here_change_in_production
JWT_SECRET=your_jwt_secret_key_here
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# ===========================================
# 智能体系统配置
# ===========================================

# 默认LLM提供商
DEFAULT_LLM_PROVIDER=openai  # openai, anthropic

# 智能体配置
MAX_AGENTS=10
AGENT_TIMEOUT=300  # 秒
MESSAGE_QUEUE_SIZE=1000
WORKFLOW_TIMEOUT=1800  # 30分钟

# 分析配置
PATTERN_CONFIDENCE_THRESHOLD=0.6
MAX_PATTERNS_PER_ANALYSIS=20
ANALYSIS_CACHE_TTL=3600  # 1小时

# ===========================================
# 市场数据配置
# ===========================================

# 数据源
MARKET_DATA_PROVIDER=yfinance  # yfinance, alpha_vantage, polygon
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
POLYGON_API_KEY=your_polygon_key

# 数据更新频率
DATA_UPDATE_INTERVAL=60  # 秒
MAX_HISTORICAL_DAYS=365

# ===========================================
# 监控和告警
# ===========================================

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# 告警配置
ALERT_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# ===========================================
# 开发和调试
# ===========================================

# 调试模式
DEBUG=true
ENABLE_PROFILING=false

# 测试配置
TEST_DATABASE_URL=sqlite:///./test.db
MOCK_LLM_RESPONSES=false

# 前端开发
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws

# ===========================================
# 部署配置
# ===========================================

# Docker配置
DOCKER_REGISTRY=your_registry.com
IMAGE_TAG=latest

# Kubernetes配置
NAMESPACE=trading-agents
REPLICAS=3

# 云服务配置
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key

# ===========================================
# 功能开关
# ===========================================

# 实验性功能
ENABLE_ADVANCED_PATTERNS=true
ENABLE_SENTIMENT_ANALYSIS=false
ENABLE_NEWS_ANALYSIS=false
ENABLE_BACKTESTING=true

# 安全功能
ENABLE_RATE_LIMITING=true
ENABLE_API_KEY_AUTH=false
ENABLE_AUDIT_LOG=true
