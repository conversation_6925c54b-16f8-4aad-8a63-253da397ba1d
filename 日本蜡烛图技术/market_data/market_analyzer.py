"""
高级市场分析器
结合技术分析、基本面分析和市场情绪分析
"""

import asyncio
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

from .yahoo_finance_provider import market_data_manager


class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def calculate_sma(prices: List[float], period: int) -> List[float]:
        """计算简单移动平均线"""
        if len(prices) < period:
            return []
        
        sma = []
        for i in range(period - 1, len(prices)):
            avg = sum(prices[i - period + 1:i + 1]) / period
            sma.append(avg)
        
        return sma
    
    @staticmethod
    def calculate_ema(prices: List[float], period: int) -> List[float]:
        """计算指数移动平均线"""
        if len(prices) < period:
            return []
        
        multiplier = 2 / (period + 1)
        ema = [sum(prices[:period]) / period]  # 第一个EMA值使用SMA
        
        for i in range(period, len(prices)):
            ema_value = (prices[i] * multiplier) + (ema[-1] * (1 - multiplier))
            ema.append(ema_value)
        
        return ema
    
    @staticmethod
    def calculate_rsi(prices: List[float], period: int = 14) -> List[float]:
        """计算相对强弱指数"""
        if len(prices) < period + 1:
            return []
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [delta if delta > 0 else 0 for delta in deltas]
        losses = [-delta if delta < 0 else 0 for delta in deltas]
        
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        rsi = []
        for i in range(period, len(gains)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            if avg_loss == 0:
                rsi.append(100)
            else:
                rs = avg_gain / avg_loss
                rsi_value = 100 - (100 / (1 + rs))
                rsi.append(rsi_value)
        
        return rsi
    
    @staticmethod
    def calculate_macd(prices: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, List[float]]:
        """计算MACD指标"""
        if len(prices) < slow:
            return {'macd': [], 'signal': [], 'histogram': []}
        
        ema_fast = TechnicalIndicators.calculate_ema(prices, fast)
        ema_slow = TechnicalIndicators.calculate_ema(prices, slow)
        
        # 对齐两个EMA数组
        start_idx = slow - fast
        macd_line = [ema_fast[i + start_idx] - ema_slow[i] for i in range(len(ema_slow))]
        
        signal_line = TechnicalIndicators.calculate_ema(macd_line, signal)
        
        # 计算柱状图
        histogram = []
        signal_start = len(macd_line) - len(signal_line)
        for i in range(len(signal_line)):
            histogram.append(macd_line[i + signal_start] - signal_line[i])
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def calculate_bollinger_bands(prices: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, List[float]]:
        """计算布林带"""
        if len(prices) < period:
            return {'upper': [], 'middle': [], 'lower': []}
        
        sma = TechnicalIndicators.calculate_sma(prices, period)
        upper = []
        lower = []
        
        for i in range(period - 1, len(prices)):
            price_slice = prices[i - period + 1:i + 1]
            std = np.std(price_slice)
            sma_value = sma[i - period + 1]
            
            upper.append(sma_value + (std * std_dev))
            lower.append(sma_value - (std * std_dev))
        
        return {
            'upper': upper,
            'middle': sma,
            'lower': lower
        }


class MarketAnalyzer:
    """高级市场分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger("MarketAnalyzer")
        self.indicators = TechnicalIndicators()
    
    async def comprehensive_analysis(
        self,
        symbol: str,
        interval: str = "1h",
        include_fundamentals: bool = True
    ) -> Dict[str, Any]:
        """综合市场分析"""
        try:
            # 获取价格数据
            price_data = await market_data_manager.get_stock_data(symbol, interval)
            
            # 获取基本面数据
            fundamental_data = None
            if include_fundamentals:
                fundamental_data = await market_data_manager.get_stock_info(symbol)
            
            # 技术分析
            technical_analysis = self._perform_technical_analysis(price_data)
            
            # 基本面分析
            fundamental_analysis = self._perform_fundamental_analysis(fundamental_data) if fundamental_data else {}
            
            # 市场情绪分析
            sentiment_analysis = self._analyze_market_sentiment(price_data, technical_analysis)
            
            # 综合评分
            overall_score = self._calculate_overall_score(technical_analysis, fundamental_analysis, sentiment_analysis)
            
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'price_data': {
                    'current_price': price_data['current_price'],
                    'previous_close': price_data['previous_close'],
                    'change': price_data['current_price'] - price_data['previous_close'],
                    'change_percent': ((price_data['current_price'] - price_data['previous_close']) / price_data['previous_close']) * 100
                },
                'technical_analysis': technical_analysis,
                'fundamental_analysis': fundamental_analysis,
                'sentiment_analysis': sentiment_analysis,
                'overall_score': overall_score,
                'recommendation': self._generate_recommendation(overall_score),
                'data_quality': self._assess_data_quality(price_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis for {symbol}: {e}")
            return self._get_fallback_analysis(symbol)
    
    def _perform_technical_analysis(self, price_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行技术分析"""
        candles = price_data.get('candles', [])
        if len(candles) < 20:
            return {'error': 'Insufficient data for technical analysis'}
        
        # 提取价格数据
        closes = [candle['close'] for candle in candles]
        highs = [candle['high'] for candle in candles]
        lows = [candle['low'] for candle in candles]
        volumes = [candle['volume'] for candle in candles]
        
        # 计算技术指标
        sma_20 = self.indicators.calculate_sma(closes, 20)
        sma_50 = self.indicators.calculate_sma(closes, 50) if len(closes) >= 50 else []
        ema_12 = self.indicators.calculate_ema(closes, 12)
        rsi = self.indicators.calculate_rsi(closes)
        macd = self.indicators.calculate_macd(closes)
        bollinger = self.indicators.calculate_bollinger_bands(closes)
        
        # 趋势分析
        trend_analysis = self._analyze_trend(closes, sma_20, sma_50)
        
        # 支撑阻力位
        support_resistance = self._find_support_resistance(highs, lows)
        
        # 成交量分析
        volume_analysis = self._analyze_volume(volumes, closes)
        
        return {
            'indicators': {
                'sma_20': sma_20[-1] if sma_20 else None,
                'sma_50': sma_50[-1] if sma_50 else None,
                'ema_12': ema_12[-1] if ema_12 else None,
                'rsi': rsi[-1] if rsi else None,
                'macd': {
                    'macd': macd['macd'][-1] if macd['macd'] else None,
                    'signal': macd['signal'][-1] if macd['signal'] else None,
                    'histogram': macd['histogram'][-1] if macd['histogram'] else None
                },
                'bollinger': {
                    'upper': bollinger['upper'][-1] if bollinger['upper'] else None,
                    'middle': bollinger['middle'][-1] if bollinger['middle'] else None,
                    'lower': bollinger['lower'][-1] if bollinger['lower'] else None
                }
            },
            'trend_analysis': trend_analysis,
            'support_resistance': support_resistance,
            'volume_analysis': volume_analysis,
            'signals': self._generate_technical_signals(closes, rsi, macd, bollinger)
        }
    
    def _perform_fundamental_analysis(self, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行基本面分析"""
        if not fundamental_data:
            return {}
        
        # 估值分析
        valuation_score = self._analyze_valuation(fundamental_data)
        
        # 财务健康度
        financial_health = self._analyze_financial_health(fundamental_data)
        
        # 成长性分析
        growth_analysis = self._analyze_growth(fundamental_data)
        
        return {
            'valuation': valuation_score,
            'financial_health': financial_health,
            'growth_analysis': growth_analysis,
            'key_metrics': {
                'pe_ratio': fundamental_data.get('pe_ratio', 0),
                'market_cap': fundamental_data.get('market_cap', 0),
                'beta': fundamental_data.get('beta', 1.0),
                'dividend_yield': fundamental_data.get('dividend_yield', 0),
                'profit_margin': fundamental_data.get('profit_margin', 0)
            }
        }
    
    def _analyze_market_sentiment(self, price_data: Dict[str, Any], technical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场情绪"""
        candles = price_data.get('candles', [])
        if len(candles) < 10:
            return {'sentiment': 'neutral', 'confidence': 0.5}
        
        # 价格动量
        recent_closes = [candle['close'] for candle in candles[-10:]]
        price_momentum = (recent_closes[-1] - recent_closes[0]) / recent_closes[0]
        
        # 成交量趋势
        recent_volumes = [candle['volume'] for candle in candles[-10:]]
        volume_trend = (sum(recent_volumes[-5:]) / 5) / (sum(recent_volumes[:5]) / 5) - 1
        
        # RSI情绪
        rsi = technical_analysis.get('indicators', {}).get('rsi')
        rsi_sentiment = 'neutral'
        if rsi:
            if rsi > 70:
                rsi_sentiment = 'overbought'
            elif rsi < 30:
                rsi_sentiment = 'oversold'
        
        # 综合情绪评分
        sentiment_score = 0
        if price_momentum > 0.02:
            sentiment_score += 1
        elif price_momentum < -0.02:
            sentiment_score -= 1
        
        if volume_trend > 0.1:
            sentiment_score += 0.5
        elif volume_trend < -0.1:
            sentiment_score -= 0.5
        
        if rsi_sentiment == 'oversold':
            sentiment_score += 0.5
        elif rsi_sentiment == 'overbought':
            sentiment_score -= 0.5
        
        # 确定情绪
        if sentiment_score > 0.5:
            sentiment = 'bullish'
        elif sentiment_score < -0.5:
            sentiment = 'bearish'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'confidence': min(abs(sentiment_score) / 2, 1.0),
            'price_momentum': price_momentum,
            'volume_trend': volume_trend,
            'rsi_sentiment': rsi_sentiment,
            'sentiment_score': sentiment_score
        }
    
    def _analyze_trend(self, closes: List[float], sma_20: List[float], sma_50: List[float]) -> Dict[str, Any]:
        """分析趋势"""
        if len(closes) < 20:
            return {'trend': 'unknown', 'strength': 0}
        
        current_price = closes[-1]
        
        # 短期趋势（基于SMA20）
        short_trend = 'neutral'
        if sma_20 and current_price > sma_20[-1]:
            short_trend = 'uptrend'
        elif sma_20 and current_price < sma_20[-1]:
            short_trend = 'downtrend'
        
        # 长期趋势（基于SMA50）
        long_trend = 'neutral'
        if sma_50 and len(sma_20) > 0 and len(sma_50) > 0:
            if sma_20[-1] > sma_50[-1]:
                long_trend = 'uptrend'
            elif sma_20[-1] < sma_50[-1]:
                long_trend = 'downtrend'
        
        # 趋势强度
        strength = 0
        if len(closes) >= 10:
            recent_change = (closes[-1] - closes[-10]) / closes[-10]
            strength = min(abs(recent_change) * 10, 1.0)
        
        return {
            'short_term': short_trend,
            'long_term': long_trend,
            'strength': strength,
            'current_vs_sma20': (current_price / sma_20[-1] - 1) * 100 if sma_20 else 0,
            'current_vs_sma50': (current_price / sma_50[-1] - 1) * 100 if sma_50 else 0
        }
    
    def _find_support_resistance(self, highs: List[float], lows: List[float]) -> Dict[str, Any]:
        """寻找支撑阻力位"""
        if len(highs) < 20 or len(lows) < 20:
            return {'support': [], 'resistance': []}
        
        # 简化的支撑阻力位识别
        recent_highs = highs[-20:]
        recent_lows = lows[-20:]
        
        # 阻力位（近期高点）
        resistance_levels = []
        for i in range(1, len(recent_highs) - 1):
            if recent_highs[i] > recent_highs[i-1] and recent_highs[i] > recent_highs[i+1]:
                resistance_levels.append(recent_highs[i])
        
        # 支撑位（近期低点）
        support_levels = []
        for i in range(1, len(recent_lows) - 1):
            if recent_lows[i] < recent_lows[i-1] and recent_lows[i] < recent_lows[i+1]:
                support_levels.append(recent_lows[i])
        
        return {
            'resistance': sorted(resistance_levels, reverse=True)[:3],  # 前3个阻力位
            'support': sorted(support_levels)[:3]  # 前3个支撑位
        }
    
    def _analyze_volume(self, volumes: List[float], closes: List[float]) -> Dict[str, Any]:
        """分析成交量"""
        if len(volumes) < 10:
            return {'trend': 'unknown', 'average': 0}
        
        recent_volumes = volumes[-10:]
        avg_volume = sum(recent_volumes) / len(recent_volumes)
        
        # 成交量趋势
        first_half = sum(recent_volumes[:5]) / 5
        second_half = sum(recent_volumes[5:]) / 5
        volume_trend = 'increasing' if second_half > first_half else 'decreasing'
        
        # 价量关系
        price_volume_correlation = 'neutral'
        if len(closes) >= 10:
            price_change = closes[-1] - closes[-10]
            volume_change = recent_volumes[-1] - recent_volumes[0]
            
            if price_change > 0 and volume_change > 0:
                price_volume_correlation = 'bullish'
            elif price_change < 0 and volume_change > 0:
                price_volume_correlation = 'bearish'
        
        return {
            'trend': volume_trend,
            'average': avg_volume,
            'current': volumes[-1],
            'vs_average': (volumes[-1] / avg_volume - 1) * 100,
            'price_volume_correlation': price_volume_correlation
        }

    def _generate_technical_signals(
        self,
        closes: List[float],
        rsi: List[float],
        macd: Dict[str, List[float]],
        bollinger: Dict[str, List[float]]
    ) -> List[Dict[str, Any]]:
        """生成技术信号"""
        signals = []

        if not closes:
            return signals

        current_price = closes[-1]

        # RSI信号
        if rsi and len(rsi) > 0:
            current_rsi = rsi[-1]
            if current_rsi > 70:
                signals.append({
                    'type': 'overbought',
                    'indicator': 'RSI',
                    'value': current_rsi,
                    'signal': 'sell',
                    'strength': min((current_rsi - 70) / 30, 1.0)
                })
            elif current_rsi < 30:
                signals.append({
                    'type': 'oversold',
                    'indicator': 'RSI',
                    'value': current_rsi,
                    'signal': 'buy',
                    'strength': min((30 - current_rsi) / 30, 1.0)
                })

        # MACD信号
        if macd['macd'] and macd['signal'] and len(macd['macd']) > 1 and len(macd['signal']) > 1:
            current_macd = macd['macd'][-1]
            current_signal = macd['signal'][-1]
            prev_macd = macd['macd'][-2]
            prev_signal = macd['signal'][-2]

            # MACD金叉死叉
            if prev_macd <= prev_signal and current_macd > current_signal:
                signals.append({
                    'type': 'golden_cross',
                    'indicator': 'MACD',
                    'signal': 'buy',
                    'strength': 0.7
                })
            elif prev_macd >= prev_signal and current_macd < current_signal:
                signals.append({
                    'type': 'death_cross',
                    'indicator': 'MACD',
                    'signal': 'sell',
                    'strength': 0.7
                })

        # 布林带信号
        if bollinger['upper'] and bollinger['lower'] and len(bollinger['upper']) > 0:
            upper_band = bollinger['upper'][-1]
            lower_band = bollinger['lower'][-1]

            if current_price > upper_band:
                signals.append({
                    'type': 'breakout_upper',
                    'indicator': 'Bollinger Bands',
                    'signal': 'sell',
                    'strength': min((current_price - upper_band) / upper_band, 1.0)
                })
            elif current_price < lower_band:
                signals.append({
                    'type': 'breakout_lower',
                    'indicator': 'Bollinger Bands',
                    'signal': 'buy',
                    'strength': min((lower_band - current_price) / lower_band, 1.0)
                })

        return signals

    def _analyze_valuation(self, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析估值"""
        pe_ratio = fundamental_data.get('pe_ratio', 0)
        market_cap = fundamental_data.get('market_cap', 0)

        # PE估值评分
        pe_score = 'fair'
        if pe_ratio > 0:
            if pe_ratio < 15:
                pe_score = 'undervalued'
            elif pe_ratio > 30:
                pe_score = 'overvalued'

        # 市值评分
        market_cap_category = 'unknown'
        if market_cap > 0:
            if market_cap > 200_000_000_000:  # 2000亿
                market_cap_category = 'mega_cap'
            elif market_cap > 10_000_000_000:  # 100亿
                market_cap_category = 'large_cap'
            elif market_cap > 2_000_000_000:  # 20亿
                market_cap_category = 'mid_cap'
            else:
                market_cap_category = 'small_cap'

        return {
            'pe_evaluation': pe_score,
            'pe_ratio': pe_ratio,
            'market_cap_category': market_cap_category,
            'market_cap': market_cap
        }

    def _analyze_financial_health(self, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析财务健康度"""
        profit_margin = fundamental_data.get('profit_margin', 0)
        beta = fundamental_data.get('beta', 1.0)
        dividend_yield = fundamental_data.get('dividend_yield', 0)

        # 盈利能力评分
        profitability_score = 'average'
        if profit_margin > 0.2:
            profitability_score = 'excellent'
        elif profit_margin > 0.1:
            profitability_score = 'good'
        elif profit_margin < 0:
            profitability_score = 'poor'

        # 风险评分
        risk_level = 'medium'
        if beta < 0.8:
            risk_level = 'low'
        elif beta > 1.5:
            risk_level = 'high'

        # 股息评分
        dividend_score = 'none'
        if dividend_yield > 0.04:
            dividend_score = 'high'
        elif dividend_yield > 0.02:
            dividend_score = 'moderate'
        elif dividend_yield > 0:
            dividend_score = 'low'

        return {
            'profitability': profitability_score,
            'risk_level': risk_level,
            'dividend_score': dividend_score,
            'profit_margin': profit_margin,
            'beta': beta,
            'dividend_yield': dividend_yield
        }

    def _analyze_growth(self, fundamental_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析成长性"""
        revenue_growth = fundamental_data.get('revenue_growth', 0)

        growth_category = 'stable'
        if revenue_growth > 0.2:
            growth_category = 'high_growth'
        elif revenue_growth > 0.1:
            growth_category = 'moderate_growth'
        elif revenue_growth < 0:
            growth_category = 'declining'

        return {
            'category': growth_category,
            'revenue_growth': revenue_growth,
            'growth_score': max(min(revenue_growth * 5, 1.0), -1.0)  # 标准化到-1到1
        }

    def _calculate_overall_score(
        self,
        technical_analysis: Dict[str, Any],
        fundamental_analysis: Dict[str, Any],
        sentiment_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算综合评分"""
        scores = {
            'technical': 0,
            'fundamental': 0,
            'sentiment': 0,
            'overall': 0
        }

        # 技术分析评分
        if 'signals' in technical_analysis:
            buy_signals = len([s for s in technical_analysis['signals'] if s['signal'] == 'buy'])
            sell_signals = len([s for s in technical_analysis['signals'] if s['signal'] == 'sell'])
            total_signals = buy_signals + sell_signals

            if total_signals > 0:
                scores['technical'] = (buy_signals - sell_signals) / total_signals

        # 基本面评分
        if fundamental_analysis:
            valuation = fundamental_analysis.get('valuation', {})
            financial_health = fundamental_analysis.get('financial_health', {})
            growth = fundamental_analysis.get('growth_analysis', {})

            fundamental_score = 0

            # 估值评分
            pe_eval = valuation.get('pe_evaluation', 'fair')
            if pe_eval == 'undervalued':
                fundamental_score += 0.3
            elif pe_eval == 'overvalued':
                fundamental_score -= 0.3

            # 盈利能力评分
            profitability = financial_health.get('profitability', 'average')
            if profitability == 'excellent':
                fundamental_score += 0.4
            elif profitability == 'good':
                fundamental_score += 0.2
            elif profitability == 'poor':
                fundamental_score -= 0.4

            # 成长性评分
            growth_score = growth.get('growth_score', 0)
            fundamental_score += growth_score * 0.3

            scores['fundamental'] = max(min(fundamental_score, 1.0), -1.0)

        # 情绪评分
        sentiment = sentiment_analysis.get('sentiment', 'neutral')
        confidence = sentiment_analysis.get('confidence', 0.5)

        if sentiment == 'bullish':
            scores['sentiment'] = confidence
        elif sentiment == 'bearish':
            scores['sentiment'] = -confidence

        # 综合评分（加权平均）
        weights = {'technical': 0.4, 'fundamental': 0.4, 'sentiment': 0.2}
        scores['overall'] = sum(scores[key] * weights[key] for key in weights.keys())

        return scores

    def _generate_recommendation(self, overall_score: Dict[str, Any]) -> Dict[str, Any]:
        """生成投资建议"""
        score = overall_score['overall']

        if score > 0.3:
            action = 'BUY'
            confidence = 'HIGH' if score > 0.6 else 'MEDIUM'
        elif score < -0.3:
            action = 'SELL'
            confidence = 'HIGH' if score < -0.6 else 'MEDIUM'
        else:
            action = 'HOLD'
            confidence = 'LOW'

        return {
            'action': action,
            'confidence': confidence,
            'score': score,
            'reasoning': self._generate_reasoning(overall_score)
        }

    def _generate_reasoning(self, overall_score: Dict[str, Any]) -> str:
        """生成推理说明"""
        technical = overall_score['technical']
        fundamental = overall_score['fundamental']
        sentiment = overall_score['sentiment']

        reasons = []

        if technical > 0.2:
            reasons.append("技术指标显示买入信号")
        elif technical < -0.2:
            reasons.append("技术指标显示卖出信号")

        if fundamental > 0.2:
            reasons.append("基本面分析积极")
        elif fundamental < -0.2:
            reasons.append("基本面分析消极")

        if sentiment > 0.2:
            reasons.append("市场情绪乐观")
        elif sentiment < -0.2:
            reasons.append("市场情绪悲观")

        if not reasons:
            reasons.append("各项指标表现中性")

        return "；".join(reasons)

    def _assess_data_quality(self, price_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        candles = price_data.get('candles', [])

        quality_score = 1.0
        issues = []

        if len(candles) < 20:
            quality_score -= 0.3
            issues.append("数据点不足")

        # 检查数据完整性
        for candle in candles[-10:]:  # 检查最近10个数据点
            if candle['volume'] == 0:
                quality_score -= 0.1
                issues.append("存在零成交量数据")
                break

        data_source = price_data.get('data_source', 'unknown')
        if data_source == 'mock_data':
            quality_score -= 0.5
            issues.append("使用模拟数据")

        return {
            'score': max(quality_score, 0.0),
            'issues': issues,
            'data_points': len(candles),
            'data_source': data_source
        }

    def _get_fallback_analysis(self, symbol: str) -> Dict[str, Any]:
        """获取备用分析结果"""
        return {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'error': 'Analysis failed, using fallback data',
            'price_data': {'current_price': 100.0, 'change': 0, 'change_percent': 0},
            'technical_analysis': {'error': 'Technical analysis unavailable'},
            'fundamental_analysis': {'error': 'Fundamental analysis unavailable'},
            'sentiment_analysis': {'sentiment': 'neutral', 'confidence': 0.5},
            'overall_score': {'technical': 0, 'fundamental': 0, 'sentiment': 0, 'overall': 0},
            'recommendation': {'action': 'HOLD', 'confidence': 'LOW', 'score': 0},
            'data_quality': {'score': 0.0, 'issues': ['Analysis failed']}
        }


# 创建全局市场分析器实例
market_analyzer = MarketAnalyzer()
