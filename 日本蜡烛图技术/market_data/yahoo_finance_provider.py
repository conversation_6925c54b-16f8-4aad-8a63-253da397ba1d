"""
Yahoo Finance数据提供商
实时获取股票市场数据
"""

import asyncio
import aiohttp
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import json


class YahooFinanceProvider:
    """Yahoo Finance数据提供商"""
    
    def __init__(self):
        self.base_url = "https://query1.finance.yahoo.com/v8/finance/chart"
        self.session: Optional[aiohttp.ClientSession] = None
        self.logger = logging.getLogger("YahooFinanceProvider")
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def get_realtime_data(
        self,
        symbol: str,
        interval: str = "1h",
        period: str = "1d"
    ) -> Dict[str, Any]:
        """获取实时数据"""
        try:
            url = f"{self.base_url}/{symbol}"
            params = {
                'interval': interval,
                'period': period,
                'includePrePost': 'true'
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_yahoo_data(data, symbol)
                else:
                    self.logger.error(f"Failed to fetch data for {symbol}: {response.status}")
                    return self._get_mock_data(symbol)
                    
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return self._get_mock_data(symbol)
    
    def _parse_yahoo_data(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """解析Yahoo Finance数据"""
        try:
            chart = data['chart']['result'][0]
            meta = chart['meta']
            indicators = chart['indicators']['quote'][0]
            timestamps = chart['timestamp']
            
            # 构建蜡烛数据
            candles = []
            for i, timestamp in enumerate(timestamps):
                if (indicators['open'][i] is not None and 
                    indicators['high'][i] is not None and
                    indicators['low'][i] is not None and
                    indicators['close'][i] is not None):
                    
                    candle = {
                        'timestamp': datetime.fromtimestamp(timestamp).isoformat(),
                        'open': float(indicators['open'][i]),
                        'high': float(indicators['high'][i]),
                        'low': float(indicators['low'][i]),
                        'close': float(indicators['close'][i]),
                        'volume': int(indicators['volume'][i]) if indicators['volume'][i] else 0
                    }
                    candles.append(candle)
            
            return {
                'symbol': symbol,
                'currency': meta.get('currency', 'USD'),
                'exchange': meta.get('exchangeName', 'Unknown'),
                'timezone': meta.get('timezone', 'UTC'),
                'current_price': meta.get('regularMarketPrice', 0),
                'previous_close': meta.get('previousClose', 0),
                'candles': candles,
                'last_updated': datetime.now().isoformat(),
                'data_source': 'yahoo_finance'
            }
            
        except Exception as e:
            self.logger.error(f"Error parsing Yahoo data: {e}")
            return self._get_mock_data(symbol)
    
    def _get_mock_data(self, symbol: str) -> Dict[str, Any]:
        """获取模拟数据（当API不可用时）"""
        base_price = 150.0
        candles = []
        
        # 生成24小时的模拟数据
        for i in range(24):
            timestamp = datetime.now() - timedelta(hours=23-i)
            
            # 简单的随机游走
            price_change = (hash(f"{symbol}_{i}") % 200 - 100) / 100.0  # -1 to 1
            open_price = base_price + price_change
            close_price = open_price + (hash(f"{symbol}_{i}_close") % 100 - 50) / 100.0
            high_price = max(open_price, close_price) + abs(hash(f"{symbol}_{i}_high") % 50) / 100.0
            low_price = min(open_price, close_price) - abs(hash(f"{symbol}_{i}_low") % 50) / 100.0
            volume = 1000000 + (hash(f"{symbol}_{i}_vol") % 500000)
            
            candle = {
                'timestamp': timestamp.isoformat(),
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            }
            candles.append(candle)
            base_price = close_price
        
        return {
            'symbol': symbol,
            'currency': 'USD',
            'exchange': 'NASDAQ',
            'timezone': 'America/New_York',
            'current_price': candles[-1]['close'],
            'previous_close': candles[-2]['close'],
            'candles': candles,
            'last_updated': datetime.now().isoformat(),
            'data_source': 'mock_data'
        }
    
    async def get_multiple_symbols(
        self,
        symbols: List[str],
        interval: str = "1h",
        period: str = "1d"
    ) -> Dict[str, Dict[str, Any]]:
        """获取多个股票的数据"""
        tasks = []
        for symbol in symbols:
            task = self.get_realtime_data(symbol, interval, period)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        data_dict = {}
        for i, result in enumerate(results):
            symbol = symbols[i]
            if isinstance(result, Exception):
                self.logger.error(f"Error fetching data for {symbol}: {result}")
                data_dict[symbol] = self._get_mock_data(symbol)
            else:
                data_dict[symbol] = result
        
        return data_dict
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """获取市场概况"""
        # 主要指数
        major_indices = ['%5EGSPC', '%5EDJI', '%5EIXIC']  # S&P 500, Dow Jones, NASDAQ
        
        try:
            indices_data = await self.get_multiple_symbols(major_indices, '1d', '5d')
            
            summary = {
                'timestamp': datetime.now().isoformat(),
                'indices': {},
                'market_status': 'open',  # 简化处理
                'data_source': 'yahoo_finance'
            }
            
            index_names = {
                '%5EGSPC': 'S&P 500',
                '%5EDJI': 'Dow Jones',
                '%5EIXIC': 'NASDAQ'
            }
            
            for symbol, data in indices_data.items():
                if data['candles']:
                    current = data['candles'][-1]['close']
                    previous = data['previous_close']
                    change = current - previous
                    change_percent = (change / previous) * 100 if previous != 0 else 0
                    
                    summary['indices'][index_names.get(symbol, symbol)] = {
                        'current': current,
                        'change': round(change, 2),
                        'change_percent': round(change_percent, 2)
                    }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error fetching market summary: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'indices': {
                    'S&P 500': {'current': 4500.0, 'change': 25.5, 'change_percent': 0.57},
                    'Dow Jones': {'current': 35000.0, 'change': 150.0, 'change_percent': 0.43},
                    'NASDAQ': {'current': 14000.0, 'change': 75.0, 'change_percent': 0.54}
                },
                'market_status': 'open',
                'data_source': 'mock_data'
            }


class MarketDataManager:
    """市场数据管理器"""
    
    def __init__(self):
        self.provider = YahooFinanceProvider()
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5分钟缓存
        self.logger = logging.getLogger("MarketDataManager")
    
    async def get_stock_data(
        self,
        symbol: str,
        interval: str = "1h",
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """获取股票数据"""
        cache_key = f"{symbol}_{interval}"
        
        # 检查缓存
        if use_cache and cache_key in self.cache:
            cached_data = self.cache[cache_key]
            cache_time = datetime.fromisoformat(cached_data['last_updated'])
            if (datetime.now() - cache_time).total_seconds() < self.cache_ttl:
                self.logger.debug(f"Using cached data for {symbol}")
                return cached_data
        
        # 获取新数据
        async with self.provider as provider:
            data = await provider.get_realtime_data(symbol, interval)
            
            # 更新缓存
            if use_cache:
                self.cache[cache_key] = data
            
            return data
    
    async def get_market_overview(self) -> Dict[str, Any]:
        """获取市场概况"""
        async with self.provider as provider:
            return await provider.get_market_summary()
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        self.logger.info("Market data cache cleared")


# 全局市场数据管理器实例
market_data_manager = MarketDataManager()
