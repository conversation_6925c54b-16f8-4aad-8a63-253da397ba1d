#!/usr/bin/env python3
"""
简单的测试服务器
验证基础功能是否正常
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="测试服务器")

@app.get("/")
async def root():
    return {"message": "测试服务器运行正常"}

@app.get("/test")
async def test():
    return {"status": "ok", "message": "API测试成功"}

if __name__ == "__main__":
    print("🧪 启动简单测试服务器...")
    print("📡 地址: http://localhost:8002")
    
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8002,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
