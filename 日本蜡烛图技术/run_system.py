#!/usr/bin/env python3
"""
蜡烛图形态识别 + TradingAgents 系统启动器
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("🚀" + "="*80)
    print("🕯️  蜡烛图形态识别系统 + TradingAgents 多智能体集成版")
    print("📊  基于《日本蜡烛图技术》+ TradingAgents理论")
    print("🤖  AI智能体验证 + 结构化辩论 + 投资建议")
    print("="*82)

def check_files():
    """检查必要文件"""
    print("🔍 检查系统文件...")
    
    required_files = [
        "frontend/build/index.html",
        "backend/src/api/main.py",
        "TradingAgents/config.yaml"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 系统文件检查通过")
    return True

def show_access_info():
    """显示访问信息"""
    print("\n🌐 系统访问信息:")
    print("="*50)
    print("🖥️  前端界面: 已在浏览器中打开")
    print("📱 本地文件: file:///Users/<USER>/Desktop/日本蜡烛图技术/frontend/build/index.html")
    print("="*50)
    print("\n🎯 功能特色:")
    print("✅ 传统蜡烛图形态识别")
    print("✅ TradingAgents多智能体验证")
    print("✅ AI智能体协作分析")
    print("✅ 结构化辩论系统")
    print("✅ 投资建议生成")
    print("✅ 批量形态验证")
    print("\n💡 使用提示:")
    print("1. 上传蜡烛图数据或使用示例数据")
    print("2. 查看形态识别结果")
    print("3. 点击'TradingAgents'标签页体验AI验证")
    print("4. 尝试智能体分析和辩论功能")
    print("\n📝 注意事项:")
    print("- 前端界面已构建并可直接使用")
    print("- TradingAgents功能已集成到前端")
    print("- 所有AI功能都已准备就绪")

def open_frontend():
    """打开前端界面"""
    frontend_path = Path("frontend/build/index.html")
    
    if frontend_path.exists():
        file_url = f"file://{frontend_path.absolute()}"
        print(f"🌐 正在打开前端界面...")
        print(f"📂 路径: {file_url}")
        
        try:
            webbrowser.open(file_url)
            print("✅ 前端界面已在浏览器中打开")
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            print(f"💡 请手动打开: {file_url}")
            return False
    else:
        print("❌ 前端构建文件不存在")
        print("💡 请先运行: cd frontend && npm run build")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查文件
    if not check_files():
        print("\n❌ 系统文件检查失败")
        print("💡 请确保所有必要文件都存在")
        return
    
    # 打开前端
    if open_frontend():
        show_access_info()
        
        print("\n🎉 系统启动完成!")
        print("🚀 开始体验强大的TradingAgents多智能体功能吧!")
        
        # 保持脚本运行
        try:
            print("\n🔄 系统运行中... (按 Ctrl+C 退出)")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n👋 系统已退出")
    else:
        print("\n❌ 前端启动失败")

if __name__ == "__main__":
    main()
