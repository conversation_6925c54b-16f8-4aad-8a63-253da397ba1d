# 蜡烛图形态测试数据说明

## 📊 数据集概览

我为你创建了几个不同的测试数据集，用于验证蜡烛图形态识别系统的功能：

### 1. `comprehensive_patterns_test.csv`
- **数据量**：153条记录
- **时间跨度**：2024年1月1日 - 2024年2月2日
- **特点**：包含完整的价格走势，从100到136.5的上升趋势
- **适用于**：测试系统的整体识别能力

### 2. `pattern_test_data.csv`
- **数据量**：200条记录
- **时间跨度**：2024年1月1日 - 2024年1月27日
- **特点**：包含多种价格模式，从100下降到4，然后上升到503
- **适用于**：测试各种趋势环境下的形态识别

### 3. `diverse_patterns_test.csv`
- **数据量**：161条记录
- **时间跨度**：2024年1月1日 - 2024年1月20日
- **特点**：包含下降趋势、底部反转、上升趋势的完整周期
- **适用于**：测试趋势转换时的形态识别

### 4. `specific_patterns_showcase.csv`
- **数据量**：250条记录
- **时间跨度**：2024年1月1日 - 2024年1月30日
- **特点**：精心设计的数据，包含多种经典形态
- **适用于**：展示系统的完整功能

## 🎯 预期识别的形态

根据数据特征，系统应该能够识别出以下形态：

### 单根蜡烛线形态
- **锤子线** (Hammer) - 在下降趋势底部
- **上吊线** (Hanging Man) - 在上升趋势顶部
- **十字线** (Doji) - 市场犹豫时刻
- **纺锤线** (Spinning Top) - 小实体长影线
- **光头光脚线** (Marubozu) - 强势蜡烛线

### 双根蜡烛线形态
- **看涨吞没** (Bullish Engulfing) - 底部反转信号
- **看跌吞没** (Bearish Engulfing) - 顶部反转信号
- **刺透形态** (Piercing Pattern) - 底部反转
- **乌云盖顶** (Dark Cloud Cover) - 顶部反转
- **孕线形态** (Harami) - 反转信号

### 三根蜡烛线形态
- **启明星** (Morning Star) - 强烈底部反转
- **黄昏星** (Evening Star) - 强烈顶部反转
- **前进白色三兵** (Three White Soldiers) - 强势上涨
- **三只乌鸦** (Three Black Crows) - 强势下跌

## 🧪 测试建议

### 1. 基础功能测试
```bash
# 测试API是否正常工作
curl -X GET "http://localhost:8000/api/v1/health"
```

### 2. 小数据集测试
使用前10-20条数据测试基本形态识别：
- 上传 `pattern_test_data.csv` 的前20行
- 检查是否识别出"三只乌鸦"形态

### 3. 完整数据集测试
- 上传完整的 `specific_patterns_showcase.csv`
- 查看识别出的所有形态
- 验证形态的置信度和描述

### 4. 前端界面测试
1. 访问 http://localhost:3000
2. 上传任一CSV文件
3. 切换"经典样式"查看黑白蜡烛图
4. 查看形态标注和详细信息
5. 测试悬停效果和工具提示

## 📈 数据特征说明

### 价格走势设计
- **下降阶段**：模拟熊市环境，容易出现底部反转形态
- **底部区域**：价格在低位震荡，适合测试各种反转信号
- **上升阶段**：模拟牛市环境，容易出现顶部反转形态
- **高位区域**：价格在高位运行，适合测试顶部形态

### 成交量设计
- 逐步递增的成交量模式
- 在关键反转点增加成交量
- 支持成交量确认功能测试

### 时间间隔
- 每小时一根蜡烛线
- 连续的时间序列
- 便于观察形态的时间关系

## 🎨 可视化效果

使用这些数据，你应该能看到：

1. **专业蜡烛图**：真正的OHLC蜡烛线，不是柱状图模拟
2. **经典黑白风格**：参考《日本蜡烛图技术》书中样式
3. **智能形态标注**：中英文对照的形态名称
4. **详细信息面板**：形态描述、交易信号、置信度
5. **统计汇总**：看涨/看跌/反转形态数量统计

## 🔍 故障排除

如果遇到问题：

1. **API错误**：检查后端是否正常运行
2. **前端显示问题**：刷新页面或检查控制台错误
3. **形态识别异常**：查看后端日志了解详细错误信息
4. **数据格式问题**：确保CSV文件格式正确

## 📝 测试记录

建议记录以下测试结果：
- [ ] 识别出的形态数量
- [ ] 各种形态类型的分布
- [ ] 置信度分布情况
- [ ] 前端显示效果
- [ ] 用户交互体验

祝测试顺利！🎯
