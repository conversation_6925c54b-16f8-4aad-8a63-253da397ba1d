#!/usr/bin/env python3
"""
简化版智能体辩论演示
展示辩论机制的核心功能
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.researchers.bullish_researcher import BullishResearcher
from agents.researchers.bearish_researcher import BearishResearcher
from agents.debate.debate_moderator import DebateModerator
from market_data.yahoo_finance_provider import market_data_manager


async def simulate_market_analysis():
    """模拟市场分析数据"""
    print("📊 模拟市场分析数据...")
    
    # 模拟技术分析结果
    technical_analysis = {
        'symbol': 'TSLA',
        'patterns': [
            {
                'name': 'MORNING_STAR',
                'chinese_name': '启明星',
                'signal': 'BULLISH',
                'confidence': 0.85,
                'description': '三根蜡烛形成的底部反转形态'
            },
            {
                'name': 'RESISTANCE_LEVEL',
                'chinese_name': '阻力位',
                'signal': 'BEARISH',
                'confidence': 0.70,
                'description': '价格接近重要阻力位'
            }
        ],
        'confidence': 0.75,
        'trend': 'UPTREND',
        'volume': 'INCREASING'
    }
    
    # 模拟市场环境
    market_context = {
        'current_price': 248.50,
        'previous_close': 245.20,
        'change': 3.30,
        'change_percent': 1.35,
        'volume': 25000000,
        'market_sentiment': 'POSITIVE',
        'sector_performance': 'OUTPERFORMING'
    }
    
    print(f"   🎯 分析标的: {technical_analysis['symbol']}")
    print(f"   💰 当前价格: ${market_context['current_price']}")
    print(f"   📈 涨跌幅: +{market_context['change_percent']:.2f}%")
    print(f"   🔍 识别形态: {len(technical_analysis['patterns'])}个")
    
    return {
        'technical_analysis': technical_analysis,
        'market_context': market_context
    }


async def run_debate_demo():
    """运行辩论演示"""
    print("\n🎭 智能体辩论演示")
    print("=" * 50)
    
    # 1. 创建智能体
    print("\n👥 创建辩论参与者...")
    
    bullish_researcher = BullishResearcher(
        agent_id="bullish_researcher_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.2}
    )
    
    bearish_researcher = BearishResearcher(
        agent_id="bearish_researcher_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.2}
    )
    
    debate_moderator = DebateModerator(
        agent_id="debate_moderator_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.3}
    )
    
    print(f"   ✅ {bullish_researcher.agent_id} - 看涨研究员")
    print(f"   ✅ {bearish_researcher.agent_id} - 看跌研究员")
    print(f"   ✅ {debate_moderator.agent_id} - 辩论主持人")
    
    # 2. 获取分析数据
    analysis_data = await simulate_market_analysis()
    
    # 3. 研究员形成初步观点
    print("\n🔬 研究员观点形成...")
    
    # 看涨研究员分析
    bullish_input = {
        'symbol': 'TSLA',
        'patterns': analysis_data['technical_analysis']['patterns'],
        'technical_analysis': analysis_data['technical_analysis'],
        'market_context': analysis_data['market_context']
    }
    
    bullish_opinion = await bullish_researcher._perform_analysis(bullish_input)
    
    print(f"   🐂 看涨研究员观点:")
    print(f"      立场: {bullish_opinion.get('opinion', 'BULLISH')}")
    print(f"      置信度: {bullish_opinion.get('confidence', 0.7):.2f}")
    print(f"      核心理由: {bullish_opinion.get('reasoning', '技术面显示积极信号')[:60]}...")
    
    # 看跌研究员分析
    bearish_opinion = await bearish_researcher._perform_analysis(bullish_input)
    
    print(f"   🐻 看跌研究员观点:")
    print(f"      立场: {bearish_opinion.get('opinion', 'BEARISH')}")
    print(f"      置信度: {bearish_opinion.get('confidence', 0.6):.2f}")
    print(f"      核心理由: {bearish_opinion.get('reasoning', '需要关注风险因素')[:60]}...")
    
    # 4. 启动辩论
    print(f"\n🎯 启动正式辩论: TSLA投资决策")
    
    debate_topic = "特斯拉(TSLA)当前是否应该买入？"
    participants = ['bullish_researcher_001', 'bearish_researcher_001']
    
    session_id = await debate_moderator.start_debate(
        topic=debate_topic,
        participants=participants,
        initial_data=bullish_input
    )
    
    print(f"   📋 辩论主题: {debate_topic}")
    print(f"   🆔 会话ID: {session_id}")
    
    # 5. 多轮辩论
    debate_rounds = [
        {
            'bullish': "技术分析显示启明星形态，这是强烈的底部反转信号，配合成交量放大，上涨概率很高。",
            'bearish': "虽然有启明星形态，但价格已接近重要阻力位，突破失败的风险较大，建议谨慎。"
        },
        {
            'bullish': "从基本面看，电动车行业前景广阔，特斯拉作为龙头企业，长期价值被低估。",
            'bearish': "但当前估值已经较高，市场预期过于乐观，任何负面消息都可能引发大幅回调。"
        },
        {
            'bullish': "综合风险收益比，当前价位提供了良好的投资机会，建议适度配置。",
            'bearish': "建议等待更明确的突破信号，或者价格回调到更安全的区域再考虑买入。"
        }
    ]
    
    for round_num, arguments in enumerate(debate_rounds, 1):
        print(f"\n🔄 第{round_num}轮辩论:")
        print(f"   🐂 看涨论点: {arguments['bullish']}")
        print(f"   🐻 看跌论点: {arguments['bearish']}")
        
        # 主持人分析
        moderation_result = await debate_moderator.moderate_round(
            session_id=session_id,
            round_number=round_num,
            bullish_argument=arguments['bullish'],
            bearish_argument=arguments['bearish']
        )
        
        print(f"   🎯 主持人分析:")
        print(f"      总结: {moderation_result.get('debate_summary', '双方观点已记录')[:80]}...")
        
        consensus_points = moderation_result.get('consensus_points', [])
        if consensus_points:
            print(f"      共识: {consensus_points[0][:60]}...")
        
        disagreements = moderation_result.get('key_disagreements', [])
        if disagreements:
            print(f"      分歧: {disagreements[0][:60]}...")
        
        # 检查置信度
        confidence = moderation_result.get('recommendation', {}).get('confidence_level', 0.5)
        print(f"      决策置信度: {confidence:.2f}")
        
        if confidence > 0.8:
            print(f"      ✅ 达成高度共识！")
            break
        
        await asyncio.sleep(1)  # 模拟思考时间
    
    # 6. 辩论总结
    print(f"\n📋 辩论总结:")
    
    debate_status = debate_moderator.get_debate_status(session_id)
    if debate_status:
        print(f"   ⏱️ 辩论时长: {debate_status['duration']:.1f}秒")
        print(f"   🔄 完成轮数: {debate_status['rounds_completed']}")
        print(f"   🤝 达成共识: {'是' if debate_status['consensus_reached'] else '否'}")
    
    # 最终建议
    final_recommendation = moderation_result.get('recommendation', {})
    print(f"\n🎯 最终投资建议:")
    print(f"   📊 建议方案: {final_recommendation.get('suggested_approach', '综合考虑双方观点')}")
    print(f"   📈 置信度: {final_recommendation.get('confidence_level', 0.6):.2f}")
    
    next_steps = final_recommendation.get('next_steps', ['继续观察市场'])
    print(f"   📝 下一步: {', '.join(next_steps[:2])}")
    
    return {
        'session_id': session_id,
        'bullish_confidence': bullish_opinion.get('confidence', 0.7),
        'bearish_confidence': bearish_opinion.get('confidence', 0.6),
        'final_recommendation': final_recommendation,
        'debate_status': debate_status
    }


async def main():
    """主函数"""
    print("🎭 智能体辩论系统演示")
    print("基于LLM的看涨vs看跌研究员辩论")
    print("=" * 60)
    
    try:
        result = await run_debate_demo()
        
        if result:
            print("\n" + "=" * 60)
            print("🎉 辩论演示完成！")
            
            print("\n✨ 演示亮点:")
            print("   🧠 AI驱动的智能辩论 - 基于LLM的观点生成")
            print("   🤖 专业角色扮演 - 看涨vs看跌研究员")
            print("   🎯 结构化辩论流程 - 多轮论证与反驳")
            print("   📊 数据驱动分析 - 基于技术分析结果")
            print("   🤝 智能共识达成 - 自动化决策收敛")
            print("   🔄 实时状态跟踪 - 辩论进度监控")
            
            print(f"\n📈 TSLA辩论结果:")
            print(f"   🐂 看涨置信度: {result['bullish_confidence']:.2f}")
            print(f"   🐻 看跌置信度: {result['bearish_confidence']:.2f}")
            print(f"   🎯 最终建议: {result['final_recommendation'].get('suggested_approach', 'N/A')}")
            print(f"   📊 决策置信度: {result['final_recommendation'].get('confidence_level', 0.6):.2f}")
        
        print("\n🚀 系统优势:")
        print("1. 🧠 多角度分析 - 避免单一视角盲点")
        print("2. 🤝 观点碰撞 - 通过辩论提高决策质量")
        print("3. 🎯 结构化流程 - 确保分析的完整性")
        print("4. 📊 量化评估 - 置信度和风险量化")
        print("5. 🔄 可追溯性 - 完整的决策过程记录")
        
        print("\n💡 下一步发展:")
        print("- 配置真实OpenAI API获得更智能的辩论")
        print("- 添加更多辩论参与者（基本面分析师等）")
        print("- 实现WebSocket实时辩论界面")
        print("- 开发辩论历史记录和回放功能")
        print("- 集成实时市场数据和新闻情绪")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🎭 启动智能体辩论系统")
    print("展示AI驱动的投资决策辩论机制")
    print()
    
    # 运行演示
    asyncio.run(main())
    
    print("\n🎉 辩论演示完成!")
    print("\n🌟 这展示了未来投资决策的新模式:")
    print("- 🤖 AI智能体团队协作")
    print("- 🧠 多维度观点碰撞")
    print("- 🎯 结构化决策流程")
    print("- 📊 量化风险评估")
