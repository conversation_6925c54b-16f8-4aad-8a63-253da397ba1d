#!/usr/bin/env python3
"""
API集成测试
测试新的多智能体API端点
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_root_endpoint():
    """测试根端点"""
    print("🔍 测试根端点...")
    
    try:
        response = requests.get(f"{BASE_URL}/")
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 根端点响应成功:")
        print(f"   消息: {data['message']}")
        print(f"   版本: {data['version']}")
        print(f"   功能: {', '.join(data['features'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 根端点测试失败: {e}")
        return False

def test_agents_status():
    """测试智能体状态端点"""
    print("\n🤖 测试智能体状态端点...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/agents/status")
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 智能体状态获取成功:")
        print(f"   活跃智能体: {data['communication']['active_agents']}")
        print(f"   发送消息: {data['communication']['messages_sent']}")
        print(f"   处理消息: {data['communication']['messages_processed']}")
        
        # 显示智能体详情
        if 'agents' in data:
            print("   智能体列表:")
            for agent_id, status in data['agents'].items():
                print(f"     - {agent_id}: {status['role']} ({'活跃' if status['is_active'] else '非活跃'})")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体状态测试失败: {e}")
        return False

def test_llm_stats():
    """测试LLM统计端点"""
    print("\n🧠 测试LLM统计端点...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/agents/llm/stats")
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ LLM统计获取成功:")
        print(f"   可用提供商: {', '.join(data['providers'])}")
        print(f"   默认提供商: {data['default_provider']}")
        
        # 显示统计信息
        if 'stats' in data:
            for provider, stats in data['stats'].items():
                print(f"   {provider}统计:")
                print(f"     - 请求次数: {stats['request_count']}")
                print(f"     - 总Token: {stats['total_tokens']}")
                print(f"     - 总成本: ${stats['total_cost']:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM统计测试失败: {e}")
        return False

def test_multi_agent_analysis():
    """测试多智能体分析"""
    print("\n📊 测试多智能体分析...")
    
    # 准备测试数据（启明星形态）
    test_data = {
        "symbol": "AAPL",
        "timeframe": "1H",
        "analysis_type": "comprehensive",
        "candles": [
            {"open": 150.0, "high": 152.0, "low": 148.0, "close": 149.0, "volume": 1000000, "timestamp": "2024-01-01T09:00:00Z"},
            {"open": 149.0, "high": 150.0, "low": 146.0, "close": 147.0, "volume": 1100000, "timestamp": "2024-01-01T10:00:00Z"},
            {"open": 147.0, "high": 148.0, "low": 144.0, "close": 145.0, "volume": 1200000, "timestamp": "2024-01-01T11:00:00Z"},
            {"open": 145.0, "high": 146.0, "low": 142.0, "close": 143.0, "volume": 1300000, "timestamp": "2024-01-01T12:00:00Z"},
            {"open": 143.0, "high": 144.0, "low": 140.0, "close": 141.0, "volume": 1400000, "timestamp": "2024-01-01T13:00:00Z"},
            # 启明星形态
            {"open": 141.0, "high": 142.0, "low": 138.0, "close": 139.0, "volume": 1500000, "timestamp": "2024-01-01T14:00:00Z"},  # 大阴线
            {"open": 139.0, "high": 140.0, "low": 138.5, "close": 139.2, "volume": 800000, "timestamp": "2024-01-01T15:00:00Z"},   # 小实体
            {"open": 139.5, "high": 143.0, "low": 139.0, "close": 142.0, "volume": 1600000, "timestamp": "2024-01-01T16:00:00Z"},  # 大阳线
        ]
    }
    
    try:
        print(f"   发送分析请求: {test_data['symbol']} ({len(test_data['candles'])}根蜡烛)")
        
        response = requests.post(
            f"{BASE_URL}/api/v1/agents/analyze",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 多智能体分析完成:")
        print(f"   分析ID: {data['analysis_id']}")
        print(f"   标的: {data['symbol']}")
        print(f"   识别形态: {len(data['patterns'])}个")
        
        # 显示识别的形态
        for i, pattern in enumerate(data['patterns'], 1):
            print(f"     {i}. {pattern['chinese_name']} ({pattern['signal']}, 置信度: {pattern['confidence']:.2f})")
        
        # 显示智能体观点
        print(f"   智能体观点: {len(data['agent_opinions'])}个")
        for opinion in data['agent_opinions']:
            print(f"     - {opinion['role']}: {opinion['opinion']} (置信度: {opinion['confidence']:.2f})")
        
        # 显示共识
        consensus = data['consensus']
        print(f"   综合共识:")
        print(f"     - 市场展望: {consensus['outlook']}")
        print(f"     - 建议操作: {consensus['recommendation']}")
        print(f"     - 整体置信度: {consensus['confidence']:.2f}")
        print(f"     - 总结: {consensus['summary']}")
        
        # 如果有工作流ID，检查工作流状态
        if data.get('workflow_id'):
            print(f"   工作流ID: {data['workflow_id']}")
            time.sleep(2)  # 等待工作流执行
            test_workflow_status(data['workflow_id'])
        
        return True
        
    except Exception as e:
        print(f"❌ 多智能体分析测试失败: {e}")
        if hasattr(e, 'response') and e.response:
            print(f"   响应内容: {e.response.text}")
        return False

def test_workflow_status(workflow_id):
    """测试工作流状态"""
    print(f"\n🔄 测试工作流状态: {workflow_id}")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/agents/workflow/{workflow_id}/status")
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 工作流状态获取成功:")
        print(f"   状态: {data['status']}")
        print(f"   进度: {data['progress']:.1%}")
        print(f"   已完成阶段: {', '.join(data['completed_stages'])}")
        
        if data.get('failed_stages'):
            print(f"   失败阶段: {', '.join(data['failed_stages'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流状态测试失败: {e}")
        return False

def test_workflow_start():
    """测试启动工作流"""
    print("\n🚀 测试启动工作流...")
    
    workflow_data = {
        "workflow_type": "market_analysis",
        "input_data": {
            "symbol": "TSLA",
            "timeframe": "4H",
            "analysis_type": "quick"
        },
        "priority": 1
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/agents/workflow/start",
            json=workflow_data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ 工作流启动成功:")
        print(f"   工作流ID: {data['workflow_id']}")
        print(f"   状态: {data['status']}")
        
        # 等待一下再检查状态
        time.sleep(1)
        test_workflow_status(data['workflow_id'])
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 多智能体API集成测试")
    print("=" * 60)
    
    tests = [
        ("根端点", test_root_endpoint),
        ("智能体状态", test_agents_status),
        ("LLM统计", test_llm_stats),
        ("多智能体分析", test_multi_agent_analysis),
        ("工作流启动", test_workflow_start),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有API测试通过！")
        print("\n✨ 多智能体系统API已成功集成！")
        print("\n🚀 下一步可以:")
        print("1. 开发前端智能体工作台界面")
        print("2. 添加WebSocket实时通信")
        print("3. 配置真实的LLM API")
        print("4. 扩展更多智能体功能")
    else:
        print("⚠️  部分API测试失败，请检查服务器状态")

if __name__ == "__main__":
    main()
