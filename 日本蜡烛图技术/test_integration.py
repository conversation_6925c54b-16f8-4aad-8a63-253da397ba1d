#!/usr/bin/env python3
"""
测试TradingAgents与蜡烛图系统集成
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path(__file__).parent / "backend" / "src"))
sys.path.insert(0, str(Path(__file__).parent / "TradingAgents"))

async def test_trading_agents_service():
    """测试TradingAgents服务"""
    print("🧪 测试TradingAgents服务集成")
    print("="*50)
    
    try:
        from services.trading_agents_service import TradingAgentsWebService
        
        # 创建服务实例
        service = TradingAgentsWebService()
        
        # 初始化服务
        print("🔧 初始化TradingAgents服务...")
        success = await service.initialize()
        
        if not success:
            print(f"❌ 服务初始化失败: {service.initialization_error}")
            return False
        
        print("✅ 服务初始化成功")
        
        # 测试系统状态
        print("📊 获取系统状态...")
        status = await service.get_system_status()
        print(f"状态: {status['success']}")
        
        if status['success']:
            data = status['data']
            print(f"  - 系统状态: {data.get('status', 'unknown')}")
            print(f"  - 已初始化: {data.get('initialized', False)}")
            
            agents = data.get('agents', {})
            print(f"  - 总智能体: {agents.get('total_agents', 0)}")
            print(f"  - 活跃智能体: {agents.get('active_agents', 0)}")
        
        # 测试形态验证
        print("\n🕯️ 测试形态验证...")
        
        # 示例蜡烛图数据
        sample_candles = [
            {
                'timestamp': '2024-01-15T09:30:00',
                'open': 150.00,
                'high': 152.50,
                'low': 149.00,
                'close': 151.20,
                'volume': 1000000
            },
            {
                'timestamp': '2024-01-15T10:30:00',
                'open': 151.20,
                'high': 151.50,
                'low': 148.80,
                'close': 149.50,
                'volume': 1200000
            },
            {
                'timestamp': '2024-01-15T11:30:00',
                'open': 149.50,
                'high': 150.00,
                'low': 149.40,
                'close': 149.60,
                'volume': 800000
            }
        ]
        
        pattern_data = {
            'pattern_name': 'doji',
            'symbol': 'AAPL',
            'candle_data': sample_candles,
            'confidence': 0.85
        }
        
        validation_result = await service.validate_pattern(pattern_data)
        
        if validation_result['success']:
            print("✅ 形态验证成功")
            data = validation_result['data']
            assessment = data.get('validation_result', {})
            print(f"  - 验证结论: {assessment.get('validation_conclusion', 'unknown')}")
            print(f"  - 可靠性等级: {assessment.get('reliability_level', 'unknown')}")
            print(f"  - 最终分数: {assessment.get('final_validation_score', 0):.1%}")
        else:
            print(f"❌ 形态验证失败: {validation_result.get('error', 'Unknown error')}")
        
        # 关闭服务
        await service.shutdown()
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_routes():
    """测试API路由"""
    print("\n🌐 测试API路由")
    print("="*50)
    
    try:
        # 这里可以添加HTTP客户端测试
        print("💡 API路由测试需要启动服务器")
        print("   请运行: python start_integrated_system.py")
        print("   然后访问: http://localhost:8000/docs")
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_frontend_components():
    """测试前端组件"""
    print("\n🎨 检查前端组件")
    print("="*50)
    
    frontend_components = [
        "frontend/src/components/TradingAgents/TradingAgentsPanel.jsx",
        "frontend/src/components/TradingAgents/PatternValidation.jsx",
        "frontend/src/components/TradingAgents/AgentAnalysis.jsx",
        "frontend/src/components/TradingAgents/DebateViewer.jsx",
        "frontend/src/components/TradingAgents/SystemStatus.jsx"
    ]
    
    missing_components = []
    for component in frontend_components:
        if not Path(component).exists():
            missing_components.append(component)
    
    if missing_components:
        print("❌ 缺少前端组件:")
        for component in missing_components:
            print(f"   - {component}")
        return False
    else:
        print("✅ 所有前端组件已创建")
        return True

def test_configuration():
    """测试配置"""
    print("\n⚙️ 检查配置文件")
    print("="*50)
    
    config_files = [
        "TradingAgents/config.yaml",
        "TradingAgents/.env",
        "backend/requirements.txt",
        "frontend/package.json"
    ]
    
    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file}")
        else:
            print(f"⚠️ {config_file} (可选)")
    
    return True

async def main():
    """主测试函数"""
    print("🚀 TradingAgents集成测试")
    print("🎯 测试蜡烛图形态识别 + 多智能体系统集成")
    print("="*80)
    
    tests = [
        ("配置检查", test_configuration),
        ("前端组件", test_frontend_components),
        ("API路由", test_api_routes),
        ("TradingAgents服务", test_trading_agents_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 测试结果摘要
    print("\n📊 测试结果摘要")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统集成成功！")
        print("\n🚀 下一步:")
        print("1. 运行: python start_integrated_system.py")
        print("2. 访问: http://localhost:3000")
        print("3. 体验TradingAgents多智能体功能")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
