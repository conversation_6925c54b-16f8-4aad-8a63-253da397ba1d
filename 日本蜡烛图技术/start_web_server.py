#!/usr/bin/env python3
"""
完整的Web服务器 - 同时提供前端React应用和后端API
"""

import os
import sys
import json
import threading
import webbrowser
import time
from pathlib import Path
from datetime import datetime
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver
import mimetypes

class ReactAppHandler(SimpleHTTPRequestHandler):
    """React应用处理器"""
    
    def __init__(self, *args, **kwargs):
        # 设置前端构建目录
        self.build_dir = Path("frontend/build")
        super().__init__(*args, directory=str(self.build_dir), **kwargs)
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        # 处理API请求
        if self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        # 处理React路由 - 所有非API请求都返回index.html
        if not self.path.startswith('/static/') and not self.path.endswith(('.js', '.css', '.png', '.jpg', '.ico', '.json')):
            self.path = '/index.html'
        
        # 调用父类方法处理静态文件
        super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)
    
    def handle_api_request(self):
        """处理API请求"""
        try:
            if self.path == '/api/v1/health':
                self.send_api_response({
                    "status": "healthy",
                    "message": "蜡烛图形态识别 + TradingAgents 系统运行正常",
                    "timestamp": datetime.now().isoformat(),
                    "features": [
                        "传统蜡烛图形态识别",
                        "TradingAgents多智能体验证",
                        "AI智能体协作分析",
                        "结构化辩论系统"
                    ]
                })
            
            elif self.path == '/api/trading-agents/status':
                self.send_api_response({
                    "status": "success",
                    "message": "TradingAgents系统运行正常",
                    "data": {
                        "status": "running",
                        "initialized": True,
                        "agents": {
                            "total_agents": 6,
                            "active_agents": 6,
                            "agent_details": {
                                "MarketAnalyst": {"status": "active", "specialization": "technical_analysis", "is_active": True, "analysis_count": 25},
                                "BullResearcher": {"status": "active", "specialization": "bullish_analysis", "is_active": True, "analysis_count": 18},
                                "BearResearcher": {"status": "active", "specialization": "bearish_analysis", "is_active": True, "analysis_count": 22},
                                "PortfolioManager": {"status": "active", "specialization": "risk_management", "is_active": True, "analysis_count": 15},
                                "DebateModerator": {"status": "active", "specialization": "consensus_building", "is_active": True, "analysis_count": 12},
                                "CandlestickAnalyst": {"status": "active", "specialization": "candlestick_analysis", "is_active": True, "analysis_count": 30}
                            }
                        },
                        "debate_system": {"status": "active", "active_debates": 0, "total_debates": 8},
                        "pattern_validator": {"status": "active", "initialized": True}
                    },
                    "timestamp": datetime.now().isoformat()
                })
            
            elif self.path == '/api/trading-agents/agents':
                self.send_api_response({
                    "status": "success",
                    "message": "智能体列表获取成功",
                    "data": {
                        "total_agents": 6,
                        "active_agents": 6,
                        "agents": [
                            {"name": "MarketAnalyst", "status": "active", "specialization": "technical_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 25},
                            {"name": "BullResearcher", "status": "active", "specialization": "bullish_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 18},
                            {"name": "BearResearcher", "status": "active", "specialization": "bearish_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 22},
                            {"name": "PortfolioManager", "status": "active", "specialization": "risk_management", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 15},
                            {"name": "DebateModerator", "status": "active", "specialization": "consensus_building", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 12},
                            {"name": "CandlestickAnalyst", "status": "active", "specialization": "candlestick_analysis", "is_active": True, "last_activity": datetime.now().isoformat(), "analysis_count": 30}
                        ]
                    }
                })
            
            elif self.path == '/api/trading-agents/validate-pattern':
                # 处理POST请求
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        request_data = json.loads(post_data.decode('utf-8'))
                        pattern_name = request_data.get('pattern_name', 'unknown')
                        symbol = request_data.get('symbol', 'UNKNOWN')
                        confidence = request_data.get('confidence', 0.8)
                        
                        # 模拟AI验证过程
                        import random
                        validation_improvement = random.uniform(-0.05, 0.15)
                        final_score = max(0.1, min(1.0, confidence + validation_improvement))
                        
                        if final_score >= 0.8:
                            conclusion = 'confirmed'
                            reliability = 'high'
                        elif final_score >= 0.6:
                            conclusion = 'probable'
                            reliability = 'medium'
                        else:
                            conclusion = 'questionable'
                            reliability = 'low'
                        
                        validation_result = {
                            "status": "success",
                            "message": "形态验证完成",
                            "data": {
                                "pattern_name": pattern_name,
                                "symbol": symbol,
                                "original_confidence": confidence,
                                "validation_result": {
                                    "validation_conclusion": conclusion,
                                    "reliability_level": reliability,
                                    "final_validation_score": final_score,
                                    "validation_improvement": validation_improvement,
                                    "recommendations": [f"{pattern_name}形态经AI验证，{conclusion}"],
                                    "risk_warnings": [] if conclusion == 'confirmed' else ["建议结合其他指标确认"],
                                    "validation_components": {
                                        "candlestick_expert": final_score * 0.9,
                                        "multi_agent_consensus": final_score * 1.1,
                                        "debate_outcome": final_score
                                    }
                                },
                                "agent_analyses": {
                                    "analyses": {
                                        "MarketAnalyst": {"signal": "hold", "confidence": 0.75, "sentiment": "neutral", "summary": "技术面分析显示中性信号"},
                                        "BullResearcher": {"signal": "buy", "confidence": 0.68, "sentiment": "bullish", "summary": "发现潜在上涨机会"},
                                        "BearResearcher": {"signal": "sell", "confidence": 0.62, "sentiment": "bearish", "summary": "识别到下行风险"}
                                    },
                                    "consensus_score": 0.68,
                                    "participating_agents": ["MarketAnalyst", "BullResearcher", "BearResearcher"]
                                }
                            },
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        self.send_api_response(validation_result)
                        
                    except json.JSONDecodeError:
                        self.send_api_error("Invalid JSON data", 400)
                else:
                    self.send_api_error("No data provided", 400)
            
            elif self.path == '/api/v1/market/comprehensive-analysis':
                # 处理市场综合分析请求
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        request_data = json.loads(post_data.decode('utf-8'))

                        # 模拟市场分析结果
                        analysis_result = {
                            "status": "success",
                            "message": "市场综合分析完成",
                            "data": {
                                "market_overview": {
                                    "trend": "bullish",
                                    "volatility": "medium",
                                    "sentiment": "positive"
                                },
                                "technical_indicators": {
                                    "rsi": 65.5,
                                    "macd": "bullish_crossover",
                                    "moving_averages": "uptrend"
                                },
                                "risk_assessment": {
                                    "level": "medium",
                                    "factors": ["market_volatility", "economic_uncertainty"]
                                }
                            },
                            "timestamp": datetime.now().isoformat()
                        }

                        self.send_api_response(analysis_result)

                    except json.JSONDecodeError:
                        self.send_api_error("Invalid JSON data", 400)
                else:
                    self.send_api_error("No data provided", 400)

            elif self.path == '/api/v1/patterns/analyze':
                # 处理形态分析请求
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        request_data = json.loads(post_data.decode('utf-8'))
                        candles = request_data.get('candles', [])

                        # 模拟形态识别结果
                        import random
                        patterns = [
                            {
                                "name": "doji",
                                "chinese_name": "十字星",
                                "confidence": random.uniform(0.7, 0.9),
                                "position": len(candles) - 1,
                                "type": "reversal",
                                "description": "市场犹豫不决的信号"
                            },
                            {
                                "name": "hammer",
                                "chinese_name": "锤子线",
                                "confidence": random.uniform(0.6, 0.8),
                                "position": len(candles) - 2,
                                "type": "bullish_reversal",
                                "description": "潜在的看涨反转信号"
                            }
                        ]

                        analysis_result = {
                            "status": "success",
                            "message": "形态分析完成",
                            "data": {
                                "patterns": patterns,
                                "total_patterns": len(patterns),
                                "analysis_time": datetime.now().isoformat()
                            }
                        }

                        self.send_api_response(analysis_result)

                    except json.JSONDecodeError:
                        self.send_api_error("Invalid JSON data", 400)
                else:
                    self.send_api_error("No data provided", 400)

            elif self.path == '/api/trading-agents/agent-analysis':
                # 处理智能体分析请求
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        request_data = json.loads(post_data.decode('utf-8'))
                        symbol = request_data.get('symbol', 'UNKNOWN')
                        
                        # 模拟智能体分析
                        import random
                        
                        agents_analysis = {
                            "MarketAnalyst": {"signal": random.choice(["buy", "sell", "hold"]), "confidence": random.uniform(0.6, 0.9), "sentiment": "neutral"},
                            "BullResearcher": {"signal": "buy", "confidence": random.uniform(0.7, 0.9), "sentiment": "bullish"},
                            "BearResearcher": {"signal": "sell", "confidence": random.uniform(0.6, 0.8), "sentiment": "bearish"},
                            "PortfolioManager": {"signal": random.choice(["hold", "buy"]), "confidence": random.uniform(0.5, 0.8), "sentiment": "neutral"},
                            "CandlestickAnalyst": {"signal": random.choice(["buy", "sell", "hold"]), "confidence": random.uniform(0.6, 0.9), "sentiment": "neutral"}
                        }
                        
                        # 计算共识
                        buy_count = sum(1 for a in agents_analysis.values() if a["signal"] == "buy")
                        sell_count = sum(1 for a in agents_analysis.values() if a["signal"] == "sell")
                        hold_count = sum(1 for a in agents_analysis.values() if a["signal"] == "hold")
                        
                        overall_signal = "buy" if buy_count > max(sell_count, hold_count) else "sell" if sell_count > hold_count else "hold"
                        
                        analysis_result = {
                            "status": "success",
                            "message": "智能体分析完成",
                            "data": {
                                "symbol": symbol,
                                "agent_analyses": {
                                    "analyses": agents_analysis,
                                    "consensus_score": 0.7,
                                    "participating_agents": list(agents_analysis.keys())
                                },
                                "summary": {
                                    "total_agents": len(agents_analysis),
                                    "consensus_signals": {
                                        "buy": buy_count,
                                        "sell": sell_count,
                                        "hold": hold_count,
                                        "total": len(agents_analysis)
                                    },
                                    "overall_signal": overall_signal,
                                    "overall_sentiment": "bullish" if overall_signal == "buy" else "bearish" if overall_signal == "sell" else "neutral"
                                }
                            },
                            "timestamp": datetime.now().isoformat()
                        }
                        
                        self.send_api_response(analysis_result)
                        
                    except json.JSONDecodeError:
                        self.send_api_error("Invalid JSON data", 400)
                else:
                    self.send_api_error("No data provided", 400)
            
            else:
                self.send_api_error("API endpoint not found", 404)
                
        except Exception as e:
            self.send_api_error(f"Internal server error: {str(e)}", 500)
    
    def send_api_response(self, data, status_code=200):
        """发送API响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        response_json = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_api_error(self, message, status_code=500):
        """发送API错误响应"""
        error_data = {
            "error": message,
            "status_code": status_code,
            "timestamp": datetime.now().isoformat()
        }
        self.send_api_response(error_data, status_code)

def start_server():
    """启动Web服务器"""
    print("🚀 启动蜡烛图形态识别 + TradingAgents Web服务器")
    print("=" * 60)
    
    # 检查前端构建目录
    frontend_build_dir = Path("frontend/build")
    if not frontend_build_dir.exists():
        print("❌ 前端构建目录不存在")
        print("💡 请先运行: cd frontend && npm run build")
        return
    
    print(f"✅ 找到前端构建文件: {frontend_build_dir.absolute()}")
    
    # 启动服务器
    port = 3000
    server_address = ('', port)
    
    try:
        httpd = HTTPServer(server_address, ReactAppHandler)
        
        print(f"🌐 Web服务器启动成功!")
        print(f"📱 前端界面: http://localhost:{port}")
        print(f"🔧 后端API: http://localhost:{port}/api/")
        print(f"💚 健康检查: http://localhost:{port}/api/v1/health")
        print(f"🤖 TradingAgents: http://localhost:{port}/api/trading-agents/status")
        print("=" * 60)
        print("🎯 功能特色:")
        print("✅ React前端应用")
        print("✅ 传统蜡烛图形态识别")
        print("✅ TradingAgents多智能体验证")
        print("✅ AI智能体协作分析")
        print("✅ 结构化辩论系统")
        print("✅ 投资建议生成")
        print("=" * 60)
        print("🔄 服务器运行中... (按 Ctrl+C 停止)")
        
        # 自动打开浏览器
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{port}')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动服务器
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    start_server()
