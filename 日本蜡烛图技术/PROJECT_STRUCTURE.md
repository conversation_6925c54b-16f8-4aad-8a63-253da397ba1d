# 📁 项目结构说明

## 🏗️ 整体架构

```
AI-Investment-Analysis-Platform/
├── 📁 agents/                    # AI智能体系统
├── 📁 backend/                   # 后端API服务
├── 📁 frontend/                  # 前端React应用
├── 📁 llm_integration/           # LLM集成层
├── 📁 market_data/               # 市场数据模块
├── 📁 data/                      # 示例数据
├── 📁 docs/                      # 项目文档
├── 📄 demo.html                  # 演示页面
├── 📄 README.md                  # 项目说明
├── 📄 LICENSE                    # 开源许可证
└── 📄 .gitignore                 # Git忽略文件
```

## 🤖 智能体系统 (`agents/`)

```
agents/
├── 📁 base/                      # 基础智能体框架
│   ├── agent.py                  # 智能体基类
│   └── __init__.py
├── 📁 analysts/                  # 技术分析师
│   ├── candlestick_expert.py     # 蜡烛图专家
│   └── __init__.py
├── 📁 researchers/               # 研究员团队
│   ├── bullish_researcher.py     # 看涨研究员
│   ├── bearish_researcher.py     # 看跌研究员
│   └── __init__.py
├── 📁 traders/                   # 交易员
│   ├── professional_trader.py    # 专业交易员
│   └── __init__.py
├── 📁 risk_managers/             # 风险管理
│   ├── risk_manager.py           # 风险管理专家
│   └── __init__.py
├── 📁 debate/                    # 辩论系统
│   ├── debate_moderator.py       # 辩论主持人
│   └── __init__.py
└── 📁 communication/             # 通信协调
    ├── hub.py                    # 通信中心
    └── __init__.py
```

## 🔧 后端服务 (`backend/`)

```
backend/
├── 📁 src/                       # 源代码
│   └── 📁 api/                   # API接口
│       ├── main.py               # 主应用入口
│       └── 📁 routes/            # 路由模块
│           ├── patterns.py       # 形态识别路由
│           ├── health.py         # 健康检查路由
│           └── agents.py         # 智能体路由
├── 📄 requirements.txt           # Python依赖
└── 📄 run_server.py              # 服务器启动脚本
```

## 🎨 前端应用 (`frontend/`)

```
frontend/
├── 📁 src/                       # 源代码
│   ├── 📁 components/            # React组件
│   │   ├── CandlestickChart.js   # 蜡烛图组件
│   │   ├── PatternResults.js     # 结果展示组件
│   │   ├── DataUpload.js         # 数据上传组件
│   │   ├── AgentWorkspace.js     # 智能体工作台
│   │   └── AgentWorkspace.css    # 工作台样式
│   ├── 📁 services/              # API服务
│   ├── 📁 utils/                 # 工具函数
│   └── App.js                    # 主应用组件
├── 📁 public/                    # 静态资源
├── 📁 build/                     # 构建输出
├── 📄 package.json               # Node.js依赖
└── 📄 package-lock.json          # 依赖锁定文件
```

## 🧠 LLM集成层 (`llm_integration/`)

```
llm_integration/
├── 📄 llm_manager.py             # LLM管理器
├── 📁 providers/                 # LLM提供商
│   ├── openai_provider.py        # OpenAI提供商
│   ├── mock_provider.py          # 模拟提供商
│   └── __init__.py
├── 📁 prompts/                   # 提示词模板
│   ├── candlestick_prompts.py    # 蜡烛图分析提示词
│   └── __init__.py
└── 📄 __init__.py
```

## 📊 市场数据模块 (`market_data/`)

```
market_data/
├── 📄 yahoo_finance_provider.py  # Yahoo Finance数据提供商
└── 📄 __init__.py
```

## 📚 示例数据 (`data/`)

```
data/
├── 📁 samples/                   # 示例数据文件
│   └── sample_data.csv           # 示例蜡烛图数据
└── 📁 test_cases/                # 测试用例
    └── pattern_test_cases.json   # 形态测试用例
```

## 📖 项目文档 (`docs/`)

```
docs/
├── 📁 api/                       # API文档
│   ├── endpoints.md              # 端点说明
│   └── examples.md               # 使用示例
└── 📁 patterns/                  # 形态说明
    ├── reversal_patterns.md      # 反转形态
    ├── continuation_patterns.md  # 持续形态
    └── doji_patterns.md          # 十字星形态
```

## 🎯 核心文件说明

### 📄 主要演示文件

- **`demo.html`** - 完整的演示页面，可直接在浏览器中体验所有功能
- **`standalone_debate_demo.py`** - 智能体辩论系统演示
- **`test_advanced_agents.py`** - 高级智能体功能测试
- **`demo_advanced_agents.py`** - 完整系统演示

### 📄 配置文件

- **`.gitignore`** - Git版本控制忽略文件
- **`requirements.txt`** - Python依赖包列表
- **`package.json`** - Node.js依赖包列表

### 📄 文档文件

- **`README.md`** - 项目主要说明文档
- **`USAGE_GUIDE.md`** - 详细使用指南
- **`LICENSE`** - MIT开源许可证

## 🔄 数据流架构

```
📊 市场数据输入
    ↓
🔍 蜡烛图专家分析
    ↓ (技术分析报告)
🔬 研究团队分析
    ↓ (看涨/看跌观点)
🎭 启动辩论会议
    ↓ (多轮结构化辩论)
🎯 主持人引导总结
    ↓ (观点碰撞论证)
🤝 智能共识达成
    ↓ (决策收敛)
💼 交易员最终决策
    ↓ (交易信号)
🛡️ 风险管理审核
    ↓ (最终批准)
📈 执行投资建议
```

## 🚀 部署架构

### 开发环境
```
本地开发
├── 后端: localhost:8000
├── 前端: localhost:3000
└── 演示: demo.html
```

### 生产环境
```
云端部署
├── 后端API服务器
├── 前端静态资源CDN
├── 数据库服务
└── 负载均衡器
```

## 📝 代码规范

### Python代码
- 遵循PEP 8规范
- 使用类型提示
- 完整的文档字符串
- 异步编程模式

### JavaScript代码
- ES6+语法
- React Hooks模式
- 组件化设计
- 响应式布局

### 文件命名
- Python: snake_case
- JavaScript: camelCase
- 组件: PascalCase
- 常量: UPPER_CASE

## 🔧 开发工具

### 推荐IDE
- **Python**: PyCharm, VSCode
- **JavaScript**: VSCode, WebStorm
- **通用**: VSCode with extensions

### 调试工具
- **后端**: FastAPI自动文档 (/docs)
- **前端**: React Developer Tools
- **API**: Postman, curl

### 版本控制
- **Git**: 分支管理
- **GitHub**: 代码托管
- **Issues**: 问题跟踪

这个项目结构设计确保了代码的模块化、可维护性和可扩展性，为未来的功能扩展和团队协作提供了良好的基础。
