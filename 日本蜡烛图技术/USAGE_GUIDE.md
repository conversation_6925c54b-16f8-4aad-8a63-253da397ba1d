# 蜡烛图形态识别系统 - 使用指南

## 🚀 快速开始

### 1. 启动系统
```bash
# 启动后端服务
cd backend
python main.py

# 启动前端服务（新终端窗口）
cd frontend
npm start
```

### 2. 访问应用
打开浏览器访问: http://localhost:3000

## 📊 界面功能说明

### 数据上传区域
- **文件上传**: 支持CSV格式的蜡烛图数据
- **示例数据**: 点击"使用示例数据"快速体验
- **数据预览**: 上传后可预览数据表格

### 分析控制区域
- **开始分析**: 点击按钮开始形态识别
- **分析进度**: 显示分析状态和进度
- **参数设置**: 可调整置信度阈值等参数

### 结果展示区域
- **形态列表**: 显示识别到的所有形态
- **统计信息**: 看涨/看跌形态数量统计
- **置信度分布**: 形态识别的可靠性评估

### 图表可视化区域
- **蜡烛图**: 交互式K线图展示
- **形态标注**: 在图表上高亮显示识别的形态
- **图表控制**: 缩放、平移、切换图表类型

## 📁 数据格式要求

### CSV文件格式
```csv
timestamp,open,high,low,close,volume
2024-01-01T09:00:00Z,100.0,102.0,99.0,99.5,1000
2024-01-01T10:00:00Z,99.5,100.5,98.0,98.2,1200
2024-01-01T11:00:00Z,98.2,99.0,97.0,97.5,900
```

### 字段说明
- **timestamp**: 时间戳（ISO格式）
- **open**: 开盘价
- **high**: 最高价  
- **low**: 最低价
- **close**: 收盘价
- **volume**: 成交量

## 🎯 形态识别结果解读

### 形态信息
- **形态名称**: 识别到的具体形态类型
- **信号类型**: 看涨(bullish)/看跌(bearish)/中性(neutral)
- **置信度**: 0-1之间的数值，越高越可靠
- **时间范围**: 形态出现的K线位置

### 信号强度
- **高置信度 (>0.8)**: 强烈信号，可重点关注
- **中等置信度 (0.5-0.8)**: 一般信号，需结合其他分析
- **低置信度 (<0.5)**: 弱信号，谨慎参考

### 形态类型
- **反转形态**: 预示趋势可能改变
- **持续形态**: 预示趋势可能延续
- **不确定形态**: 市场方向不明确

## 🔧 高级功能

### API直接调用
```bash
# 分析蜡烛图数据
curl -X POST "http://localhost:8000/api/v1/analyze" \
  -H "Content-Type: application/json" \
  -d '{"candles": [...]}'

# 获取支持的形态列表
curl -X GET "http://localhost:8000/api/v1/patterns/list"
```

### 参数调优
- **最小置信度**: 过滤低可靠性的形态
- **形态类型过滤**: 只显示特定类型的形态
- **信号过滤**: 只显示看涨或看跌信号

## ⚠️ 注意事项

### 数据质量
- 确保数据完整性，避免缺失值
- 数据时间序列应连续
- 价格数据应合理（high≥max(open,close), low≤min(open,close)）

### 分析限制
- 至少需要3-5根K线才能进行有效分析
- 形态识别基于历史数据，不保证未来表现
- 建议结合其他技术分析方法使用

### 性能考虑
- 大量数据（>1000根K线）可能需要较长分析时间
- 建议分批处理超大数据集
- 浏览器内存限制可能影响大数据集的可视化

## 🐛 常见问题

### Q: 上传文件后没有反应？
A: 检查文件格式是否正确，确保包含必需的字段

### Q: 分析结果为空？
A: 可能数据中没有明显的形态，或置信度阈值设置过高

### Q: 图表显示异常？
A: 刷新页面重试，或检查数据中是否有异常值

### Q: API调用失败？
A: 确认后端服务正常运行，检查请求格式是否正确

## 📚 学习资源

### 推荐阅读
- 《日本蜡烛图技术》- Steve Nison
- 《蜡烛图方法》- Gregory L. Morris

### 在线资源
- [蜡烛图形态大全](https://example.com)
- [技术分析基础](https://example.com)

## 🤝 技术支持

如遇到问题或需要帮助，请：
1. 查看本使用指南
2. 检查README.md文档
3. 提交Issue到项目仓库
4. 联系开发团队
