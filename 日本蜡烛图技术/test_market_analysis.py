#!/usr/bin/env python3
"""
测试市场分析功能
"""

import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

from market_data.market_analyzer import market_analyzer
from market_data.yahoo_finance_provider import market_data_manager


async def test_market_data_manager():
    """测试市场数据管理器"""
    print("🔍 测试市场数据管理器...")
    
    try:
        # 测试获取股票数据
        print("\n📊 获取AAPL股票数据...")
        stock_data = await market_data_manager.get_stock_data("AAPL", "1h")
        print(f"✅ 成功获取数据，包含 {len(stock_data.get('candles', []))} 个数据点")
        print(f"   当前价格: ${stock_data.get('current_price', 0):.2f}")
        print(f"   数据源: {stock_data.get('data_source', 'unknown')}")
        
        # 测试获取股票信息
        print("\n📈 获取AAPL股票信息...")
        stock_info = await market_data_manager.get_stock_info("AAPL")
        print(f"✅ 成功获取股票信息")
        print(f"   公司名称: {stock_info.get('company_name', 'N/A')}")
        print(f"   市值: ${stock_info.get('market_cap', 0):,}")
        print(f"   PE比率: {stock_info.get('pe_ratio', 0):.2f}")
        
        # 测试监控列表
        print("\n📋 测试监控列表...")
        market_data_manager.add_to_watchlist("AAPL")
        market_data_manager.add_to_watchlist("TSLA")
        market_data_manager.add_to_watchlist("GOOGL")
        
        watchlist_data = await market_data_manager.get_watchlist_data()
        print(f"✅ 监控列表包含 {len(watchlist_data)} 只股票")
        
        # 测试缓存统计
        cache_stats = market_data_manager.get_cache_stats()
        print(f"\n💾 缓存统计:")
        print(f"   缓存大小: {cache_stats['cache_size']}")
        print(f"   监控列表: {cache_stats['watchlist_size']}")
        print(f"   活跃提醒: {cache_stats['active_alerts']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 市场数据管理器测试失败: {e}")
        return False


async def test_market_analyzer():
    """测试市场分析器"""
    print("\n🧠 测试市场分析器...")
    
    try:
        # 测试综合分析
        print("\n📊 执行AAPL综合分析...")
        analysis = await market_analyzer.comprehensive_analysis(
            symbol="AAPL",
            interval="1h",
            include_fundamentals=True
        )
        
        print(f"✅ 综合分析完成")
        print(f"   股票代码: {analysis['symbol']}")
        print(f"   当前价格: ${analysis['price_data']['current_price']:.2f}")
        print(f"   价格变化: {analysis['price_data']['change_percent']:.2f}%")
        
        # 技术分析结果
        technical = analysis.get('technical_analysis', {})
        if 'indicators' in technical:
            indicators = technical['indicators']
            print(f"\n📈 技术指标:")
            if indicators.get('rsi'):
                print(f"   RSI(14): {indicators['rsi']:.2f}")
            if indicators.get('sma_20'):
                print(f"   SMA(20): ${indicators['sma_20']:.2f}")
            if indicators.get('ema_12'):
                print(f"   EMA(12): ${indicators['ema_12']:.2f}")
        
        # 基本面分析结果
        fundamental = analysis.get('fundamental_analysis', {})
        if fundamental:
            print(f"\n💼 基本面分析:")
            valuation = fundamental.get('valuation', {})
            if valuation:
                print(f"   估值评级: {valuation.get('pe_evaluation', 'N/A')}")
            
            financial_health = fundamental.get('financial_health', {})
            if financial_health:
                print(f"   盈利能力: {financial_health.get('profitability', 'N/A')}")
                print(f"   风险水平: {financial_health.get('risk_level', 'N/A')}")
        
        # 情绪分析结果
        sentiment = analysis.get('sentiment_analysis', {})
        if sentiment:
            print(f"\n😊 市场情绪:")
            print(f"   情绪: {sentiment.get('sentiment', 'neutral')}")
            print(f"   置信度: {sentiment.get('confidence', 0):.2f}")
            print(f"   价格动量: {sentiment.get('price_momentum', 0)*100:.2f}%")
        
        # 综合评分
        overall_score = analysis.get('overall_score', {})
        if overall_score:
            print(f"\n⭐ 综合评分:")
            print(f"   技术分析: {overall_score.get('technical', 0):.2f}")
            print(f"   基本面: {overall_score.get('fundamental', 0):.2f}")
            print(f"   市场情绪: {overall_score.get('sentiment', 0):.2f}")
            print(f"   综合评分: {overall_score.get('overall', 0):.2f}")
        
        # 投资建议
        recommendation = analysis.get('recommendation', {})
        if recommendation:
            print(f"\n💡 投资建议:")
            print(f"   操作建议: {recommendation.get('action', 'HOLD')}")
            print(f"   置信度: {recommendation.get('confidence', 'LOW')}")
            print(f"   推理: {recommendation.get('reasoning', 'N/A')}")
        
        # 数据质量
        data_quality = analysis.get('data_quality', {})
        if data_quality:
            print(f"\n📊 数据质量:")
            print(f"   质量评分: {data_quality.get('score', 0):.2f}")
            print(f"   数据点数: {data_quality.get('data_points', 0)}")
            if data_quality.get('issues'):
                print(f"   问题: {', '.join(data_quality['issues'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 市场分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_multiple_symbols():
    """测试多股票分析"""
    print("\n🔄 测试多股票分析...")
    
    symbols = ["AAPL", "TSLA", "GOOGL"]
    
    try:
        # 获取多个股票数据
        multiple_data = await market_data_manager.get_multiple_stocks(symbols, "1h")
        
        print(f"✅ 成功获取 {len(multiple_data)} 只股票数据")
        
        for symbol, data in multiple_data.items():
            current_price = data.get('current_price', 0)
            change_percent = ((current_price - data.get('previous_close', current_price)) / data.get('previous_close', current_price)) * 100 if data.get('previous_close') else 0
            print(f"   {symbol}: ${current_price:.2f} ({change_percent:+.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 多股票分析测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试市场分析功能")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_market_data_manager(),
        test_market_analyzer(),
        test_multiple_symbols()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    # 统计结果
    passed = sum(1 for result in results if result is True)
    total = len(results)
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！市场分析功能正常工作")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        for i, result in enumerate(results):
            if result is not True:
                print(f"   测试 {i+1} 失败: {result}")
    
    print("\n💡 提示:")
    print("   - 当前使用模拟数据，配置真实API可获得完整功能")
    print("   - 可以通过 http://localhost:8000/docs 查看API文档")
    print("   - 前端界面可通过新的'市场分析'标签页访问")


if __name__ == "__main__":
    asyncio.run(main())
