#!/usr/bin/env python3
"""
独立的智能体辩论演示
不依赖外部库，展示辩论机制核心功能
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.researchers.bullish_researcher import BullishResearcher
from agents.researchers.bearish_researcher import BearishResearcher
from agents.debate.debate_moderator import DebateModerator


def create_sample_market_data():
    """创建示例市场数据"""
    return {
        'symbol': 'TSLA',
        'current_price': 248.50,
        'previous_close': 245.20,
        'change': 3.30,
        'change_percent': 1.35,
        'patterns': [
            {
                'name': 'MORNING_STAR',
                'chinese_name': '启明星',
                'signal': 'BULLISH',
                'confidence': 0.85,
                'description': '三根蜡烛形成的强烈底部反转信号'
            },
            {
                'name': 'RESISTANCE_LEVEL',
                'chinese_name': '阻力位测试',
                'signal': 'NEUTRAL',
                'confidence': 0.70,
                'description': '价格正在测试重要阻力位'
            }
        ],
        'technical_analysis': {
            'trend': 'UPTREND',
            'volume': 'INCREASING',
            'rsi': 65.5,
            'macd': 'BULLISH_CROSSOVER'
        },
        'market_context': {
            'sector': 'Electric Vehicles',
            'market_sentiment': 'POSITIVE',
            'recent_news': 'Strong Q4 delivery numbers'
        }
    }


async def run_intelligent_debate():
    """运行智能辩论演示"""
    print("🎭 智能体辩论系统演示")
    print("基于LLM的专业投资决策辩论")
    print("=" * 60)
    
    # 1. 创建智能体团队
    print("\n👥 组建专业辩论团队...")
    
    bullish_researcher = BullishResearcher(
        agent_id="bullish_researcher_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.2}
    )
    
    bearish_researcher = BearishResearcher(
        agent_id="bearish_researcher_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.2}
    )
    
    debate_moderator = DebateModerator(
        agent_id="debate_moderator_001",
        llm_config={'model': 'gpt-4', 'temperature': 0.3}
    )
    
    print(f"   ✅ {bullish_researcher.agent_id} - 🐂 看涨研究员")
    print(f"   ✅ {bearish_researcher.agent_id} - 🐻 看跌研究员")
    print(f"   ✅ {debate_moderator.agent_id} - 🎯 辩论主持人")
    
    # 2. 准备分析数据
    print("\n📊 准备市场分析数据...")
    market_data = create_sample_market_data()
    
    print(f"   🎯 分析标的: {market_data['symbol']}")
    print(f"   💰 当前价格: ${market_data['current_price']}")
    print(f"   📈 涨跌幅: +{market_data['change_percent']:.2f}%")
    print(f"   🔍 技术形态: {len(market_data['patterns'])}个")
    
    for pattern in market_data['patterns']:
        signal_emoji = "📈" if pattern['signal'] == 'BULLISH' else "📉" if pattern['signal'] == 'BEARISH' else "➡️"
        print(f"     {signal_emoji} {pattern['chinese_name']}: {pattern['signal']} (置信度: {pattern['confidence']:.2f})")
    
    # 3. 研究员独立分析
    print("\n🔬 研究员独立分析阶段...")
    
    analysis_input = {
        'symbol': market_data['symbol'],
        'patterns': market_data['patterns'],
        'technical_analysis': market_data['technical_analysis'],
        'market_context': market_data['market_context']
    }
    
    # 看涨研究员分析
    print("   🐂 看涨研究员分析中...")
    bullish_opinion = await bullish_researcher._perform_analysis(analysis_input)
    
    print(f"      观点: {bullish_opinion.get('opinion', 'BULLISH')}")
    print(f"      置信度: {bullish_opinion.get('confidence', 0.7):.2f}")
    print(f"      核心论点: {bullish_opinion.get('reasoning', '技术面显示积极信号')[:80]}...")
    
    key_factors = bullish_opinion.get('key_factors', [])
    if key_factors:
        print(f"      关键因素: {key_factors[0][:60]}...")
    
    # 看跌研究员分析
    print("   🐻 看跌研究员分析中...")
    bearish_opinion = await bearish_researcher._perform_analysis(analysis_input)
    
    print(f"      观点: {bearish_opinion.get('opinion', 'BEARISH')}")
    print(f"      置信度: {bearish_opinion.get('confidence', 0.6):.2f}")
    print(f"      核心论点: {bearish_opinion.get('reasoning', '需要关注风险因素')[:80]}...")
    
    risk_factors = bearish_opinion.get('risk_factors', [])
    if risk_factors:
        print(f"      风险因素: {risk_factors[0][:60]}...")
    
    # 4. 启动正式辩论
    print(f"\n🎯 启动正式辩论会议")
    
    debate_topic = f"{market_data['symbol']}投资决策：当前市场环境下是否应该买入？"
    participants = ['bullish_researcher_001', 'bearish_researcher_001']
    
    session_id = await debate_moderator.start_debate(
        topic=debate_topic,
        participants=participants,
        initial_data=analysis_input
    )
    
    print(f"   📋 辩论主题: {debate_topic}")
    print(f"   👥 参与者: 看涨研究员 vs 看跌研究员")
    print(f"   🆔 会话ID: {session_id}")
    
    # 5. 多轮结构化辩论
    print(f"\n🔄 开始多轮结构化辩论...")
    
    debate_rounds = [
        {
            'round_theme': '技术分析观点',
            'bullish': f"启明星形态显示强烈的底部反转信号，置信度{bullish_opinion.get('confidence', 0.8):.2f}，配合成交量放大，技术面非常积极。",
            'bearish': f"虽然有反转形态，但价格接近阻力位，突破失败风险较高，当前置信度仅{bearish_opinion.get('confidence', 0.6):.2f}。"
        },
        {
            'round_theme': '市场环境分析',
            'bullish': "电动车行业景气度高，特斯拉Q4交付数据强劲，市场情绪积极，基本面支持技术面突破。",
            'bearish': "但宏观经济不确定性增加，利率环境对成长股不利，估值已经较高，下行风险不容忽视。"
        },
        {
            'round_theme': '投资策略建议',
            'bullish': "综合风险收益比，当前价位提供了良好的中长期投资机会，建议分批建仓。",
            'bearish': "建议等待更明确的突破确认或价格回调到更安全区域，当前风险收益比不够吸引人。"
        }
    ]
    
    for round_num, round_data in enumerate(debate_rounds, 1):
        print(f"\n   🔄 第{round_num}轮: {round_data['round_theme']}")
        print(f"      🐂 看涨论点: {round_data['bullish']}")
        print(f"      🐻 看跌论点: {round_data['bearish']}")
        
        # 主持人分析和总结
        print(f"      🎯 主持人分析中...")
        moderation_result = await debate_moderator.moderate_round(
            session_id=session_id,
            round_number=round_num,
            bullish_argument=round_data['bullish'],
            bearish_argument=round_data['bearish']
        )
        
        # 显示主持人分析结果
        debate_summary = moderation_result.get('debate_summary', '双方观点已记录分析')
        print(f"      📝 辩论总结: {debate_summary[:100]}...")
        
        consensus_points = moderation_result.get('consensus_points', [])
        if consensus_points:
            print(f"      🤝 共识点: {consensus_points[0][:80]}...")
        
        disagreements = moderation_result.get('key_disagreements', [])
        if disagreements:
            print(f"      ⚡ 主要分歧: {disagreements[0][:80]}...")
        
        # 检查决策置信度
        recommendation = moderation_result.get('recommendation', {})
        confidence = recommendation.get('confidence_level', 0.5)
        print(f"      📊 当前决策置信度: {confidence:.2f}")
        
        if confidence > 0.8:
            print(f"      ✅ 达成高度共识，辩论可以结束")
            break
        elif round_num < len(debate_rounds):
            print(f"      🔄 需要继续深入讨论...")
        
        await asyncio.sleep(1)  # 模拟思考和分析时间
    
    # 6. 辩论总结和最终决策
    print(f"\n📋 辩论总结与最终决策")
    
    debate_status = debate_moderator.get_debate_status(session_id)
    if debate_status:
        print(f"   ⏱️ 辩论总时长: {debate_status['duration']:.1f}秒")
        print(f"   🔄 完成轮数: {debate_status['rounds_completed']}/{debate_status['max_rounds']}")
        print(f"   🤝 是否达成共识: {'是' if debate_status['consensus_reached'] else '否'}")
    
    # 最终投资建议
    final_recommendation = moderation_result.get('recommendation', {})
    print(f"\n🎯 最终投资决策:")
    print(f"   📊 建议策略: {final_recommendation.get('suggested_approach', '综合考虑双方观点')}")
    print(f"   📈 决策置信度: {final_recommendation.get('confidence_level', 0.6):.2f}")
    
    next_steps = final_recommendation.get('next_steps', ['继续监控市场变化'])
    print(f"   📝 后续行动: {', '.join(next_steps[:2])}")
    
    # 7. 决策质量评估
    print(f"\n📊 决策质量评估:")
    
    bullish_conf = bullish_opinion.get('confidence', 0.7)
    bearish_conf = bearish_opinion.get('confidence', 0.6)
    final_conf = final_recommendation.get('confidence_level', 0.6)
    
    print(f"   🐂 看涨观点强度: {bullish_conf:.2f}")
    print(f"   🐻 看跌观点强度: {bearish_conf:.2f}")
    print(f"   ⚖️ 观点分歧度: {abs(bullish_conf - bearish_conf):.2f}")
    print(f"   🎯 最终决策置信度: {final_conf:.2f}")
    
    if abs(bullish_conf - bearish_conf) < 0.2:
        print(f"   💡 分析: 双方观点接近，市场存在不确定性")
    elif bullish_conf > bearish_conf:
        print(f"   💡 分析: 看涨观点占优，但需关注风险")
    else:
        print(f"   💡 分析: 看跌观点占优，建议谨慎操作")
    
    return {
        'session_id': session_id,
        'symbol': market_data['symbol'],
        'bullish_confidence': bullish_conf,
        'bearish_confidence': bearish_conf,
        'final_confidence': final_conf,
        'recommendation': final_recommendation,
        'debate_status': debate_status
    }


async def main():
    """主演示函数"""
    print("🎭 智能体辩论系统 - 第四阶段演示")
    print("展示AI驱动的专业投资决策辩论机制")
    print("=" * 70)
    
    try:
        result = await run_intelligent_debate()
        
        if result:
            print("\n" + "=" * 70)
            print("🎉 智能体辩论演示圆满完成！")
            
            print("\n✨ 系统核心特色:")
            print("   🧠 LLM驱动智能分析 - GPT-4级别的专业解读")
            print("   🤖 多智能体协作 - 看涨vs看跌专业对抗")
            print("   🎯 结构化辩论流程 - 多轮深入论证")
            print("   📊 量化决策支持 - 置信度和风险评估")
            print("   🤝 智能共识达成 - 自动化决策收敛")
            print("   🔄 全程可追溯 - 完整的决策路径记录")
            
            print(f"\n📈 {result['symbol']}辩论成果:")
            print(f"   🐂 看涨观点强度: {result['bullish_confidence']:.2f}")
            print(f"   🐻 看跌观点强度: {result['bearish_confidence']:.2f}")
            print(f"   🎯 最终决策置信度: {result['final_confidence']:.2f}")
            print(f"   📊 建议策略: {result['recommendation'].get('suggested_approach', 'N/A')}")
            
            print(f"\n🏆 辩论系统优势:")
            print("   1. 🔍 多角度深度分析 - 避免单一视角盲点")
            print("   2. ⚡ 观点激烈碰撞 - 通过辩论发现盲区")
            print("   3. 🎯 结构化决策流程 - 确保分析完整性")
            print("   4. 📊 风险量化评估 - 数据驱动决策支持")
            print("   5. 🤝 智能共识机制 - 自动化最优解搜索")
        
        print("\n🚀 第四阶段已完成功能:")
        print("✅ 智能体辩论机制 - 看涨vs看跌实时对抗")
        print("✅ 专业主持人系统 - 结构化辩论引导")
        print("✅ 多轮论证流程 - 逐步深入核心问题")
        print("✅ 智能共识达成 - 自动化决策收敛")
        print("✅ 实时状态跟踪 - 辩论进度全程监控")
        
        print("\n💡 下一步发展方向:")
        print("🔥 立即可做:")
        print("  - 配置真实OpenAI API获得更智能的辩论效果")
        print("  - 集成实时市场数据源")
        print("  - 开发WebSocket实时辩论界面")
        
        print("📈 短期目标:")
        print("  - 添加更多辩论参与者（基本面分析师、量化策略师）")
        print("  - 实现辩论历史记录和回放功能")
        print("  - 开发辩论质量评估系统")
        
        print("🎯 中期目标:")
        print("  - 部署到生产环境，支持多用户")
        print("  - 开发移动端实时辩论应用")
        print("  - 集成新闻情绪和社交媒体数据")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🎭 启动第四阶段：智能体辩论系统")
    print("展示AI驱动的专业投资决策辩论")
    print()
    
    # 运行演示
    asyncio.run(main())
    
    print("\n🎉 第四阶段辩论演示完成!")
    print("\n🌟 这标志着投资决策的新时代:")
    print("- 🤖 AI智能体专业团队协作")
    print("- 🧠 多维度观点激烈碰撞")
    print("- 🎯 结构化透明决策流程")
    print("- 📊 量化风险和收益评估")
    print("- 🚀 超越人类单一视角的局限性")
