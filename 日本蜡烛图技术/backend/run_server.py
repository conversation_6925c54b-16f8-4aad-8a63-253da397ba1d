#!/usr/bin/env python3
"""
直接运行FastAPI服务器
"""

import uvicorn
import sys
import traceback

try:
    from src.api.main import app
    print("✅ 应用导入成功")
except Exception as e:
    print(f"❌ 应用导入失败: {e}")
    traceback.print_exc()
    sys.exit(1)

if __name__ == "__main__":
    print("🚀 启动多智能体交易系统API服务器...")
    print("📡 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔄 按 Ctrl+C 停止服务器")

    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        traceback.print_exc()
