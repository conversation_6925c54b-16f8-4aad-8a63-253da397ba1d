# 蜡烛图形态识别系统依赖

# 核心依赖
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0
plotly>=5.0.0

# Web框架
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0

# 数据处理
yfinance>=0.1.63
requests>=2.25.0

# 测试
pytest>=6.2.0
pytest-cov>=2.12.0

# 开发工具
black>=21.0.0
flake8>=3.9.0
mypy>=0.910

# 可选：机器学习（用于高级形态识别）
scikit-learn>=1.0.0

# 🆕 多智能体系统依赖
# LLM集成
openai>=1.3.0
anthropic>=0.7.0
langchain>=0.1.0
langchain-openai>=0.0.2
langchain-anthropic>=0.1.0

# 异步和通信
websockets>=12.0
redis>=5.0.1
aiofiles>=23.2.1

# 工作流和任务管理
celery>=5.3.0
kombu>=5.3.0

# 配置管理
python-dotenv>=1.0.0
pyyaml>=6.0

# TradingAgents专用依赖
aiohttp>=3.9.1
