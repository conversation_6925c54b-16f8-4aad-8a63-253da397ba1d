"""
Configuration Management
"""

import yaml
import os
from typing import Dict, Any, Optional
import logging


class Config:
    """Configuration manager for TradingAgents"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Initialize configuration"""
        self.config_path = config_path
        self.config_data = {}
        self.logger = logging.getLogger("Config")
        
        self._load_config()
        self._load_environment_variables()
    
    def _load_config(self):
        """Load configuration from YAML file"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as file:
                    self.config_data = yaml.safe_load(file) or {}
                self.logger.info(f"Configuration loaded from {self.config_path}")
            else:
                self.logger.warning(f"Config file {self.config_path} not found, using defaults")
                self.config_data = self._get_default_config()
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            self.config_data = self._get_default_config()
    
    def _load_environment_variables(self):
        """Load environment variables"""
        # Load from .env file if it exists
        env_file = ".env"
        if os.path.exists(env_file):
            try:
                with open(env_file, 'r') as file:
                    for line in file:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
            except Exception as e:
                self.logger.error(f"Error loading .env file: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'system': {
                'name': 'TradingAgents',
                'version': '1.0.0',
                'environment': 'development'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'agents': {
                'market_analyst': {'enabled': True},
                'bull_researcher': {'enabled': True},
                'bear_researcher': {'enabled': True},
                'portfolio_manager': {'enabled': True},
                'debate_moderator': {'enabled': True},
                'execution_agent': {'enabled': True}
            },
            'debate_system': {
                'enabled': True,
                'max_rounds': 5,
                'consensus_threshold': 0.7
            },
            'workflow': {
                'enabled': True,
                'max_concurrent_workflows': 10
            },
            'market_data': {
                'providers': {
                    'primary': 'mock_data'
                }
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            
            # Handle environment variable substitution
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                if ':' in env_var:
                    env_var, default_val = env_var.split(':', 1)
                    value = os.getenv(env_var, default_val)
                else:
                    value = os.getenv(env_var, default)
            
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """Set configuration value"""
        keys = key.split('.')
        config = self.config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None):
        """Save configuration to file"""
        save_path = path or self.config_path
        try:
            with open(save_path, 'w') as file:
                yaml.dump(self.config_data, file, default_flow_style=False)
            self.logger.info(f"Configuration saved to {save_path}")
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def reload(self):
        """Reload configuration"""
        self._load_config()
        self._load_environment_variables()
        self.logger.info("Configuration reloaded")
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration data"""
        return self.config_data.copy()
