# TradingAgents System Configuration

# System Settings
system:
  name: "TradingAgents"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true
  timezone: "UTC"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/trading_agents.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# AI/LLM Configuration
llm:
  provider: "deepseek"  # deepseek, openai, anthropic, local
  model: "deepseek-v3"
  temperature: 0.3
  max_tokens: 2000
  timeout: 30
  retry_attempts: 3

  # DeepSeek specific settings (SiliconFlow)
  deepseek:
    api_key: "${DEEPSEEK_API_KEY}"
    api_url: "${DEEPSEEK_API_URL}"
    model: "${DEEPSEEK_MODEL}"

  # OpenAI specific settings
  openai:
    api_key: "${OPENAI_API_KEY}"
    organization: "${OPENAI_ORG_ID}"

  # Anthropic specific settings
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"

# Agent Configuration
agents:
  # Market Analyst Agent
  market_analyst:
    enabled: true
    name: "MarketAnalyst"
    model: "deepseek-v3"
    temperature: 0.2
    max_tokens: 1500
    specialization: "technical_analysis"
    capabilities:
      - "chart_pattern_recognition"
      - "technical_indicators"
      - "market_sentiment"
      - "economic_analysis"
    
  # Bull Researcher Agent
  bull_researcher:
    enabled: true
    name: "BullResearcher"
    model: "deepseek-v3"
    temperature: 0.4
    max_tokens: 1200
    specialization: "bullish_analysis"
    capabilities:
      - "growth_analysis"
      - "catalyst_identification"
      - "momentum_analysis"
      - "opportunity_assessment"
    
  # Bear Researcher Agent
  bear_researcher:
    enabled: true
    name: "BearResearcher"
    model: "gpt-4"
    temperature: 0.4
    max_tokens: 1200
    specialization: "bearish_analysis"
    capabilities:
      - "risk_assessment"
      - "downside_analysis"
      - "market_correction"
      - "threat_identification"
    
  # Portfolio Manager Agent
  portfolio_manager:
    enabled: true
    name: "PortfolioManager"
    model: "gpt-4"
    temperature: 0.1
    max_tokens: 1000
    specialization: "portfolio_management"
    capabilities:
      - "position_sizing"
      - "risk_management"
      - "diversification"
      - "allocation_optimization"
    
  # Debate Moderator Agent
  debate_moderator:
    enabled: true
    name: "DebateModerator"
    model: "gpt-4"
    temperature: 0.2
    max_tokens: 800
    specialization: "debate_facilitation"
    capabilities:
      - "consensus_building"
      - "argument_evaluation"
      - "decision_synthesis"
      - "conflict_resolution"
    
  # Execution Agent
  execution_agent:
    enabled: true
    name: "ExecutionAgent"
    model: "gpt-3.5-turbo"
    temperature: 0.1
    max_tokens: 500
    specialization: "trade_execution"
    capabilities:
      - "order_management"
      - "execution_timing"
      - "slippage_minimization"
      - "market_impact"

  # Candlestick Analyst Agent
  candlestick_analyst:
    enabled: true
    name: "CandlestickAnalyst"
    model: "deepseek-ai/DeepSeek-V3"
    temperature: 0.2
    max_tokens: 2000
    specialization: "candlestick_analysis"
    capabilities:
      - "pattern_recognition"
      - "pattern_validation"
      - "japanese_candlestick_theory"
      - "market_psychology_analysis"

# Debate System Configuration
debate_system:
  enabled: true
  max_rounds: 5
  round_timeout: 300  # seconds
  consensus_threshold: 0.7
  min_participants: 2
  max_participants: 6
  
  # Debate rules
  rules:
    - "Each agent must present evidence-based arguments"
    - "Arguments must be relevant to the topic"
    - "Agents should acknowledge valid opposing points"
    - "Final consensus requires majority agreement"
  
  # Scoring system
  scoring:
    argument_quality: 0.4
    evidence_strength: 0.3
    logical_consistency: 0.2
    persuasiveness: 0.1

# Workflow Engine Configuration
workflow:
  enabled: true
  max_concurrent_workflows: 10
  workflow_timeout: 1800  # seconds
  retry_attempts: 3
  
  # Available workflows
  workflows:
    trading_decision:
      steps:
        - "market_data_collection"
        - "agent_analysis"
        - "debate_session"
        - "risk_assessment"
        - "decision_synthesis"
        - "execution_planning"
      
    market_analysis:
      steps:
        - "data_gathering"
        - "technical_analysis"
        - "fundamental_analysis"
        - "sentiment_analysis"
        - "report_generation"
    
    portfolio_review:
      steps:
        - "portfolio_assessment"
        - "performance_analysis"
        - "risk_evaluation"
        - "rebalancing_recommendations"

# Market Data Configuration
market_data:
  providers:
    primary: "yahoo_finance"
    secondary: "alpha_vantage"
    
  yahoo_finance:
    enabled: true
    timeout: 10
    retry_attempts: 3
    
  alpha_vantage:
    enabled: true
    api_key: "${ALPHA_VANTAGE_API_KEY}"
    timeout: 15
    retry_attempts: 3
    
  # Data refresh intervals (seconds)
  refresh_intervals:
    real_time: 60
    intraday: 300
    daily: 3600
    
  # Caching settings
  cache:
    enabled: true
    ttl: 300  # seconds
    max_size: 1000

# Risk Management Configuration
risk_management:
  enabled: true
  
  # Position sizing
  position_sizing:
    max_portfolio_risk: 0.15
    max_single_position: 0.05
    min_position_size: 0.001
    
  # Stop loss settings
  stop_loss:
    enabled: true
    default_percentage: 0.05
    trailing_stop: true
    
  # Risk metrics
  metrics:
    var_confidence: 0.95
    max_drawdown_threshold: 0.20
    sharpe_ratio_target: 1.5

# Trading Configuration
trading:
  enabled: false  # Set to true for live trading
  mode: "simulation"  # simulation, paper, live
  
  # Broker settings
  broker:
    name: "interactive_brokers"  # interactive_brokers, alpaca, etc.
    api_key: "${BROKER_API_KEY}"
    secret_key: "${BROKER_SECRET_KEY}"
    sandbox: true
    
  # Order settings
  orders:
    default_order_type: "market"
    timeout: 60
    max_slippage: 0.01

# Database Configuration
database:
  enabled: true
  type: "postgresql"  # postgresql, sqlite, mysql
  
  postgresql:
    host: "${DB_HOST:localhost}"
    port: "${DB_PORT:5432}"
    database: "${DB_NAME:trading_agents}"
    username: "${DB_USER:postgres}"
    password: "${DB_PASSWORD}"
    
  # Connection pool settings
  pool:
    min_size: 5
    max_size: 20
    timeout: 30

# Cache Configuration
cache:
  enabled: true
  type: "redis"  # redis, memory, disk
  
  redis:
    host: "${REDIS_HOST:localhost}"
    port: "${REDIS_PORT:6379}"
    database: 0
    password: "${REDIS_PASSWORD}"
    
  # Cache settings
  settings:
    default_ttl: 300
    max_memory: "100MB"

# API Configuration
api:
  enabled: true
  host: "0.0.0.0"
  port: 8000
  debug: true
  
  # CORS settings
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE"]
    allow_headers: ["*"]
    
  # Rate limiting
  rate_limit:
    enabled: true
    requests_per_minute: 100

# Monitoring Configuration
monitoring:
  enabled: true
  
  # Metrics
  metrics:
    enabled: true
    port: 9090
    
  # Health checks
  health_checks:
    enabled: true
    interval: 30
    
  # Alerting
  alerts:
    enabled: true
    channels:
      - "email"
      - "slack"

# Security Configuration
security:
  # API authentication
  api_auth:
    enabled: false
    type: "jwt"  # jwt, api_key
    
  # Encryption
  encryption:
    enabled: true
    algorithm: "AES-256"
    
  # Rate limiting
  rate_limiting:
    enabled: true
    requests_per_minute: 100
