"""
智能体相关API路由
提供多智能体分析、工作流管理等功能
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncio
import sys
import os

# 添加智能体系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../'))

from agents.communication.hub import CommunicationHub
from agents.base.agent import Message, MessageType, AgentRole
from llm_integration.llm_manager import llm_manager

router = APIRouter()

# 全局通信中心实例
communication_hub: Optional[CommunicationHub] = None


class CandleData(BaseModel):
    """蜡烛数据模型"""
    open: float
    high: float
    low: float
    close: float
    volume: int
    timestamp: str


class MultiAgentAnalysisRequest(BaseModel):
    """多智能体分析请求"""
    symbol: str
    timeframe: str = "1H"
    candles: List[CandleData]
    analysis_type: str = "comprehensive"  # comprehensive, quick, detailed


class WorkflowRequest(BaseModel):
    """工作流请求"""
    workflow_type: str
    input_data: Dict[str, Any]
    priority: int = 1


class AgentStatusResponse(BaseModel):
    """智能体状态响应"""
    agent_id: str
    role: str
    is_active: bool
    analysis_count: int = 0


class MultiAgentAnalysisResponse(BaseModel):
    """多智能体分析响应"""
    analysis_id: str
    symbol: str
    timestamp: str
    patterns: List[Dict[str, Any]]
    agent_opinions: List[Dict[str, Any]]
    consensus: Dict[str, Any]
    confidence: float
    workflow_id: Optional[str] = None


async def get_communication_hub() -> CommunicationHub:
    """获取或创建通信中心"""
    global communication_hub
    
    if communication_hub is None:
        communication_hub = CommunicationHub()
        await communication_hub.start()
        
        # 注册一些基础智能体（简化版）
        from demo_complete_system import SimpleAgent
        
        agents = [
            SimpleAgent("technical_analyst_001", AgentRole.TECHNICAL_ANALYST),
            SimpleAgent("bullish_researcher_001", AgentRole.BULLISH_RESEARCHER, "bullish"),
            SimpleAgent("bearish_researcher_001", AgentRole.BEARISH_RESEARCHER, "bearish"),
            SimpleAgent("trader_001", AgentRole.TRADER),
            SimpleAgent("risk_manager_001", AgentRole.RISK_MANAGER)
        ]
        
        for agent in agents:
            communication_hub.register_agent(agent)
            agent.communication_hub = communication_hub
    
    return communication_hub


@router.get("/status", response_model=Dict[str, Any])
async def get_agents_status():
    """获取所有智能体状态"""
    try:
        hub = await get_communication_hub()
        
        # 获取智能体状态
        agent_statuses = hub.get_agent_status()
        
        # 获取通信统计
        comm_stats = hub.get_communication_stats()
        
        return {
            "agents": agent_statuses,
            "communication": comm_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取智能体状态失败: {str(e)}")


@router.post("/analyze", response_model=MultiAgentAnalysisResponse)
async def multi_agent_analyze(
    request: MultiAgentAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """启动多智能体分析"""
    try:
        hub = await get_communication_hub()
        
        # 生成分析ID
        analysis_id = f"analysis_{datetime.now().timestamp()}"
        
        # 模拟形态识别（这里应该调用现有的形态识别系统）
        patterns = await simulate_pattern_recognition(request.candles)
        
        # 创建分析报告消息
        analysis_message = Message(
            message_type=MessageType.ANALYSIS_REPORT,
            from_agent="pattern_analyzer",
            to_agents=["technical_analyst_001", "bullish_researcher_001", "bearish_researcher_001"],
            content={
                'symbol': request.symbol,
                'timeframe': request.timeframe,
                'patterns': patterns,
                'candles': [candle.dict() for candle in request.candles],
                'analysis_id': analysis_id
            },
            timestamp=datetime.now(),
            message_id=analysis_id
        )
        
        # 发送分析报告
        await hub.send_message(analysis_message)
        
        # 启动工作流（如果是comprehensive分析）
        workflow_id = None
        if request.analysis_type == "comprehensive":
            workflow_id = await hub.start_workflow(
                workflow_type='market_analysis',
                input_data={
                    'symbol': request.symbol,
                    'timeframe': request.timeframe,
                    'analysis_id': analysis_id,
                    'patterns': patterns
                }
            )
        
        # 等待初步分析结果
        await asyncio.sleep(1)
        
        # 收集智能体观点（简化版）
        agent_opinions = await collect_agent_opinions(hub, analysis_id)
        
        # 生成共识
        consensus = generate_consensus(patterns, agent_opinions)
        
        return MultiAgentAnalysisResponse(
            analysis_id=analysis_id,
            symbol=request.symbol,
            timestamp=datetime.now().isoformat(),
            patterns=patterns,
            agent_opinions=agent_opinions,
            consensus=consensus,
            confidence=consensus.get('confidence', 0.6),
            workflow_id=workflow_id
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"多智能体分析失败: {str(e)}")


@router.post("/workflow/start")
async def start_workflow(request: WorkflowRequest):
    """启动工作流"""
    try:
        hub = await get_communication_hub()
        
        workflow_id = await hub.start_workflow(
            workflow_type=request.workflow_type,
            input_data=request.input_data
        )
        
        return {
            "workflow_id": workflow_id,
            "status": "started",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动工作流失败: {str(e)}")


@router.get("/workflow/{workflow_id}/status")
async def get_workflow_status(workflow_id: str):
    """获取工作流状态"""
    try:
        hub = await get_communication_hub()
        
        status = hub.get_workflow_status(workflow_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="工作流不存在")
        
        return status
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流状态失败: {str(e)}")


@router.get("/llm/stats")
async def get_llm_stats():
    """获取LLM使用统计"""
    try:
        stats = llm_manager.get_provider_stats()
        providers = llm_manager.get_available_providers()
        default_provider = llm_manager.default_provider
        
        return {
            "providers": providers,
            "default_provider": default_provider,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取LLM统计失败: {str(e)}")


async def simulate_pattern_recognition(candles: List[CandleData]) -> List[Dict[str, Any]]:
    """模拟形态识别（临时实现）"""
    # 这里应该调用现有的形态识别系统
    # 暂时返回模拟数据
    
    patterns = []
    
    if len(candles) >= 3:
        # 检查是否有启明星形态的特征
        last_three = candles[-3:]
        if (last_three[0].close < last_three[0].open and  # 第一根是阴线
            abs(last_three[1].close - last_three[1].open) < (last_three[1].high - last_three[1].low) * 0.3 and  # 第二根是小实体
            last_three[2].close > last_three[2].open):  # 第三根是阳线
            
            patterns.append({
                'name': 'MORNING_STAR',
                'chinese_name': '启明星',
                'signal': 'BULLISH',
                'confidence': 0.85,
                'start_index': len(candles) - 3,
                'end_index': len(candles) - 1,
                'description': '三根蜡烛组成的底部反转形态'
            })
    
    # 检查趋势
    if len(candles) >= 5:
        recent_closes = [c.close for c in candles[-5:]]
        if all(recent_closes[i] > recent_closes[i+1] for i in range(len(recent_closes)-1)):
            patterns.append({
                'name': 'DOWNTREND',
                'chinese_name': '下降趋势',
                'signal': 'BEARISH',
                'confidence': 0.75,
                'start_index': len(candles) - 5,
                'end_index': len(candles) - 1,
                'description': '价格持续下降趋势'
            })
    
    return patterns


async def collect_agent_opinions(hub: CommunicationHub, analysis_id: str) -> List[Dict[str, Any]]:
    """收集智能体观点"""
    # 简化实现，实际应该从消息历史中收集
    opinions = [
        {
            'agent_id': 'technical_analyst_001',
            'role': 'technical_analyst',
            'opinion': '技术面显示潜在反转信号',
            'confidence': 0.7,
            'timestamp': datetime.now().isoformat()
        },
        {
            'agent_id': 'bullish_researcher_001',
            'role': 'bullish_researcher',
            'opinion': '发现看涨形态，建议关注买入机会',
            'confidence': 0.8,
            'timestamp': datetime.now().isoformat()
        },
        {
            'agent_id': 'bearish_researcher_001',
            'role': 'bearish_researcher',
            'opinion': '仍需关注下行风险，建议谨慎',
            'confidence': 0.6,
            'timestamp': datetime.now().isoformat()
        }
    ]
    
    return opinions


def generate_consensus(patterns: List[Dict[str, Any]], opinions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """生成共识"""
    bullish_signals = len([p for p in patterns if p.get('signal') == 'BULLISH'])
    bearish_signals = len([p for p in patterns if p.get('signal') == 'BEARISH'])
    
    avg_confidence = sum(op.get('confidence', 0) for op in opinions) / len(opinions) if opinions else 0.5
    
    if bullish_signals > bearish_signals:
        outlook = 'BULLISH'
        recommendation = 'BUY'
    elif bearish_signals > bullish_signals:
        outlook = 'BEARISH'
        recommendation = 'SELL'
    else:
        outlook = 'NEUTRAL'
        recommendation = 'HOLD'
    
    return {
        'outlook': outlook,
        'recommendation': recommendation,
        'confidence': avg_confidence,
        'bullish_signals': bullish_signals,
        'bearish_signals': bearish_signals,
        'summary': f'基于{len(patterns)}个形态和{len(opinions)}个智能体观点的综合分析'
    }
