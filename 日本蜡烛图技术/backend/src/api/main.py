"""
蜡烛图形态识别系统 FastAPI 主应用
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from .routes import patterns, health, agents, trading_agents

# 创建FastAPI应用
app = FastAPI(
    title="蜡烛图形态识别系统 - 多智能体版",
    description="基于《日本蜡烛图技术》+ TradingAgents理论的智能交易分析系统",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(patterns.router, prefix="/api/v1", tags=["形态识别"])
app.include_router(agents.router, prefix="/api/v1/agents", tags=["智能体系统"])
app.include_router(trading_agents.router, tags=["TradingAgents多智能体系统"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "蜡烛图形态识别系统 - 多智能体版 API",
        "version": "2.0.0",
        "features": [
            "传统蜡烛图形态识别",
            "TradingAgents多智能体验证",
            "AI智能体协作分析",
            "结构化辩论系统",
            "LLM智能解读",
            "批量形态验证",
            "投资建议生成"
        ],
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "message": str(exc),
            "type": type(exc).__name__
        }
    )
