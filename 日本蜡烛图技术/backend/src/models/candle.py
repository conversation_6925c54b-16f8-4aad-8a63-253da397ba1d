"""
蜡烛图数据模型
基于《日本蜡烛图技术》一书的核心概念实现
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
from enum import Enum


class CandleColor(Enum):
    """蜡烛线颜色"""
    WHITE = "white"  # 阳线（白色）- 收盘价 > 开盘价
    BLACK = "black"  # 阴线（黑色）- 收盘价 < 开盘价
    DOJI = "doji"    # 十字线 - 收盘价 ≈ 开盘价


class CandleType(Enum):
    """蜡烛线类型"""
    NORMAL = "normal"           # 普通蜡烛线
    HAMMER = "hammer"           # 锤子线
    HANGING_MAN = "hanging_man" # 上吊线
    DOJI = "doji"              # 十字线
    LONG_LEGGED_DOJI = "long_legged_doji"  # 长腿十字线
    GRAVESTONE_DOJI = "gravestone_doji"    # 墓碑十字线
    DRAGONFLY_DOJI = "dragonfly_doji"      # 蜻蜓十字线
    SPINNING_TOP = "spinning_top"          # 纺锤线
    MARUBOZU = "marubozu"                  # 光头光脚线


@dataclass
class Candle:
    """
    蜡烛图数据结构
    
    根据书中描述，每根蜡烛线需要四个价格：
    - 开市价（最初价）
    - 最高价
    - 最低价  
    - 收市价（最后价）
    """
    open: float                    # 开盘价
    high: float                    # 最高价
    low: float                     # 最低价
    close: float                   # 收盘价
    volume: float                  # 成交量
    timestamp: datetime            # 时间戳
    
    # 计算属性
    _body_size: Optional[float] = None
    _upper_shadow: Optional[float] = None
    _lower_shadow: Optional[float] = None
    _color: Optional[CandleColor] = None
    _type: Optional[CandleType] = None

    def __post_init__(self):
        """数据验证"""
        if self.high < max(self.open, self.close):
            raise ValueError("最高价不能低于开盘价和收盘价的最大值")
        if self.low > min(self.open, self.close):
            raise ValueError("最低价不能高于开盘价和收盘价的最小值")
        if self.volume < 0:
            raise ValueError("成交量不能为负数")

    @property
    def body_size(self) -> float:
        """实体大小（绝对值）"""
        if self._body_size is None:
            self._body_size = abs(self.close - self.open)
        return self._body_size

    @property
    def body_size_percent(self) -> float:
        """实体大小占整个价格区间的百分比"""
        total_range = self.high - self.low
        if total_range == 0:
            return 0
        return self.body_size / total_range

    @property
    def upper_shadow(self) -> float:
        """上影线长度"""
        if self._upper_shadow is None:
            self._upper_shadow = self.high - max(self.open, self.close)
        return self._upper_shadow

    @property
    def lower_shadow(self) -> float:
        """下影线长度"""
        if self._lower_shadow is None:
            self._lower_shadow = min(self.open, self.close) - self.low
        return self._lower_shadow

    @property
    def total_range(self) -> float:
        """总价格区间"""
        return self.high - self.low

    @property
    def range(self) -> float:
        """价格区间（与total_range相同）"""
        return self.total_range

    @property
    def color(self) -> CandleColor:
        """蜡烛线颜色"""
        if self._color is None:
            if abs(self.close - self.open) < 0.001:  # 考虑浮点数精度
                self._color = CandleColor.DOJI
            elif self.close > self.open:
                self._color = CandleColor.WHITE
            else:
                self._color = CandleColor.BLACK
        return self._color

    @property
    def is_bullish(self) -> bool:
        """是否为看涨蜡烛线（阳线）"""
        return self.color == CandleColor.WHITE

    @property
    def is_bearish(self) -> bool:
        """是否为看跌蜡烛线（阴线）"""
        return self.color == CandleColor.BLACK

    @property
    def is_doji(self) -> bool:
        """是否为十字线"""
        return self.color == CandleColor.DOJI

    @property
    def has_upper_shadow(self) -> bool:
        """是否有上影线"""
        return self.upper_shadow > 0

    @property
    def has_lower_shadow(self) -> bool:
        """是否有下影线"""
        return self.lower_shadow > 0

    @property
    def is_marubozu(self) -> bool:
        """是否为光头光脚线（没有影线）"""
        return not self.has_upper_shadow and not self.has_lower_shadow

    def is_small_body(self, threshold: float = 0.3) -> bool:
        """
        是否为小实体
        threshold: 实体占总区间的比例阈值
        """
        return self.body_size_percent < threshold

    def is_long_upper_shadow(self, threshold: float = 2.0) -> bool:
        """
        是否有长上影线
        threshold: 上影线与实体的比例阈值
        """
        if self.body_size == 0:
            return self.upper_shadow > 0
        return self.upper_shadow / self.body_size > threshold

    def is_long_lower_shadow(self, threshold: float = 2.0) -> bool:
        """
        是否有长下影线
        threshold: 下影线与实体的比例阈值
        """
        if self.body_size == 0:
            return self.lower_shadow > 0
        return self.lower_shadow / self.body_size > threshold

    def get_body_midpoint(self) -> float:
        """获取实体中点价格"""
        return (self.open + self.close) / 2

    def get_total_midpoint(self) -> float:
        """获取总区间中点价格"""
        return (self.high + self.low) / 2

    def __str__(self) -> str:
        return (f"Candle(O:{self.open:.2f}, H:{self.high:.2f}, "
                f"L:{self.low:.2f}, C:{self.close:.2f}, "
                f"Color:{self.color.value})")

    def __repr__(self) -> str:
        return self.__str__()


@dataclass
class CandleSequence:
    """蜡烛线序列，用于形态识别"""
    candles: List[Candle]
    
    def __post_init__(self):
        if not self.candles:
            raise ValueError("蜡烛线序列不能为空")
        
        # 按时间排序
        self.candles.sort(key=lambda c: c.timestamp)
    
    def __len__(self) -> int:
        return len(self.candles)
    
    def __getitem__(self, index: int) -> Candle:
        return self.candles[index]
    
    def __iter__(self):
        return iter(self.candles)
    
    @property
    def first(self) -> Candle:
        """第一根蜡烛线"""
        return self.candles[0]
    
    @property
    def last(self) -> Candle:
        """最后一根蜡烛线"""
        return self.candles[-1]
    
    def get_highest_high(self) -> float:
        """获取序列中的最高价"""
        return max(candle.high for candle in self.candles)
    
    def get_lowest_low(self) -> float:
        """获取序列中的最低价"""
        return min(candle.low for candle in self.candles)
    
    def get_price_range(self) -> float:
        """获取价格区间"""
        return self.get_highest_high() - self.get_lowest_low()
    
    def is_uptrend(self) -> bool:
        """判断是否为上升趋势（简单判断：最后收盘价 > 第一开盘价）"""
        return self.last.close > self.first.open
    
    def is_downtrend(self) -> bool:
        """判断是否为下降趋势"""
        return self.last.close < self.first.open
