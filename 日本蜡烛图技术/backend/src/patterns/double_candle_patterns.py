"""
双根蜡烛线形态识别器
基于《日本蜡烛图技术》一书第四章、第六章的内容实现
"""

from typing import List, Optional
from datetime import datetime
from ..models import (
    Candle, PatternResult, PatternName, PatternType, 
    PatternSignal, PatternCategory
)
from ..utils import PatternUtils


class DoubleCandlePatternRecognizer:
    """双根蜡烛线形态识别器"""
    
    def __init__(self):
        self.pattern_utils = PatternUtils()
    
    def identify_patterns(self, candles: List[Candle], index: int) -> List[PatternResult]:
        """
        识别指定位置结束的双根蜡烛线形态
        
        Args:
            candles: 蜡烛线列表
            index: 第二根蜡烛线的索引
            
        Returns:
            List[PatternResult]: 识别到的形态列表
        """
        if index < 1 or index >= len(candles):
            return []
        
        candle1 = candles[index - 1]  # 第一根蜡烛线
        candle2 = candles[index]      # 第二根蜡烛线
        patterns = []
        
        # 获取趋势背景
        trend_context = "unknown"
        if index >= 5:
            trend_context = self.pattern_utils.get_trend_direction(candles[:index])
        
        # 识别各种双根蜡烛线形态
        patterns.extend(self._identify_engulfing_patterns(candle1, candle2, index, trend_context))
        patterns.extend(self._identify_piercing_dark_cloud(candle1, candle2, index, trend_context))
        patterns.extend(self._identify_harami_patterns(candle1, candle2, index, trend_context))
        patterns.extend(self._identify_tweezers_patterns(candle1, candle2, index, trend_context))
        patterns.extend(self._identify_kicking_patterns(candle1, candle2, index, trend_context))
        patterns.extend(self._identify_meeting_lines(candle1, candle2, index, trend_context))
        
        return patterns

    def _identify_engulfing_patterns(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别吞没形态"""
        patterns = []

        # 吞没形态的基本条件：
        # 1. 第二根蜡烛线的实体完全包含第一根蜡烛线的实体
        # 2. 两根蜡烛线颜色相反

        if candle1.color == candle2.color:
            return patterns

        # 检查是否为吞没
        c1_body_top = max(candle1.open, candle1.close)
        c1_body_bottom = min(candle1.open, candle1.close)
        c2_body_top = max(candle2.open, candle2.close)
        c2_body_bottom = min(candle2.open, candle2.close)

        if not (c2_body_top > c1_body_top and c2_body_bottom < c1_body_bottom):
            return patterns

        # 判断是看涨还是看跌吞没
        if candle1.is_bearish and candle2.is_bullish:
            # 看涨吞没
            pattern_name = PatternName.ENGULFING_BULLISH
            signal = PatternSignal.BULLISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.75
            description = "看涨吞没形态，阳线完全吞没前一根阴线"

            # 在下降趋势中更可靠
            if trend_context == "downtrend":
                confidence += 0.1
                description += "，在下降趋势中出现，反转信号强烈"

        else:
            # 看跌吞没
            pattern_name = PatternName.ENGULFING_BEARISH
            signal = PatternSignal.BEARISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.75
            description = "看跌吞没形态，阴线完全吞没前一根阳线"

            # 在上升趋势中更可靠
            if trend_context == "uptrend":
                confidence += 0.1
                description += "，在上升趋势中出现，反转信号强烈"

        # 如果第二根蜡烛线实体明显更大，置信度更高
        if candle2.body_size > candle1.body_size * 1.5:
            confidence += 0.05
            description += "，第二根蜡烛线实体明显更大"

        pattern = PatternResult(
            pattern_name=pattern_name,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.DOUBLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index - 1,
            end_index=index,
            start_time=candle1.timestamp,
            end_time=candle2.timestamp,
            description=description,
            trend_context=trend_context,
            key_levels={
                "support": min(candle1.low, candle2.low),
                "resistance": max(candle1.high, candle2.high)
            }
        )

        patterns.append(pattern)
        return patterns

    def _identify_piercing_dark_cloud(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别刺透形态和乌云盖顶"""
        patterns = []

        # 两根蜡烛线颜色必须相反
        if candle1.color == candle2.color:
            return patterns

        if candle1.is_bearish and candle2.is_bullish:
            # 可能是刺透形态
            # 条件：第二根阳线开盘低于第一根阴线最低价，收盘在第一根阴线实体中部以上但低于开盘价
            if candle2.open <= candle1.low:
                c1_midpoint = (candle1.open + candle1.close) / 2
                if candle2.close > c1_midpoint and candle2.close < candle1.open:
                    pattern_name = PatternName.PIERCING_PATTERN
                    signal = PatternSignal.BULLISH
                    pattern_type = PatternType.REVERSAL
                    confidence = 0.7
                    description = "刺透形态，阳线深入阴线实体"

                    # 在下降趋势中更可靠
                    if trend_context == "downtrend":
                        confidence += 0.1
                        description += "，在下降趋势中出现"

                    # 如果刺透程度更深，置信度更高
                    penetration = (candle2.close - candle1.close) / (candle1.open - candle1.close)
                    if penetration > 0.6:
                        confidence += 0.05
                        description += "，刺透程度较深"

                    pattern = PatternResult(
                        pattern_name=pattern_name,
                        pattern_type=pattern_type,
                        pattern_category=PatternCategory.DOUBLE,
                        signal=signal,
                        confidence=min(confidence, 1.0),
                        start_index=index - 1,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle2.timestamp,
                        description=description,
                        trend_context=trend_context
                    )
                    patterns.append(pattern)

        elif candle1.is_bullish and candle2.is_bearish:
            # 可能是乌云盖顶
            # 条件：第二根阴线开盘高于第一根阳线最高价，收盘在第一根阳线实体中部以下
            if candle2.open > candle1.high:
                c1_midpoint = (candle1.open + candle1.close) / 2
                if candle2.close < c1_midpoint and candle2.close > candle1.open:
                    pattern_name = PatternName.DARK_CLOUD_COVER
                    signal = PatternSignal.BEARISH
                    pattern_type = PatternType.REVERSAL
                    confidence = 0.7
                    description = "乌云盖顶，阴线深入阳线实体"

                    # 在上升趋势中更可靠
                    if trend_context == "uptrend":
                        confidence += 0.1
                        description += "，在上升趋势中出现"

                    # 如果覆盖程度更深，置信度更高
                    penetration = (candle1.close - candle2.close) / (candle1.close - candle1.open)
                    if penetration > 0.6:
                        confidence += 0.05
                        description += "，覆盖程度较深"

                    pattern = PatternResult(
                        pattern_name=pattern_name,
                        pattern_type=pattern_type,
                        pattern_category=PatternCategory.DOUBLE,
                        signal=signal,
                        confidence=min(confidence, 1.0),
                        start_index=index - 1,
                        end_index=index,
                        start_time=candle1.timestamp,
                        end_time=candle2.timestamp,
                        description=description,
                        trend_context=trend_context
                    )
                    patterns.append(pattern)

        return patterns

    def _identify_harami_patterns(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别孕线形态"""
        patterns = []

        # 孕线形态的基本条件：
        # 1. 第一根蜡烛线的实体完全包含第二根蜡烛线的实体
        # 2. 两根蜡烛线颜色相反（普通孕线）或第二根是十字线（十字孕线）

        # 检查包含关系
        c1_body_top = max(candle1.open, candle1.close)
        c1_body_bottom = min(candle1.open, candle1.close)
        c2_body_top = max(candle2.open, candle2.close)
        c2_body_bottom = min(candle2.open, candle2.close)

        if not (c1_body_top > c2_body_top and c1_body_bottom < c2_body_bottom):
            return patterns

        # 第一根蜡烛线应该有较大的实体
        if candle1.is_small_body(0.4):
            return patterns

        if candle2.is_doji:
            # 十字孕线
            pattern_name = PatternName.HARAMI_CROSS
            pattern_type = PatternType.REVERSAL
            confidence = 0.8  # 十字孕线比普通孕线更可靠
            description = "十字孕线，大实体后出现十字线"

            if candle1.is_bullish:
                signal = PatternSignal.BEARISH
                description += "，看跌反转信号"
            else:
                signal = PatternSignal.BULLISH
                description += "，看涨反转信号"

        elif candle1.color != candle2.color:
            # 普通孕线
            if candle1.is_bullish and candle2.is_bearish:
                pattern_name = PatternName.HARAMI_BEARISH
                signal = PatternSignal.BEARISH
                description = "看跌孕线，大阳线后出现小阴线"
            else:
                pattern_name = PatternName.HARAMI_BULLISH
                signal = PatternSignal.BULLISH
                description = "看涨孕线，大阴线后出现小阳线"

            pattern_type = PatternType.REVERSAL
            confidence = 0.7
        else:
            # 同色孕线，信号较弱
            return patterns

        # 在相应趋势中更可靠
        if trend_context == "uptrend" and signal == PatternSignal.BEARISH:
            confidence += 0.1
            description += "，在上升趋势中出现"
        elif trend_context == "downtrend" and signal == PatternSignal.BULLISH:
            confidence += 0.1
            description += "，在下降趋势中出现"

        # 如果第二根蜡烛线特别小，置信度更高
        if candle2.body_size < candle1.body_size * 0.3:
            confidence += 0.05
            description += "，第二根蜡烛线实体很小"

        pattern = PatternResult(
            pattern_name=pattern_name,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.DOUBLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index - 1,
            end_index=index,
            start_time=candle1.timestamp,
            end_time=candle2.timestamp,
            description=description,
            trend_context=trend_context,
            key_levels={
                "support": min(candle1.low, candle2.low),
                "resistance": max(candle1.high, candle2.high)
            }
        )

        patterns.append(pattern)
        return patterns

    def _identify_tweezers_patterns(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别平头顶部/底部形态"""
        patterns = []

        # 平头形态的基本条件：
        # 1. 两根蜡烛线的高点或低点几乎相同
        # 2. 颜色相反更可靠

        price_tolerance = (candle1.high + candle2.high) * 0.002  # 0.2%的价格容差

        # 检查平头顶部
        if abs(candle1.high - candle2.high) <= price_tolerance:
            # 两根蜡烛线高点相近
            if candle1.color != candle2.color:  # 颜色相反更可靠
                pattern_name = PatternName.TWEEZERS_TOP
                signal = PatternSignal.BEARISH
                pattern_type = PatternType.REVERSAL
                confidence = 0.65
                description = "平头顶部，两根蜡烛线高点相同"

                # 在上升趋势中更可靠
                if trend_context == "uptrend":
                    confidence += 0.1
                    description += "，在上升趋势中出现，看跌反转信号"

                # 如果第二根是阴线，更可靠
                if candle2.is_bearish:
                    confidence += 0.05
                    description += "，第二根为阴线"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.DOUBLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 1,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle2.timestamp,
                    description=description,
                    trend_context=trend_context,
                    key_levels={"resistance": max(candle1.high, candle2.high)}
                )
                patterns.append(pattern)

        # 检查平头底部
        if abs(candle1.low - candle2.low) <= price_tolerance:
            # 两根蜡烛线低点相近
            if candle1.color != candle2.color:  # 颜色相反更可靠
                pattern_name = PatternName.TWEEZERS_BOTTOM
                signal = PatternSignal.BULLISH
                pattern_type = PatternType.REVERSAL
                confidence = 0.65
                description = "平头底部，两根蜡烛线低点相同"

                # 在下降趋势中更可靠
                if trend_context == "downtrend":
                    confidence += 0.1
                    description += "，在下降趋势中出现，看涨反转信号"

                # 如果第二根是阳线，更可靠
                if candle2.is_bullish:
                    confidence += 0.05
                    description += "，第二根为阳线"

                pattern = PatternResult(
                    pattern_name=pattern_name,
                    pattern_type=pattern_type,
                    pattern_category=PatternCategory.DOUBLE,
                    signal=signal,
                    confidence=min(confidence, 1.0),
                    start_index=index - 1,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle2.timestamp,
                    description=description,
                    trend_context=trend_context,
                    key_levels={"support": min(candle1.low, candle2.low)}
                )
                patterns.append(pattern)

        return patterns
    
    def _identify_engulfing_patterns(self, candle1: Candle, candle2: Candle, 
                                   index: int, trend_context: str) -> List[PatternResult]:
        """识别吞没形态"""
        patterns = []
        
        if not self.pattern_utils.is_engulfing(candle1, candle2):
            return patterns
        
        # 判断是看涨还是看跌吞没
        if candle1.is_bearish and candle2.is_bullish:
            # 看涨吞没形态
            confidence = 0.8
            if trend_context == "downtrend":
                confidence = 0.9  # 在下降趋势中更可靠
            
            patterns.append(PatternResult(
                pattern_name=PatternName.ENGULFING_BULLISH,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BULLISH,
                confidence=confidence,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description="看涨吞没形态：第二根阳线完全包含第一根阴线，表示买盘力量强劲",
                trend_context=trend_context
            ))
            
        elif candle1.is_bullish and candle2.is_bearish:
            # 看跌吞没形态
            confidence = 0.8
            if trend_context == "uptrend":
                confidence = 0.9  # 在上升趋势中更可靠
            
            patterns.append(PatternResult(
                pattern_name=PatternName.ENGULFING_BEARISH,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BEARISH,
                confidence=confidence,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description="看跌吞没形态：第二根阴线完全包含第一根阳线，表示卖压强劲",
                trend_context=trend_context
            ))
        
        return patterns
    
    def _identify_piercing_dark_cloud(self, candle1: Candle, candle2: Candle, 
                                    index: int, trend_context: str) -> List[PatternResult]:
        """识别刺透形态和乌云盖顶形态"""
        patterns = []
        
        # 计算渗透比例
        penetration_ratio = self.pattern_utils.calculate_penetration_ratio(candle1, candle2)
        
        # 刺透形态：第一根为阴线，第二根为阳线，渗透比例在50%以上
        if (candle1.is_bearish and candle2.is_bullish and 
            candle2.open < candle1.close and candle2.close > candle1.get_body_midpoint() and
            penetration_ratio >= 0.5):
            
            confidence = 0.7 + min(0.2, (penetration_ratio - 0.5) * 0.4)  # 渗透越深置信度越高
            if trend_context == "downtrend":
                confidence += 0.1
            
            patterns.append(PatternResult(
                pattern_name=PatternName.PIERCING_PATTERN,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BULLISH,
                confidence=confidence,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description=f"刺透形态：阳线深入阴线实体{penetration_ratio:.1%}，显示买盘力量增强",
                trend_context=trend_context,
                key_levels={"penetration_ratio": penetration_ratio}
            ))
        
        # 乌云盖顶形态：第一根为阳线，第二根为阴线，渗透比例在50%以上
        elif (candle1.is_bullish and candle2.is_bearish and 
              candle2.open > candle1.close and candle2.close < candle1.get_body_midpoint() and
              penetration_ratio >= 0.5):
            
            confidence = 0.7 + min(0.2, (penetration_ratio - 0.5) * 0.4)
            if trend_context == "uptrend":
                confidence += 0.1
            
            patterns.append(PatternResult(
                pattern_name=PatternName.DARK_CLOUD_COVER,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BEARISH,
                confidence=confidence,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description=f"乌云盖顶形态：阴线深入阳线实体{penetration_ratio:.1%}，显示卖压增强",
                trend_context=trend_context,
                key_levels={"penetration_ratio": penetration_ratio}
            ))
        
        return patterns
    
    def _identify_harami_patterns(self, candle1: Candle, candle2: Candle, 
                                index: int, trend_context: str) -> List[PatternResult]:
        """识别孕线形态"""
        patterns = []
        
        if not self.pattern_utils.is_harami(candle1, candle2):
            return patterns
        
        # 十字孕线：第二根蜡烛线是十字线
        if candle2.is_doji:
            signal = PatternSignal.BEARISH if trend_context == "uptrend" else PatternSignal.BULLISH
            confidence = 0.8
            
            patterns.append(PatternResult(
                pattern_name=PatternName.HARAMI_CROSS,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=signal,
                confidence=confidence,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description="十字孕线：大实体后出现十字线，表示市场犹豫，可能反转",
                trend_context=trend_context
            ))
        
        # 普通孕线形态
        else:
            # 根据第一根蜡烛线的颜色和趋势判断信号
            if candle1.is_bullish and trend_context == "uptrend":
                # 上升趋势中的看跌孕线
                patterns.append(PatternResult(
                    pattern_name=PatternName.HARAMI_BEARISH,
                    pattern_type=PatternType.REVERSAL,
                    pattern_category=PatternCategory.DOUBLE,
                    signal=PatternSignal.BEARISH,
                    confidence=0.7,
                    start_index=index - 1,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle2.timestamp,
                    description="看跌孕线：大阳线后出现小实体，显示上涨动能减弱",
                    trend_context=trend_context
                ))
            
            elif candle1.is_bearish and trend_context == "downtrend":
                # 下降趋势中的看涨孕线
                patterns.append(PatternResult(
                    pattern_name=PatternName.HARAMI_BULLISH,
                    pattern_type=PatternType.REVERSAL,
                    pattern_category=PatternCategory.DOUBLE,
                    signal=PatternSignal.BULLISH,
                    confidence=0.7,
                    start_index=index - 1,
                    end_index=index,
                    start_time=candle1.timestamp,
                    end_time=candle2.timestamp,
                    description="看涨孕线：大阴线后出现小实体，显示下跌动能减弱",
                    trend_context=trend_context
                ))
        
        return patterns
    
    def _identify_tweezers_patterns(self, candle1: Candle, candle2: Candle, 
                                  index: int, trend_context: str) -> List[PatternResult]:
        """识别平头形态（镊子形态）"""
        patterns = []
        
        # 平头顶部：两根蜡烛线有相似的最高价
        if (self.pattern_utils.is_similar_highs(candle1, candle2) and 
            trend_context == "uptrend"):
            
            patterns.append(PatternResult(
                pattern_name=PatternName.TWEEZERS_TOP,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BEARISH,
                confidence=0.7,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description="平头顶部：两根蜡烛线最高价相近，显示上方阻力强劲",
                trend_context=trend_context,
                key_levels={"resistance_level": max(candle1.high, candle2.high)}
            ))
        
        # 平头底部：两根蜡烛线有相似的最低价
        elif (self.pattern_utils.is_similar_lows(candle1, candle2) and 
              trend_context == "downtrend"):
            
            patterns.append(PatternResult(
                pattern_name=PatternName.TWEEZERS_BOTTOM,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.DOUBLE,
                signal=PatternSignal.BULLISH,
                confidence=0.7,
                start_index=index - 1,
                end_index=index,
                start_time=candle1.timestamp,
                end_time=candle2.timestamp,
                description="平头底部：两根蜡烛线最低价相近，显示下方支撑强劲",
                trend_context=trend_context,
                key_levels={"support_level": min(candle1.low, candle2.low)}
            ))
        
        return patterns

    def _identify_kicking_patterns(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别踢腿形态（跳空缺口形态）"""
        patterns = []

        # 踢腿形态特征：
        # 1. 两根蜡烛线都是光头光脚线或接近光头光脚
        # 2. 两根蜡烛线之间有明显跳空
        # 3. 颜色相反

        if not (candle1.is_marubozu or self._is_near_marubozu(candle1)):
            return patterns

        if not (candle2.is_marubozu or self._is_near_marubozu(candle2)):
            return patterns

        if candle1.color == candle2.color:
            return patterns

        # 检查跳空
        gap_exists = False
        if candle1.is_bullish and candle2.is_bearish:
            # 看涨踢腿：第二根阴线开盘低于第一根阳线收盘
            gap_exists = candle2.open < candle1.close
        elif candle1.is_bearish and candle2.is_bullish:
            # 看跌踢腿：第二根阳线开盘高于第一根阴线收盘
            gap_exists = candle2.open > candle1.close

        if not gap_exists:
            return patterns

        confidence = 0.8
        if candle1.is_bearish and candle2.is_bullish:
            signal = PatternSignal.BULLISH
            pattern_type = PatternType.REVERSAL
            description = "看涨踢腿形态，强烈的底部反转信号"
        else:
            signal = PatternSignal.BEARISH
            pattern_type = PatternType.REVERSAL
            description = "看跌踢腿形态，强烈的顶部反转信号"

        patterns.append(PatternResult(
            pattern_name=PatternName.KICKING_PATTERN,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.DOUBLE,
            signal=signal,
            confidence=confidence,
            start_index=index - 1,
            end_index=index,
            start_time=candle1.timestamp,
            end_time=candle2.timestamp,
            description=description,
            trend_context=trend_context
        ))

        return patterns

    def _identify_meeting_lines(self, candle1: Candle, candle2: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别会合线形态"""
        patterns = []

        # 会合线特征：
        # 1. 两根蜡烛线颜色相反
        # 2. 收盘价相同或非常接近
        # 3. 在趋势中出现

        if candle1.color == candle2.color:
            return patterns

        # 检查收盘价是否接近
        price_tolerance = abs(candle1.close + candle2.close) * 0.001  # 0.1%容差
        if abs(candle1.close - candle2.close) > price_tolerance:
            return patterns

        confidence = 0.65
        pattern_type = PatternType.REVERSAL

        if trend_context == "uptrend" and candle1.is_bullish and candle2.is_bearish:
            signal = PatternSignal.BEARISH
            description = "看跌会合线，在上升趋势中的反转信号"
        elif trend_context == "downtrend" and candle1.is_bearish and candle2.is_bullish:
            signal = PatternSignal.BULLISH
            description = "看涨会合线，在下降趋势中的反转信号"
        else:
            return patterns  # 只在明确趋势中识别

        patterns.append(PatternResult(
            pattern_name=PatternName.MEETING_LINES,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.DOUBLE,
            signal=signal,
            confidence=confidence,
            start_index=index - 1,
            end_index=index,
            start_time=candle1.timestamp,
            end_time=candle2.timestamp,
            description=description,
            trend_context=trend_context,
            key_levels={"meeting_level": candle1.close}
        ))

        return patterns

    def _is_near_marubozu(self, candle: Candle) -> bool:
        """判断是否接近光头光脚线"""
        shadow_threshold = candle.body_size * 0.1  # 影线不超过实体的10%
        return (candle.upper_shadow <= shadow_threshold and
                candle.lower_shadow <= shadow_threshold)
