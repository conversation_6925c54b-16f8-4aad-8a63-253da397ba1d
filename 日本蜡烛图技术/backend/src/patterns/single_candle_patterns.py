"""
单根蜡烛线形态识别器
基于《日本蜡烛图技术》一书第四章、第八章的内容实现
"""

from typing import List, Optional
from datetime import datetime
from ..models import (
    Candle, PatternResult, PatternName, PatternType, 
    PatternSignal, PatternCategory
)
from ..utils import PatternUtils


class SingleCandlePatternRecognizer:
    """单根蜡烛线形态识别器"""
    
    def __init__(self):
        self.pattern_utils = PatternUtils()
    
    def identify_patterns(self, candles: List[Candle], index: int) -> List[PatternResult]:
        """
        识别指定位置的单根蜡烛线形态
        
        Args:
            candles: 蜡烛线列表
            index: 要分析的蜡烛线索引
            
        Returns:
            List[PatternResult]: 识别到的形态列表
        """
        if index < 0 or index >= len(candles):
            return []
        
        candle = candles[index]
        patterns = []
        
        # 获取趋势背景（需要至少5根蜡烛线的历史数据）
        trend_context = "unknown"
        if index >= 5:
            trend_context = self.pattern_utils.get_trend_direction(candles[:index+1])
        
        # 计算平均实体大小和价格区间（用于判断相对大小）
        avg_body_size = self.pattern_utils.calculate_average_body_size(candles[:index+1])
        avg_range = self.pattern_utils.calculate_average_range(candles[:index+1])
        
        # 识别各种单根蜡烛线形态
        patterns.extend(self._identify_doji_patterns(candle, index, trend_context))
        patterns.extend(self._identify_hammer_hanging_man(candle, index, trend_context))
        patterns.extend(self._identify_spinning_top(candle, index, avg_body_size))
        patterns.extend(self._identify_marubozu(candle, index))
        patterns.extend(self._identify_shooting_star_inverted_hammer(candle, index, trend_context))
        patterns.extend(self._identify_long_candles(candle, index, avg_body_size, trend_context))
        patterns.extend(self._identify_high_wave_candles(candle, index, avg_range))

        return patterns

    def _identify_doji_patterns(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别十字线形态"""
        patterns = []

        if not candle.is_doji:
            return patterns

        # 基础十字线
        confidence = 0.6
        signal = PatternSignal.NEUTRAL
        description = "十字线表示市场犹豫不决"

        # 根据影线长度判断具体类型
        if candle.is_long_upper_shadow(1.5) and candle.lower_shadow < candle.body_size * 0.1:
            # 墓碑十字线
            pattern_name = PatternName.GRAVESTONE_DOJI
            confidence = 0.75
            signal = PatternSignal.BEARISH if trend_context == "uptrend" else PatternSignal.NEUTRAL
            description = "墓碑十字线，长上影线表示上方抛压沉重"

        elif candle.is_long_lower_shadow(1.5) and candle.upper_shadow < candle.body_size * 0.1:
            # 蜻蜓十字线
            pattern_name = PatternName.DRAGONFLY_DOJI
            confidence = 0.75
            signal = PatternSignal.BULLISH if trend_context == "downtrend" else PatternSignal.NEUTRAL
            description = "蜻蜓十字线，长下影线表示下方支撑强劲"

        elif candle.is_long_upper_shadow(1.0) and candle.is_long_lower_shadow(1.0):
            # 长腿十字线
            pattern_name = PatternName.LONG_LEGGED_DOJI
            confidence = 0.7
            description = "长腿十字线，上下影线都很长，表示激烈争夺"

        else:
            # 普通十字线
            pattern_name = PatternName.DOJI
            description = "普通十字线，表示买卖双方力量均衡"

        # 在趋势中的十字线更有意义
        if trend_context in ["uptrend", "downtrend"]:
            confidence += 0.1
            if trend_context == "uptrend":
                signal = PatternSignal.BEARISH
                description += "，在上升趋势中可能预示反转"
            else:
                signal = PatternSignal.BULLISH
                description += "，在下降趋势中可能预示反转"

        pattern = PatternResult(
            pattern_name=pattern_name,
            pattern_type=PatternType.INDECISION,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description,
            trend_context=trend_context
        )

        patterns.append(pattern)
        return patterns

    def _identify_hammer_hanging_man(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别锤子线和上吊线形态"""
        patterns = []

        # 锤子线/上吊线的基本条件：
        # 1. 小实体
        # 2. 长下影线（至少是实体的2倍）
        # 3. 很短或没有上影线

        if not candle.is_small_body(0.3):
            return patterns

        if not candle.is_long_lower_shadow(2.0):
            return patterns

        # 上影线应该很短
        if candle.upper_shadow > candle.body_size * 0.5:
            return patterns

        # 根据趋势背景判断是锤子线还是上吊线
        if trend_context == "downtrend":
            # 在下降趋势中是锤子线（看涨反转）
            pattern_name = PatternName.HAMMER
            signal = PatternSignal.BULLISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.75
            description = "锤子线，在下降趋势中出现，预示可能的看涨反转"

        elif trend_context == "uptrend":
            # 在上升趋势中是上吊线（看跌反转）
            pattern_name = PatternName.HANGING_MAN
            signal = PatternSignal.BEARISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.7  # 上吊线需要后续确认，置信度稍低
            description = "上吊线，在上升趋势中出现，预示可能的看跌反转，需要后续确认"

        else:
            # 趋势不明确时，按锤子线处理但置信度较低
            pattern_name = PatternName.HAMMER
            signal = PatternSignal.NEUTRAL
            pattern_type = PatternType.INDECISION
            confidence = 0.5
            description = "锤子线形态，但趋势背景不明确"

        # 如果是阳线，置信度稍高
        if candle.is_bullish and pattern_name == PatternName.HAMMER:
            confidence += 0.05
            description += "，阳线锤子更可靠"
        elif candle.is_bearish and pattern_name == PatternName.HANGING_MAN:
            confidence += 0.05
            description += "，阴线上吊更可靠"

        pattern = PatternResult(
            pattern_name=pattern_name,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description,
            trend_context=trend_context,
            key_levels={
                "support": candle.low,
                "resistance": candle.high
            }
        )

        patterns.append(pattern)
        return patterns

    def _identify_spinning_top(self, candle: Candle, index: int, avg_body_size: float) -> List[PatternResult]:
        """识别纺锤线形态"""
        patterns = []

        # 纺锤线的特征：
        # 1. 小实体
        # 2. 上下都有影线
        # 3. 影线长度相对较长

        if not candle.is_small_body(0.25):
            return patterns

        if not (candle.has_upper_shadow and candle.has_lower_shadow):
            return patterns

        # 影线应该比实体长
        if candle.upper_shadow < candle.body_size or candle.lower_shadow < candle.body_size:
            return patterns

        confidence = 0.6
        signal = PatternSignal.NEUTRAL
        description = "纺锤线，小实体配上下影线，表示市场犹豫不决"

        # 如果实体特别小，置信度更高
        if candle.body_size < avg_body_size * 0.3:
            confidence += 0.1
            description += "，实体极小"

        # 如果上下影线都很长，置信度更高
        if candle.is_long_upper_shadow(1.5) and candle.is_long_lower_shadow(1.5):
            confidence += 0.1
            description += "，上下影线都很长"

        pattern = PatternResult(
            pattern_name=PatternName.SPINNING_TOP,
            pattern_type=PatternType.INDECISION,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description
        )

        patterns.append(pattern)
        return patterns

    def _identify_marubozu(self, candle: Candle, index: int) -> List[PatternResult]:
        """识别光头光脚线形态"""
        patterns = []

        # 光头光脚线：没有上下影线或影线极短
        if not candle.is_marubozu:
            # 检查是否是接近光头光脚的情况
            shadow_threshold = candle.body_size * 0.05  # 影线不超过实体的5%
            if candle.upper_shadow > shadow_threshold or candle.lower_shadow > shadow_threshold:
                return patterns

        confidence = 0.7
        pattern_type = PatternType.CONTINUATION

        if candle.is_bullish:
            signal = PatternSignal.BULLISH
            description = "阳线光头光脚，强烈看涨信号"
        else:
            signal = PatternSignal.BEARISH
            description = "阴线光头光脚，强烈看跌信号"

        # 如果实体较大，置信度更高
        if candle.body_size_percent > 0.8:
            confidence += 0.1
            description += "，实体占据大部分价格区间"

        pattern = PatternResult(
            pattern_name=PatternName.MARUBOZU,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description
        )

        patterns.append(pattern)
        return patterns

    def _identify_shooting_star_inverted_hammer(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别流星和倒锤子形态"""
        patterns = []

        # 流星/倒锤子的基本条件：
        # 1. 小实体
        # 2. 长上影线（至少是实体的2倍）
        # 3. 很短或没有下影线

        if not candle.is_small_body(0.3):
            return patterns

        if not candle.is_long_upper_shadow(2.0):
            return patterns

        # 下影线应该很短
        if candle.lower_shadow > candle.body_size * 0.5:
            return patterns

        # 根据趋势背景判断是流星还是倒锤子
        if trend_context == "uptrend":
            # 在上升趋势中是流星（看跌反转）
            pattern_name = PatternName.SHOOTING_STAR
            signal = PatternSignal.BEARISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.75
            description = "流星，在上升趋势中出现，预示可能的看跌反转"

        elif trend_context == "downtrend":
            # 在下降趋势中是倒锤子（看涨反转）
            pattern_name = PatternName.INVERTED_HAMMER
            signal = PatternSignal.BULLISH
            pattern_type = PatternType.REVERSAL
            confidence = 0.7  # 倒锤子需要后续确认，置信度稍低
            description = "倒锤子，在下降趋势中出现，预示可能的看涨反转，需要后续确认"

        else:
            # 趋势不明确时，按流星处理但置信度较低
            pattern_name = PatternName.SHOOTING_STAR
            signal = PatternSignal.NEUTRAL
            pattern_type = PatternType.INDECISION
            confidence = 0.5
            description = "流星形态，但趋势背景不明确"

        # 如果是阴线流星，置信度稍高
        if candle.is_bearish and pattern_name == PatternName.SHOOTING_STAR:
            confidence += 0.05
            description += "，阴线流星更可靠"
        elif candle.is_bullish and pattern_name == PatternName.INVERTED_HAMMER:
            confidence += 0.05
            description += "，阳线倒锤更可靠"

        pattern = PatternResult(
            pattern_name=pattern_name,
            pattern_type=pattern_type,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description,
            trend_context=trend_context,
            key_levels={
                "support": candle.low,
                "resistance": candle.high
            }
        )

        patterns.append(pattern)
        return patterns
    
    def _identify_doji_patterns(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别十字线形态"""
        patterns = []
        
        if not candle.is_doji:
            return patterns
        
        # 基础十字线
        confidence = 0.6
        signal = PatternSignal.NEUTRAL
        description = "十字线表示市场犹豫不决，买卖双方力量平衡"
        
        # 根据趋势背景调整信号和置信度
        if trend_context == "uptrend":
            signal = PatternSignal.BEARISH
            confidence = 0.7
            description += "，在上升趋势中可能预示反转"
        elif trend_context == "downtrend":
            signal = PatternSignal.BULLISH
            confidence = 0.7
            description += "，在下降趋势中可能预示反转"
        
        # 判断特殊类型的十字线
        pattern_name = PatternName.DOJI
        
        # 长腿十字线：上下影线都很长
        if candle.is_long_upper_shadow() and candle.is_long_lower_shadow():
            pattern_name = PatternName.LONG_LEGGED_DOJI
            confidence += 0.1
            description = "长腿十字线显示市场极度不确定性"
        
        # 墓碑十字线：只有长上影线，无下影线
        elif candle.is_long_upper_shadow() and not candle.has_lower_shadow:
            pattern_name = PatternName.GRAVESTONE_DOJI
            signal = PatternSignal.BEARISH
            confidence = 0.8
            description = "墓碑十字线是强烈的看跌信号"
        
        # 蜻蜓十字线：只有长下影线，无上影线
        elif candle.is_long_lower_shadow() and not candle.has_upper_shadow:
            pattern_name = PatternName.DRAGONFLY_DOJI
            signal = PatternSignal.BULLISH
            confidence = 0.8
            description = "蜻蜓十字线是强烈的看涨信号"
        
        patterns.append(PatternResult(
            pattern_name=pattern_name,
            pattern_type=PatternType.REVERSAL,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=confidence,
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description,
            trend_context=trend_context
        ))
        
        return patterns
    
    def _identify_hammer_hanging_man(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别锤子线和上吊线"""
        patterns = []
        
        # 锤子线/上吊线的特征：
        # 1. 小实体
        # 2. 长下影线（至少是实体的2倍）
        # 3. 无上影线或很短的上影线
        
        if not candle.is_small_body():
            return patterns
        
        if not candle.is_long_lower_shadow():
            return patterns
        
        # 上影线应该很短或没有
        if candle.has_upper_shadow and candle.upper_shadow > candle.body_size:
            return patterns
        
        # 根据趋势背景判断是锤子线还是上吊线
        if trend_context == "downtrend":
            # 在下降趋势中是锤子线（看涨反转）
            patterns.append(PatternResult(
                pattern_name=PatternName.HAMMER,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.SINGLE,
                signal=PatternSignal.BULLISH,
                confidence=0.75,
                start_index=index,
                end_index=index,
                start_time=candle.timestamp,
                end_time=candle.timestamp,
                description="锤子线在下降趋势中是看涨反转信号，表示卖压减弱",
                trend_context=trend_context
            ))
        elif trend_context == "uptrend":
            # 在上升趋势中是上吊线（看跌反转）
            patterns.append(PatternResult(
                pattern_name=PatternName.HANGING_MAN,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.SINGLE,
                signal=PatternSignal.BEARISH,
                confidence=0.75,
                start_index=index,
                end_index=index,
                start_time=candle.timestamp,
                end_time=candle.timestamp,
                description="上吊线在上升趋势中是看跌反转信号，表示买盘力量减弱",
                trend_context=trend_context
            ))
        
        return patterns
    
    def _identify_spinning_top(self, candle: Candle, index: int, avg_body_size: float) -> List[PatternResult]:
        """识别纺锤线"""
        patterns = []
        
        # 纺锤线特征：
        # 1. 很小的实体
        # 2. 上下都有影线（长短不重要）
        
        if not candle.is_small_body(threshold=0.2):  # 更严格的小实体标准
            return patterns
        
        if not (candle.has_upper_shadow and candle.has_lower_shadow):
            return patterns
        
        patterns.append(PatternResult(
            pattern_name=PatternName.SPINNING_TOP,
            pattern_type=PatternType.INDECISION,
            pattern_category=PatternCategory.SINGLE,
            signal=PatternSignal.NEUTRAL,
            confidence=0.6,
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description="纺锤线表示市场犹豫不决，买卖双方势均力敌",
        ))
        
        return patterns
    
    def _identify_marubozu(self, candle: Candle, index: int) -> List[PatternResult]:
        """识别光头光脚线"""
        patterns = []
        
        if not candle.is_marubozu:
            return patterns
        
        # 光头光脚线表示强烈的方向性
        signal = PatternSignal.BULLISH if candle.is_bullish else PatternSignal.BEARISH
        
        patterns.append(PatternResult(
            pattern_name=PatternName.MARUBOZU,
            pattern_type=PatternType.CONTINUATION,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=0.8,
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=f"光头光脚线显示强烈的{'看涨' if candle.is_bullish else '看跌'}情绪",
        ))
        
        return patterns
    
    def _identify_shooting_star_inverted_hammer(self, candle: Candle, index: int, trend_context: str) -> List[PatternResult]:
        """识别流星和倒锤子"""
        patterns = []
        
        # 流星/倒锤子的特征：
        # 1. 小实体
        # 2. 长上影线（至少是实体的2倍）
        # 3. 无下影线或很短的下影线
        
        if not candle.is_small_body():
            return patterns
        
        if not candle.is_long_upper_shadow():
            return patterns
        
        # 下影线应该很短或没有
        if candle.has_lower_shadow and candle.lower_shadow > candle.body_size:
            return patterns
        
        # 根据趋势背景判断是流星还是倒锤子
        if trend_context == "uptrend":
            # 在上升趋势中是流星（看跌反转）
            patterns.append(PatternResult(
                pattern_name=PatternName.SHOOTING_STAR,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.SINGLE,
                signal=PatternSignal.BEARISH,
                confidence=0.75,
                start_index=index,
                end_index=index,
                start_time=candle.timestamp,
                end_time=candle.timestamp,
                description="流星在上升趋势中是看跌反转信号，表示上方抛压沉重",
                trend_context=trend_context
            ))
        elif trend_context == "downtrend":
            # 在下降趋势中是倒锤子（可能的看涨反转，需要确认）
            patterns.append(PatternResult(
                pattern_name=PatternName.INVERTED_HAMMER,
                pattern_type=PatternType.REVERSAL,
                pattern_category=PatternCategory.SINGLE,
                signal=PatternSignal.BULLISH,
                confidence=0.6,  # 倒锤子需要后续确认，置信度较低
                start_index=index,
                end_index=index,
                start_time=candle.timestamp,
                end_time=candle.timestamp,
                description="倒锤子在下降趋势中可能是看涨反转信号，但需要后续确认",
                trend_context=trend_context
            ))
        
        return patterns

    def _identify_long_candles(self, candle: Candle, index: int, avg_body_size: float, trend_context: str) -> List[PatternResult]:
        """识别长蜡烛线（大阳线/大阴线）"""
        patterns = []

        # 长蜡烛线：实体明显大于平均实体
        if candle.body_size < avg_body_size * 1.5:
            return patterns

        # 影线相对较短
        if candle.upper_shadow > candle.body_size * 0.3 or candle.lower_shadow > candle.body_size * 0.3:
            return patterns

        confidence = 0.7
        pattern_type = PatternType.CONTINUATION

        if candle.is_bullish:
            signal = PatternSignal.BULLISH
            description = "大阳线，强烈看涨信号"
            if trend_context == "uptrend":
                confidence += 0.1
                description += "，在上升趋势中确认趋势持续"
        else:
            signal = PatternSignal.BEARISH
            description = "大阴线，强烈看跌信号"
            if trend_context == "downtrend":
                confidence += 0.1
                description += "，在下降趋势中确认趋势持续"

        # 如果实体特别大，置信度更高
        if candle.body_size > avg_body_size * 2.0:
            confidence += 0.1
            description += "，实体极大"

        patterns.append(PatternResult(
            pattern_name="long_candle",
            pattern_type=pattern_type,
            pattern_category=PatternCategory.SINGLE,
            signal=signal,
            confidence=min(confidence, 1.0),
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description,
            trend_context=trend_context
        ))

        return patterns

    def _identify_high_wave_candles(self, candle: Candle, index: int, avg_range: float) -> List[PatternResult]:
        """识别高浪线（长上下影线的蜡烛线）"""
        patterns = []

        # 高浪线特征：
        # 1. 上下影线都很长
        # 2. 实体相对较小
        # 3. 总体价格区间较大

        if candle.range < avg_range * 1.2:
            return patterns

        if not (candle.is_long_upper_shadow(1.5) and candle.is_long_lower_shadow(1.5)):
            return patterns

        if not candle.is_small_body(0.4):
            return patterns

        confidence = 0.65
        description = "高浪线，上下影线都很长，显示市场激烈争夺"

        # 如果影线特别长，置信度更高
        if candle.upper_shadow > candle.body_size * 3 and candle.lower_shadow > candle.body_size * 3:
            confidence += 0.1
            description += "，影线极长"

        patterns.append(PatternResult(
            pattern_name="high_wave",
            pattern_type=PatternType.INDECISION,
            pattern_category=PatternCategory.SINGLE,
            signal=PatternSignal.NEUTRAL,
            confidence=confidence,
            start_index=index,
            end_index=index,
            start_time=candle.timestamp,
            end_time=candle.timestamp,
            description=description
        ))

        return patterns
