"""
TradingAgents服务 - 为网站提供多智能体验证功能
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加TradingAgents路径
trading_agents_path = Path(__file__).parent.parent / "trading_agents_core"
sys.path.insert(0, str(trading_agents_path))

from integrations.candlestick_integration import CandlestickPatternValidator
from core.agent_manager import AgentManager
from core.debate_system import DebateSystem
from utils.config import Config


class TradingAgentsWebService:
    """
    TradingAgents网站服务 - 为前端提供AI智能体功能
    """
    
    def __init__(self):
        """初始化服务"""
        self.logger = logging.getLogger("TradingAgentsWebService")
        
        # 初始化配置
        config_path = trading_agents_path / "config.yaml"
        self.config = Config(str(config_path))
        
        # 核心组件
        self.pattern_validator = None
        self.agent_manager = None
        self.debate_system = None
        
        # 服务状态
        self.is_initialized = False
        self.initialization_error = None
        
        self.logger.info("TradingAgents网站服务创建完成")
    
    async def initialize(self):
        """初始化服务"""
        if self.is_initialized:
            return True
        
        self.logger.info("🔧 初始化TradingAgents网站服务...")
        
        try:
            # 初始化蜡烛图形态验证器
            self.pattern_validator = CandlestickPatternValidator()
            await self.pattern_validator.initialize()
            
            # 初始化智能体管理器
            self.agent_manager = AgentManager(self.config)
            await self.agent_manager.initialize_agents()
            
            # 初始化辩论系统
            self.debate_system = DebateSystem(self.config)
            await self.debate_system.initialize()
            
            self.is_initialized = True
            self.logger.info("✅ TradingAgents网站服务初始化完成")
            return True
            
        except Exception as e:
            self.initialization_error = str(e)
            self.logger.error(f"❌ TradingAgents服务初始化失败: {e}")
            return False
    
    async def validate_pattern(self, pattern_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证蜡烛图形态
        
        Args:
            pattern_data: 包含形态信息的数据
            {
                'pattern_name': str,
                'symbol': str,
                'candle_data': List[Dict],
                'confidence': float
            }
            
        Returns:
            验证结果
        """
        if not self.is_initialized:
            await self.initialize()
        
        if not self.is_initialized:
            return {
                'success': False,
                'error': f'服务初始化失败: {self.initialization_error}',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            self.logger.info(f"🕯️ 验证形态: {pattern_data.get('pattern_name')} ({pattern_data.get('symbol')})")
            
            # 调用验证器
            result = await self.pattern_validator.validate_pattern(
                pattern_name=pattern_data['pattern_name'],
                symbol=pattern_data['symbol'],
                candle_data=pattern_data['candle_data'],
                pattern_confidence=pattern_data.get('confidence', 0.8)
            )
            
            # 格式化返回结果
            if result.get('validation_status') == 'completed':
                return {
                    'success': True,
                    'data': {
                        'pattern_name': result['pattern_name'],
                        'symbol': result['symbol'],
                        'original_confidence': result['original_confidence'],
                        'validation_result': result['final_assessment'],
                        'agent_analyses': self._format_agent_analyses(result.get('agent_validation', {})),
                        'debate_summary': self._format_debate_summary(result.get('debate_result', {})),
                        'candlestick_analysis': self._format_candlestick_analysis(result.get('candlestick_analysis', {}))
                    },
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', '验证失败'),
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"❌ 形态验证失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def batch_validate_patterns(self, patterns_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量验证形态"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.is_initialized:
            return {
                'success': False,
                'error': f'服务初始化失败: {self.initialization_error}'
            }
        
        try:
            self.logger.info(f"📊 批量验证{len(patterns_data)}个形态")
            
            # 执行批量验证
            results = await self.pattern_validator.batch_validate_patterns(patterns_data)
            
            # 生成摘要
            summary = await self.pattern_validator.get_validation_summary(results)
            
            return {
                'success': True,
                'data': {
                    'results': [self._format_validation_result(r) for r in results],
                    'summary': summary
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 批量验证失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_agent_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """获取智能体分析"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.is_initialized:
            return {
                'success': False,
                'error': f'服务初始化失败: {self.initialization_error}'
            }
        
        try:
            self.logger.info(f"🤖 获取智能体分析: {symbol}")
            
            # 执行多智能体分析
            analysis_result = await self.agent_manager.analyze_symbol(symbol, market_data)
            
            return {
                'success': True,
                'data': {
                    'symbol': symbol,
                    'agent_analyses': analysis_result.get('agent_analyses', {}),
                    'summary': analysis_result.get('summary', {}),
                    'timestamp': analysis_result.get('timestamp')
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 智能体分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def conduct_debate(self, topic: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """进行智能体辩论"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.is_initialized:
            return {
                'success': False,
                'error': f'服务初始化失败: {self.initialization_error}'
            }
        
        try:
            self.logger.info(f"🎭 进行智能体辩论: {topic}")
            
            # 执行辩论
            debate_result = await self.debate_system.conduct_debate(
                topic=topic,
                analysis_results=context
            )
            
            return {
                'success': True,
                'data': {
                    'topic': topic,
                    'debate_result': self._format_debate_summary(debate_result),
                    'rounds': debate_result.get('rounds', []),
                    'consensus': debate_result.get('summary', {})
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 智能体辩论失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            if not self.is_initialized:
                return {
                    'success': True,
                    'data': {
                        'status': 'not_initialized',
                        'initialized': False,
                        'error': self.initialization_error
                    }
                }
            
            # 获取各组件状态
            agent_status = await self.agent_manager.get_agent_status()
            debate_status = self.debate_system.get_status()
            
            return {
                'success': True,
                'data': {
                    'status': 'running',
                    'initialized': True,
                    'agents': agent_status,
                    'debate_system': debate_status,
                    'pattern_validator': {
                        'status': 'active',
                        'initialized': self.pattern_validator.is_initialized if self.pattern_validator else False
                    }
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取系统状态失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _format_agent_analyses(self, agent_validation: Dict[str, Any]) -> Dict[str, Any]:
        """格式化智能体分析结果"""
        agent_analyses = agent_validation.get('agent_analyses', {})
        formatted_analyses = {}
        
        for agent_name, analysis in agent_analyses.items():
            if 'error' not in analysis:
                formatted_analyses[agent_name] = {
                    'signal': analysis.get('signal', 'hold'),
                    'confidence': analysis.get('confidence', 0.5),
                    'sentiment': analysis.get('sentiment', 'neutral'),
                    'perspective': analysis.get('perspective', 'general'),
                    'summary': analysis.get('analysis', 'Analysis completed')
                }
            else:
                formatted_analyses[agent_name] = {
                    'error': analysis['error'],
                    'status': 'failed'
                }
        
        return {
            'analyses': formatted_analyses,
            'consensus_score': agent_validation.get('consensus_score', 0.5),
            'participating_agents': agent_validation.get('participating_agents', [])
        }
    
    def _format_debate_summary(self, debate_result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化辩论结果"""
        if not debate_result or debate_result.get('status') == 'failed':
            return {
                'status': 'failed',
                'error': debate_result.get('error', 'Debate failed')
            }
        
        summary = debate_result.get('summary', {})
        return {
            'status': 'completed',
            'decision': summary.get('decision', 'hold'),
            'confidence': summary.get('confidence', 0.5),
            'reasoning': summary.get('reasoning', 'No reasoning provided'),
            'total_rounds': summary.get('total_rounds', 0),
            'consensus_reached': debate_result.get('consensus_reached', False)
        }
    
    def _format_candlestick_analysis(self, candlestick_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """格式化蜡烛图分析结果"""
        if not candlestick_analysis:
            return {'status': 'not_available'}
        
        detected_patterns = candlestick_analysis.get('detected_patterns', [])
        overall_assessment = candlestick_analysis.get('overall_assessment', {})
        
        return {
            'status': 'completed',
            'detected_patterns': len(detected_patterns),
            'pattern_details': detected_patterns,
            'overall_assessment': {
                'pattern_count': overall_assessment.get('pattern_count', 0),
                'reliability': overall_assessment.get('overall_reliability', 'medium'),
                'market_outlook': overall_assessment.get('market_outlook', 'neutral'),
                'confidence': overall_assessment.get('confidence_level', 0.5)
            },
            'ai_analysis': candlestick_analysis.get('ai_analysis', 'AI analysis not available')
        }
    
    def _format_validation_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化单个验证结果"""
        if result.get('validation_status') != 'completed':
            return {
                'pattern_name': result.get('pattern_name', 'unknown'),
                'symbol': result.get('symbol', 'unknown'),
                'status': 'failed',
                'error': result.get('error', 'Validation failed')
            }
        
        final_assessment = result.get('final_assessment', {})
        return {
            'pattern_name': result['pattern_name'],
            'symbol': result['symbol'],
            'status': 'completed',
            'validation_conclusion': final_assessment.get('validation_conclusion', 'unknown'),
            'reliability_level': final_assessment.get('reliability_level', 'unknown'),
            'final_score': final_assessment.get('final_validation_score', 0.0),
            'confidence_improvement': final_assessment.get('validation_improvement', 0.0),
            'recommendations': final_assessment.get('recommendations', []),
            'risk_warnings': final_assessment.get('risk_warnings', [])
        }
    
    async def shutdown(self):
        """关闭服务"""
        self.logger.info("🔄 关闭TradingAgents网站服务...")
        
        try:
            if self.pattern_validator:
                await self.pattern_validator.shutdown()
            if self.debate_system:
                await self.debate_system.shutdown()
            if self.agent_manager:
                await self.agent_manager.shutdown()
            
            self.is_initialized = False
            self.logger.info("✅ TradingAgents网站服务关闭完成")
            
        except Exception as e:
            self.logger.error(f"❌ 服务关闭失败: {e}")


# 全局服务实例
_trading_agents_service = None


async def get_trading_agents_service() -> TradingAgentsWebService:
    """获取TradingAgents服务实例"""
    global _trading_agents_service
    
    if _trading_agents_service is None:
        _trading_agents_service = TradingAgentsWebService()
        await _trading_agents_service.initialize()
    
    return _trading_agents_service
