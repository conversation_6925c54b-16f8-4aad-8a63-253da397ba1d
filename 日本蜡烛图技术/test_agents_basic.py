"""
基础智能体系统测试
验证核心组件是否正常工作
"""

import asyncio
import sys
import os

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend/src'))

def test_llm_integration():
    """测试LLM集成"""
    print("🔧 测试LLM集成...")
    
    try:
        from llm_integration.llm_manager import llm_manager
        
        # 检查可用的提供商
        providers = llm_manager.get_available_providers()
        print(f"✅ 可用的LLM提供商: {providers}")
        
        # 检查默认提供商
        default = llm_manager.default_provider
        print(f"✅ 默认提供商: {default}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM集成测试失败: {e}")
        return False

async def test_llm_response():
    """测试LLM响应生成"""
    print("\n🧠 测试LLM响应生成...")
    
    try:
        from llm_integration.llm_manager import llm_manager
        
        # 简单的测试提示
        test_prompt = "请分析以下蜡烛图形态：锤子线形态出现在下降趋势的底部。"
        
        response = await llm_manager.generate_response(
            prompt=test_prompt,
            system_prompt="你是一位专业的蜡烛图分析师。"
        )
        
        print(f"✅ LLM响应生成成功:")
        print(f"   模型: {response.model}")
        print(f"   Token数量: {response.tokens_used}")
        print(f"   响应时间: {response.response_time:.2f}秒")
        print(f"   成本: ${response.cost:.4f}")
        print(f"   内容预览: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM响应测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_communication_hub():
    """测试通信中心"""
    print("\n📡 测试通信中心...")
    
    try:
        from agents.communication.hub import CommunicationHub
        
        hub = CommunicationHub()
        print("✅ 通信中心创建成功")
        
        # 检查路由规则
        rules = hub.routing_rules
        print(f"✅ 路由规则数量: {len(rules)}")
        
        # 检查工作流模板
        workflows = hub.workflow_templates
        print(f"✅ 工作流模板数量: {len(workflows)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 通信中心测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base_agent():
    """测试基础智能体"""
    print("\n🤖 测试基础智能体...")
    
    try:
        from agents.base.agent import AgentRole, MessageType
        
        # 检查枚举
        print(f"✅ 智能体角色数量: {len(list(AgentRole))}")
        print(f"✅ 消息类型数量: {len(list(MessageType))}")
        
        # 列出一些角色
        roles = [role.value for role in list(AgentRole)[:3]]
        print(f"✅ 示例角色: {roles}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_workflow():
    """测试简单工作流"""
    print("\n🔄 测试简单工作流...")
    
    try:
        from agents.communication.hub import CommunicationHub
        
        hub = CommunicationHub()
        await hub.start()
        
        print("✅ 通信中心启动成功")
        
        # 获取统计信息
        stats = hub.get_communication_stats()
        print(f"✅ 通信统计: {stats}")
        
        await hub.stop()
        print("✅ 通信中心停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🎯 多智能体系统基础测试")
    print("=" * 50)
    
    tests = [
        ("LLM集成", test_llm_integration),
        ("LLM响应", test_llm_response),
        ("通信中心", test_communication_hub),
        ("基础智能体", test_base_agent),
        ("简单工作流", test_simple_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统基础组件正常工作")
        print("\n🚀 下一步:")
        print("1. 配置真实的LLM API密钥")
        print("2. 运行完整的演示脚本")
        print("3. 集成到现有的后端API")
    else:
        print("⚠️  部分测试失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())
