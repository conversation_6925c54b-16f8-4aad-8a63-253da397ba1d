"""
多智能体交易系统演示
展示如何使用新的智能体架构进行市场分析
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from agents.base.agent import AgentRole
from agents.analysts.candlestick_expert import CandlestickPatternExpert
from agents.communication.hub import CommunicationHub


class SimpleResearchAgent:
    """简化的研究员智能体（演示用）"""
    
    def __init__(self, agent_id: str, bias: str):
        self.agent_id = agent_id
        self.bias = bias  # 'bullish' or 'bearish'
        self.role = AgentRole.BULLISH_RESEARCHER if bias == 'bullish' else AgentRole.BEARISH_RESEARCHER
        self.communication_hub = None
        self.is_active = True
    
    async def process_message(self, message):
        """处理消息"""
        if message.message_type.value == 'analysis_report':
            # 模拟研究员分析
            patterns = message.content.get('patterns', [])
            
            if self.bias == 'bullish':
                opinion = self._generate_bullish_opinion(patterns)
            else:
                opinion = self._generate_bearish_opinion(patterns)
            
            print(f"🔍 {self.agent_id}: {opinion['summary']}")
            return None
    
    def _generate_bullish_opinion(self, patterns):
        bullish_patterns = [p for p in patterns if p.get('signal') == 'BULLISH']
        return {
            'summary': f"发现{len(bullish_patterns)}个看涨形态，建议关注买入机会",
            'confidence': 0.8 if bullish_patterns else 0.3
        }
    
    def _generate_bearish_opinion(self, patterns):
        bearish_patterns = [p for p in patterns if p.get('signal') == 'BEARISH']
        return {
            'summary': f"发现{len(bearish_patterns)}个看跌形态，建议谨慎观望",
            'confidence': 0.8 if bearish_patterns else 0.3
        }
    
    def get_status(self):
        return {
            'agent_id': self.agent_id,
            'role': self.role.value,
            'is_active': self.is_active
        }


async def demo_multi_agent_analysis():
    """演示多智能体分析流程"""
    
    print("🚀 启动多智能体交易分析系统演示")
    print("=" * 60)
    
    # 1. 创建通信中心
    hub = CommunicationHub()
    await hub.start()
    print("✅ 通信中心已启动")
    
    # 2. 创建智能体
    # 蜡烛图专家
    candlestick_expert = CandlestickPatternExpert(
        agent_id="candlestick_expert_001",
        llm_config={
            'model': 'gpt-4',
            'temperature': 0.1
        }
    )
    
    # 研究员
    bullish_researcher = SimpleResearchAgent("bullish_researcher_001", "bullish")
    bearish_researcher = SimpleResearchAgent("bearish_researcher_001", "bearish")
    
    # 注册智能体
    hub.register_agent(candlestick_expert)
    hub.register_agent(bullish_researcher)
    hub.register_agent(bearish_researcher)
    
    print("✅ 智能体团队已组建:")
    print("   📈 蜡烛图形态专家")
    print("   🐂 看涨研究员")
    print("   🐻 看跌研究员")
    print()
    
    # 3. 准备测试数据
    test_market_data = {
        'symbol': 'AAPL',
        'candles': [
            # 模拟一个下降趋势后的反转形态
            {'open': 150.0, 'high': 152.0, 'low': 148.0, 'close': 149.0, 'volume': 1000000, 'timestamp': '2024-01-01T09:00:00Z'},
            {'open': 149.0, 'high': 150.0, 'low': 146.0, 'close': 147.0, 'volume': 1100000, 'timestamp': '2024-01-01T10:00:00Z'},
            {'open': 147.0, 'high': 148.0, 'low': 144.0, 'close': 145.0, 'volume': 1200000, 'timestamp': '2024-01-01T11:00:00Z'},
            {'open': 145.0, 'high': 146.0, 'low': 142.0, 'close': 143.0, 'volume': 1300000, 'timestamp': '2024-01-01T12:00:00Z'},
            {'open': 143.0, 'high': 144.0, 'low': 140.0, 'close': 141.0, 'volume': 1400000, 'timestamp': '2024-01-01T13:00:00Z'},
            # 启明星形态
            {'open': 141.0, 'high': 142.0, 'low': 138.0, 'close': 139.0, 'volume': 1500000, 'timestamp': '2024-01-01T14:00:00Z'},  # 大阴线
            {'open': 139.0, 'high': 140.0, 'low': 138.5, 'close': 139.2, 'volume': 800000, 'timestamp': '2024-01-01T15:00:00Z'},   # 小实体
            {'open': 139.5, 'high': 143.0, 'low': 139.0, 'close': 142.0, 'volume': 1600000, 'timestamp': '2024-01-01T16:00:00Z'},  # 大阳线
        ]
    }
    
    print("📊 开始分析市场数据...")
    print(f"   标的: {test_market_data['symbol']}")
    print(f"   数据点: {len(test_market_data['candles'])}根蜡烛")
    print()
    
    # 4. 执行分析
    try:
        print("🔍 蜡烛图专家开始分析...")
        analysis_result = await candlestick_expert.analyze(test_market_data)
        
        print("✅ 形态识别完成!")
        print(f"   发现形态: {len(analysis_result['patterns'])}个")
        print(f"   综合置信度: {analysis_result['confidence']:.2f}")
        print()
        
        # 显示识别到的形态
        if analysis_result['patterns']:
            print("📋 识别到的形态:")
            for i, pattern in enumerate(analysis_result['patterns'][:3], 1):
                print(f"   {i}. {pattern['chinese_name']} ({pattern['name']})")
                print(f"      信号: {pattern['signal']}")
                print(f"      置信度: {pattern['confidence']:.2f}")
                print(f"      位置: 第{pattern['start_index']}-{pattern['end_index']}根蜡烛")
                print()
        
        # 显示交易信号
        if analysis_result['signals']:
            print("💡 交易信号:")
            for i, signal in enumerate(analysis_result['signals'][:2], 1):
                print(f"   {i}. {signal['pattern_name']}")
                print(f"      方向: {signal['signal_type']}")
                print(f"      强度: {signal['strength']:.2f}")
                print(f"      风险等级: {signal['risk_level']}")
                print()
        
        # 显示分析推理
        print("🧠 分析推理:")
        print(f"   {analysis_result['reasoning']}")
        print()
        
        # 5. 发送分析报告给研究员
        print("📤 向研究团队发送分析报告...")
        
        from agents.base.agent import Message, MessageType
        
        message = Message(
            message_type=MessageType.ANALYSIS_REPORT,
            from_agent=candlestick_expert.agent_id,
            to_agents=[bullish_researcher.agent_id, bearish_researcher.agent_id],
            content=analysis_result,
            timestamp=datetime.now(),
            message_id=f"analysis_{datetime.now().timestamp()}"
        )
        
        await hub.send_message(message)
        
        # 等待消息处理
        await asyncio.sleep(1)
        
        print()
        print("📊 智能体状态:")
        agent_statuses = hub.get_agent_status()
        for agent_id, status in agent_statuses.items():
            print(f"   {agent_id}: {status['role']} - {'🟢 活跃' if status['is_active'] else '🔴 非活跃'}")
        
        print()
        print("📈 通信统计:")
        comm_stats = hub.get_communication_stats()
        print(f"   发送消息: {comm_stats['messages_sent']}")
        print(f"   处理消息: {comm_stats['messages_processed']}")
        print(f"   活跃智能体: {comm_stats['active_agents']}")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 6. 清理资源
        await hub.stop()
        print()
        print("🛑 系统已停止")


async def demo_workflow():
    """演示工作流功能"""
    print("\n" + "=" * 60)
    print("🔄 工作流演示")
    print("=" * 60)
    
    hub = CommunicationHub()
    await hub.start()
    
    # 启动市场分析工作流
    workflow_id = await hub.start_workflow(
        workflow_type='market_analysis',
        input_data={
            'symbol': 'AAPL',
            'timeframe': '1h',
            'analysis_type': 'comprehensive'
        }
    )
    
    print(f"✅ 工作流已启动: {workflow_id}")
    
    # 模拟工作流进度
    await asyncio.sleep(2)
    
    # 完成分析阶段
    await hub.complete_workflow_stage(
        workflow_id=workflow_id,
        stage_name='analysis',
        result={
            'technical_analysis': {'patterns': 3, 'signals': 2},
            'fundamental_analysis': {'rating': 'BUY', 'target': 160}
        }
    )
    
    print("✅ 分析阶段完成")
    
    await asyncio.sleep(1)
    
    # 完成研究阶段
    await hub.complete_workflow_stage(
        workflow_id=workflow_id,
        stage_name='research',
        result={
            'bullish_view': {'confidence': 0.8, 'reasoning': '技术面支持突破'},
            'bearish_view': {'confidence': 0.3, 'reasoning': '基本面存在风险'}
        }
    )
    
    print("✅ 研究阶段完成")
    
    # 检查工作流状态
    status = hub.get_workflow_status(workflow_id)
    if status:
        print(f"📊 工作流进度: {status['progress']:.1%}")
        print(f"   已完成阶段: {', '.join(status['completed_stages'])}")
    
    await hub.stop()


if __name__ == "__main__":
    print("🎯 多智能体交易系统演示程序")
    print("基于TradingAgents框架理论构建")
    print()
    
    # 运行演示
    asyncio.run(demo_multi_agent_analysis())
    
    # 运行工作流演示
    asyncio.run(demo_workflow())
    
    print("\n🎉 演示完成!")
    print("\n💡 下一步:")
    print("1. 集成真实的LLM API")
    print("2. 添加更多智能体类型")
    print("3. 完善前端界面")
    print("4. 添加历史数据回测")
    print("5. 实现实时市场数据接入")
