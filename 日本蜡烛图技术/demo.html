<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🕯️ AI智能体投资分析平台 - 演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .demo-card h3 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .api-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .api-demo h3 {
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .api-test {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .api-test h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .api-test pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running {
            background: #52c41a;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background: #ff4d4f;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            color: white;
            text-align: center;
        }
        
        .feature-item h4 {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕯️ AI智能体投资分析平台</h1>
            <p>基于《日本蜡烛图技术》+ TradingAgents + GPT-4的革命性投资决策系统</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🔧 后端API服务</h3>
                <p><span class="status-indicator status-running"></span>服务状态：正在运行</p>
                <p>完整的RESTful API接口，支持蜡烛图形态识别、多智能体分析、工作流管理等功能。</p>
                <a href="http://localhost:8000/docs" target="_blank" class="demo-button">
                    打开API文档
                </a>
            </div>
            
            <div class="demo-card">
                <h3>🤖 智能体辩论系统</h3>
                <p>看涨vs看跌研究员实时辩论，通过AI观点碰撞提高投资决策质量。</p>
                <button class="demo-button" onclick="runDebateDemo()">
                    启动辩论演示
                </button>
            </div>
            
            <div class="demo-card">
                <h3>📊 形态识别测试</h3>
                <p>基于《日本蜡烛图技术》的经典形态识别算法，支持20+种经典形态。</p>
                <button class="demo-button" onclick="testPatternRecognition()">
                    测试形态识别
                </button>
            </div>
            
            <div class="demo-card">
                <h3>🧠 LLM智能分析</h3>
                <p>集成GPT-4的专业市场分析，提供机构级投资建议和风险评估。</p>
                <button class="demo-button" onclick="testLLMAnalysis()">
                    测试智能分析
                </button>
            </div>
        </div>
        
        <div class="api-demo">
            <h3>🚀 快速API测试</h3>
            
            <div class="api-test">
                <h4>1. 健康检查</h4>
                <button class="demo-button" onclick="testHealth()">测试健康检查</button>
                <pre id="health-result">点击按钮测试API连接...</pre>
            </div>
            
            <div class="api-test">
                <h4>2. 智能体状态查询</h4>
                <button class="demo-button" onclick="testAgentStatus()">查询智能体状态</button>
                <pre id="agent-result">点击按钮查询智能体状态...</pre>
            </div>
            
            <div class="api-test">
                <h4>3. 蜡烛图形态分析</h4>
                <button class="demo-button" onclick="testPatternAnalysis()">分析示例数据</button>
                <pre id="pattern-result">点击按钮分析蜡烛图形态...</pre>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-item">
                <h4>🧠 AI智能体团队</h4>
                <p>5个专业智能体协作</p>
            </div>
            <div class="feature-item">
                <h4>🎭 辩论决策机制</h4>
                <p>看涨vs看跌观点碰撞</p>
            </div>
            <div class="feature-item">
                <h4>📊 专业形态识别</h4>
                <p>20+种经典蜡烛图形态</p>
            </div>
            <div class="feature-item">
                <h4>🔄 实时工作流</h4>
                <p>结构化决策流程</p>
            </div>
        </div>
        
        <div class="footer">
            <p>🌟 这是全球首个AI智能体驱动的投资决策辩论系统</p>
            <p>结合传统技术分析与现代AI技术的革命性平台</p>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 测试健康检查
        async function testHealth() {
            const resultElement = document.getElementById('health-result');
            resultElement.textContent = '正在测试...';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `错误: ${error.message}`;
            }
        }
        
        // 测试智能体状态
        async function testAgentStatus() {
            const resultElement = document.getElementById('agent-result');
            resultElement.textContent = '正在查询智能体状态...';
            
            try {
                const response = await fetch(`${API_BASE}/agents/status`);
                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `错误: ${error.message}`;
            }
        }
        
        // 测试形态分析
        async function testPatternAnalysis() {
            const resultElement = document.getElementById('pattern-result');
            resultElement.textContent = '正在分析蜡烛图形态...';
            
            const sampleData = {
                candles: [
                    {"open": 100.0, "high": 102.0, "low": 99.0, "close": 99.5, "volume": 1500, "timestamp": "2024-01-01T09:00:00Z"},
                    {"open": 99.5, "high": 100.5, "low": 98.0, "close": 98.2, "volume": 1600, "timestamp": "2024-01-01T10:00:00Z"},
                    {"open": 98.2, "high": 99.0, "low": 97.0, "close": 97.5, "volume": 1700, "timestamp": "2024-01-01T11:00:00Z"},
                    {"open": 97.5, "high": 98.0, "low": 96.0, "close": 96.8, "volume": 1800, "timestamp": "2024-01-01T12:00:00Z"},
                    {"open": 96.8, "high": 97.5, "low": 95.5, "close": 97.2, "volume": 1900, "timestamp": "2024-01-01T13:00:00Z"},
                    {"open": 97.2, "high": 98.5, "low": 96.8, "close": 98.0, "volume": 2000, "timestamp": "2024-01-01T14:00:00Z"}
                ]
            };
            
            try {
                const response = await fetch(`${API_BASE}/analyze`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(sampleData)
                });
                const data = await response.json();
                resultElement.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = `错误: ${error.message}`;
            }
        }
        
        // 运行辩论演示
        function runDebateDemo() {
            alert('🎭 智能体辩论演示\n\n这将启动看涨vs看跌研究员的实时辩论。\n\n请在终端中运行：\npython3 standalone_debate_demo.py');
        }
        
        // 测试形态识别
        function testPatternRecognition() {
            alert('📊 形态识别测试\n\n系统支持识别20+种经典蜡烛图形态：\n• 启明星/黄昏星\n• 三只乌鸦/前进白色三兵\n• 锤子线/上吊线\n• 吞没形态\n• 十字星系列\n\n请使用上方的"分析示例数据"按钮测试！');
        }
        
        // 测试LLM分析
        function testLLMAnalysis() {
            alert('🧠 LLM智能分析\n\n系统集成GPT-4提供：\n• 专业市场解读\n• 投资建议生成\n• 风险评估分析\n• 多智能体协作\n\n当前使用模拟响应，配置真实API密钥可获得完整功能！');
        }
        
        // 页面加载时自动测试健康检查
        window.onload = function() {
            setTimeout(testHealth, 1000);
        };
    </script>
</body>
</html>
