#!/usr/bin/env python3
"""
快速测试多智能体系统的基础功能
"""

import os
import sys

def test_imports():
    """测试基础导入"""
    print("🔧 测试基础导入...")
    
    try:
        # 测试LLM集成
        from llm_integration.llm_manager import llm_manager
        print("✅ LLM管理器导入成功")
        
        # 测试智能体基础类
        from agents.base.agent import AgentRole, MessageType
        print("✅ 智能体基础类导入成功")
        
        # 测试通信中心
        from agents.communication.hub import CommunicationHub
        print("✅ 通信中心导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_manager():
    """测试LLM管理器"""
    print("\n🧠 测试LLM管理器...")
    
    try:
        from llm_integration.llm_manager import llm_manager
        
        # 检查提供商
        providers = llm_manager.get_available_providers()
        print(f"✅ 可用提供商: {providers}")
        
        # 检查默认提供商
        default = llm_manager.default_provider
        print(f"✅ 默认提供商: {default}")
        
        # 检查统计信息
        stats = llm_manager.get_provider_stats()
        print(f"✅ 提供商统计: {len(stats)} 个提供商")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM管理器测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("\n🌍 测试环境配置...")
    
    # 检查环境变量
    env_vars = [
        'MOCK_LLM_RESPONSES',
        'DEFAULT_LLM_PROVIDER',
        'DEBUG'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'Not Set')
        print(f"   {var}: {value}")
    
    # 检查.env文件
    env_file = '.env'
    if os.path.exists(env_file):
        print(f"✅ 环境配置文件存在: {env_file}")
    else:
        print(f"⚠️  环境配置文件不存在: {env_file}")
    
    return True

def test_file_structure():
    """测试文件结构"""
    print("\n📁 测试文件结构...")
    
    required_dirs = [
        'agents',
        'agents/base',
        'agents/communication',
        'agents/analysts',
        'llm_integration',
        'llm_integration/providers'
    ]
    
    required_files = [
        'agents/base/agent.py',
        'agents/communication/hub.py',
        'agents/analysts/candlestick_expert.py',
        'llm_integration/llm_manager.py',
        'llm_integration/providers/base.py',
        'llm_integration/providers/openai_provider.py',
        'llm_integration/providers/mock_provider.py'
    ]
    
    all_good = True
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ 目录存在: {dir_path}")
        else:
            print(f"❌ 目录缺失: {dir_path}")
            all_good = False
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ 文件存在: {file_path}")
        else:
            print(f"❌ 文件缺失: {file_path}")
            all_good = False
    
    return all_good

def main():
    """主测试函数"""
    print("🎯 多智能体系统快速测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("环境配置", test_environment),
        ("基础导入", test_imports),
        ("LLM管理器", test_llm_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔍 运行测试: {test_name}")
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 基础测试全部通过！")
        print("\n🚀 下一步:")
        print("1. 运行异步测试: python -c \"import asyncio; from quick_test_async import test_async; asyncio.run(test_async())\"")
        print("2. 配置LLM API密钥（如果需要真实调用）")
        print("3. 运行完整演示")
    else:
        print("⚠️  部分测试失败，请检查文件结构和导入")
        
        if passed >= 2:  # 如果基础结构OK
            print("\n💡 建议:")
            print("- 检查Python路径设置")
            print("- 确认所有依赖已安装")
            print("- 查看详细错误信息")

if __name__ == "__main__":
    main()
