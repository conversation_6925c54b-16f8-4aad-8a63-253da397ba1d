#!/usr/bin/env python3
"""
测试形态识别功能
"""

import requests
import json
from datetime import datetime

def test_pattern_analysis():
    """测试形态分析API"""
    print("🧪 测试形态识别功能...")
    
    # 模拟蜡烛图数据
    test_candles = [
        {"open": 100, "high": 105, "low": 98, "close": 102, "volume": 1000, "timestamp": "2024-01-01T09:00:00Z"},
        {"open": 102, "high": 108, "low": 101, "close": 107, "volume": 1200, "timestamp": "2024-01-01T10:00:00Z"},
        {"open": 107, "high": 109, "low": 105, "close": 106, "volume": 900, "timestamp": "2024-01-01T11:00:00Z"},
        {"open": 106, "high": 106, "low": 103, "close": 104, "volume": 800, "timestamp": "2024-01-01T12:00:00Z"},
        {"open": 104, "high": 107, "low": 102, "close": 105, "volume": 1100, "timestamp": "2024-01-01T13:00:00Z"}
    ]
    
    # 测试API调用
    try:
        url = "http://localhost:3000/api/v1/patterns/analyze"
        payload = {
            "candles": test_candles,
            "start_index": 0,
            "end_index": None
        }
        
        print(f"📡 发送请求到: {url}")
        print(f"📊 蜡烛数据: {len(test_candles)} 根蜡烛线")
        
        response = requests.post(url, json=payload, timeout=10)
        
        print(f"📈 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 形态识别成功!")
            print(f"📋 响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查响应结构
            if 'data' in result and 'patterns' in result['data']:
                patterns = result['data']['patterns']
                print(f"\n🎯 识别到的形态:")
                for i, pattern in enumerate(patterns, 1):
                    print(f"  {i}. {pattern.get('chinese_name', pattern.get('name', 'Unknown'))}")
                    print(f"     - 置信度: {pattern.get('confidence', 0):.2%}")
                    print(f"     - 类型: {pattern.get('type', 'Unknown')}")
                    print(f"     - 位置: {pattern.get('position', 'Unknown')}")
                    print(f"     - 描述: {pattern.get('description', 'No description')}")
            else:
                print("⚠️ 响应格式异常")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 服务器未运行或端口错误")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_trading_agents_status():
    """测试TradingAgents状态"""
    print("\n🤖 测试TradingAgents状态...")
    
    try:
        url = "http://localhost:3000/api/trading-agents/status"
        response = requests.get(url, timeout=10)
        
        print(f"📈 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ TradingAgents状态正常!")
            
            if 'data' in result:
                data = result['data']
                agents = data.get('agents', {})
                print(f"🤖 智能体总数: {agents.get('total_agents', 0)}")
                print(f"🟢 活跃智能体: {agents.get('active_agents', 0)}")
                
                if 'agent_details' in agents:
                    print("📋 智能体详情:")
                    for name, details in agents['agent_details'].items():
                        status = "🟢" if details.get('is_active') else "🔴"
                        print(f"  {status} {name}: {details.get('specialization', 'Unknown')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_pattern_validation():
    """测试形态验证"""
    print("\n🔍 测试形态验证...")
    
    try:
        url = "http://localhost:3000/api/trading-agents/validate-pattern"
        payload = {
            "pattern_name": "doji",
            "symbol": "TEST",
            "confidence": 0.75,
            "candle_data": [
                {"open": 100, "high": 101, "low": 99, "close": 100, "volume": 1000}
            ]
        }
        
        response = requests.post(url, json=payload, timeout=10)
        
        print(f"📈 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 形态验证成功!")
            
            if 'data' in result and 'validation_result' in result['data']:
                validation = result['data']['validation_result']
                print(f"🎯 验证结论: {validation.get('validation_conclusion', 'Unknown')}")
                print(f"📊 可靠性: {validation.get('reliability_level', 'Unknown')}")
                print(f"🔢 最终得分: {validation.get('final_validation_score', 0):.2%}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试蜡烛图形态识别系统")
    print("=" * 50)
    
    # 测试各个功能
    test_pattern_analysis()
    test_trading_agents_status()
    test_pattern_validation()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成!")
    print("\n💡 使用提示:")
    print("1. 如果所有测试都通过，说明后端API正常工作")
    print("2. 现在可以在浏览器中使用示例数据测试前端")
    print("3. 如果前端仍有问题，请检查浏览器控制台的错误信息")

if __name__ == "__main__":
    main()
