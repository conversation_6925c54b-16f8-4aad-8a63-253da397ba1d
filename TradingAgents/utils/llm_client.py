"""
LLM Client - Handles communication with various LLM providers
"""

import asyncio
import aiohttp
import json
import logging
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class LLMClient:
    """
    Universal LLM client supporting multiple providers
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LLM client"""
        self.config = config
        self.logger = logging.getLogger("LLMClient")
        
        # Get provider configuration
        self.provider = config.get('provider', 'deepseek')
        self.model = config.get('model', 'deepseek-v3')
        self.temperature = config.get('temperature', 0.3)
        self.max_tokens = config.get('max_tokens', 2000)
        self.timeout = config.get('timeout', 30)
        self.retry_attempts = config.get('retry_attempts', 3)
        
        # Provider-specific configuration
        if self.provider == 'deepseek':
            self.api_key = os.getenv('DEEPSEEK_API_KEY')
            self.api_url = os.getenv('DEEPSEEK_API_URL', 'https://api.siliconflow.cn/v1/chat/completions')
            self.model = os.getenv('DEEPSEEK_MODEL', 'deepseek-ai/DeepSeek-V3')
        elif self.provider == 'openai':
            self.api_key = os.getenv('OPENAI_API_KEY')
            self.api_url = 'https://api.openai.com/v1/chat/completions'
            self.model = config.get('model', 'gpt-4')
        else:
            self.logger.warning(f"Unknown provider: {self.provider}, falling back to mock")
            self.provider = 'mock'
        
        self.logger.info(f"LLM Client initialized with provider: {self.provider}, model: {self.model}")
    
    async def generate_response(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """
        Generate response from LLM
        
        Args:
            prompt: User prompt
            system_prompt: System prompt (optional)
            **kwargs: Additional parameters
            
        Returns:
            Response dictionary with content and metadata
        """
        if self.provider == 'mock':
            return await self._mock_response(prompt, system_prompt)
        
        for attempt in range(self.retry_attempts):
            try:
                if self.provider == 'deepseek':
                    return await self._deepseek_request(prompt, system_prompt, **kwargs)
                elif self.provider == 'openai':
                    return await self._openai_request(prompt, system_prompt, **kwargs)
                else:
                    return await self._mock_response(prompt, system_prompt)
                    
            except Exception as e:
                self.logger.warning(f"LLM request attempt {attempt + 1} failed: {e}")
                if attempt == self.retry_attempts - 1:
                    self.logger.error(f"All LLM request attempts failed, falling back to mock response")
                    return await self._mock_response(prompt, system_prompt)
                await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff
    
    async def _deepseek_request(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Make request to DeepSeek API via SiliconFlow"""
        if not self.api_key:
            raise ValueError("DeepSeek API key not configured")
        
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Prepare request payload
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get('temperature', self.temperature),
            "max_tokens": kwargs.get('max_tokens', self.max_tokens),
            "stream": False
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            async with session.post(self.api_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    return {
                        'content': result['choices'][0]['message']['content'],
                        'model': self.model,
                        'provider': self.provider,
                        'usage': result.get('usage', {}),
                        'timestamp': datetime.now().isoformat(),
                        'success': True
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"DeepSeek API error {response.status}: {error_text}")
    
    async def _openai_request(self, prompt: str, system_prompt: str = None, **kwargs) -> Dict[str, Any]:
        """Make request to OpenAI API"""
        if not self.api_key:
            raise ValueError("OpenAI API key not configured")
        
        # Prepare messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Prepare request payload
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": kwargs.get('temperature', self.temperature),
            "max_tokens": kwargs.get('max_tokens', self.max_tokens)
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            async with session.post(self.api_url, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    return {
                        'content': result['choices'][0]['message']['content'],
                        'model': self.model,
                        'provider': self.provider,
                        'usage': result.get('usage', {}),
                        'timestamp': datetime.now().isoformat(),
                        'success': True
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"OpenAI API error {response.status}: {error_text}")
    
    async def _mock_response(self, prompt: str, system_prompt: str = None) -> Dict[str, Any]:
        """Generate mock response for testing"""
        await asyncio.sleep(0.1)  # Simulate API delay
        
        # Generate contextual mock response based on prompt content
        if "market analysis" in prompt.lower() or "technical analysis" in prompt.lower():
            content = "Based on technical analysis, the market shows mixed signals with RSI at neutral levels and MACD indicating potential bullish momentum. Volume patterns suggest moderate investor interest."
        elif "bullish" in prompt.lower() or "buy" in prompt.lower():
            content = "Strong bullish indicators are present including positive earnings momentum, sector rotation benefits, and technical breakout patterns. Recommend considering long positions with appropriate risk management."
        elif "bearish" in prompt.lower() or "sell" in prompt.lower():
            content = "Several bearish factors warrant caution including overvaluation concerns, weakening fundamentals, and potential market headwinds. Risk management should be prioritized."
        elif "portfolio" in prompt.lower() or "risk" in prompt.lower():
            content = "From a portfolio management perspective, position sizing should be conservative at 3-5% allocation with stop-loss at 8% below entry. Diversification across sectors recommended."
        elif "debate" in prompt.lower() or "consensus" in prompt.lower():
            content = "After considering multiple viewpoints, the analysis suggests a balanced approach with slight bullish bias. Consensus indicates cautious optimism with risk-aware positioning."
        else:
            content = "Analysis complete. Based on available data and market conditions, recommend maintaining current strategy with close monitoring of key indicators and risk factors."
        
        return {
            'content': content,
            'model': 'mock-model',
            'provider': 'mock',
            'usage': {'prompt_tokens': len(prompt), 'completion_tokens': len(content), 'total_tokens': len(prompt) + len(content)},
            'timestamp': datetime.now().isoformat(),
            'success': True,
            'mock': True
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check LLM client health"""
        try:
            test_response = await self.generate_response("Hello, this is a health check.")
            
            return {
                'status': 'healthy',
                'provider': self.provider,
                'model': self.model,
                'response_time': 'normal',
                'last_check': datetime.now().isoformat(),
                'test_successful': test_response.get('success', False)
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'provider': self.provider,
                'model': self.model,
                'error': str(e),
                'last_check': datetime.now().isoformat(),
                'test_successful': False
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get client status"""
        return {
            'provider': self.provider,
            'model': self.model,
            'temperature': self.temperature,
            'max_tokens': self.max_tokens,
            'api_configured': bool(self.api_key),
            'timeout': self.timeout,
            'retry_attempts': self.retry_attempts
        }
