"""
Agent Manager - Manages the lifecycle and coordination of all trading agents
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from agents.base.base_agent import BaseAgent
from agents.market_analyst import MarketAnalyst
from agents.bull_researcher import BullResearcher
from agents.bear_researcher import BearResearcher
from agents.portfolio_manager import PortfolioManager
from agents.debate_moderator import DebateModerator
from agents.execution_agent import ExecutionAgent
from utils.config import Config


class AgentManager:
    """
    Manages all trading agents and their interactions
    """
    
    def __init__(self, config: Config):
        """Initialize the agent manager"""
        self.config = config
        self.logger = logging.getLogger("AgentManager")
        
        # Agent registry
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_configs = config.get('agents', {})
        
        # Agent status tracking
        self.agent_status: Dict[str, Dict[str, Any]] = {}
        
        self.logger.info("AgentManager initialized")
    
    async def initialize_agents(self):
        """Initialize all configured agents"""
        self.logger.info("Initializing trading agents...")
        
        # Define agent classes
        agent_classes = {
            'market_analyst': <PERSON><PERSON><PERSON><PERSON><PERSON>,
            'bull_researcher': <PERSON><PERSON><PERSON><PERSON><PERSON>,
            'bear_researcher': <PERSON>Researcher,
            'portfolio_manager': PortfolioManager,
            'debate_moderator': DebateModerator,
            'execution_agent': ExecutionAgent
        }
        
        # Initialize each enabled agent
        for agent_type, agent_class in agent_classes.items():
            agent_config = self.agent_configs.get(agent_type, {})
            
            if agent_config.get('enabled', False):
                try:
                    # Create agent instance
                    agent = agent_class(
                        name=agent_config.get('name', agent_type),
                        config=agent_config,
                        global_config=self.config
                    )
                    
                    # Initialize the agent
                    await agent.initialize()
                    
                    # Register the agent
                    self.agents[agent_type] = agent
                    
                    # Update status
                    self.agent_status[agent_type] = {
                        'status': 'active',
                        'initialized_at': datetime.now().isoformat(),
                        'capabilities': agent_config.get('capabilities', []),
                        'specialization': agent_config.get('specialization', 'general')
                    }
                    
                    self.logger.info(f"✅ {agent_type} agent initialized")
                    
                except Exception as e:
                    self.logger.error(f"❌ Failed to initialize {agent_type} agent: {e}")
                    self.agent_status[agent_type] = {
                        'status': 'failed',
                        'error': str(e),
                        'failed_at': datetime.now().isoformat()
                    }
            else:
                self.logger.info(f"⏸️ {agent_type} agent disabled in configuration")
        
        self.logger.info(f"Agent initialization complete. Active agents: {len(self.agents)}")
    
    async def analyze_symbol(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate multi-agent analysis of a symbol"""
        self.logger.info(f"Starting multi-agent analysis for {symbol}")
        
        analysis_results = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'agent_analyses': {},
            'summary': {}
        }
        
        # Run analysis with available agents
        analysis_tasks = []
        
        # Market Analyst
        if 'market_analyst' in self.agents:
            task = self._run_agent_analysis('market_analyst', symbol, market_data)
            analysis_tasks.append(('market_analyst', task))
        
        # Bull Researcher
        if 'bull_researcher' in self.agents:
            task = self._run_agent_analysis('bull_researcher', symbol, market_data)
            analysis_tasks.append(('bull_researcher', task))
        
        # Bear Researcher
        if 'bear_researcher' in self.agents:
            task = self._run_agent_analysis('bear_researcher', symbol, market_data)
            analysis_tasks.append(('bear_researcher', task))
        
        # Portfolio Manager
        if 'portfolio_manager' in self.agents:
            task = self._run_agent_analysis('portfolio_manager', symbol, market_data)
            analysis_tasks.append(('portfolio_manager', task))
        
        # Execute all analysis tasks concurrently
        if analysis_tasks:
            results = await asyncio.gather(
                *[task for _, task in analysis_tasks],
                return_exceptions=True
            )
            
            # Process results
            for i, (agent_name, _) in enumerate(analysis_tasks):
                result = results[i]
                if isinstance(result, Exception):
                    self.logger.error(f"❌ {agent_name} analysis failed: {result}")
                    analysis_results['agent_analyses'][agent_name] = {
                        'status': 'failed',
                        'error': str(result)
                    }
                else:
                    analysis_results['agent_analyses'][agent_name] = result
                    self.logger.info(f"✅ {agent_name} analysis completed")
        
        # Generate summary
        analysis_results['summary'] = self._generate_analysis_summary(analysis_results['agent_analyses'])
        
        self.logger.info(f"Multi-agent analysis completed for {symbol}")
        return analysis_results
    
    async def _run_agent_analysis(self, agent_name: str, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run analysis with a specific agent"""
        agent = self.agents[agent_name]
        
        try:
            # Call the agent's analyze method
            result = await agent.analyze(symbol, market_data)
            
            return {
                'status': 'success',
                'agent': agent_name,
                'analysis': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Agent {agent_name} analysis failed: {e}")
            raise
    
    def _generate_analysis_summary(self, agent_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a summary from all agent analyses"""
        summary = {
            'total_agents': len(agent_analyses),
            'successful_analyses': 0,
            'failed_analyses': 0,
            'consensus_signals': {},
            'conflicting_views': [],
            'overall_sentiment': 'neutral'
        }
        
        signals = []
        sentiments = []
        
        for agent_name, analysis in agent_analyses.items():
            if analysis.get('status') == 'success':
                summary['successful_analyses'] += 1
                
                # Extract signals and sentiments
                agent_analysis = analysis.get('analysis', {})
                if 'signal' in agent_analysis:
                    signals.append(agent_analysis['signal'])
                if 'sentiment' in agent_analysis:
                    sentiments.append(agent_analysis['sentiment'])
                    
            else:
                summary['failed_analyses'] += 1
        
        # Calculate consensus
        if signals:
            buy_signals = signals.count('buy')
            sell_signals = signals.count('sell')
            hold_signals = signals.count('hold')
            
            summary['consensus_signals'] = {
                'buy': buy_signals,
                'sell': sell_signals,
                'hold': hold_signals,
                'total': len(signals)
            }
            
            # Determine overall signal
            if buy_signals > sell_signals and buy_signals > hold_signals:
                summary['overall_signal'] = 'buy'
            elif sell_signals > buy_signals and sell_signals > hold_signals:
                summary['overall_signal'] = 'sell'
            else:
                summary['overall_signal'] = 'hold'
        
        # Calculate sentiment
        if sentiments:
            bullish_count = sentiments.count('bullish')
            bearish_count = sentiments.count('bearish')
            
            if bullish_count > bearish_count:
                summary['overall_sentiment'] = 'bullish'
            elif bearish_count > bullish_count:
                summary['overall_sentiment'] = 'bearish'
            else:
                summary['overall_sentiment'] = 'neutral'
        
        return summary
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgent]:
        """Get a specific agent by name"""
        return self.agents.get(agent_name)
    
    def get_active_agents(self) -> List[str]:
        """Get list of active agent names"""
        return list(self.agents.keys())
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        status = {
            'total_agents': len(self.agent_status),
            'active_agents': len(self.agents),
            'agent_details': {}
        }
        
        for agent_name, agent_status in self.agent_status.items():
            agent = self.agents.get(agent_name)
            
            status['agent_details'][agent_name] = {
                **agent_status,
                'is_active': agent is not None,
                'last_activity': agent.get_last_activity() if agent else None
            }
        
        return status
    
    async def restart_agent(self, agent_name: str) -> bool:
        """Restart a specific agent"""
        self.logger.info(f"Restarting agent: {agent_name}")
        
        try:
            # Stop the agent if it exists
            if agent_name in self.agents:
                await self.agents[agent_name].shutdown()
                del self.agents[agent_name]
            
            # Reinitialize the agent
            agent_config = self.agent_configs.get(agent_name, {})
            if agent_config.get('enabled', False):
                # This would need the agent class mapping logic
                # For now, we'll just mark it as needing restart
                self.agent_status[agent_name] = {
                    'status': 'restarting',
                    'restart_requested_at': datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ Agent {agent_name} restart initiated")
                return True
            else:
                self.logger.warning(f"Agent {agent_name} is disabled in configuration")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to restart agent {agent_name}: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown all agents"""
        self.logger.info("Shutting down all agents...")
        
        shutdown_tasks = []
        for agent_name, agent in self.agents.items():
            task = agent.shutdown()
            shutdown_tasks.append((agent_name, task))
        
        if shutdown_tasks:
            results = await asyncio.gather(
                *[task for _, task in shutdown_tasks],
                return_exceptions=True
            )
            
            for i, (agent_name, _) in enumerate(shutdown_tasks):
                result = results[i]
                if isinstance(result, Exception):
                    self.logger.error(f"❌ Error shutting down {agent_name}: {result}")
                else:
                    self.logger.info(f"✅ {agent_name} shutdown complete")
        
        self.agents.clear()
        self.logger.info("All agents shutdown complete")
