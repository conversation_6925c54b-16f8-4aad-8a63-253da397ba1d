"""
Market Data Provider - Handles market data collection and management
"""

import asyncio
import logging
from typing import Dict, Any
from datetime import datetime, timedelta
import random


class MarketDataProvider:
    """Provides market data for trading agents"""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger("MarketDataProvider")
        self.market_config = config.get('market_data', {})
        
        self.cache = {}
        self.cache_ttl = self.market_config.get('cache', {}).get('ttl', 300)
        
        self.logger.info("MarketDataProvider initialized")
    
    async def initialize(self):
        """Initialize market data provider"""
        self.logger.info("Initializing market data provider...")
        self.logger.info("✅ Market data provider ready")
    
    async def get_symbol_data(self, symbol: str, timeframe: str = "1H") -> Dict[str, Any]:
        """Get market data for a symbol"""
        self.logger.info(f"📊 Getting market data for {symbol}")
        
        # Check cache first
        cache_key = f"{symbol}_{timeframe}"
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if self._is_cache_valid(cached_data['timestamp']):
                self.logger.info(f"Using cached data for {symbol}")
                return cached_data['data']
        
        # Generate mock market data
        market_data = await self._generate_mock_data(symbol, timeframe)
        
        # Cache the data
        self.cache[cache_key] = {
            'data': market_data,
            'timestamp': datetime.now()
        }
        
        return market_data
    
    async def _generate_mock_data(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """Generate mock market data for testing"""
        base_price = 100.0 + random.uniform(-20, 20)
        
        # Generate candle data
        candles = []
        for i in range(50):  # 50 periods of data
            open_price = base_price + random.uniform(-2, 2)
            high_price = open_price + random.uniform(0, 3)
            low_price = open_price - random.uniform(0, 3)
            close_price = open_price + random.uniform(-2, 2)
            volume = random.randint(100000, 1000000)
            
            candle = {
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'timestamp': (datetime.now() - timedelta(hours=50-i)).isoformat()
            }
            candles.append(candle)
            base_price = close_price  # Use close as next base
        
        # Generate fundamental data
        fundamentals = {
            'pe_ratio': round(15 + random.uniform(-5, 10), 2),
            'pb_ratio': round(2 + random.uniform(-1, 2), 2),
            'market_cap': random.randint(1000000000, 100000000000),
            'revenue_growth': round(random.uniform(-0.1, 0.3), 3),
            'debt_to_equity': round(random.uniform(0.1, 2.0), 2),
            'current_ratio': round(random.uniform(0.5, 3.0), 2),
            'roe': round(random.uniform(0.05, 0.25), 3),
            'roa': round(random.uniform(0.02, 0.15), 3),
            'profit_margin': round(random.uniform(0.05, 0.30), 3)
        }
        
        return {
            'symbol': symbol,
            'timeframe': timeframe,
            'candles': candles,
            'fundamentals': fundamentals,
            'current_price': candles[-1]['close'],
            'previous_close': candles[-2]['close'] if len(candles) > 1 else candles[-1]['close'],
            'data_source': 'mock_data',
            'timestamp': datetime.now().isoformat()
        }
    
    def _is_cache_valid(self, timestamp: datetime) -> bool:
        """Check if cached data is still valid"""
        return (datetime.now() - timestamp).total_seconds() < self.cache_ttl
    
    async def get_status(self) -> Dict[str, Any]:
        """Get market data provider status"""
        return {
            'provider': 'mock_data',
            'cache_size': len(self.cache),
            'cache_ttl': self.cache_ttl,
            'status': 'active'
        }
    
    async def shutdown(self):
        """Shutdown market data provider"""
        self.logger.info("Shutting down market data provider...")
        self.cache.clear()
        self.logger.info("✅ Market data provider shutdown complete")
