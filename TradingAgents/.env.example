# TradingAgents Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# AI/LLM Configuration
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Anthropic Configuration (optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# DeepSeek Configuration (SiliconFlow)
DEEPSEEK_API_KEY=sk-bwcddjskpuvtqvxzqowssqkxhfjuutwazmjhwroxekgrtxki
DEEPSEEK_API_URL=https://api.siliconflow.cn/deepseek-ai/DeepSeek-V3
DEEPSEEK_MODEL=deepseek-v3

# =============================================================================
# Market Data Providers
# =============================================================================

# Alpha Vantage (for stock data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Yahoo Finance (free, no key required)
YAHOO_FINANCE_ENABLED=true

# Polygon.io (optional)
POLYGON_API_KEY=your_polygon_api_key_here

# IEX Cloud (optional)
IEX_CLOUD_API_KEY=your_iex_cloud_api_key_here

# =============================================================================
# Trading/Broker Configuration
# =============================================================================

# Interactive Brokers
IB_HOST=127.0.0.1
IB_PORT=7497
IB_CLIENT_ID=1

# Alpaca Trading
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets  # Use paper trading by default

# TD Ameritrade (optional)
TD_AMERITRADE_API_KEY=your_td_ameritrade_api_key_here
TD_AMERITRADE_REDIRECT_URI=your_redirect_uri_here

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_agents
DB_USER=postgres
DB_PASSWORD=your_postgres_password_here

# SQLite (alternative)
SQLITE_DB_PATH=./data/trading_agents.db

# =============================================================================
# Cache Configuration
# =============================================================================

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# =============================================================================
# Monitoring & Alerting
# =============================================================================

# Sentry (error tracking)
SENTRY_DSN=your_sentry_dsn_here

# Slack (notifications)
SLACK_WEBHOOK_URL=your_slack_webhook_url_here
SLACK_CHANNEL=#trading-alerts

# Email (notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
NOTIFICATION_EMAIL=<EMAIL>

# =============================================================================
# Security Configuration
# =============================================================================

# JWT Secret (for API authentication)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Keys (for external access)
API_KEY=your_api_key_here

# Encryption Key
ENCRYPTION_KEY=your_encryption_key_here

# =============================================================================
# System Configuration
# =============================================================================

# Environment
ENVIRONMENT=development  # development, staging, production
DEBUG=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/trading_agents.log

# Timezone
TIMEZONE=UTC

# =============================================================================
# Performance Configuration
# =============================================================================

# Worker processes
WORKER_PROCESSES=4
WORKER_THREADS=2

# Memory limits
MAX_MEMORY_MB=2048

# Request timeouts
REQUEST_TIMEOUT=30
LLM_TIMEOUT=60

# =============================================================================
# Feature Flags
# =============================================================================

# Enable/disable features
ENABLE_LIVE_TRADING=false
ENABLE_PAPER_TRADING=true
ENABLE_BACKTESTING=true
ENABLE_REAL_TIME_DATA=true
ENABLE_AGENT_DEBATES=true
ENABLE_RISK_MANAGEMENT=true
ENABLE_NOTIFICATIONS=true
ENABLE_METRICS=true

# =============================================================================
# Development Configuration
# =============================================================================

# Development tools
ENABLE_DEBUG_MODE=true
ENABLE_PROFILING=false
ENABLE_TESTING_MODE=false

# Mock data (for development)
USE_MOCK_DATA=false
MOCK_DATA_PATH=./data/mock/

# =============================================================================
# External Services
# =============================================================================

# News APIs
NEWS_API_KEY=your_news_api_key_here
ALPHA_VANTAGE_NEWS_ENABLED=true

# Social Sentiment
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# Economic Data
FRED_API_KEY=your_fred_api_key_here

# =============================================================================
# Backup & Recovery
# =============================================================================

# Backup configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups/

# Cloud storage (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_S3_BUCKET=your_s3_bucket_here
AWS_REGION=us-east-1

# =============================================================================
# Notes
# =============================================================================

# 1. Never commit the actual .env file to version control
# 2. Keep your API keys secure and rotate them regularly
# 3. Use paper trading accounts for testing
# 4. Set up proper monitoring and alerting for production
# 5. Review and update these settings regularly
