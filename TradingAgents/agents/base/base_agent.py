"""
Base Agent Class - Foundation for all trading agents
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime
import json

from utils.config import Config
from utils.llm_client import LLMClient


class BaseAgent(ABC):
    """
    Abstract base class for all trading agents
    """
    
    def __init__(self, name: str, config: Dict[str, Any], global_config: Config):
        """Initialize the base agent"""
        self.name = name
        self.config = config
        self.global_config = global_config
        self.logger = logging.getLogger(f"Agent.{name}")
        
        # Agent properties
        self.agent_type = self.__class__.__name__
        self.specialization = config.get('specialization', 'general')
        self.capabilities = config.get('capabilities', [])
        
        # LLM configuration
        self.model = config.get('model', 'gpt-4')
        self.temperature = config.get('temperature', 0.3)
        self.max_tokens = config.get('max_tokens', 1000)
        
        # State tracking
        self.is_initialized = False
        self.is_active = False
        self.last_activity = None
        self.analysis_count = 0
        
        # Performance metrics
        self.metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'average_response_time': 0.0,
            'last_error': None
        }

        # Initialize LLM client
        try:
            llm_config = global_config.get('llm', {})
            self.llm_client = LLMClient(llm_config)
        except Exception as e:
            self.logger.warning(f"Failed to initialize LLM client: {e}")
            self.llm_client = None

        self.logger.info(f"Agent {name} ({self.agent_type}) created")
    
    async def initialize(self):
        """Initialize the agent"""
        self.logger.info(f"Initializing agent {self.name}...")
        
        try:
            # Perform agent-specific initialization
            await self._initialize_agent()
            
            self.is_initialized = True
            self.is_active = True
            self.last_activity = datetime.now()
            
            self.logger.info(f"✅ Agent {self.name} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Agent {self.name} initialization failed: {e}")
            raise
    
    @abstractmethod
    async def _initialize_agent(self):
        """Agent-specific initialization logic"""
        pass
    
    @abstractmethod
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform analysis on a given symbol
        
        Args:
            symbol: Stock symbol to analyze
            market_data: Market data for the symbol
            
        Returns:
            Analysis result dictionary
        """
        pass
    
    async def process_request(self, request_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a generic request
        
        Args:
            request_type: Type of request
            data: Request data
            
        Returns:
            Response dictionary
        """
        self.logger.info(f"Processing {request_type} request")
        
        start_time = datetime.now()
        
        try:
            # Update activity tracking
            self.last_activity = start_time
            self.analysis_count += 1
            
            # Route to appropriate handler
            if request_type == 'analyze':
                result = await self.analyze(data.get('symbol'), data.get('market_data', {}))
            elif request_type == 'debate':
                result = await self.participate_in_debate(data.get('topic'), data.get('context', {}))
            else:
                result = await self._handle_custom_request(request_type, data)
            
            # Update metrics
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics(True, response_time)
            
            return {
                'status': 'success',
                'agent': self.name,
                'request_type': request_type,
                'result': result,
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            # Update metrics
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_metrics(False, response_time, str(e))
            
            self.logger.error(f"❌ Request processing failed: {e}")
            
            return {
                'status': 'error',
                'agent': self.name,
                'request_type': request_type,
                'error': str(e),
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
    
    async def participate_in_debate(self, topic: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Participate in a debate
        
        Args:
            topic: Debate topic
            context: Debate context and previous arguments
            
        Returns:
            Agent's argument and position
        """
        self.logger.info(f"Participating in debate: {topic}")
        
        try:
            # Generate agent's perspective based on specialization
            argument = await self._generate_debate_argument(topic, context)
            
            return {
                'agent': self.name,
                'specialization': self.specialization,
                'position': argument.get('position', 'neutral'),
                'argument': argument.get('argument', ''),
                'evidence': argument.get('evidence', []),
                'confidence': argument.get('confidence', 0.5),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Debate participation failed: {e}")
            raise
    
    @abstractmethod
    async def _generate_debate_argument(self, topic: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate argument for debate"""
        pass
    
    async def _generate_llm_response(self, prompt: str, system_prompt: str = None) -> str:
        """Generate response using LLM client"""
        if not self.llm_client:
            return f"LLM not available. Mock response for: {prompt[:100]}..."

        try:
            response = await self.llm_client.generate_response(prompt, system_prompt)
            return response.get('content', 'No response generated')
        except Exception as e:
            self.logger.error(f"LLM generation failed: {e}")
            return f"Analysis unavailable due to technical issues. Mock response for: {prompt[:100]}..."

    async def _handle_custom_request(self, request_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle custom request types"""
        self.logger.warning(f"Unhandled request type: {request_type}")
        return {'message': f'Request type {request_type} not supported by {self.name}'}
    
    def _update_metrics(self, success: bool, response_time: float, error: str = None):
        """Update agent performance metrics"""
        self.metrics['total_analyses'] += 1
        
        if success:
            self.metrics['successful_analyses'] += 1
        else:
            self.metrics['failed_analyses'] += 1
            self.metrics['last_error'] = error
        
        # Update average response time
        total_time = self.metrics['average_response_time'] * (self.metrics['total_analyses'] - 1)
        self.metrics['average_response_time'] = (total_time + response_time) / self.metrics['total_analyses']
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'name': self.name,
            'type': self.agent_type,
            'specialization': self.specialization,
            'capabilities': self.capabilities,
            'is_initialized': self.is_initialized,
            'is_active': self.is_active,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'analysis_count': self.analysis_count,
            'metrics': self.metrics
        }
    
    def get_last_activity(self) -> Optional[datetime]:
        """Get last activity timestamp"""
        return self.last_activity
    
    def get_capabilities(self) -> List[str]:
        """Get agent capabilities"""
        return self.capabilities
    
    def get_specialization(self) -> str:
        """Get agent specialization"""
        return self.specialization
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            # Basic health check
            health_status = {
                'agent': self.name,
                'status': 'healthy' if self.is_active else 'inactive',
                'initialized': self.is_initialized,
                'last_activity': self.last_activity.isoformat() if self.last_activity else None,
                'metrics': self.metrics,
                'timestamp': datetime.now().isoformat()
            }
            
            # Agent-specific health checks
            agent_health = await self._agent_health_check()
            health_status.update(agent_health)
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"❌ Health check failed: {e}")
            return {
                'agent': self.name,
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _agent_health_check(self) -> Dict[str, Any]:
        """Agent-specific health check"""
        return {'agent_specific_health': 'ok'}
    
    async def shutdown(self):
        """Shutdown the agent"""
        self.logger.info(f"Shutting down agent {self.name}...")
        
        try:
            # Perform agent-specific cleanup
            await self._shutdown_agent()
            
            self.is_active = False
            self.logger.info(f"✅ Agent {self.name} shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Agent {self.name} shutdown failed: {e}")
            raise
    
    async def _shutdown_agent(self):
        """Agent-specific shutdown logic"""
        pass
    
    def __str__(self) -> str:
        """String representation of the agent"""
        return f"{self.agent_type}(name={self.name}, specialization={self.specialization})"
    
    def __repr__(self) -> str:
        """Detailed representation of the agent"""
        return (f"{self.agent_type}(name={self.name}, specialization={self.specialization}, "
                f"active={self.is_active}, analyses={self.analysis_count})")
