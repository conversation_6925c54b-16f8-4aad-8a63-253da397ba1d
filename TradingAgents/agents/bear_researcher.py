"""
Bear Researcher Agent - Specialized in identifying bearish risks and threats
"""

from typing import Dict, Any
from datetime import datetime
from agents.base.base_agent import BaseAgent


class BearResearcher(BaseAgent):
    """Bear Researcher Agent focusing on bearish analysis and risk identification"""
    
    async def _initialize_agent(self):
        """Initialize bear researcher capabilities"""
        self.logger.info("Initializing Bear Researcher capabilities...")
        
        self.focus_areas = [
            'risk_factors', 'downside_catalysts', 'negative_news',
            'earnings_misses', 'analyst_downgrades', 'market_headwinds'
        ]
        
        self.logger.info("✅ Bear Researcher initialized")
    
    async def analyze(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform bearish analysis"""
        self.logger.info(f"🐻 Performing bearish analysis for {symbol}")
        
        analysis = {
            'symbol': symbol,
            'analyst': self.name,
            'perspective': 'bearish',
            'timestamp': datetime.now().isoformat(),
            'risks': await self._identify_risks(symbol, market_data),
            'threats': await self._identify_threats(symbol, market_data),
            'weakness': await self._analyze_weakness(symbol, market_data),
            'signal': 'sell',  # Bear researcher tends to be bearish
            'confidence': 0.6,
            'sentiment': 'bearish'
        }
        
        return analysis
    
    async def _identify_risks(self, symbol: str, market_data: Dict[str, Any]) -> list:
        """Identify bearish risks"""
        return [
            "Overvaluation concerns",
            "Technical resistance levels",
            "Market volatility risks"
        ]
    
    async def _identify_threats(self, symbol: str, market_data: Dict[str, Any]) -> list:
        """Identify potential threats"""
        return [
            "Competitive pressure",
            "Regulatory challenges",
            "Economic headwinds"
        ]
    
    async def _analyze_weakness(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze bearish weakness"""
        return {
            'price_weakness': 'potential',
            'volume_weakness': 'declining',
            'relative_weakness': 'underperforming'
        }
    
    async def _generate_debate_argument(self, topic: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate bearish argument for debate"""
        return {
            'position': 'bearish',
            'argument': f"There are significant risks and concerns regarding {topic}",
            'evidence': [
                "Technical weakness signals",
                "Fundamental concerns",
                "Unfavorable risk-reward ratio"
            ],
            'confidence': 0.7
        }
