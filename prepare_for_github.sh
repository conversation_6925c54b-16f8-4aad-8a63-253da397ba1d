#!/bin/bash

# 蜡烛图形态识别系统 - GitHub上传准备脚本
# 作者: Bert-cheng
# 日期: $(date +%Y-%m-%d)

echo "🚀 准备蜡烛图形态识别系统上传到GitHub..."
echo "============================================================"

# 1. 清理不需要的文件
echo "🧹 清理不需要的文件..."

# 删除构建文件
rm -rf frontend/build/
rm -rf frontend/dist/
rm -rf backend/dist/
rm -rf backend/__pycache__/
rm -rf backend/src/__pycache__/
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null
find . -name "*.pyd" -delete 2>/dev/null

# 删除日志文件
rm -rf logs/
rm -rf backend/logs/
rm -rf TradingAgents/logs/
find . -name "*.log" -delete 2>/dev/null

# 删除临时文件
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
rm -rf temp/
rm -rf tmp/

# 删除IDE文件
rm -rf .vscode/
rm -rf .idea/
find . -name "*.swp" -delete 2>/dev/null
find . -name "*.swo" -delete 2>/dev/null

# 删除OS生成的文件
find . -name ".DS_Store" -delete 2>/dev/null
find . -name "._*" -delete 2>/dev/null
find . -name "Thumbs.db" -delete 2>/dev/null

# 删除node_modules
rm -rf frontend/node_modules/
rm -rf node_modules/

# 2. 清理敏感信息
echo "🔒 清理敏感信息..."

# 备份并清理环境变量文件
if [ -f "backend/src/trading_agents_core/.env" ]; then
    echo "# 请添加您的API密钥" > backend/src/trading_agents_core/.env.example
    echo "DEEPSEEK_API_KEY=your_deepseek_api_key_here" >> backend/src/trading_agents_core/.env.example
    echo "OPENAI_API_KEY=your_openai_api_key_here" >> backend/src/trading_agents_core/.env.example
    rm -f backend/src/trading_agents_core/.env
fi

if [ -f "TradingAgents/.env" ]; then
    cp TradingAgents/.env TradingAgents/.env.example 2>/dev/null
    rm -f TradingAgents/.env
fi

# 清理配置文件中的敏感信息
if [ -f "backend/src/trading_agents_core/config.yaml" ]; then
    cp backend/src/trading_agents_core/config.yaml backend/src/trading_agents_core/config.yaml.example 2>/dev/null
fi

if [ -f "TradingAgents/config.yaml" ]; then
    cp TradingAgents/config.yaml TradingAgents/config.yaml.example 2>/dev/null
fi

# 3. 检查项目结构
echo "📁 检查项目结构..."
echo "主要目录:"
ls -la | grep "^d"

echo ""
echo "前端文件:"
ls -la frontend/ | head -10

echo ""
echo "后端文件:"
ls -la backend/ | head -10

# 4. 验证重要文件存在
echo "✅ 验证重要文件..."

important_files=(
    "README.md"
    "frontend/package.json"
    "frontend/src/App.js"
    "backend/requirements.txt"
    "complete_web_server.py"
    ".gitignore"
)

for file in "${important_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - 存在"
    else
        echo "❌ $file - 缺失"
    fi
done

# 5. 统计代码行数
echo ""
echo "📊 代码统计:"
echo "Python文件:"
find . -name "*.py" -not -path "./TradingAgents/*" | wc -l | xargs echo "文件数量:"
find . -name "*.py" -not -path "./TradingAgents/*" -exec wc -l {} + | tail -1 | awk '{print "总行数: " $1}'

echo ""
echo "JavaScript/React文件:"
find frontend/src -name "*.js" -o -name "*.jsx" | wc -l | xargs echo "文件数量:"
find frontend/src -name "*.js" -o -name "*.jsx" -exec wc -l {} + | tail -1 | awk '{print "总行数: " $1}'

# 6. 生成项目信息
echo ""
echo "🎯 项目信息:"
echo "项目名称: 基于《日本蜡烛图技术》的智能形态识别系统"
echo "技术栈: Python (FastAPI) + React + TradingAgents"
echo "主要功能: 蜡烛图形态识别、AI智能体验证、市场分析、投资建议"
echo "作者: Bert-cheng"
echo "准备时间: $(date)"

echo ""
echo "============================================================"
echo "✅ 项目准备完成！"
echo ""
echo "📋 接下来的步骤:"
echo "1. 检查 .gitignore 文件确保敏感信息被忽略"
echo "2. 在GitHub上创建新仓库或清理现有仓库"
echo "3. 初始化Git仓库: git init"
echo "4. 添加文件: git add ."
echo "5. 提交: git commit -m '初始提交: 蜡烛图形态识别系统'"
echo "6. 连接远程仓库: git remote add origin https://github.com/Bert-cheng/your-repo-name.git"
echo "7. 推送: git push -u origin main"
echo ""
echo "⚠️  注意: 请确保在上传前检查所有敏感信息已被移除！"
echo "============================================================"
